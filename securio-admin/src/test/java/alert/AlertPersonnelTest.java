package alert;

import com.wiwj.securio.alert.domain.Alert;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警人员信息功能测试
 */
public class AlertPersonnelTest {

    @Test
    public void testAssignedUserIdsManagement() {
        Alert alert = new Alert();
        
        // 测试添加分配人
        alert.addAssignedUserId("1");
        alert.addAssignedUserId("2");
        alert.addAssignedUserId("3");
        
        assertEquals("1,2,3", alert.getAssignedUserIds());
        assertTrue(alert.hasAssignedUserId("1"));
        assertTrue(alert.hasAssignedUserId("2"));
        assertTrue(alert.hasAssignedUserId("3"));
        assertFalse(alert.hasAssignedUserId("4"));
        
        // 测试移除分配人
        alert.removeAssignedUserId("2");
        assertEquals("1,3", alert.getAssignedUserIds());
        assertTrue(alert.hasAssignedUserId("1"));
        assertFalse(alert.hasAssignedUserId("2"));
        assertTrue(alert.hasAssignedUserId("3"));
        
        // 测试获取分配人列表
        assertEquals(2, alert.getAssignedUserIdsList().size());
        assertTrue(alert.getAssignedUserIdsList().contains("1"));
        assertTrue(alert.getAssignedUserIdsList().contains("3"));
    }

    @Test
    public void testChannelInfoManagement() {
        Alert alert = new Alert();
        
        // 测试设置协作空间信息
        alert.setChannelInfo("channel_123", "安全事件处理群");
        
        assertTrue(alert.hasChannelInfo());
        assertEquals("channel_123", alert.getChannelId());
        assertEquals("安全事件处理群", alert.getChannelName());
        assertEquals("安全事件处理群", alert.getChannelDisplayName());
        
        // 测试协作空间归属检查
        assertTrue(alert.belongsToChannel("channel_123"));
        assertFalse(alert.belongsToChannel("channel_456"));
        
        // 测试清空协作空间信息
        alert.setChannelInfo(null, null);
        assertFalse(alert.hasChannelInfo());
        assertEquals("未分配", alert.getChannelDisplayName());
    }

    @Test
    public void testPersonnelWorkflow() {
        Alert alert = new Alert();
        
        // 模拟告警处理流程
        // 1. 分配人员
        alert.addAssignedUserId("1");
        alert.addAssignedUserId("2");
        assertEquals("1,2", alert.getAssignedUserIds());
        
        // 2. 认领告警
        alert.setAckedBy("1");
        alert.setAckedByName("张三");
        alert.setAckedAt(System.currentTimeMillis());
        
        assertEquals("1", alert.getAckedBy());
        assertEquals("张三", alert.getAckedByName());
        assertNotNull(alert.getAckedAt());
        
        // 3. 解决告警
        alert.setResolvedBy("1");
        alert.setResolvedByName("张三");
        alert.setResolvedAt(System.currentTimeMillis());
        alert.setStatus("resolved");
        
        assertEquals("1", alert.getResolvedBy());
        assertEquals("张三", alert.getResolvedByName());
        assertNotNull(alert.getResolvedAt());
        assertTrue(alert.isResolved());
    }

    @Test
    public void testEmptyAssignedUserIds() {
        Alert alert = new Alert();
        
        // 测试空字符串处理
        alert.setAssignedUserIds("");
        assertTrue(alert.getAssignedUserIdsList().isEmpty());
        assertFalse(alert.hasAssignedUserId("1"));
        
        // 测试null值处理
        alert.setAssignedUserIds(null);
        assertTrue(alert.getAssignedUserIdsList().isEmpty());
        assertFalse(alert.hasAssignedUserId("1"));
        
        // 测试只有逗号的情况
        alert.setAssignedUserIds(",");
        assertTrue(alert.getAssignedUserIdsList().isEmpty());
        
        // 测试包含空格的逗号分隔
        alert.setAssignedUserIds(" 1 , 2 , 3 ");
        assertEquals(3, alert.getAssignedUserIdsList().size());
        assertTrue(alert.getAssignedUserIdsList().contains("1"));
        assertTrue(alert.getAssignedUserIdsList().contains("2"));
        assertTrue(alert.getAssignedUserIdsList().contains("3"));
    }
} 