package alert;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wiwj.SecurioApplication;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.SysUserAccount;
import com.wiwj.securio.alert.integration.flashduty.model.*;
import com.wiwj.securio.alert.integration.flashduty.service.FlashDutyService;
import com.wiwj.securio.alert.service.IAlertAsyncService;
import com.wiwj.securio.alert.service.IAlertExportService;
import com.wiwj.securio.alert.service.IAlertService;
import com.wiwj.securio.alert.service.ISysUserAccountService;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@SpringBootTest(classes = SecurioApplication.class)
public class AlertTest {


    @Autowired
    private IAlertService alertService;

    @Autowired
    private FlashDutyService flashDutyService;

    @Autowired
    private IAlertExportService alertExportService;

    @Autowired
    private IAlertAsyncService alertAsyncService;

    private static final Logger logger = LoggerFactory.getLogger(AlertTest.class);

    @Test
    public void testQueryAlertList() {
        // 创建请求参数
        AlertListRequest request = new AlertListRequest();

        // 设置时间范围为最近24小时
        long now = Instant.now().getEpochSecond();
        long oneDayAgo = Instant.now().minus(1, ChronoUnit.DAYS).getEpochSecond();
        request.setStart_time(oneDayAgo);
        request.setEnd_time(now);

        // 设置分页参数
        request.setP(1);
        request.setLimit(10);

        Map<String, String> labels = Maps.newHashMap();
//        labels.put("eventId", "shm990059114864161");
        labels.put("eventId", "shm1022915614675801");
        request.setLabels(labels);

        // 查询告警列表
        AlertListResponse response = flashDutyService.queryAlertList(request);

        // 打印结果
        logger.info("查询结果: {}", JSON.toJSONString(response));

        // 如果有数据，打印第一条告警信息
        if (response != null && response.getData() != null &&
                response.getData().getItems() != null && !response.getData().getItems().isEmpty()) {

            logger.info("第一条告警信息: {}", JSON.toJSONString(response.getData().getItems().get(0)));
        } else {
            logger.info("未查询到告警数据");
        }
    }

    @Test
    public void testSyncIncident() {
        alertAsyncService.batchSyncFlashDutyIncidentsForRecentAlerts();
    }

    @Test
    public void testRemoveIncident() {

        List<PageIncidentResponseData.IncidentItem> allLists = new ArrayList<>();
        boolean hasNext = true;
        String search_after_ctx = null;

        while (hasNext) {
            IncidentListRequest request1 = new IncidentListRequest();
            request1.setEnd_time(System.currentTimeMillis() / 1000);
            request1.setStart_time(System.currentTimeMillis() / 1000 - 24 * 60 * 60);
            //统一告警测试组
            request1.setChannel_ids(Arrays.asList(4858823265785L));
            request1.setSearch_after_ctx(search_after_ctx);
            request1.setLimit(100);
            IncidentListResponse incidentListResponse = flashDutyService.queryIncidentList(request1);
            PageIncidentResponseData data = incidentListResponse.getData();
            List<PageIncidentResponseData.IncidentItem> items = data.getItems();
            allLists.addAll(items);
            if (!data.getHas_next_page()) {
                hasNext = false;
            } else {
                search_after_ctx = data.getSearch_after_ctx();
            }
        }


        AtomicInteger count = new AtomicInteger();

        allLists.forEach(item -> {
            System.out.println(item.getTitle());
            if (item.getTitle().contains("Zeek异常检测")) {
                count.getAndIncrement();
            }
        });

        System.out.println("一共：" + allLists.size());
        System.out.println("zeek条目：" + count.get());

        List<String> ids = allLists.stream().map(PageIncidentResponseData.IncidentItem::getIncident_id).collect(Collectors.toList());

        //批量删除告警数据
        Lists.partition(ids, 100).forEach(pids -> {
            IncidentRemoveRequest request = new IncidentRemoveRequest();
            request.setIncident_ids(pids);
            IncidentRemoveResponse incidentRemoveResponse = flashDutyService.removeIncidents(request);
        });

    }


    @Test
    public void testGetIncidentList() {
        IncidentListRequest request = new IncidentListRequest();
        long endTime = System.currentTimeMillis() / 1000;
        long startTime = endTime - 3600 * 24;
        request.setLimit(100);
        request.setStart_time(startTime);
        request.setEnd_time(endTime);
//        List<Long> channelIds = Arrays.asList(2664529391785L, 2662743577785L, 2663447718785L);
//        request.setChannel_ids(channelIds);
        HashMap<String, String> labels = Maps.newHashMap();
        labels.put("eventId", "shm990059114864161");
        request.setLabels(labels);
        IncidentListResponse incidentListResponse = flashDutyService.queryIncidentList(request);

        PageIncidentResponseData data = incidentListResponse.getData();
        List<PageIncidentResponseData.IncidentItem> items = data.getItems();
        items.forEach(item -> {
            if (item.getLabels() != null && item.getLabels().size() > 0 && item.getLabels().containsKey("machineIP")) {
                System.out.println(item.getTitle());
            }
        });

    }

    @Autowired
    private ISysUserAccountService sysUserAccountService;

    @Test
    public void testAcknowledgeIncidents() {
        // 测试认领故障功能
        List<String> incidentIds = Arrays.asList("685ba1178502884031be15f0");
        String appkey = sysUserAccountService.getAppKeyByUserId(8415549L);
        FlashDutyResponse.FlashDutyError error = flashDutyService.acknowledgeIncidents(incidentIds, appkey);

        if (error != null) {
            System.out.println("认领故障失败，错误码: " + error.getCode() + ", 错误信息: " + error.getMessage());
        } else {
            System.out.println("认领故障成功");
        }
    }

    @Test
    public void testAssignedUserIds() {
        System.out.println("=== 测试分派人员ID列表功能 ===");

        Alert alert = new Alert();
        alert.setTitle("测试告警");
        alert.setDescription("测试分派人员功能");
        alert.setSeverity("high");
        alert.setStatus("triggered");

        // 测试设置分派人员ID列表
        List<String> userIds = Arrays.asList("user001", "user002", "user003");
        alert.setAssignedUserIdsList(userIds);

        System.out.println("设置的分派人员ID字符串: " + alert.getAssignedUserIds());
        System.out.println("获取的分派人员ID列表: " + alert.getAssignedUserIdsList());

        // 测试添加分派人员ID
        alert.addAssignedUserId("user004");
        System.out.println("添加用户后: " + alert.getAssignedUserIdsList());

        // 测试重复添加（应该不会重复）
        alert.addAssignedUserId("user001");
        System.out.println("重复添加后: " + alert.getAssignedUserIdsList());

        // 测试移除分派人员ID
        alert.removeAssignedUserId("user002");
        System.out.println("移除用户后: " + alert.getAssignedUserIdsList());

        // 测试检查是否包含指定用户ID
        System.out.println("是否包含user001: " + alert.hasAssignedUserId("user001"));
        System.out.println("是否包含user002: " + alert.hasAssignedUserId("user002"));

        // 测试空值处理
        Alert emptyAlert = new Alert();
        System.out.println("空告警的分派人员列表: " + emptyAlert.getAssignedUserIdsList());
        emptyAlert.addAssignedUserId("user001");
        System.out.println("添加用户后: " + emptyAlert.getAssignedUserIdsList());

        System.out.println("分派人员ID列表功能测试完成！");
    }

    @Test
    public void testChannelInfo() {
        System.out.println("=== 测试协作空间功能 ===");

        Alert alert = new Alert();
        alert.setTitle("测试告警");
        alert.setDescription("测试协作空间功能");
        alert.setSeverity("high");
        alert.setStatus("triggered");

        // 测试设置协作空间信息
        alert.setChannelInfo("channel_001", "运维团队协作空间");

        System.out.println("协作空间ID: " + alert.getChannelId());
        System.out.println("协作空间名称: " + alert.getChannelName());
        System.out.println("协作空间显示名称: " + alert.getChannelDisplayName());
        System.out.println("是否有协作空间信息: " + alert.hasChannelInfo());

        // 测试检查是否属于指定协作空间
        System.out.println("是否属于channel_001: " + alert.belongsToChannel("channel_001"));
        System.out.println("是否属于channel_002: " + alert.belongsToChannel("channel_002"));

        // 测试只有ID没有名称的情况
        Alert alert2 = new Alert();
        alert2.setChannelId("channel_002");
        System.out.println("只有ID的协作空间显示名称: " + alert2.getChannelDisplayName());
        System.out.println("是否有协作空间信息: " + alert2.hasChannelInfo());

        // 测试空值处理
        Alert emptyAlert = new Alert();
        System.out.println("空告警的协作空间显示名称: " + emptyAlert.getChannelDisplayName());
        System.out.println("是否有协作空间信息: " + emptyAlert.hasChannelInfo());
        System.out.println("是否属于指定协作空间: " + emptyAlert.belongsToChannel("channel_001"));

        System.out.println("协作空间功能测试完成！");
    }

    @Test
    public void testAddAlert() throws Exception {
        String token = "3e3951771ef641faaa1336e2ba3e7d0a";

        // Grafana Legacy Alerting 格式的JSON数据
        String jsonData = "{\n" +
                "  \"title\": \"cbs域名请求量异常通知\",\n" +
//                "  \"alert_severity\": \"Warning\",\n" +
                "  \"description\": \"cbs域名请求量异常，最近10分钟内的请求量相比于上一个10分钟的请求量下降幅度超过50%&&最近10分钟请求小于100，请关注\",\n" +
                "  \"labels\": {\n" +
                "    \"alertname\": \"cbs域名请求量异常通知\",\n" +
                "    \"check\": \"cbs域名请求量异常通知\",\n" +
                "    \"current_v_cbs_A\": \"74\",\n" +
                "    \"current_v_cbs_B\": \"74\",\n" +
                "    \"current_v_cbs_H\": \"644\",\n" +
                "    \"generatorURL\": \"http://grafana.ops.private.com/alerting/grafana/e3c881cc-6b86-40af-87d6-0e64a583665b/view?orgId=6\",\n" +
                "    \"grafana_folder\": \"CBS\",\n" +
                "    \"msg_http_host_keyword\": \"wuxi.cbs.bacic5i5j.com\",\n" +
                "    \"name\": \"cbs请求量及状态码\",\n" +
                "    \"severity\": \"warning\",\n" +
                "    \"summary\": \"cbs域名请求量异常通知\",\n" +
                "    \"valueString\": \"[ var='A' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=74 ], [ var='B' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=74 ], [ var='D' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=644 ], [ var='E' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=1 ], [ var='H' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=644 ]\"\n" +
                "  }\n" +
                "}";

        System.out.println("Grafana Legacy Alerting JSON数据: " + jsonData);

        // 处理webhook告警
        alertService.processWebhookAlert(token, jsonData);
    }

    @Test
    public void testFlashDuty() {
        String url = "https://api.flashcat.cloud/event/push/alert/grafana?integration_key=9aa8d2b11c1056fb891bc4fb7974bcbe785";

        String token = "9aa8d2b11c1056fb891bc4fb7974bcbe785";

        // Grafana Legacy Alerting 格式的JSON数据
        String jsonData = "{\n" +
                "  \"title\": \"cbs域名请求量异常通知\",\n" +
                "  \"alert_severity\": \"Warning\",\n" +
                "  \"description\": \"cbs域名请求量异常，最近10分钟内的请求量相比于上一个10分钟的请求量下降幅度超过50%&&最近10分钟请求小于100，请关注\",\n" +
                "  \"labels\": {\n" +
                "    \"alertname\": \"cbs域名请求量异常通知\",\n" +
                "    \"check\": \"cbs域名请求量异常通知\",\n" +
                "    \"current_v_cbs_A\": \"74\",\n" +
                "    \"current_v_cbs_B\": \"74\",\n" +
                "    \"current_v_cbs_H\": \"644\",\n" +
                "    \"generatorURL\": \"http://grafana.ops.private.com/alerting/grafana/e3c881cc-6b86-40af-87d6-0e64a583665b/view?orgId=6\",\n" +
                "    \"grafana_folder\": \"CBS\",\n" +
                "    \"msg_http_host_keyword\": \"wuxi.cbs.bacic5i5j.com\",\n" +
                "    \"name\": \"cbs请求量及状态码\",\n" +
                "    \"severity\": \"warning\",\n" +
                "    \"summary\": \"cbs域名请求量异常通知\",\n" +
                "    \"valueString\": \"[ var='A' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=74 ], [ var='B' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=74 ], [ var='D' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=644 ], [ var='E' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=1 ], [ var='H' labels={msg.http_host.keyword=wuxi.cbs.bacic5i5j.com} value=644 ]\"\n" +
                "  }\n" +
                "}";
        Alert alert = new Alert();
        alert.setRawData(jsonData);
        flashDutyService.sendFlashDutyStandardAlert2(jsonData, url);
    }

    @Test
    public void testAddSkyWalkingAlert() {
        String json = "[\n" +
                "  {\n" +
                "      \"scopeId\": 1,\n" +
                "      \"scope\": \"SERVICE\",\n" +
                "      \"name\": \"external-gateway-starter\",\n" +
                "      \"id0\": \"ZXh0ZXJuYWwtZ2F0ZXdheS1zdGFydGVy.1\",\n" +
                "      \"id1\": \"\",\n" +
                "      \"ruleName\": \"service_percentile_rule\",\n" +
                "      \"alarmMessage\": \"服务【external-gateway-starter】P50 平均响应时间在最近10分钟内超过500毫秒\",\n" +
                "      \"tags\": [\n" +
                "          {\n" +
                "              \"key\": \"level\",\n" +
                "              \"value\": \"P2\"\n" +
                "          },\n" +
                "          {\n" +
                "              \"key\": \"alarm_name\",\n" +
                "              \"value\": \"P50服务平均响应时间\"\n" +
                "          }\n" +
                "      ],\n" +
                "      \"startTime\": 1749020117815,\n" +
                "      \"hooks\": [\n" +
                "          \"webhook.default\"\n" +
                "      ]\n" +
                "  },\n" +
                "  {\n" +
                "      \"scopeId\": 2,\n" +
                "      \"scope\": \"SERVICE\",\n" +
                "      \"name\": \"external-gateway-starter2\",\n" +
                "      \"id0\": \"ZXh0ZXJuYWwtZ2F0ZXdheS1zdGFydGVy.2\",\n" +
                "      \"id1\": \"\",\n" +
                "      \"ruleName\": \"service_percentile_rule\",\n" +
                "      \"alarmMessage\": \"服务【external-gateway-starter】P50 平均响应时间在最近10分钟内超过500毫秒\",\n" +
                "      \"tags\": [\n" +
                "          {\n" +
                "              \"key\": \"level\",\n" +
                "              \"value\": \"P2\"\n" +
                "          },\n" +
                "          {\n" +
                "              \"key\": \"alarm_name\",\n" +
                "              \"value\": \"P50服务平均响应时间\"\n" +
                "          }\n" +
                "      ],\n" +
                "      \"startTime\": 1749020117815,\n" +
                "      \"hooks\": [\n" +
                "          \"webhook.default\"\n" +
                "      ]\n" +
                "  }\n" +
                "]";

        String webhookUrl = "https://api.flashcat.cloud/event/push/alert/skywalking?integration_key=22fa4de07fdb526bbc5aae2803f70a7c785";
        // 处理webhook告警
        flashDutyService.forwardRawDataToFlashDuty(json, webhookUrl, 1234L);

        // 输出处理结果
        System.out.println("夜莺监控(N9E)告警处理完成");
    }


    @Test
    public void testAddZabbixAlert() {
        String json = "{\n" +
                "  \"event_action\": \"trigger\",\n" +
                "  \"severity\": \"High\",\n" +
                "  \"check\": \"CPU usage\",\n" +
                "  \"alert_message\": \"CPU utilization is 95.2% on server-01 (Threshold: 90%)\",\n" +
                "  \"alert_subject\": \"[PROBLEM] High CPU usage on server-01\",\n" +
                "  \"event_id\": \"5678\",\n" +
                "  \"event_tags\": \"class:network, component:network, description:GigabitEthernet1/0/14 Interface, interface:GigabitEthernet1/0/14, scope:performance, target:h3c, target:h3c-comware,a:1\",\n" +
                "  \"trigger_id\": \"5678\",\n" +
                "  \"trigger_description\": \"CPU usage > 90% on {HOST.NAME}\",\n" +
                "  \"trigger_expression\": \"{server-01:system.cpu.util.avg(5m)}>90\",\n" +
                "  \"host_group\": \"Linux Servers\",\n" +
                "  \"host_ip\": \"*************\",\n" +
                "  \"host_name\": \"server-01\",\n" +
                "  \"item_name\": \"CPU utilization\",\n" +
                "  \"item_value\": \"95.2\",\n" +
                "  \"zabbix_url\": \"https://zabbix.example.com/tr_events.php?triggerid=678&eventid=12345\"\n" +
                "}";

        String webhookUrl = "https://api.flashcat.cloud/event/push/alert/zabbix?integration_key=744f0f943bc155dbb49c8a1d05fac694785";

        // 处理webhook告警
        flashDutyService.forwardRawDataToFlashDuty(json, webhookUrl, 1234L);

        // 输出处理结果
        System.out.println("zabbix告警处理完成");
    }

    @Test
    public void testAddGrafanaAlert() {
        String json = "{\n" +
                "  \"receiver\": \"\",\n" +
                "  \"status\": \"firing\",\n" +
                "  \"alerts\": [\n" +
                "    {\n" +
                "      \"status\": \"firing\",\n" +
                "      \"labels\": {\n" +
                "        \"alertname\": \"TestAlert2\",\n" +
                "        \"instance\": \"Grafana2\"\n" +
                "      },\n" +
                "      \"annotations\": {\n" +
                "        \"summary\": \"Notification test\"\n" +
                "      },\n" +
                "      \"startsAt\": \"2025-05-23T10:07:45.217828087+08:00\",\n" +
                "      \"endsAt\": \"0001-01-01T00:00:00Z\",\n" +
                "      \"generatorURL\": \"\",\n" +
                "      \"fingerprint\": \"57c6d9296de2ad39111\",\n" +
                "      \"silenceURL\": \"http://grafana.ops.private.com/alerting/silence/new?alertmanager=grafana\\u0026matcher=alertname%3DTestAlert\\u0026matcher=instance%3DGrafana\",\n" +
                "      \"dashboardURL\": \"\",\n" +
                "      \"panelURL\": \"\",\n" +
                "      \"values\": null,\n" +
                "      \"valueString\": \"[ metric='foo' labels={instance=bar} value=10 ]\"\n" +
                "    }, {\n" +
                "      \"status\": \"firing\",\n" +
                "      \"labels\": {\n" +
                "        \"alertname\": \"TestAlert3\",\n" +
                "        \"instance\": \"Grafana\"\n" +
                "      },\n" +
                "      \"annotations\": {\n" +
                "        \"summary\": \"Notification test3\"\n" +
                "      },\n" +
                "      \"startsAt\": \"2025-05-23T10:07:45.217828087+08:00\",\n" +
                "      \"endsAt\": \"0001-01-01T00:00:00Z\",\n" +
                "      \"generatorURL\": \"\",\n" +
                "      \"fingerprint\": \"57c6d9296de2ad39133\",\n" +
                "      \"silenceURL\": \"http://grafana.ops.private.com/alerting/silence/new?alertmanager=grafana\\u0026matcher=alertname%3DTestAlert\\u0026matcher=instance%3DGrafana\",\n" +
                "      \"dashboardURL\": \"\",\n" +
                "      \"panelURL\": \"\",\n" +
                "      \"values\": null,\n" +
                "      \"valueString\": \"[ metric='foo' labels={instance=bar} value=10 ]\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"groupLabels\": {\n" +
                "\n" +
                "  },\n" +
                "  \"commonLabels\": {\n" +
                "    \"alertname\": \"TestAlert\",\n" +
                "    \"instance\": \"Grafana\"\n" +
                "  },\n" +
                "  \"commonAnnotations\": {\n" +
                "    \"summary\": \"Notification test\"\n" +
                "  },\n" +
                "  \"externalURL\": \"http://grafana.ops.private.com/\",\n" +
                "  \"version\": \"1\",\n" +
                "  \"groupKey\": \"{alertname=\\\"TestAlert\\\", instance=\\\"Grafana\\\"}2025-05-23 10:07:45.217828087 +0800 CST m=+6363473.108680593\",\n" +
                "  \"truncatedAlerts\": 0,\n" +
                "  \"orgId\": 6,\n" +
                "  \"title\": \"[FIRING:1]  (TestAlert Grafana)\",\n" +
                "  \"state\": \"alerting\",\n" +
                "  \"message\": \"**Firing**\\n\\nValue: [no value]\\nLabels:\\n - alertname = TestAlert\\n - instance = Grafana\\nAnnotations:\\n - summary = Notification test\\nSilence: http://grafana.ops.private.com/alerting/silence/new?alertmanager=grafana\\u0026matcher=alertname%3DTestAlert\\u0026matcher=instance%3DGrafana\\n\"\n" +
                "}";

        String webhookUrl = "https://api.flashcat.cloud/event/push/alert/grafana?integration_key=9aa8d2b11c1056fb891bc4fb7974bcbe785";
        // 处理webhook告警
        flashDutyService.forwardRawDataToFlashDuty(json, webhookUrl, 1234L);

        // 输出处理结果
        System.out.println("granfnan告警处理完成");
    }


    @Test
    public void testAddN9eAlert() throws Exception {
        // 使用现有的token，这里使用与其他测试相同的token
        // 注意：在实际运行前，需要确保数据库中存在对应的告警输入配置
        String token = "c021ac7a13ef5f18947f4b12caf37c97785";
        String webhookUrl = "https://api.flashcat.cloud/event/push/alert/n9e?integration_key=c021ac7a13ef5f18947f4b12caf37c97785";

        // 夜莺监控(N9E)格式的JSON数据
        String jsonData = "{"
                + "\"id\": 587662,"
                + "\"cate\": \"prometheus\","
                + "\"cluster\": \"秦淮机房\","
                + "\"datasource_id\": 1,"
                + "\"group_id\": 11,"
                + "\"group_name\": \"集团运维中心-通用告警规则配置专用组\","
                + "\"hash\": \"b08381f83f993987ad69ad908e7d192a\","
                + "\"rule_id\": 5680,"
                + "\"rule_name\": \"【apisix】后端节点***********:6628 异常，upstream名称：/cbs-sit1-apisix/upstreams/567441796146135852\","
                + "\"rule_note\": \"\","
                + "\"rule_prod\": \"metric\","
                + "\"rule_algo\": \"\","
                + "\"severity\": 2,"
                + "\"prom_for_duration\": 60,"
                + "\"prom_ql\": \"apisix_upstream_status == 0\","
                + "\"rule_config\": {"
                + "    \"queries\": ["
                + "        {"
                + "            \"keys\": {"
                + "                \"labelKey\": \"\","
                + "                \"metricKey\": \"\","
                + "                \"valueKey\": \"\""
                + "            },"
                + "            \"prom_ql\": \" apisix_upstream_status == 0\","
                + "            \"severity\": 2,"
                + "            \"unit\": \"none\""
                + "        }"
                + "    ]"
                + "},"
                + "\"prom_eval_interval\": 30,"
                + "\"callbacks\": [],"
                + "\"runbook_url\": \"\","
                + "\"notify_recovered\": 1,"
                + "\"notify_channels\": [],"
                + "\"notify_groups\": [],"
                + "\"notify_groups_obj\": [],"
                + "\"target_ident\": \"************\","
                + "\"target_note\": \"\","
                + "\"trigger_time\": 1747965031,"
                + "\"trigger_value\": \"0\","
                + "\"trigger_values\": \"\","
                + "\"trigger_values_json\": {"
                + "    \"values_with_unit\": {"
                + "        \"v\": {"
                + "            \"value\": 0,"
                + "            \"unit\": \"\","
                + "            \"text\": \"0.00\","
                + "            \"stat\": 0"
                + "        }"
                + "    }"
                + "},"
                + "\"tags\": ["
                + "    \"__name__=apisix_upstream_status\","
                + "    \"application=apisix\","
                + "    \"business=apisix网关\","
                + "    \"department=运维部\","
                + "    \"env=sit\","
                + "    \"ident=************\","
                + "    \"instance=************:9091\","
                + "    \"ip=***********\","
                + "    \"job=cbs-sit1-apisix\","
                + "    \"machineIP=************\","
                + "    \"name=/cbs-sit1-apisix/upstreams/567441796146135852\","
                + "    \"port=6628\","
                + "    \"region=beijing-qh\","
                + "    \"alert_id=1234\","
                + "    \"rulename=【apisix】后端节点{{$labels.ip}}:{{$labels.port}} 异常，upstream名称：{{$labels.name}}\","
                + "    \"service=cbs-sit1-apisix\""
                + "],"
                + "\"tags_map\": {"
                + "    \"__name__\": \"apisix_upstream_status\","
                + "    \"application\": \"apisix\","
                + "    \"business\": \"apisix网关\","
                + "    \"department\": \"运维部\","
                + "    \"env\": \"sit\","
                + "    \"ident\": \"************\","
                + "    \"instance\": \"************:9091\","
                + "    \"ip\": \"***********\","
                + "    \"job\": \"cbs-sit1-apisix\","
                + "    \"alert_id\": \"1234\","
                + "    \"machineIP\": \"************\","
                + "    \"name\": \"/cbs-sit1-apisix/upstreams/567441796146135852\","
                + "    \"port\": \"6628\","
                + "    \"region\": \"beijing-qh\","
                + "    \"rulename\": \"【apisix】后端节点{{$labels.ip}}:{{$labels.port}} 异常，upstream名称：{{$labels.name}}\","
                + "    \"service\": \"cbs-sit1-apisix\""
                + "},"
                + "\"original_tags\": null,"
                + "\"annotations\": {},"
                + "\"is_recovered\": false,"
                + "\"notify_users_obj\": [],"
                + "\"last_eval_time\": 1747965031,"
                + "\"last_sent_time\": 1747965031,"
                + "\"notify_cur_number\": 60,"
                + "\"first_trigger_time\": 1747752631,"
                + "\"extra_config\": null,"
                + "\"status\": 0,"
                + "\"claimant\": \"\","
                + "\"sub_rule_id\": 0,"
                + "\"extra_info\": null,"
                + "\"target\": {"
                + "    \"id\": 15497,"
                + "    \"group_id\": 0,"
                + "    \"group_objs\": null,"
                + "    \"ident\": \"************\","
                + "    \"note\": \"\","
                + "    \"tags\": ["
                + "        \"application=apisix\","
                + "        \"business=apisix网关\","
                + "        \"department=运维部\","
                + "        \"env=sit\","
                + "        \"machineIP=************\""
                + "    ],"
                + "    \"tags_maps\": {"
                + "        \"application\": \"apisix\","
                + "        \"business\": \"apisix网关\","
                + "        \"department\": \"运维部\","
                + "        \"env\": \"sit\","
                + "        \"machineIP\": \"************\","
                + "        \"region\": \"beijing-qh\""
                + "    },"
                + "    \"update_at\": 1747965016,"
                + "    \"host_ip\": \"************\","
                + "    \"agent_version\": \"v0.4.3\","
                + "    \"engine_name\": \"default\","
                + "    \"os\": \"linux\","
                + "    \"host_tags\": ["
                + "        \"region=beijing-qh\""
                + "    ],"
                + "    \"unixtime\": 1747965014041,"
                + "    \"offset\": 1986,"
                + "    \"target_up\": 2,"
                + "    \"mem_util\": 17.879441109869575,"
                + "    \"cpu_num\": 2,"
                + "    \"cpu_util\": 4.1946308718539775,"
                + "    \"arch\": \"amd64\","
                + "    \"remote_addr\": \"************\","
                + "    \"group_ids\": ["
                + "        623"
                + "    ],"
                + "    \"group_names\": ["
                + "        \"集团运维中心-测试环境-中间件-apisix\""
                + "    ]"
                + "},"
                + "\"recover_config\": {"
                + "    \"judge_type\": 0,"
                + "    \"recover_exp\": \"\""
                + "},"
                + "\"rule_hash\": \"5210d1678610b08cd2abbd8c78fb7daa\","
                + "\"extra_info_map\": null"
                + "}";

        System.out.println("夜莺监控(N9E)JSON数据: " + jsonData);

        try {
            System.out.println("开始处理夜莺监控(N9E)告警数据...");

            // 处理webhook告警
            flashDutyService.forwardRawDataToFlashDuty(jsonData, webhookUrl, 1234L);

            // 输出处理结果
            System.out.println("夜莺监控(N9E)告警处理完成");
        } catch (Exception e) {
            System.err.println("处理夜莺监控(N9E)告警数据时发生异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }


    }

    /**
     * 测试告警导出功能
     */
    @Test
    public void testAlertExport() {
        System.out.println("=== 测试告警导出功能 ===");

        try {
            // 设置时间范围：近一周
            long endTime = System.currentTimeMillis() / 1000;
            long startTime = endTime - 3600 * 24 * 7;

            System.out.println("查询时间范围：");
            System.out.println("开始时间：" + startTime + " (" + new java.util.Date(startTime * 1000) + ")");
            System.out.println("结束时间：" + endTime + " (" + new java.util.Date(endTime * 1000) + ")");

            // 测试分页查询所有告警数据
            List<PageIncidentResponseData.IncidentItem> incidents = alertExportService.queryAllIncidentsInTimeRange(startTime, endTime);

            System.out.println("查询到告警数量：" + incidents.size());

            if (incidents.size() > 0) {
                System.out.println("\n前3条告警信息：");
                for (int i = 0; i < Math.min(3, incidents.size()); i++) {
                    PageIncidentResponseData.IncidentItem incident = incidents.get(i);
                    System.out.println((i + 1) + ". ID: " + incident.getIncident_id());
                    System.out.println("   标题: " + incident.getTitle());
                    System.out.println("   级别: " + incident.getIncident_severity());
                    System.out.println("   状态: " + incident.getProgress());
                    if (incident.getLabels() != null) {
                        System.out.println("   标签: " + incident.getLabels());
                    }
                    System.out.println();
                }
            }

            System.out.println("告警导出功能测试完成！");

        } catch (Exception e) {
            System.err.println("测试告警导出功能时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试查询所有告警数据（分页）
     */
    @Test
    public void testQueryAllIncidents() {
        System.out.println("=== 测试分页查询所有告警数据 ===");

        try {
            // 设置时间范围：近3天
            long endTime = System.currentTimeMillis() / 1000;
            long startTime = endTime - 3600 * 24 * 3;

            System.out.println("查询近3天的告警数据...");

            List<PageIncidentResponseData.IncidentItem> allIncidents = alertExportService.queryAllIncidentsInTimeRange(startTime, endTime);

            System.out.println("总共查询到 " + allIncidents.size() + " 条告警");

            // 统计不同级别的告警数量
            Map<String, Integer> severityCount = new HashMap<>();
            Map<String, Integer> progressCount = new HashMap<>();

            for (PageIncidentResponseData.IncidentItem incident : allIncidents) {
                String severity = incident.getIncident_severity();
                String progress = incident.getProgress();

                severityCount.put(severity, severityCount.getOrDefault(severity, 0) + 1);
                progressCount.put(progress, progressCount.getOrDefault(progress, 0) + 1);
            }

            System.out.println("\n告警级别统计：");
            severityCount.forEach((severity, count) -> System.out.println("  " + severity + ": " + count));

            System.out.println("\n告警状态统计：");
            progressCount.forEach((progress, count) -> System.out.println("  " + progress + ": " + count));

        } catch (Exception e) {
            System.err.println("测试查询告警数据时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试获取团队列表功能
     */
    @Test
    public void testGetTeamList() {
        System.out.println("=== 测试获取FlashDuty团队列表 ===");

        try {
            // 创建团队列表查询请求
            TeamListRequest request = new TeamListRequest();
            request.setP(1); // 第一页
            request.setLimit(100); // 每页20条
            request.setAsc(true); // 升序
            request.setOrderby("created_at"); // 按团队名称排序

            System.out.println("查询参数：");
            System.out.println("页码：" + request.getP());
            System.out.println("每页条数：" + request.getLimit());
            System.out.println("排序：" + (request.getAsc() ? "升序" : "降序"));
            System.out.println("排序字段：" + request.getOrderby());

            // 调用接口获取团队列表
            TeamListResponse response = flashDutyService.queryTeamList(request);

            if (response != null && response.getData() != null) {
                PageTeamResponseData data = response.getData();
                System.out.println("查询结果：");
                System.out.println("总数：" + data.getTotal());
                System.out.println("当前页：" + data.getP());
                System.out.println("每页条数：" + data.getLimit());

                if (data.getItems() != null && !data.getItems().isEmpty()) {
                    System.out.println("\n团队列表：");
                    for (int i = 0; i < Math.min(5, data.getItems().size()); i++) {
                        PageTeamResponseData.TeamItem team = data.getItems().get(i);
                        System.out.println((i + 1) + ". 团队ID: " + team.getTeam_id());
                        System.out.println("   团队名称: " + team.getTeam_name());
                        System.out.println("   描述: " + team.getDescription());
                        System.out.println("   创建时间: " + team.getCreated_at() + " (" + new java.util.Date(team.getCreated_at() * 1000) + ")");
                        System.out.println("   更新时间: " + team.getUpdated_at() + " (" + new java.util.Date(team.getUpdated_at() * 1000) + ")");
                        System.out.println("   更新人: " + team.getUpdated_by_name() + " (ID: " + team.getUpdated_by() + ")");
                        System.out.println("   创建人ID: " + team.getCreator_id());
                        System.out.println("   关联ID: " + team.getRef_id());
                        if (team.getPerson_ids() != null && !team.getPerson_ids().isEmpty()) {
                            System.out.println("   团队成员ID: " + team.getPerson_ids());
                            System.out.println("   成员数量: " + team.getPerson_ids().size());
                        }
                        System.out.println();
                    }

                    if (data.getItems().size() > 5) {
                        System.out.println("... 还有 " + (data.getItems().size() - 5) + " 个团队");
                    }
                } else {
                    System.out.println("没有找到团队数据");
                }
            } else {
                System.out.println("获取团队列表失败或返回数据为空");
            }

            System.out.println("团队列表查询测试完成！");

        } catch (Exception e) {
            System.err.println("测试获取团队列表时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试根据人员ID查询团队列表
     */
    @Test
    public void testGetTeamListByPersonId() {
        System.out.println("=== 测试根据人员ID查询团队列表 ===");

        try {
            // 创建团队列表查询请求，指定人员ID
            TeamListRequest request = new TeamListRequest();
            request.setP(1); // 第一页
            request.setLimit(10); // 每页10条
            request.setAsc(true); // 升序
            request.setPerson_id(123456); // 示例人员ID，实际使用时需要使用真实的人员ID

            System.out.println("查询参数：");
            System.out.println("人员ID：" + request.getPerson_id());
            System.out.println("页码：" + request.getP());
            System.out.println("每页条数：" + request.getLimit());

            // 调用接口获取包含指定人员的团队列表
            TeamListResponse response = flashDutyService.queryTeamList(request);

            if (response != null && response.getData() != null) {
                PageTeamResponseData data = response.getData();
                System.out.println("查询结果：");
                System.out.println("包含人员ID " + request.getPerson_id() + " 的团队总数：" + data.getTotal());

                if (data.getItems() != null && !data.getItems().isEmpty()) {
                    System.out.println("\n相关团队列表：");
                    for (PageTeamResponseData.TeamItem team : data.getItems()) {
                        System.out.println("- 团队: " + team.getTeam_name() + " (ID: " + team.getTeam_id() + ")");
                        System.out.println("  描述: " + team.getDescription());
                        if (team.getPerson_ids() != null) {
                            System.out.println("  成员数量: " + team.getPerson_ids().size());
                            System.out.println("  是否包含指定人员: " + team.getPerson_ids().contains(Long.valueOf(request.getPerson_id())));
                        }
                        System.out.println();
                    }
                } else {
                    System.out.println("没有找到包含指定人员的团队");
                }
            } else {
                System.out.println("查询失败或返回数据为空");
            }

        } catch (Exception e) {
            System.err.println("测试根据人员ID查询团队列表时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试根据查询条件搜索团队
     */
    @Test
    public void testSearchTeams() {
        System.out.println("=== 测试根据查询条件搜索团队 ===");

        try {
            // 创建团队搜索请求
            TeamListRequest request = new TeamListRequest();
            request.setP(1); // 第一页
            request.setLimit(10); // 每页10条
            request.setAsc(true); // 升序
            request.setOrderby("team_name"); // 按团队名称排序
            request.setQuery("运维"); // 搜索包含"运维"的团队名称或描述

            System.out.println("搜索参数：");
            System.out.println("搜索关键词：" + request.getQuery());
            System.out.println("搜索范围：团队名称和描述");
            System.out.println("排序方式：按团队名称升序");

            // 调用接口搜索团队
            TeamListResponse response = flashDutyService.queryTeamList(request);

            if (response != null && response.getData() != null) {
                PageTeamResponseData data = response.getData();
                System.out.println("搜索结果：");
                System.out.println("匹配的团队总数：" + data.getTotal());

                if (data.getItems() != null && !data.getItems().isEmpty()) {
                    System.out.println("\n搜索结果列表：");
                    for (PageTeamResponseData.TeamItem team : data.getItems()) {
                        System.out.println("- 团队: " + team.getTeam_name() + " (ID: " + team.getTeam_id() + ")");
                        System.out.println("  描述: " + team.getDescription());
                        System.out.println("  创建时间: " + new java.util.Date(team.getCreated_at() * 1000));
                        if (team.getPerson_ids() != null) {
                            System.out.println("  成员数量: " + team.getPerson_ids().size());
                        }
                        System.out.println();
                    }
                } else {
                    System.out.println("没有找到匹配的团队");
                }
            } else {
                System.out.println("搜索失败或返回数据为空");
            }

        } catch (Exception e) {
            System.err.println("测试搜索团队时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试获取成员列表功能
     */
    @Test
    public void testGetMemberList() {
        System.out.println("=== 测试获取FlashDuty成员列表 ===");

        try {
            // 创建成员列表查询请求
            MemberListRequest request = new MemberListRequest();
            request.setP(1); // 第一页
            request.setLimit(100); // 每页20条
            request.setAsc(true); // 升序
            request.setOrderby("created_at"); // 按成员名称排序

            System.out.println("查询参数：");
            System.out.println("页码：" + request.getP());
            System.out.println("每页条数：" + request.getLimit());
            System.out.println("排序：" + (request.getAsc() ? "升序" : "降序"));
            System.out.println("排序字段：" + request.getOrderby());

            // 调用接口获取成员列表
            MemberListResponse response = flashDutyService.queryMemberList(request);

            if (response != null && response.getData() != null) {
                PageMemberResponseData data = response.getData();

                if (data.getItems() != null && !data.getItems().isEmpty()) {
                    System.out.println("\n成员列表：");
                    for (int i = 0; i < data.getItems().size(); i++) {
                        PageMemberResponseData.MemberItem member = data.getItems().get(i);
                        System.out.println((i + 1) + ". 成员ID: " + member.getMember_id());
                        System.out.println("   成员名称: " + member.getMember_name());
                        System.out.println("   电话: " + member.getPhone() + " (验证状态: " + member.getPhone_verified() + ")");
                        System.out.println("   邮箱: " + member.getEmail() + " (验证状态: " + member.getEmail_verified() + ")");
                        System.out.println("   状态: " + member.getStatus());
                        System.out.println("   时区: " + member.getTime_zone());
                        System.out.println("   语言: " + member.getLocale());
                        System.out.println("   创建时间: " + member.getCreated_at() + " (" + new java.util.Date(member.getCreated_at() * 1000) + ")");
                        System.out.println("   更新时间: " + member.getUpdated_at() + " (" + new java.util.Date(member.getUpdated_at() * 1000) + ")");
                        System.out.println("   关联ID: " + member.getRef_id());
                        if (member.getAccount_role_ids() != null && !member.getAccount_role_ids().isEmpty()) {
                            System.out.println("   角色ID列表: " + member.getAccount_role_ids());
                        }
                        System.out.println();
                    }

                    if (data.getItems().size() > 5) {
                        System.out.println("... 还有 " + (data.getItems().size() - 5) + " 个成员");
                    }
                } else {
                    System.out.println("没有找到成员数据");
                }
            } else {
                System.out.println("获取成员列表失败或返回数据为空");
            }

            System.out.println("成员列表查询测试完成！");

        } catch (Exception e) {
            System.err.println("测试获取成员列表时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试根据角色ID查询成员列表
     */
    @Test
    public void testGetMemberListByRoleId() {
        System.out.println("=== 测试根据角色ID查询成员列表 ===");

        try {
            // 创建成员列表查询请求，指定角色ID
            MemberListRequest request = new MemberListRequest();
            request.setP(1); // 第一页
            request.setLimit(100); // 每页10条
            request.setAsc(true); // 升序
//            request.setRole_id(1); // 示例角色ID，实际使用时需要使用真实的角色ID
            request.setOrderby("created_at"); // 按创建时间排序

            System.out.println("查询参数：");
            System.out.println("角色ID：" + request.getRole_id());
            System.out.println("页码：" + request.getP());
            System.out.println("每页条数：" + request.getLimit());
            System.out.println("排序字段：" + request.getOrderby());

            // 调用接口获取指定角色的成员列表
            MemberListResponse response = flashDutyService.queryMemberList(request);

            if (response != null && response.getData() != null) {
                PageMemberResponseData data = response.getData();
                System.out.println("查询结果：");
                System.out.println("角色ID " + request.getRole_id() + " 的成员总数：" + data.getTotal());

                if (data.getItems() != null && !data.getItems().isEmpty()) {
                    System.out.println("\n相关成员列表：");
                    for (PageMemberResponseData.MemberItem member : data.getItems()) {
                        System.out.println("- 成员: " + member.getMember_name() + " (ID: " + member.getMember_id() + ")");
                        System.out.println("  邮箱: " + member.getEmail());
                        System.out.println("  状态: " + member.getStatus());
                        System.out.println("  创建时间: " + new java.util.Date(member.getCreated_at() * 1000));
                        if (member.getAccount_role_ids() != null) {
                            System.out.println("  拥有角色: " + member.getAccount_role_ids());
//                            System.out.println("  是否包含指定角色: " + member.getAccount_role_ids().contains(Long.valueOf(request.getRole_id())));
                        }
                        System.out.println();
                    }
                } else {
                    System.out.println("没有找到拥有指定角色的成员");
                }
            } else {
                System.out.println("查询失败或返回数据为空");
            }

        } catch (Exception e) {
            System.err.println("测试根据角色ID查询成员列表时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试根据查询条件搜索成员
     */
    @Test
    public void testSearchMembers() {
        System.out.println("=== 测试根据查询条件搜索成员 ===");

        try {
            // 创建成员搜索请求
            MemberListRequest request = new MemberListRequest();
            request.setP(1); // 第一页
            request.setLimit(10); // 每页10条
            request.setAsc(true); // 升序
            request.setOrderby("member_name"); // 按成员名称排序
            request.setQuery("admin"); // 搜索包含"admin"的成员

            System.out.println("搜索参数：");
            System.out.println("搜索关键词：" + request.getQuery());
            System.out.println("排序方式：按成员名称升序");

            // 调用接口搜索成员
            MemberListResponse response = flashDutyService.queryMemberList(request);

            if (response != null && response.getData() != null) {
                PageMemberResponseData data = response.getData();
                System.out.println("搜索结果：");
                System.out.println("匹配的成员总数：" + data.getTotal());

                if (data.getItems() != null && !data.getItems().isEmpty()) {
                    System.out.println("\n搜索结果列表：");
                    for (PageMemberResponseData.MemberItem member : data.getItems()) {
                        System.out.println("- 成员: " + member.getMember_name() + " (ID: " + member.getMember_id() + ")");
                        System.out.println("  邮箱: " + member.getEmail());
                        System.out.println("  电话: " + member.getPhone());
                        System.out.println("  状态: " + member.getStatus());
                        System.out.println("  时区: " + member.getTime_zone());
                        System.out.println("  创建时间: " + new java.util.Date(member.getCreated_at() * 1000));
                        if (member.getAccount_role_ids() != null && !member.getAccount_role_ids().isEmpty()) {
                            System.out.println("  角色数量: " + member.getAccount_role_ids().size());
                        }
                        System.out.println();
                    }
                } else {
                    System.out.println("没有找到匹配的成员");
                }
            } else {
                System.out.println("搜索失败或返回数据为空");
            }

        } catch (Exception e) {
            System.err.println("测试搜索成员时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试获取所有成员统计信息
     */
    @Test
    public void testGetMemberStatistics() {
        System.out.println("=== 测试获取成员统计信息 ===");

        try {
            // 创建成员统计查询请求
            MemberListRequest request = new MemberListRequest();
            request.setP(1); // 第一页
            request.setLimit(100); // 每页100条，获取更多数据用于统计
            request.setAsc(true); // 升序
            request.setOrderby("created_at"); // 按创建时间排序

            System.out.println("正在获取成员数据进行统计分析...");

            // 调用接口获取成员列表
            MemberListResponse response = flashDutyService.queryMemberList(request);

            if (response != null && response.getData() != null) {
                PageMemberResponseData data = response.getData();
                System.out.println("统计结果：");
                System.out.println("成员总数：" + data.getTotal());

                if (data.getItems() != null && !data.getItems().isEmpty()) {
                    // 统计各种状态的成员数量
                    java.util.Map<String, Integer> statusCount = new java.util.HashMap<>();
                    java.util.Map<String, Integer> timezoneCount = new java.util.HashMap<>();
                    java.util.Map<String, Integer> localeCount = new java.util.HashMap<>();
                    int verifiedPhoneCount = 0;
                    int verifiedEmailCount = 0;

                    for (PageMemberResponseData.MemberItem member : data.getItems()) {
                        // 统计状态
                        statusCount.put(member.getStatus(), statusCount.getOrDefault(member.getStatus(), 0) + 1);

                        // 统计时区
                        timezoneCount.put(member.getTime_zone(), timezoneCount.getOrDefault(member.getTime_zone(), 0) + 1);

                        // 统计语言
                        localeCount.put(member.getLocale(), localeCount.getOrDefault(member.getLocale(), 0) + 1);

                        // 统计验证状态
                        if ("true".equals(member.getPhone_verified()) || "verified".equals(member.getPhone_verified())) {
                            verifiedPhoneCount++;
                        }
                        if ("true".equals(member.getEmail_verified()) || "verified".equals(member.getEmail_verified())) {
                            verifiedEmailCount++;
                        }
                    }

                    System.out.println("\n状态分布：");
                    statusCount.forEach((status, count) ->
                            System.out.println("  " + status + ": " + count + " 人"));

                    System.out.println("\n时区分布：");
                    timezoneCount.forEach((timezone, count) ->
                            System.out.println("  " + timezone + ": " + count + " 人"));

                    System.out.println("\n语言分布：");
                    localeCount.forEach((locale, count) ->
                            System.out.println("  " + locale + ": " + count + " 人"));

                    System.out.println("\n验证状态：");
                    System.out.println("  电话已验证: " + verifiedPhoneCount + " 人");
                    System.out.println("  邮箱已验证: " + verifiedEmailCount + " 人");

                    // 显示最近创建的成员
                    System.out.println("\n最近创建的5个成员：");
                    data.getItems().stream()
                            .sorted((a, b) -> Long.compare(b.getCreated_at(), a.getCreated_at()))
                            .limit(5)
                            .forEach(member -> {
                                System.out.println("  " + member.getMember_name() +
                                        " (创建时间: " + new java.util.Date(member.getCreated_at() * 1000) + ")");
                            });

                } else {
                    System.out.println("没有找到成员数据");
                }
            } else {
                System.out.println("获取成员统计信息失败");
            }

        } catch (Exception e) {
            System.err.println("测试获取成员统计信息时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试告警导出功能，验证成员姓名映射
     */
    @Test
    public void testAlertExportWithMemberNameMapping() {
        System.out.println("=== 测试告警导出功能（包含成员姓名映射） ===");

        try {
            // 先测试获取成员列表，验证数据是否可用
            MemberListRequest memberRequest = new MemberListRequest();
            memberRequest.setP(1); // 第一页
            memberRequest.setLimit(100); // 每页100条
            memberRequest.setAsc(true); // 升序
            memberRequest.setOrderby("created_at"); // 按创建时间排序

            System.out.println("正在获取成员列表进行姓名映射测试...");

            MemberListResponse memberResponse = flashDutyService.queryMemberList(memberRequest);

            if (memberResponse != null && memberResponse.getData() != null && memberResponse.getData().getItems() != null) {
                System.out.println("成功获取成员列表，共 " + memberResponse.getData().getItems().size() + " 个成员");

                // 显示前5个成员信息
                System.out.println("\n成员ID到姓名映射示例：");
                for (int i = 0; i < Math.min(5, memberResponse.getData().getItems().size()); i++) {
                    PageMemberResponseData.MemberItem member = memberResponse.getData().getItems().get(i);
                    System.out.println("  ID: " + member.getMember_id() + " -> 姓名: " + member.getMember_name());
                }

                // 现在测试获取告警数据
                System.out.println("\n正在获取告警数据进行导出测试...");

                // 设置时间范围（最近一天）
                long endTime = System.currentTimeMillis() / 1000; // 当前时间
                long startTime = endTime - 24 * 60 * 60; // 24小时前

                System.out.println("查询时间范围：");
                System.out.println("开始时间：" + new java.util.Date(startTime * 1000));
                System.out.println("结束时间：" + new java.util.Date(endTime * 1000));

                // 查询告警数据
                List<PageIncidentResponseData.IncidentItem> incidents = alertExportService.queryAllIncidentsInTimeRange(startTime, endTime);

                if (incidents != null && !incidents.isEmpty()) {
                    System.out.println("成功获取 " + incidents.size() + " 条告警数据");

                    // 检查是否有关闭人ID，并验证映射功能
                    System.out.println("\n关闭人ID映射验证：");
                    java.util.Set<Long> closerIds = new java.util.HashSet<>();

                    for (PageIncidentResponseData.IncidentItem incident : incidents) {
                        if (incident.getCloser_id() != null) {
                            closerIds.add(incident.getCloser_id());
                        }
                    }

                    if (!closerIds.isEmpty()) {
                        System.out.println("发现 " + closerIds.size() + " 个不同的关闭人ID：");

                        // 创建成员ID到姓名的映射（模拟导出服务中的逻辑）
                        java.util.Map<Long, String> memberNameMap = new java.util.HashMap<>();
                        for (PageMemberResponseData.MemberItem member : memberResponse.getData().getItems()) {
                            if (member.getMember_id() != null && member.getMember_name() != null) {
                                memberNameMap.put(member.getMember_id(), member.getMember_name());
                            }
                        }

                        // 验证关闭人ID的映射
                        for (Long closerId : closerIds) {
                            String memberName = memberNameMap.getOrDefault(closerId, String.valueOf(closerId));
                            System.out.println("  关闭人ID: " + closerId + " -> 姓名: " + memberName);
                        }

                        System.out.println("\n导出功能验证成功！成员姓名映射正常工作。");
                    } else {
                        System.out.println("当前时间范围内的告警都没有关闭人信息，无法验证姓名映射功能");
                    }

                    // 显示几个告警示例
                    System.out.println("\n告警数据示例：");
                    for (int i = 0; i < Math.min(3, incidents.size()); i++) {
                        PageIncidentResponseData.IncidentItem incident = incidents.get(i);
                        System.out.println((i + 1) + ". 告警ID: " + incident.getIncident_id());
                        System.out.println("   标题: " + incident.getTitle());
                        System.out.println("   严重程度: " + incident.getIncident_severity());
                        System.out.println("   状态: " + incident.getProgress());
                        System.out.println("   关闭人ID: " + incident.getCloser_id());
                        System.out.println();
                    }

                } else {
                    System.out.println("指定时间范围内没有找到告警数据");
                }

            } else {
                System.out.println("获取成员列表失败，无法进行姓名映射测试");
            }

            System.out.println("告警导出功能测试完成！");

        } catch (Exception e) {
            System.err.println("测试告警导出功能时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试Top20告警统计功能
     */
    @Test
    public void testTop20AlertStatistics() {
        System.out.println("=== 测试Top20告警统计功能 ===");

        try {
            // 设置时间范围（最近三天）
            long endTime = System.currentTimeMillis() / 1000;
            long startTime = endTime - 3 * 24 * 60 * 60; // 三天前

            System.out.println("统计时间范围：");
            System.out.println("开始时间：" + new java.util.Date(startTime * 1000));
            System.out.println("结束时间：" + new java.util.Date(endTime * 1000));

            // 获取告警数据
            List<PageIncidentResponseData.IncidentItem> incidents = alertExportService.queryAllIncidentsInTimeRange(startTime, endTime);

            if (incidents != null && !incidents.isEmpty()) {
                System.out.println("成功获取 " + incidents.size() + " 条告警数据，开始统计分析...");

                // 统计Top20告警对象（按labels.ident分组）
                Map<String, Integer> identCount = new HashMap<>();
                Map<String, List<String>> identTitles = new HashMap<>();
                Map<String, String> identStatus = new HashMap<>();

                for (PageIncidentResponseData.IncidentItem incident : incidents) {
                    Map<String, String> labels = incident.getLabels();
                    if (labels != null && labels.get("ident") != null && !labels.get("ident").trim().isEmpty()) {
                        String ident = labels.get("ident");

                        // 统计数量
                        identCount.put(ident, identCount.getOrDefault(ident, 0) + 1);

                        // 收集标题
                        identTitles.computeIfAbsent(ident, k -> new ArrayList<>()).add(incident.getTitle());

                        // 判断处理状态
                        String currentStatus = identStatus.get(ident);
                        String incidentStatus = "closed".equalsIgnoreCase(incident.getProgress()) ? "closed" : "open";

                        if (currentStatus == null) {
                            identStatus.put(ident, incidentStatus);
                        } else if (!currentStatus.equals(incidentStatus)) {
                            identStatus.put(ident, "mixed"); // 部分处理
                        }
                    }
                }

                System.out.println("\n=== Top20告警对象统计 ===");
                System.out.println("发现 " + identCount.size() + " 个不同的告警对象");

                // 按数量排序，取前10显示
                identCount.entrySet().stream()
                        .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                        .limit(10)
                        .forEach(entry -> {
                            String ident = entry.getKey();
                            Integer count = entry.getValue();
                            String status = identStatus.get(ident);
                            String statusText = "closed".equals(status) ? "已处理" :
                                    "open".equals(status) ? "未处理" : "部分处理";

                            System.out.println("- 告警对象: " + ident);
                            System.out.println("  数量: " + count);
                            System.out.println("  状态: " + statusText);
                            System.out.println("  示例告警: " + identTitles.get(ident).get(0));
                            System.out.println();
                        });

                // 统计Top20告警检查项（按告警标题分组）
                Map<String, Integer> titleCount = new HashMap<>();
                Map<String, String> titleStatus = new HashMap<>();

                for (PageIncidentResponseData.IncidentItem incident : incidents) {
                    String title = incident.getTitle();
                    if (title != null && !title.trim().isEmpty()) {
                        // 统计数量
                        titleCount.put(title, titleCount.getOrDefault(title, 0) + 1);

                        // 判断处理状态
                        String currentStatus = titleStatus.get(title);
                        String incidentStatus = "closed".equalsIgnoreCase(incident.getProgress()) ? "closed" : "open";

                        if (currentStatus == null) {
                            titleStatus.put(title, incidentStatus);
                        } else if (!currentStatus.equals(incidentStatus)) {
                            titleStatus.put(title, "mixed");
                        }
                    }
                }

                System.out.println("\n=== Top20告警检查项统计 ===");
                System.out.println("发现 " + titleCount.size() + " 个不同的告警标题");

                // 按数量排序，取前10显示，并测试类型判断逻辑
                titleCount.entrySet().stream()
                        .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                        .limit(10)
                        .forEach(entry -> {
                            String title = entry.getKey();
                            Integer count = entry.getValue();
                            String status = titleStatus.get(title);
                            String statusText = "closed".equals(status) ? "已处理" :
                                    "open".equals(status) ? "未处理" : "部分处理";

                            // 测试类型判断逻辑
                            String type = determineAlertType(title);
                            String reason = extractAlertReason(title);

                            System.out.println("- 告警标题: " + title);
                            System.out.println("  数量: " + count);
                            System.out.println("  类型: " + type);
                            System.out.println("  告警原因: " + reason);
                            System.out.println("  状态: " + statusText);
                            System.out.println();
                        });

            } else {
                System.out.println("指定时间范围内没有找到告警数据");
            }

            System.out.println("Top20告警统计测试完成！");

        } catch (Exception e) {
            System.err.println("测试Top20告警统计时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试告警类型判断逻辑
     */
    private String determineAlertType(String title) {
        if (title == null) {
            return "待确认";
        }

        // 检查是否包含中文中括号
        if (title.contains("【") && title.contains("】")) {
            int start = title.indexOf("【");
            int end = title.indexOf("】", start);
            if (end > start) {
                return title.substring(start, end + 1);
            }
        }

        // 根据关键词判断类型
        if (title.contains("主机监控服务失联")) {
            return "【主机故障告警】";
        } else if (title.matches(".*QH-S\\d+-.*-H\\d+-\\d+U.*")) {
            return "【秦淮机房网络设备】";
        } else if (title.contains("主机：") && title.contains("重启")) {
            return "【系统告警】";
        } else if (title.contains("关系型数据库")) {
            return "【数据库】";
        } else if (title.contains("Unavailable by ICMP ping")) {
            return "【辰运大厦监控摄像头】";
        } else {
            return "待确认";
        }
    }

    /**
     * 测试告警原因提取逻辑
     */
    private String extractAlertReason(String title) {
        if (title == null) {
            return "待确认";
        }

        int slashIndex = title.indexOf('/');
        if (slashIndex > 0) {
            return title.substring(0, slashIndex).trim();
        } else {
            return "待确认";
        }
    }

    /**
     * 测试告警分类功能
     */
    @Test
    public void testAlertCategory() throws Exception {
        System.out.println("=== 测试告警分类功能 ===");

        // 测试安全告警分类
        Alert securityAlert = new Alert();
        securityAlert.setTitle("WAF攻击告警");
        securityAlert.setDescription("检测到SQL注入攻击");
        securityAlert.setSeverity("high");
        securityAlert.setSourceType("safeline_waf");
        securityAlert.setEventId("test_security_001");
        securityAlert.setSourceIdent("waf_001");
        securityAlert.setOccurredAt(System.currentTimeMillis());

        // 调用自动设置分类的方法
        securityAlert.setAlertCategoryBySourceType();

        System.out.println("安全告警分类: " + securityAlert.getAlertCategory());
        System.out.println("安全告警分类枚举: " + securityAlert.getAlertCategoryEnum());

        // 测试运维告警分类
        Alert opsAlert = new Alert();
        opsAlert.setTitle("CPU使用率告警");
        opsAlert.setDescription("CPU使用率超过80%");
        opsAlert.setSeverity("medium");
        opsAlert.setSourceType("grafana");
        opsAlert.setEventId("test_ops_001");
        opsAlert.setSourceIdent("grafana_001");
        opsAlert.setOccurredAt(System.currentTimeMillis());

        // 调用自动设置分类的方法
        opsAlert.setAlertCategoryBySourceType();

        System.out.println("运维告警分类: " + opsAlert.getAlertCategory());
        System.out.println("运维告警分类枚举: " + opsAlert.getAlertCategoryEnum());

        // 测试手动设置分类
        Alert manualAlert = new Alert();
        manualAlert.setTitle("手动分类告警");
        manualAlert.setDescription("手动设置分类的告警");
        manualAlert.setSeverity("low");
        manualAlert.setSourceType("grafana");
        manualAlert.setAlertCategory("security"); // 手动设置为安全告警
        manualAlert.setEventId("test_manual_001");
        manualAlert.setSourceIdent("manual_001");
        manualAlert.setOccurredAt(System.currentTimeMillis());

        // 调用自动设置分类的方法（应该不会覆盖手动设置的值）
        manualAlert.setAlertCategoryBySourceType();

        System.out.println("手动设置告警分类: " + manualAlert.getAlertCategory());
        System.out.println("手动设置告警分类枚举: " + manualAlert.getAlertCategoryEnum());

        System.out.println("告警分类功能测试完成！");
    }

    @Autowired
    private ISysUserAccountService userAccountService;

    @Test
    public void testGetUser() {
        SysUserAccount sysUserAccount = userAccountService.selectSysUserAccountById(3L);
        System.out.println(JSON.toJSONString(sysUserAccount));
    }

    @Test
    public void testGetIncidents() {
        String json = "{\"asc\":true,\"end_time\":**********,\"labels\":{\"eventId\":\"wangsu_waf_zy57h05k\"},\"limit\":100,\"p\":1,\"start_time\":**********}";
        IncidentListRequest request = JSON.parseObject(json, IncidentListRequest.class);
        IncidentListResponse incidentListResponse = flashDutyService.queryIncidentList(request);
        System.out.println(JSON.toJSONString(incidentListResponse));
    }


    @Test
    public void testGetAccount() {
        List<SysUserAccount> sysUserAccounts = sysUserAccountService.selectSysUserAccountByAccountIds(Arrays.asList("*************", "*************"));
        System.out.println(JSON.toJSONString(sysUserAccounts));
    }

    @Autowired
    private WxCpService wxCpService;

    @Test
    public void testGetWx() throws WxErrorException {
        String userId = wxCpService.getUserService().getUserId("***********");
        System.out.println(userId);
//        WxCpUser byId = wxCpService.getUserService().getById("8426818");
//        System.out.println(JSON.toJSONString(byId));

//        wxCpService.getOauth2Service().getUserInfo()
    }

}
