server:
  port: 8233
nacos:
  serverAddr: ************:8848,************:8848,************:8848
securio:
  agentmgr:
    grpc:
      server:
        port: 9233

# 应用配置
app:
  # 主机分析配置
  host-analysis:
    # 主机基本信息数据源配置：cmdb 或 agent
    basic-info-source: cmdb
    # 数据获取超时时间（秒）
    timeout: 30
# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.wiwj.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

wukong:
  cmdb:
    # 悟空 CMDB API 基础 URL
    base-url: https://wukong.5i5j.com/prod-api
    # API 超时时间（毫秒）
    timeout: 30000
    # 连接超时时间（毫秒）
    connect-timeout: 10000
    # 读取超时时间（毫秒）
    read-timeout: 30000
    # 最大重试次数
    max-retries: 3
    # 是否启用 SSL 验证
    ssl-enabled: true
    # API 版本
    api-version: v1
    # 用户代理
    user-agent: Securio-Plugin/1.0.0
    # API 访问令牌
    token: B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd 

#xxl:
#  job:
#    admin:
#      addresses: http://localhost:8080/xxl-job-admin  # 调度中心地址
#    executor:
#      appname: securio-job  # 应用标识，集群内唯一
#      ip:  # 自动获取或指定执行器 IP
#      port: 9999  # 执行器通讯端口
#      logpath: /data/logs/wukong/xxl-job  # 任务日志存储路径
#      logretentiondays: 30  # 日志保留天数
