package com.wiwj.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.wiwj.system.dto.SysUserDto;
import com.wiwj.system.service.*;
import com.wiwj.framework.web.service.LdapUserService;
import com.wiwj.framework.web.domain.LdapPerson;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.core.domain.entity.SysDept;
import com.wiwj.common.core.domain.entity.SysRole;
import com.wiwj.common.core.domain.entity.SysUser;
import com.wiwj.common.core.page.TableDataInfo;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.common.utils.SecurityUtils;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.common.utils.poi.ExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(SysUserController.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysApplicationService applicationService;

    @Autowired
    private ISysMenuService sysMenuService;

    @Autowired
    private LdapUserService ldapUserService;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user)
    {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @GetMapping("/listSimpleAll")
    public AjaxResult listSimpleAll()
    {
        List<SysUserDto> list = userService.listSimpleAll();
        return AjaxResult.success(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user)
    {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }


    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = { "/", "/{userId}" })
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        if (StringUtils.isNotNull(userId))
        {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));

            List<String> applicationCodes = sysMenuService.getApplicationCode(userId);
            ajax.put("applicationList", applicationService.selectSysApplications(applicationCodes));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user)
    {
        if (!userService.checkUserNameUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        if (ArrayUtils.contains(userIds, getUserId()))
        {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId)
    {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysRole.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds)
    {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取部门树列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDept dept)
    {
        return success(deptService.selectDeptTreeList(dept));
    }

    /**
     * 同步LDAP用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PostMapping("/syncLdap")
    public AjaxResult syncLdapUsers(@RequestBody Map<String, Object> params)
    {
        try {
            // 获取参数
            @SuppressWarnings("unchecked")
            List<String> employeeIds = (List<String>) params.get("employeeIds");
            Long deptId = params.get("deptId") != null ? Long.valueOf(params.get("deptId").toString()) : null;
            Boolean updateExisting = (Boolean) params.getOrDefault("updateExisting", false);
            Boolean assignDefaultRole = (Boolean) params.getOrDefault("assignDefaultRole", true);

            if (employeeIds == null || employeeIds.isEmpty()) {
                return error("员工工号不能为空");
            }

            // 记录同步结果
            List<Map<String, Object>> results = new ArrayList<>();
            int successCount = 0;
            int failCount = 0;

            for (String employeeId : employeeIds) {
                Map<String, Object> result = new HashMap<>();
                result.put("employeeId", employeeId);
                
                try {
                    // 调用LDAP用户同步服务
                    SysUser syncedUser = ldapUserService.syncSingleUser(employeeId);
                    
                    if (syncedUser != null) {
                        // 如果指定了部门，更新用户部门
                        if (deptId != null && !deptId.equals(syncedUser.getDeptId())) {
                            syncedUser.setDeptId(deptId);
                            syncedUser.setUpdateBy(getUsername());
                            userService.updateUserProfile(syncedUser);
                        }
                        
                        successCount++;
                        result.put("success", true);
                        result.put("message", "同步成功");
                        result.put("userId", syncedUser.getUserId());
                        result.put("userName", syncedUser.getUserName());
                    } else {
                        failCount++;
                        result.put("success", false);
                        result.put("message", "在LDAP中未找到该用户");
                    }
                } catch (Exception e) {
                    failCount++;
                    result.put("success", false);
                    result.put("message", "同步失败: " + e.getMessage());
                    log.error("同步LDAP用户失败，工号: {}, 错误: {}", employeeId, e.getMessage(), e);
                }
                
                results.add(result);
            }

            // 构建响应消息
            String message = String.format("同步完成，成功: %d, 失败: %d", successCount, failCount);
            
            AjaxResult ajax = AjaxResult.success(message);
            ajax.put("results", results);
            ajax.put("successCount", successCount);
            ajax.put("failCount", failCount);
            ajax.put("totalCount", employeeIds.size());
            
            return ajax;
            
        } catch (Exception e) {
            log.error("LDAP用户同步失败", e);
            return error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 查询LDAP用户信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/queryLdap/{employeeId}")
    public AjaxResult queryLdapUser(@PathVariable String employeeId)
    {
        try {
            LdapPerson ldapUser = ldapUserService.queryUserByUsername(employeeId);
            if (ldapUser != null) {
                AjaxResult result = AjaxResult.success("查询成功");
                result.put("data", ldapUser);
                return result;
            } else {
                return error("在LDAP中未找到该用户");
            }
        } catch (Exception e) {
            log.error("查询LDAP用户失败，工号: {}, 错误: {}", employeeId, e.getMessage(), e);
            return error("查询失败: " + e.getMessage());
        }
    }
}
