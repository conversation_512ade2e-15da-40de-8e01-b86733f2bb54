#!/bin/bash

# 拉取最新代码
echo "正在执行git pull..."
git pull
if [ $? -ne 0 ]; then
    echo "git pull失败"
    exit 1
fi

# 使用Maven打包
echo "正在执行mvn clean package..."
/data/jenkins-tools/mvn3.6.3/bin/mvn clean package -DskipTests
if [ $? -ne 0 ]; then
    echo "Maven打包失败"
    exit 1
fi


# 脚本配置
LOCAL_JAR_PATH="/data/gitcode/securio-manager/securio-admin/target/securio-admin.jar"  # 本地 JAR 包路径
REMOTE_USER="app_user_5i5j"
REMOTE_HOSTS=("************" "************")
REMOTE_DEPLOY_DIR="/home/<USER>/ops/webapp/securio-manager"
REMOTE_LIB_DIR="$REMOTE_DEPLOY_DIR/lib"
REMOTE_BIN_DIR="$REMOTE_DEPLOY_DIR/bin"
JAR_NAME="securio-admin.jar"
BACKUP_DIR="$REMOTE_DEPLOY_DIR/backup"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 检查本地 JAR 包是否存在
if [ ! -f "$LOCAL_JAR_PATH" ]; then
    log "错误：本地 JAR 包 $LOCAL_JAR_PATH 不存在！"
    exit 1
fi

# 遍历每台远程机器
for HOST in "${REMOTE_HOSTS[@]}"; do
    log "开始部署到 $HOST ..."

    # 1. 测试 SSH 连接
    ssh -q -o ConnectTimeout=5 "$REMOTE_USER@$HOST" "exit"
    if [ $? -ne 0 ]; then
        log "错误：无法通过 SSH 连接到 $HOST，请检查网络或公钥配置！"
        continue
    fi

    # 2. 创建备份目录和 lib 目录（如果不存在）
    log "创建备份目录 $BACKUP_DIR 和 $REMOTE_LIB_DIR（如果不存在）..."
    ssh "$REMOTE_USER@$HOST" "
        mkdir -p $BACKUP_DIR
        mkdir -p $REMOTE_LIB_DIR
    "

    # 3. 备份远程现有的 JAR 包（如果存在）
    log "备份远程 JAR 包（如果存在）..."
    ssh "$REMOTE_USER@$HOST" "
        if [ -f \"$REMOTE_LIB_DIR/$JAR_NAME\" ]; then
            BACKUP_FILE=\"$BACKUP_DIR/$JAR_NAME.\$(date +%Y%m%d_%H%M%S)\"
            mv \"$REMOTE_LIB_DIR/$JAR_NAME\" \"\$BACKUP_FILE\"
            echo \"已备份到 \$BACKUP_FILE\"
        else
            echo \"远程 JAR 包不存在，无需备份\"
        fi
    "

    # 4. 拷贝新 JAR 包到远程机器的 lib 目录
    log "拷贝新 JAR 包到 $REMOTE_LIB_DIR..."
    scp "$LOCAL_JAR_PATH" "$REMOTE_USER@$HOST:$REMOTE_LIB_DIR/$JAR_NAME"
    if [ $? -eq 0 ]; then
        log "JAR 包拷贝成功"
    else
        log "错误：JAR 包拷贝失败！"
        continue
    fi
    # 5. 执行服务停止和启动（切换到工作目录）
    log "停止服务..."
    ssh "$REMOTE_USER@$HOST" "
        if [ -f \"$REMOTE_BIN_DIR/stop.sh\" ]; then
            cd \"$REMOTE_DEPLOY_DIR\" && bash \"$REMOTE_BIN_DIR/stop.sh\"
            sleep 2  # 等待服务完全停止
            echo \"服务已停止\"
        else
            echo \"警告：stop.sh 不存在，跳过停止步骤\"
        fi
    "

    log "启动服务..."
    ssh "$REMOTE_USER@$HOST" "
        if [ -f \"$REMOTE_BIN_DIR/start.sh\" ]; then
            cd \"$REMOTE_DEPLOY_DIR\" && bash \"$REMOTE_BIN_DIR/start.sh\"
            echo \"服务已启动\"
        else
            echo \"错误：start.sh 不存在，无法启动服务！\"
        fi
    "

    # 6. 检查服务状态（假设 start.sh 启动后会有进程）
    log "检查服务状态..."
    ssh "$REMOTE_USER@$HOST" "
        sleep 2  # 等待服务启动
        ps -ef | grep \"$JAR_NAME\" | grep -v grep || echo \"服务未运行，请检查 $REMOTE_BIN_DIR/start.sh 执行情况！\"
    "

    log "完成部署到 $HOST"
done

log "所有部署任务完成！"