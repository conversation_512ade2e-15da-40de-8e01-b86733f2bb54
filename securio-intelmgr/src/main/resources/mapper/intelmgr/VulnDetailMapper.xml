<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.VulnDetailMapper">
    
    <resultMap type="VulnDetail" id="VulnDetailResult">
        <result property="id"    column="id"    />
        <result property="description"    column="description"    />
        <result property="solution"    column="solution"    />
        <result property="assesInfo"    column="asses_info"    />
        <result property="cvssAccessComplexity"    column="cvss_access_complexity"    />
        <result property="cvssAuthentication"    column="cvss_authentication"    />
        <result property="cvssConfidentialityImpact"    column="cvss_confidentiality_impact"    />
        <result property="cvssIntegrityImpact"    column="cvss_integrity_impact"    />
        <result property="cvssAvailabilityImpact"    column="cvss_availability_impact"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectVulnDetailVo">
        select id, description, solution, asses_info, cvss_access_complexity, cvss_authentication, cvss_confidentiality_impact, cvss_integrity_impact, cvss_availability_impact, created_at, updated_at from vuln_detail
    </sql>

    <select id="selectVulnDetailList" parameterType="VulnDetail" resultMap="VulnDetailResult">
        <include refid="selectVulnDetailVo"/>
        <where>  
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="solution != null  and solution != ''"> and solution = #{solution}</if>
            <if test="assesInfo != null  and assesInfo != ''"> and asses_info = #{assesInfo}</if>
            <if test="cvssAccessComplexity != null  and cvssAccessComplexity != ''"> and cvss_access_complexity = #{cvssAccessComplexity}</if>
            <if test="cvssAuthentication != null  and cvssAuthentication != ''"> and cvss_authentication = #{cvssAuthentication}</if>
            <if test="cvssConfidentialityImpact != null  and cvssConfidentialityImpact != ''"> and cvss_confidentiality_impact = #{cvssConfidentialityImpact}</if>
            <if test="cvssIntegrityImpact != null  and cvssIntegrityImpact != ''"> and cvss_integrity_impact = #{cvssIntegrityImpact}</if>
            <if test="cvssAvailabilityImpact != null  and cvssAvailabilityImpact != ''"> and cvss_availability_impact = #{cvssAvailabilityImpact}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectVulnDetailById" parameterType="Integer" resultMap="VulnDetailResult">
        <include refid="selectVulnDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertVulnDetail" parameterType="VulnDetail">
        insert into vuln_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="description != null">description,</if>
            <if test="solution != null">solution,</if>
            <if test="assesInfo != null">asses_info,</if>
            <if test="cvssAccessComplexity != null">cvss_access_complexity,</if>
            <if test="cvssAuthentication != null">cvss_authentication,</if>
            <if test="cvssConfidentialityImpact != null">cvss_confidentiality_impact,</if>
            <if test="cvssIntegrityImpact != null">cvss_integrity_impact,</if>
            <if test="cvssAvailabilityImpact != null">cvss_availability_impact,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="description != null">#{description},</if>
            <if test="solution != null">#{solution},</if>
            <if test="assesInfo != null">#{assesInfo},</if>
            <if test="cvssAccessComplexity != null">#{cvssAccessComplexity},</if>
            <if test="cvssAuthentication != null">#{cvssAuthentication},</if>
            <if test="cvssConfidentialityImpact != null">#{cvssConfidentialityImpact},</if>
            <if test="cvssIntegrityImpact != null">#{cvssIntegrityImpact},</if>
            <if test="cvssAvailabilityImpact != null">#{cvssAvailabilityImpact},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateVulnDetail" parameterType="VulnDetail">
        update vuln_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="description != null">description = #{description},</if>
            <if test="solution != null">solution = #{solution},</if>
            <if test="assesInfo != null">asses_info = #{assesInfo},</if>
            <if test="cvssAccessComplexity != null">cvss_access_complexity = #{cvssAccessComplexity},</if>
            <if test="cvssAuthentication != null">cvss_authentication = #{cvssAuthentication},</if>
            <if test="cvssConfidentialityImpact != null">cvss_confidentiality_impact = #{cvssConfidentialityImpact},</if>
            <if test="cvssIntegrityImpact != null">cvss_integrity_impact = #{cvssIntegrityImpact},</if>
            <if test="cvssAvailabilityImpact != null">cvss_availability_impact = #{cvssAvailabilityImpact},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVulnDetailById" parameterType="Integer">
        delete from vuln_detail where id = #{id}
    </delete>

    <delete id="deleteVulnDetailByIds" parameterType="String">
        delete from vuln_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>