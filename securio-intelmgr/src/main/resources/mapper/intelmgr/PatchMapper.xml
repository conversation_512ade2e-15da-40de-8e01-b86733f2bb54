<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.PatchMapper">
    
    <resultMap type="Patch" id="PatchResult">
        <result property="id"    column="id"    />
        <result property="level"    column="level"    />
        <result property="type"    column="type"    />
        <result property="number"    column="number"    />
        <result property="name"    column="name"    />
        <result property="os"    column="os"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="description"    column="description"    />
        <result property="solution"    column="solution"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectPatchVo">
        select id, level, type, number, name, os, publish_time, description, solution, created_at, updated_at from patch
    </sql>

    <select id="selectPatchList" parameterType="Patch" resultMap="PatchResult">
        <include refid="selectPatchVo"/>
        <where>  
            <if test="level != null "> and level = #{level}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="os != null  and os != ''"> and os = #{os}</if>
            <if test="publishTime != null "> and publish_time = #{publishTime}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="solution != null  and solution != ''"> and solution = #{solution}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
        order by publish_time desc
    </select>
    
    <select id="selectPatchById" parameterType="String" resultMap="PatchResult">
        <include refid="selectPatchVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPatch" parameterType="Patch">
        insert into patch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="level != null">level,</if>
            <if test="type != null">type,</if>
            <if test="number != null">number,</if>
            <if test="name != null">name,</if>
            <if test="os != null">os,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="description != null">description,</if>
            <if test="solution != null">solution,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="level != null">#{level},</if>
            <if test="type != null">#{type},</if>
            <if test="number != null">#{number},</if>
            <if test="name != null">#{name},</if>
            <if test="os != null">#{os},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="description != null">#{description},</if>
            <if test="solution != null">#{solution},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updatePatch" parameterType="Patch">
        update patch
        <trim prefix="SET" suffixOverrides=",">
            <if test="level != null">level = #{level},</if>
            <if test="type != null">type = #{type},</if>
            <if test="number != null">number = #{number},</if>
            <if test="name != null">name = #{name},</if>
            <if test="os != null">os = #{os},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="description != null">description = #{description},</if>
            <if test="solution != null">solution = #{solution},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatchById" parameterType="String">
        delete from patch where id = #{id}
    </delete>

    <delete id="deletePatchByIds" parameterType="String">
        delete from patch where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>