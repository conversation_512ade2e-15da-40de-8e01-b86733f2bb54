<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.VulnMapper">
    
    <resultMap type="Vuln" id="VulnResult">
        <result property="id"    column="id"    />
        <result property="level"    column="level"    />
        <result property="name"    column="name"    />
        <result property="cvssScore"    column="cvss_score"    />
        <result property="cve"    column="cve"    />
        <result property="cnnvd"    column="cnnvd"    />
        <result property="cnvd"    column="cnvd"    />
        <result property="bid"    column="bid"    />
        <result property="cwe"    column="cwe"    />
        <result property="type"    column="type"    />
        <result property="cvssVector"    column="cvss_vector"    />
        <result property="cvssAccessVector"    column="cvss_access_vector"    />
        <result property="hasPoc"    column="has_poc"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="modifyDate"    column="modify_date"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectVulnVo">
        select id, level, name, cvss_score, cve, cnnvd, cnvd, bid, cwe, type, cvss_vector, cvss_access_vector, has_poc, publish_date, modify_date, created_at, updated_at from vuln
    </sql>

    <select id="selectVulnList" parameterType="Vuln" resultMap="VulnResult">
        <include refid="selectVulnVo"/>
        <where>  
            <if test="level != null "> and level = #{level}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="cvssScore != null "> and cvss_score = #{cvssScore}</if>
            <if test="cve != null  and cve != ''"> and cve = #{cve}</if>
            <if test="cnnvd != null  and cnnvd != ''"> and cnnvd = #{cnnvd}</if>
            <if test="cnvd != null  and cnvd != ''"> and cnvd = #{cnvd}</if>
            <if test="bid != null  and bid != ''"> and bid = #{bid}</if>
            <if test="cwe != null  and cwe != ''"> and cwe = #{cwe}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="cvssVector != null  and cvssVector != ''"> and cvss_vector = #{cvssVector}</if>
            <if test="cvssAccessVector != null  and cvssAccessVector != ''"> and cvss_access_vector = #{cvssAccessVector}</if>
            <if test="hasPoc != null "> and has_poc = #{hasPoc}</if>
            <if test="publishDate != null "> and publish_date = #{publishDate}</if>
            <if test="modifyDate != null "> and modify_date = #{modifyDate}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
        order by publish_date desc
    </select>
    
    <select id="selectVulnById" parameterType="Integer" resultMap="VulnResult">
        <include refid="selectVulnVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertVuln" parameterType="Vuln" useGeneratedKeys="true" keyProperty="id">
        insert into vuln
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="level != null">level,</if>
            <if test="name != null">name,</if>
            <if test="cvssScore != null">cvss_score,</if>
            <if test="cve != null">cve,</if>
            <if test="cnnvd != null">cnnvd,</if>
            <if test="cnvd != null">cnvd,</if>
            <if test="bid != null">bid,</if>
            <if test="cwe != null">cwe,</if>
            <if test="type != null">type,</if>
            <if test="cvssVector != null">cvss_vector,</if>
            <if test="cvssAccessVector != null">cvss_access_vector,</if>
            <if test="hasPoc != null">has_poc,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="modifyDate != null">modify_date,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="level != null">#{level},</if>
            <if test="name != null">#{name},</if>
            <if test="cvssScore != null">#{cvssScore},</if>
            <if test="cve != null">#{cve},</if>
            <if test="cnnvd != null">#{cnnvd},</if>
            <if test="cnvd != null">#{cnvd},</if>
            <if test="bid != null">#{bid},</if>
            <if test="cwe != null">#{cwe},</if>
            <if test="type != null">#{type},</if>
            <if test="cvssVector != null">#{cvssVector},</if>
            <if test="cvssAccessVector != null">#{cvssAccessVector},</if>
            <if test="hasPoc != null">#{hasPoc},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="modifyDate != null">#{modifyDate},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateVuln" parameterType="Vuln">
        update vuln
        <trim prefix="SET" suffixOverrides=",">
            <if test="level != null">level = #{level},</if>
            <if test="name != null">name = #{name},</if>
            <if test="cvssScore != null">cvss_score = #{cvssScore},</if>
            <if test="cve != null">cve = #{cve},</if>
            <if test="cnnvd != null">cnnvd = #{cnnvd},</if>
            <if test="cnvd != null">cnvd = #{cnvd},</if>
            <if test="bid != null">bid = #{bid},</if>
            <if test="cwe != null">cwe = #{cwe},</if>
            <if test="type != null">type = #{type},</if>
            <if test="cvssVector != null">cvss_vector = #{cvssVector},</if>
            <if test="cvssAccessVector != null">cvss_access_vector = #{cvssAccessVector},</if>
            <if test="hasPoc != null">has_poc = #{hasPoc},</if>
            <if test="publishDate != null">publish_date = #{publishDate},</if>
            <if test="modifyDate != null">modify_date = #{modifyDate},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVulnById" parameterType="Integer">
        delete from vuln where id = #{id}
    </delete>

    <delete id="deleteVulnByIds" parameterType="String">
        delete from vuln where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>