<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.PatchTraitMapper">
    
    <resultMap type="PatchTrait" id="PatchTraitResult">
        <result property="id"    column="id"    />
        <result property="patchId"    column="patch_id"    />
        <result property="trait"    column="trait"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectPatchTraitVo">
        select id, patch_id, trait, created_at from patch_trait
    </sql>

    <select id="selectPatchTraitList" parameterType="PatchTrait" resultMap="PatchTraitResult">
        <include refid="selectPatchTraitVo"/>
        <where>  
            <if test="patchId != null  and patchId != ''"> and patch_id = #{patchId}</if>
            <if test="trait != null  and trait != ''"> and trait = #{trait}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectPatchTraitById" parameterType="Integer" resultMap="PatchTraitResult">
        <include refid="selectPatchTraitVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPatchTrait" parameterType="PatchTrait" useGeneratedKeys="true" keyProperty="id">
        insert into patch_trait
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">patch_id,</if>
            <if test="trait != null and trait != ''">trait,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">#{patchId},</if>
            <if test="trait != null and trait != ''">#{trait},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updatePatchTrait" parameterType="PatchTrait">
        update patch_trait
        <trim prefix="SET" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">patch_id = #{patchId},</if>
            <if test="trait != null and trait != ''">trait = #{trait},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatchTraitById" parameterType="Integer">
        delete from patch_trait where id = #{id}
    </delete>

    <delete id="deletePatchTraitByIds" parameterType="String">
        delete from patch_trait where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectPatchTraitByPatchIds" resultMap="PatchTraitResult">
        <include refid="selectPatchTraitVo"/>
        where patch_id in
        <foreach item="patchId" collection="list" open="(" separator="," close=")">
            #{patchId}
        </foreach>
    </select>
</mapper>