<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.VulnReferenceMapper">
    
    <resultMap type="VulnReference" id="VulnReferenceResult">
        <result property="id"    column="id"    />
        <result property="vulnId"    column="vuln_id"    />
        <result property="name"    column="name"    />
        <result property="url"    column="url"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectVulnReferenceVo">
        select id, vuln_id, name, url, created_at from vuln_reference
    </sql>

    <select id="selectVulnReferenceList" parameterType="VulnReference" resultMap="VulnReferenceResult">
        <include refid="selectVulnReferenceVo"/>
        <where>  
            <if test="vulnId != null "> and vuln_id = #{vulnId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectVulnReferenceById" parameterType="Integer" resultMap="VulnReferenceResult">
        <include refid="selectVulnReferenceVo"/>
        where id = #{id}
    </select>
    
    <select id="selectVulnReferenceByVulnId" parameterType="Integer" resultMap="VulnReferenceResult">
        <include refid="selectVulnReferenceVo"/>
        where vuln_id = #{vulnId}
    </select>
        
    <insert id="insertVulnReference" parameterType="VulnReference" useGeneratedKeys="true" keyProperty="id">
        insert into vuln_reference
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vulnId != null">vuln_id,</if>
            <if test="name != null">name,</if>
            <if test="url != null">url,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vulnId != null">#{vulnId},</if>
            <if test="name != null">#{name},</if>
            <if test="url != null">#{url},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updateVulnReference" parameterType="VulnReference">
        update vuln_reference
        <trim prefix="SET" suffixOverrides=",">
            <if test="vulnId != null">vuln_id = #{vulnId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="url != null">url = #{url},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVulnReferenceById" parameterType="Integer">
        delete from vuln_reference where id = #{id}
    </delete>

    <delete id="deleteVulnReferenceByIds" parameterType="String">
        delete from vuln_reference where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>