<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.VulnExploitInfoMapper">
    
    <resultMap type="VulnExploitInfo" id="VulnExploitInfoResult">
        <result property="id"    column="id"    />
        <result property="vulnId"    column="vuln_id"    />
        <result property="source"    column="source"    />
        <result property="title"    column="title"    />
        <result property="link"    column="link"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectVulnExploitInfoVo">
        select id, vuln_id, source, title, link, created_at from vuln_exploit_info
    </sql>

    <select id="selectVulnExploitInfoList" parameterType="VulnExploitInfo" resultMap="VulnExploitInfoResult">
        <include refid="selectVulnExploitInfoVo"/>
        <where>  
            <if test="vulnId != null "> and vuln_id = #{vulnId}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="link != null  and link != ''"> and link = #{link}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectVulnExploitInfoById" parameterType="Integer" resultMap="VulnExploitInfoResult">
        <include refid="selectVulnExploitInfoVo"/>
        where id = #{id}
    </select>
    
    <select id="selectVulnExploitInfoByVulnId" parameterType="Integer" resultMap="VulnExploitInfoResult">
        <include refid="selectVulnExploitInfoVo"/>
        where vuln_id = #{vulnId}
    </select>
        
    <insert id="insertVulnExploitInfo" parameterType="VulnExploitInfo" useGeneratedKeys="true" keyProperty="id">
        insert into vuln_exploit_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vulnId != null">vuln_id,</if>
            <if test="source != null">source,</if>
            <if test="title != null">title,</if>
            <if test="link != null">link,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vulnId != null">#{vulnId},</if>
            <if test="source != null">#{source},</if>
            <if test="title != null">#{title},</if>
            <if test="link != null">#{link},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updateVulnExploitInfo" parameterType="VulnExploitInfo">
        update vuln_exploit_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="vulnId != null">vuln_id = #{vulnId},</if>
            <if test="source != null">source = #{source},</if>
            <if test="title != null">title = #{title},</if>
            <if test="link != null">link = #{link},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVulnExploitInfoById" parameterType="Integer">
        delete from vuln_exploit_info where id = #{id}
    </delete>

    <delete id="deleteVulnExploitInfoByIds" parameterType="String">
        delete from vuln_exploit_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>