<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.PatchReferenceMapper">
    
    <resultMap type="PatchReference" id="PatchReferenceResult">
        <result property="id"    column="id"    />
        <result property="patchId"    column="patch_id"    />
        <result property="source"    column="source"    />
        <result property="title"    column="title"    />
        <result property="link"    column="link"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectPatchReferenceVo">
        select id, patch_id, source, title, link, created_at from patch_reference
    </sql>

    <select id="selectPatchReferenceList" parameterType="PatchReference" resultMap="PatchReferenceResult">
        <include refid="selectPatchReferenceVo"/>
        <where>  
            <if test="patchId != null  and patchId != ''"> and patch_id = #{patchId}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="link != null  and link != ''"> and link = #{link}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectPatchReferenceById" parameterType="Integer" resultMap="PatchReferenceResult">
        <include refid="selectPatchReferenceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPatchReference" parameterType="PatchReference" useGeneratedKeys="true" keyProperty="id">
        insert into patch_reference
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">patch_id,</if>
            <if test="source != null">source,</if>
            <if test="title != null">title,</if>
            <if test="link != null">link,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">#{patchId},</if>
            <if test="source != null">#{source},</if>
            <if test="title != null">#{title},</if>
            <if test="link != null">#{link},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updatePatchReference" parameterType="PatchReference">
        update patch_reference
        <trim prefix="SET" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">patch_id = #{patchId},</if>
            <if test="source != null">source = #{source},</if>
            <if test="title != null">title = #{title},</if>
            <if test="link != null">link = #{link},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatchReferenceById" parameterType="Integer">
        delete from patch_reference where id = #{id}
    </delete>

    <delete id="deletePatchReferenceByIds" parameterType="String">
        delete from patch_reference where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>