<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.PatchSupersedingMapper">
    
    <resultMap type="PatchSuperseding" id="PatchSupersedingResult">
        <result property="id"    column="id"    />
        <result property="patchId"    column="patch_id"    />
        <result property="supersedingId"    column="superseding_id"    />
        <result property="title"    column="title"    />
        <result property="titleEn"    column="title_en"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectPatchSupersedingVo">
        select id, patch_id, superseding_id, title, title_en, created_at from patch_superseding
    </sql>

    <select id="selectPatchSupersedingList" parameterType="PatchSuperseding" resultMap="PatchSupersedingResult">
        <include refid="selectPatchSupersedingVo"/>
        <where>  
            <if test="patchId != null  and patchId != ''"> and patch_id = #{patchId}</if>
            <if test="supersedingId != null  and supersedingId != ''"> and superseding_id = #{supersedingId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en = #{titleEn}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectPatchSupersedingById" parameterType="Integer" resultMap="PatchSupersedingResult">
        <include refid="selectPatchSupersedingVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPatchSuperseding" parameterType="PatchSuperseding" useGeneratedKeys="true" keyProperty="id">
        insert into patch_superseding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">patch_id,</if>
            <if test="supersedingId != null and supersedingId != ''">superseding_id,</if>
            <if test="title != null">title,</if>
            <if test="titleEn != null">title_en,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">#{patchId},</if>
            <if test="supersedingId != null and supersedingId != ''">#{supersedingId},</if>
            <if test="title != null">#{title},</if>
            <if test="titleEn != null">#{titleEn},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updatePatchSuperseding" parameterType="PatchSuperseding">
        update patch_superseding
        <trim prefix="SET" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">patch_id = #{patchId},</if>
            <if test="supersedingId != null and supersedingId != ''">superseding_id = #{supersedingId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="titleEn != null">title_en = #{titleEn},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatchSupersedingById" parameterType="Integer">
        delete from patch_superseding where id = #{id}
    </delete>

    <delete id="deletePatchSupersedingByIds" parameterType="String">
        delete from patch_superseding where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>