package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.VulnReferenceMapper;
import com.wiwj.securio.intelmgr.domain.VulnReference;
import com.wiwj.securio.intelmgr.service.IVulnReferenceService;

/**
 * 漏洞参考链接Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class VulnReferenceServiceImpl implements IVulnReferenceService {
    @Autowired
    private VulnReferenceMapper vulnReferenceMapper;

    /**
     * 查询漏洞参考链接
     *
     * @param id 漏洞参考链接主键
     * @return 漏洞参考链接
     */
    @Override
    public VulnReference selectVulnReferenceById(Integer id) {
        return vulnReferenceMapper.selectVulnReferenceById(id);
    }

    /**
     * 查询漏洞参考链接列表
     *
     * @param vulnReference 漏洞参考链接
     * @return 漏洞参考链接
     */
    @Override
    public List<VulnReference> selectVulnReferenceList(VulnReference vulnReference) {
        return vulnReferenceMapper.selectVulnReferenceList(vulnReference);
    }

    /**
     * 新增漏洞参考链接
     *
     * @param vulnReference 漏洞参考链接
     * @return 结果
     */
    @Override
    public int insertVulnReference(VulnReference vulnReference) {
        return vulnReferenceMapper.insertVulnReference(vulnReference);
    }

    /**
     * 修改漏洞参考链接
     *
     * @param vulnReference 漏洞参考链接
     * @return 结果
     */
    @Override
    public int updateVulnReference(VulnReference vulnReference) {
        return vulnReferenceMapper.updateVulnReference(vulnReference);
    }

    /**
     * 批量删除漏洞参考链接
     *
     * @param ids 需要删除的漏洞参考链接主键
     * @return 结果
     */
    @Override
    public int deleteVulnReferenceByIds(Integer[] ids) {
        return vulnReferenceMapper.deleteVulnReferenceByIds(ids);
    }

    /**
     * 删除漏洞参考链接信息
     *
     * @param id 漏洞参考链接主键
     * @return 结果
     */
    @Override
    public int deleteVulnReferenceById(Integer id) {
        return vulnReferenceMapper.deleteVulnReferenceById(id);
    }

    /**
     * 根据漏洞ID查询相关参考链接
     *
     * @param vulnId 漏洞ID
     * @return 漏洞参考链接集合
     */
    @Override
    public List<VulnReference> selectVulnReferenceByVulnId(Integer vulnId) {
        return vulnReferenceMapper.selectVulnReferenceByVulnId(vulnId);
    }
}
