package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.PatchTrait;

/**
 * 补丁特性Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IPatchTraitService {
    /**
     * 查询补丁特性
     *
     * @param id 补丁特性主键
     * @return 补丁特性
     */
    public PatchTrait selectPatchTraitById(Integer id);

    /**
     * 查询补丁特性列表
     *
     * @param patchTrait 补丁特性
     * @return 补丁特性集合
     */
    public List<PatchTrait> selectPatchTraitList(PatchTrait patchTrait);

    /**
     * 新增补丁特性
     *
     * @param patchTrait 补丁特性
     * @return 结果
     */
    public int insertPatchTrait(PatchTrait patchTrait);

    /**
     * 修改补丁特性
     *
     * @param patchTrait 补丁特性
     * @return 结果
     */
    public int updatePatchTrait(PatchTrait patchTrait);

    /**
     * 批量删除补丁特性
     *
     * @param ids 需要删除的补丁特性主键集合
     * @return 结果
     */
    public int deletePatchTraitByIds(Integer[] ids);

    /**
     * 删除补丁特性信息
     *
     * @param id 补丁特性主键
     * @return 结果
     */
    public int deletePatchTraitById(Integer id);

    /**
     * 根据补丁ID查询补丁特性
     *
     * @param patchIds 补丁ID列表
     * @return 补丁特性列表
     */
    public List<PatchTrait> selectPatchTraitByPatchIds(List<String> patchIds);
}
