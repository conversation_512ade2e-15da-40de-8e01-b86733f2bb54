package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 补丁基本信息对象 patch
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public class Patch extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 补丁ID */
    private String id;

    /** 补丁级别：1低危，2中危，3高危，4严重 */
    @Excel(name = "补丁级别：1低危，2中危，3高危，4严重")
    private Integer level;

    /** 补丁类型 */
    @Excel(name = "补丁类型")
    private String type;

    /** 补丁编号 */
    @Excel(name = "补丁编号")
    private String number;

    /** 补丁名称 */
    @Excel(name = "补丁名称")
    private String name;

    /** 操作系统 */
    @Excel(name = "操作系统")
    private String os;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date publishTime;

    /** 补丁描述 */
    @Excel(name = "补丁描述")
    private String description;

    /** 解决方案 */
    @Excel(name = "解决方案")
    private String solution;

    /** u7279u5f81u4fe1u606f */
    private String feature;

    /** u521bu5efau65f6u95f4 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "u521bu5efau65f6u95f4", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(String id){
        this.id = id;
    }

    public String getId(){
        return id;
    }
    public void setLevel(Integer level){
        this.level = level;
    }

    public Integer getLevel(){
        return level;
    }
    public void setType(String type){
        this.type = type;
    }

    public String getType(){
        return type;
    }
    public void setNumber(String number){
        this.number = number;
    }

    public String getNumber(){
        return number;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }
    public void setOs(String os){
        this.os = os;
    }

    public String getOs(){
        return os;
    }
    public void setPublishTime(Date publishTime){
        this.publishTime = publishTime;
    }

    public Date getPublishTime(){
        return publishTime;
    }
    public void setDescription(String description){
        this.description = description;
    }

    public String getDescription(){
        return description;
    }
    public void setSolution(String solution){
        this.solution = solution;
    }

    public String getSolution(){
        return solution;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getFeature() {
        return feature;
    }

    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt){
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("level", getLevel())
            .append("type", getType())
            .append("number", getNumber())
            .append("name", getName())
            .append("os", getOs())
            .append("publishTime", getPublishTime())
            .append("description", getDescription())
            .append("solution", getSolution())
            .append("feature", getFeature())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
