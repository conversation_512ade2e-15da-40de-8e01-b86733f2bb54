package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.PatchReference;
import com.wiwj.securio.intelmgr.service.IPatchReferenceService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 补丁参考链接Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patchReference")
public class PatchReferenceController extends BaseController {
    @Autowired
    private IPatchReferenceService patchReferenceService;

    /**
     * 查询补丁参考链接列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping("/list")
    public TableDataInfo list(PatchReference patchReference) {
        startPage();
        List<PatchReference> list = patchReferenceService.selectPatchReferenceList(patchReference);
        return getDataTable(list);
    }

    /**
     * 导出补丁参考链接列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁参考链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatchReference patchReference) {
        List<PatchReference> list = patchReferenceService.selectPatchReferenceList(patchReference);
        ExcelUtil<PatchReference> util = new ExcelUtil<PatchReference>(PatchReference.class);
        util.exportExcel(response, list, "补丁参考链接数据");
    }

    /**
     * 获取补丁参考链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(patchReferenceService.selectPatchReferenceById(id));
    }

    /**
     * 新增补丁参考链接
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁参考链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatchReference patchReference) {
        return toAjax(patchReferenceService.insertPatchReference(patchReference));
    }

    /**
     * 修改补丁参考链接
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁参考链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatchReference patchReference) {
        return toAjax(patchReferenceService.updatePatchReference(patchReference));
    }

    /**
     * 删除补丁参考链接
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁参考链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patchReferenceService.deletePatchReferenceByIds(ids));
    }
}
