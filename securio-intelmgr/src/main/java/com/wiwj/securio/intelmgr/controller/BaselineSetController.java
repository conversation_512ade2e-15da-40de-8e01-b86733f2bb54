package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.BaselineSet;
import com.wiwj.securio.intelmgr.service.IBaselineSetService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 基线分类Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/intelmgr/baselineSet")
public class BaselineSetController extends BaseController {
    @Autowired
    private IBaselineSetService baselineSetService;

    /**
     * 查询基线分类列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @GetMapping("/list")
    public TableDataInfo list(BaselineSet baselineSet) {
        List<BaselineSet> list = baselineSetService.selectBaselineSetList(baselineSet);
        return getDataTable(list);
    }

    /**
     * 导出基线分类列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @Log(title = "基线分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaselineSet baselineSet) {
        List<BaselineSet> list = baselineSetService.selectBaselineSetList(baselineSet);
        ExcelUtil<BaselineSet> util = new ExcelUtil<BaselineSet>(BaselineSet.class);
        util.exportExcel(response, list, "基线分类数据");
    }

    /**
     * 获取基线分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(baselineSetService.selectBaselineSetById(id));
    }

    /**
     * 新增基线分类
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @Log(title = "基线分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaselineSet baselineSet) {
        return toAjax(baselineSetService.insertBaselineSet(baselineSet));
    }

    /**
     * 修改基线分类
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @Log(title = "基线分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaselineSet baselineSet) {
        return toAjax(baselineSetService.updateBaselineSet(baselineSet));
    }

    /**
     * 删除基线分类
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @Log(title = "基线分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(baselineSetService.deleteBaselineSetByIds(ids));
    }
}
