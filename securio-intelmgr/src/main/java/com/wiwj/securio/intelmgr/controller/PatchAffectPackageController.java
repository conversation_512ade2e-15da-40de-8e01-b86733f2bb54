package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.PatchAffectPackage;
import com.wiwj.securio.intelmgr.service.IPatchAffectPackageService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 补丁影响包Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patchAffectPackage")
public class PatchAffectPackageController extends BaseController {
    @Autowired
    private IPatchAffectPackageService patchAffectPackageService;

    /**
     * 查询补丁影响包列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping("/list")
    public TableDataInfo list(PatchAffectPackage patchAffectPackage) {
        startPage();
        List<PatchAffectPackage> list = patchAffectPackageService.selectPatchAffectPackageList(patchAffectPackage);
        return getDataTable(list);
    }

    /**
     * 导出补丁影响包列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁影响包", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatchAffectPackage patchAffectPackage) {
        List<PatchAffectPackage> list = patchAffectPackageService.selectPatchAffectPackageList(patchAffectPackage);
        ExcelUtil<PatchAffectPackage> util = new ExcelUtil<PatchAffectPackage>(PatchAffectPackage.class);
        util.exportExcel(response, list, "补丁影响包数据");
    }

    /**
     * 获取补丁影响包详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(patchAffectPackageService.selectPatchAffectPackageById(id));
    }

    /**
     * 新增补丁影响包
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁影响包", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatchAffectPackage patchAffectPackage) {
        return toAjax(patchAffectPackageService.insertPatchAffectPackage(patchAffectPackage));
    }

    /**
     * 修改补丁影响包
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁影响包", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatchAffectPackage patchAffectPackage) {
        return toAjax(patchAffectPackageService.updatePatchAffectPackage(patchAffectPackage));
    }

    /**
     * 删除补丁影响包
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁影响包", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patchAffectPackageService.deletePatchAffectPackageByIds(ids));
    }
}
