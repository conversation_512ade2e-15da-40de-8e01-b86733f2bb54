package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.PatchCveMapper;
import com.wiwj.securio.intelmgr.domain.PatchCve;
import com.wiwj.securio.intelmgr.service.IPatchCveService;

/**
 * 补丁CVEService业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class PatchCveServiceImpl implements IPatchCveService {
    @Autowired
    private PatchCveMapper patchCveMapper;

    /**
     * 查询补丁CVE
     *
     * @param id 补丁CVE主键
     * @return 补丁CVE
     */
    @Override
    public PatchCve selectPatchCveById(Integer id) {
        return patchCveMapper.selectPatchCveById(id);
    }

    /**
     * 查询补丁CVE列表
     *
     * @param patchCve 补丁CVE
     * @return 补丁CVE
     */
    @Override
    public List<PatchCve> selectPatchCveList(PatchCve patchCve) {
        return patchCveMapper.selectPatchCveList(patchCve);
    }

    /**
     * 新增补丁CVE
     *
     * @param patchCve 补丁CVE
     * @return 结果
     */
    @Override
    public int insertPatchCve(PatchCve patchCve) {
        return patchCveMapper.insertPatchCve(patchCve);
    }

    /**
     * 修改补丁CVE
     *
     * @param patchCve 补丁CVE
     * @return 结果
     */
    @Override
    public int updatePatchCve(PatchCve patchCve) {
        return patchCveMapper.updatePatchCve(patchCve);
    }

    /**
     * 批量删除补丁CVE
     *
     * @param ids 需要删除的补丁CVE主键
     * @return 结果
     */
    @Override
    public int deletePatchCveByIds(Integer[] ids) {
        return patchCveMapper.deletePatchCveByIds(ids);
    }

    /**
     * 删除补丁CVE信息
     *
     * @param id 补丁CVE主键
     * @return 结果
     */
    @Override
    public int deletePatchCveById(Integer id) {
        return patchCveMapper.deletePatchCveById(id);
    }
}
