package com.wiwj.securio.intelmgr.mapper;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.PatchSuperseded;

/**
 * 被替代的补丁Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface PatchSupersededMapper {
    /**
     * 查询被替代的补丁
     *
     * @param id 被替代的补丁主键
     * @return 被替代的补丁
     */
    public PatchSuperseded selectPatchSupersededById(Integer id);

    /**
     * 查询被替代的补丁列表
     *
     * @param patchSuperseded 被替代的补丁
     * @return 被替代的补丁集合
     */
    public List<PatchSuperseded> selectPatchSupersededList(PatchSuperseded patchSuperseded);

    /**
     * 新增被替代的补丁
     *
     * @param patchSuperseded 被替代的补丁
     * @return 结果
     */
    public int insertPatchSuperseded(PatchSuperseded patchSuperseded);

    /**
     * 修改被替代的补丁
     *
     * @param patchSuperseded 被替代的补丁
     * @return 结果
     */
    public int updatePatchSuperseded(PatchSuperseded patchSuperseded);

    /**
     * 删除被替代的补丁
     *
     * @param id 被替代的补丁主键
     * @return 结果
     */
    public int deletePatchSupersededById(Integer id);

    /**
     * 批量删除被替代的补丁
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatchSupersededByIds(Integer[] ids);
}
