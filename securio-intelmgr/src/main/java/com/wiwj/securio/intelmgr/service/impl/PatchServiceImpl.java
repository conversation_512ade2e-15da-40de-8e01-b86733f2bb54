package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.wiwj.securio.intelmgr.mapper.PatchMapper;
import com.wiwj.securio.intelmgr.domain.Patch;
import com.wiwj.securio.intelmgr.domain.PatchTrait;
import com.wiwj.securio.intelmgr.domain.PatchAffectProduct;
import com.wiwj.securio.intelmgr.domain.PatchAffectPackage;
import com.wiwj.securio.intelmgr.domain.PatchCve;
import com.wiwj.securio.intelmgr.domain.PatchReference;
import com.wiwj.securio.intelmgr.domain.PatchSuperseded;
import com.wiwj.securio.intelmgr.domain.PatchSuperseding;
import com.wiwj.securio.intelmgr.service.IPatchService;
import com.wiwj.securio.intelmgr.service.IPatchTraitService;
import com.wiwj.securio.intelmgr.service.IPatchAffectProductService;
import com.wiwj.securio.intelmgr.service.IPatchAffectPackageService;
import com.wiwj.securio.intelmgr.service.IPatchCveService;
import com.wiwj.securio.intelmgr.service.IPatchReferenceService;
import com.wiwj.securio.intelmgr.service.IPatchSupersededService;
import com.wiwj.securio.intelmgr.service.IPatchSupersedingService;

/**
 * 补丁基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class PatchServiceImpl implements IPatchService {
    @Autowired
    private PatchMapper patchMapper;
    
    @Autowired
    private IPatchTraitService patchTraitService;
    
    @Autowired
    private IPatchAffectProductService patchAffectProductService;
    
    @Autowired
    private IPatchAffectPackageService patchAffectPackageService;
    
    @Autowired
    private IPatchCveService patchCveService;
    
    @Autowired
    private IPatchReferenceService patchReferenceService;
    
    @Autowired
    private IPatchSupersededService patchSupersededService;
    
    @Autowired
    private IPatchSupersedingService patchSupersedingService;

    /**
     * 查询补丁基本信息
     *
     * @param id 补丁基本信息主键
     * @return 补丁基本信息
     */
    @Override
    public Patch selectPatchById(String id) {
        return patchMapper.selectPatchById(id);
    }

    /**
     * 查询补丁基本信息列表
     *
     * @param patch 补丁基本信息
     * @return 补丁基本信息
     */
    @Override
    public List<Patch> selectPatchList(Patch patch) {
        return patchMapper.selectPatchList(patch);
    }

    /**
     * 新增补丁基本信息
     *
     * @param patch 补丁基本信息
     * @return 结果
     */
    @Override
    public int insertPatch(Patch patch) {
        patch.setId(UUID.randomUUID().toString());
        return patchMapper.insertPatch(patch);
    }

    /**
     * 修改补丁基本信息
     *
     * @param patch 补丁基本信息
     * @return 结果
     */
    @Override
    public int updatePatch(Patch patch) {
        return patchMapper.updatePatch(patch);
    }

    /**
     * 批量删除补丁基本信息
     *
     * @param ids 需要删除的补丁基本信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePatchByIds(String[] ids) {
        // 遍历删除所有关联数据
        for (String id : ids) {
            // 1. 删除特性信息
            PatchTrait traitQuery = new PatchTrait();
            traitQuery.setPatchId(id);
            List<PatchTrait> traits = patchTraitService.selectPatchTraitList(traitQuery);
            for (PatchTrait trait : traits) {
                patchTraitService.deletePatchTraitById(trait.getId());
            }
            
            // 2. 删除CVE信息
            PatchCve cveQuery = new PatchCve();
            cveQuery.setPatchId(id);
            List<PatchCve> cves = patchCveService.selectPatchCveList(cveQuery);
            for (PatchCve cve : cves) {
                patchCveService.deletePatchCveById(cve.getId());
            }
            
            // 3. 删除影响产品信息
            PatchAffectProduct productQuery = new PatchAffectProduct();
            productQuery.setPatchId(id);
            List<PatchAffectProduct> products = patchAffectProductService.selectPatchAffectProductList(productQuery);
            for (PatchAffectProduct product : products) {
                patchAffectProductService.deletePatchAffectProductById(product.getId());
            }
            
            // 4. 删除影响包信息
            PatchAffectPackage packageQuery = new PatchAffectPackage();
            packageQuery.setPatchId(id);
            List<PatchAffectPackage> packages = patchAffectPackageService.selectPatchAffectPackageList(packageQuery);
            for (PatchAffectPackage pkg : packages) {
                patchAffectPackageService.deletePatchAffectPackageById(pkg.getId());
            }
            
            // 5. 删除被替代补丁信息
            PatchSuperseded supersededQuery = new PatchSuperseded();
            supersededQuery.setPatchId(id);
            List<PatchSuperseded> supersededs = patchSupersededService.selectPatchSupersededList(supersededQuery);
            for (PatchSuperseded superseded : supersededs) {
                patchSupersededService.deletePatchSupersededById(superseded.getId());
            }
            
            // 6. 删除替代补丁信息
            PatchSuperseding supersedingQuery = new PatchSuperseding();
            supersedingQuery.setPatchId(id);
            List<PatchSuperseding> supersedings = patchSupersedingService.selectPatchSupersedingList(supersedingQuery);
            for (PatchSuperseding superseding : supersedings) {
                patchSupersedingService.deletePatchSupersedingById(superseding.getId());
            }
            
            // 7. 删除参考链接信息
            PatchReference referenceQuery = new PatchReference();
            referenceQuery.setPatchId(id);
            List<PatchReference> references = patchReferenceService.selectPatchReferenceList(referenceQuery);
            for (PatchReference reference : references) {
                patchReferenceService.deletePatchReferenceById(reference.getId());
            }
        }
        
        // 最后删除补丁主数据
        return patchMapper.deletePatchByIds(ids);
    }

    /**
     * 删除补丁基本信息信息
     *
     * @param id 补丁基本信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePatchById(String id) {
        // 删除单个补丁时也需要级联删除
        String[] ids = {id};
        return deletePatchByIds(ids);
    }
}
