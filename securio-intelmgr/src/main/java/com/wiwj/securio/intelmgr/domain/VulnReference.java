package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 漏洞参考链接对象 vuln_reference
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public class VulnReference extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    /** 漏洞ID */
    @Excel(name = "漏洞ID")
    private Integer vulnId;

    /** 参考名称 */
    @Excel(name = "参考名称")
    private String name;

    /** 参考URL */
    @Excel(name = "参考URL")
    private String url;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    public void setId(Integer id){
        this.id = id;
    }

    public Integer getId(){
        return id;
    }
    public void setVulnId(Integer vulnId){
        this.vulnId = vulnId;
    }

    public Integer getVulnId(){
        return vulnId;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }
    public void setUrl(String url){
        this.url = url;
    }

    public String getUrl(){
        return url;
    }
    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("vulnId", getVulnId())
            .append("name", getName())
            .append("url", getUrl())
            .append("createdAt", getCreatedAt())
            .toString();
    }
}
