package com.wiwj.securio.intelmgr.mapper;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.PatchAffectProduct;

/**
 * 补丁影响产品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface PatchAffectProductMapper {
    /**
     * 查询补丁影响产品
     *
     * @param id 补丁影响产品主键
     * @return 补丁影响产品
     */
    public PatchAffectProduct selectPatchAffectProductById(Integer id);

    /**
     * 查询补丁影响产品列表
     *
     * @param patchAffectProduct 补丁影响产品
     * @return 补丁影响产品集合
     */
    public List<PatchAffectProduct> selectPatchAffectProductList(PatchAffectProduct patchAffectProduct);

    /**
     * 新增补丁影响产品
     *
     * @param patchAffectProduct 补丁影响产品
     * @return 结果
     */
    public int insertPatchAffectProduct(PatchAffectProduct patchAffectProduct);

    /**
     * 修改补丁影响产品
     *
     * @param patchAffectProduct 补丁影响产品
     * @return 结果
     */
    public int updatePatchAffectProduct(PatchAffectProduct patchAffectProduct);

    /**
     * 删除补丁影响产品
     *
     * @param id 补丁影响产品主键
     * @return 结果
     */
    public int deletePatchAffectProductById(Integer id);

    /**
     * 批量删除补丁影响产品
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatchAffectProductByIds(Integer[] ids);
}
