package com.wiwj.securio.intelmgr.mapper;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.CheckItem;

/**
 * 核查项配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface CheckItemMapper {
    /**
     * 查询核查项配置
     *
     * @param id 核查项配置主键
     * @return 核查项配置
     */
    public CheckItem selectCheckItemById(String id);

    /**
     * 查询核查项配置列表
     *
     * @param checkItem 核查项配置
     * @return 核查项配置集合
     */
    public List<CheckItem> selectCheckItemList(CheckItem checkItem);

    /**
     * 新增核查项配置
     *
     * @param checkItem 核查项配置
     * @return 结果
     */
    public int insertCheckItem(CheckItem checkItem);

    /**
     * 修改核查项配置
     *
     * @param checkItem 核查项配置
     * @return 结果
     */
    public int updateCheckItem(CheckItem checkItem);

    /**
     * 删除核查项配置
     *
     * @param id 核查项配置主键
     * @return 结果
     */
    public int deleteCheckItemById(String id);

    /**
     * 批量删除核查项配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckItemByIds(String[] ids);
}
