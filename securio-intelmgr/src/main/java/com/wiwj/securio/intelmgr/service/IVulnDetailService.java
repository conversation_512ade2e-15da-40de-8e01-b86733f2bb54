package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.VulnDetail;

/**
 * 漏洞详情Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IVulnDetailService {
    /**
     * 查询漏洞详情
     *
     * @param id 漏洞详情主键
     * @return 漏洞详情
     */
    public VulnDetail selectVulnDetailById(Integer id);

    /**
     * 查询漏洞详情列表
     *
     * @param vulnDetail 漏洞详情
     * @return 漏洞详情集合
     */
    public List<VulnDetail> selectVulnDetailList(VulnDetail vulnDetail);

    /**
     * 新增漏洞详情
     *
     * @param vulnDetail 漏洞详情
     * @return 结果
     */
    public int insertVulnDetail(VulnDetail vulnDetail);

    /**
     * 修改漏洞详情
     *
     * @param vulnDetail 漏洞详情
     * @return 结果
     */
    public int updateVulnDetail(VulnDetail vulnDetail);

    /**
     * 批量删除漏洞详情
     *
     * @param ids 需要删除的漏洞详情主键集合
     * @return 结果
     */
    public int deleteVulnDetailByIds(Integer[] ids);

    /**
     * 删除漏洞详情信息
     *
     * @param id 漏洞详情主键
     * @return 结果
     */
    public int deleteVulnDetailById(Integer id);
}
