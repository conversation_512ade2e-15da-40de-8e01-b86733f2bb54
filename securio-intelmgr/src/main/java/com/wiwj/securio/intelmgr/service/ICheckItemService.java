package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.CheckItem;

/**
 * 核查项配置Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface ICheckItemService {
    /**
     * 查询核查项配置
     *
     * @param id 核查项配置主键
     * @return 核查项配置
     */
    public CheckItem selectCheckItemById(String id);

    /**
     * 查询核查项配置列表
     *
     * @param checkItem 核查项配置
     * @return 核查项配置集合
     */
    public List<CheckItem> selectCheckItemList(CheckItem checkItem);

    /**
     * 新增核查项配置
     *
     * @param checkItem 核查项配置
     * @return 结果
     */
    public int insertCheckItem(CheckItem checkItem);

    /**
     * 修改核查项配置
     *
     * @param checkItem 核查项配置
     * @return 结果
     */
    public int updateCheckItem(CheckItem checkItem);

    /**
     * 批量删除核查项配置
     *
     * @param ids 需要删除的核查项配置主键集合
     * @return 结果
     */
    public int deleteCheckItemByIds(String[] ids);

    /**
     * 删除核查项配置信息
     *
     * @param id 核查项配置主键
     * @return 结果
     */
    public int deleteCheckItemById(String id);
}
