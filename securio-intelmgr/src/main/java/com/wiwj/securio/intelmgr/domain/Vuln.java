package com.wiwj.securio.intelmgr.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 漏洞基本信息对象 vuln
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public class Vuln extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 漏洞ID */
    private Integer id;

    /** 漏洞级别：1低危，2中危，3高危，4严重 */
    @Excel(name = "漏洞级别：1低危，2中危，3高危，4严重")
    private Integer level;

    /** 漏洞名称 */
    @Excel(name = "漏洞名称")
    private String name;

    /** CVSS评分 */
    @Excel(name = "CVSS评分")
    private BigDecimal cvssScore;

    /** CVE编号 */
    @Excel(name = "CVE编号")
    private String cve;

    /** CNNVD编号 */
    @Excel(name = "CNNVD编号")
    private String cnnvd;

    /** CNVD编号 */
    @Excel(name = "CNVD编号")
    private String cnvd;

    /** BID编号 */
    @Excel(name = "BID编号")
    private String bid;

    /** CWE编号 */
    @Excel(name = "CWE编号")
    private String cwe;

    /** 漏洞类型 */
    @Excel(name = "漏洞类型")
    private Integer type;

    /** CVSS向量 */
    @Excel(name = "CVSS向量")
    private String cvssVector;

    /** CVSS访问途径 */
    @Excel(name = "CVSS访问途径")
    private String cvssAccessVector;

    /** 是否有POC */
    @Excel(name = "是否有POC")
    private Integer hasPoc;

    /** 发布日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishDate;

    /** 修改日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyDate;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    public void setId(Integer id){
        this.id = id;
    }

    public Integer getId(){
        return id;
    }
    public void setLevel(Integer level){
        this.level = level;
    }

    public Integer getLevel(){
        return level;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }
    public void setCvssScore(BigDecimal cvssScore){
        this.cvssScore = cvssScore;
    }

    public BigDecimal getCvssScore(){
        return cvssScore;
    }
    public void setCve(String cve){
        this.cve = cve;
    }

    public String getCve(){
        return cve;
    }
    public void setCnnvd(String cnnvd){
        this.cnnvd = cnnvd;
    }

    public String getCnnvd(){
        return cnnvd;
    }
    public void setCnvd(String cnvd){
        this.cnvd = cnvd;
    }

    public String getCnvd(){
        return cnvd;
    }
    public void setBid(String bid){
        this.bid = bid;
    }

    public String getBid(){
        return bid;
    }
    public void setCwe(String cwe){
        this.cwe = cwe;
    }

    public String getCwe(){
        return cwe;
    }
    public void setType(Integer type){
        this.type = type;
    }

    public Integer getType(){
        return type;
    }
    public void setCvssVector(String cvssVector){
        this.cvssVector = cvssVector;
    }

    public String getCvssVector(){
        return cvssVector;
    }
    public void setCvssAccessVector(String cvssAccessVector){
        this.cvssAccessVector = cvssAccessVector;
    }

    public String getCvssAccessVector(){
        return cvssAccessVector;
    }
    public void setHasPoc(Integer hasPoc){
        this.hasPoc = hasPoc;
    }

    public Integer getHasPoc(){
        return hasPoc;
    }
    public void setPublishDate(Date publishDate){
        this.publishDate = publishDate;
    }

    public Date getPublishDate(){
        return publishDate;
    }
    public void setModifyDate(Date modifyDate){
        this.modifyDate = modifyDate;
    }

    public Date getModifyDate(){
        return modifyDate;
    }
    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt){
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("level", getLevel())
            .append("name", getName())
            .append("cvssScore", getCvssScore())
            .append("cve", getCve())
            .append("cnnvd", getCnnvd())
            .append("cnvd", getCnvd())
            .append("bid", getBid())
            .append("cwe", getCwe())
            .append("type", getType())
            .append("cvssVector", getCvssVector())
            .append("cvssAccessVector", getCvssAccessVector())
            .append("hasPoc", getHasPoc())
            .append("publishDate", getPublishDate())
            .append("modifyDate", getModifyDate())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
