package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.PatchTrait;
import com.wiwj.securio.intelmgr.service.IPatchTraitService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 补丁特性Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patchTrait")
public class PatchTraitController extends BaseController {
    @Autowired
    private IPatchTraitService patchTraitService;

    /**
     * 查询补丁特性列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping("/list")
    public TableDataInfo list(PatchTrait patchTrait) {
        startPage();
        List<PatchTrait> list = patchTraitService.selectPatchTraitList(patchTrait);
        return getDataTable(list);
    }

    /**
     * 导出补丁特性列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁特性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatchTrait patchTrait) {
        List<PatchTrait> list = patchTraitService.selectPatchTraitList(patchTrait);
        ExcelUtil<PatchTrait> util = new ExcelUtil<PatchTrait>(PatchTrait.class);
        util.exportExcel(response, list, "补丁特性数据");
    }

    /**
     * 获取补丁特性详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(patchTraitService.selectPatchTraitById(id));
    }

    /**
     * 新增补丁特性
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁特性", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatchTrait patchTrait) {
        return toAjax(patchTraitService.insertPatchTrait(patchTrait));
    }

    /**
     * 修改补丁特性
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁特性", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatchTrait patchTrait) {
        return toAjax(patchTraitService.updatePatchTrait(patchTrait));
    }

    /**
     * 删除补丁特性
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁特性", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patchTraitService.deletePatchTraitByIds(ids));
    }
}
