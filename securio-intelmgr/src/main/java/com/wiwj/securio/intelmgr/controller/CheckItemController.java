package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.CheckItem;
import com.wiwj.securio.intelmgr.service.ICheckItemService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 核查项配置Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/intelmgr/checkItem")
public class CheckItemController extends BaseController {
    @Autowired
    private ICheckItemService checkItemService;

    /**
     * 查询核查项配置列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:checkItem')")
    @GetMapping("/list")
    public TableDataInfo list(CheckItem checkItem) {
        startPage();
        List<CheckItem> list = checkItemService.selectCheckItemList(checkItem);
        return getDataTable(list);
    }

    /**
     * 导出核查项配置列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:checkItem')")
    @Log(title = "核查项配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckItem checkItem) {
        List<CheckItem> list = checkItemService.selectCheckItemList(checkItem);
        ExcelUtil<CheckItem> util = new ExcelUtil<CheckItem>(CheckItem.class);
        util.exportExcel(response, list, "核查项配置数据");
    }

    /**
     * 获取核查项配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:checkItem')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(checkItemService.selectCheckItemById(id));
    }

    /**
     * 新增核查项配置
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:checkItem')")
    @Log(title = "核查项配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckItem checkItem) {
        return toAjax(checkItemService.insertCheckItem(checkItem));
    }

    /**
     * 修改核查项配置
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:checkItem')")
    @Log(title = "核查项配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckItem checkItem) {
        return toAjax(checkItemService.updateCheckItem(checkItem));
    }

    /**
     * 删除核查项配置
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:checkItem')")
    @Log(title = "核查项配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(checkItemService.deleteCheckItemByIds(ids));
    }
}
