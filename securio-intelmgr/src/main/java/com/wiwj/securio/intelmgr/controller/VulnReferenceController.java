package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.VulnReference;
import com.wiwj.securio.intelmgr.service.IVulnReferenceService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 漏洞参考链接Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/intelmgr/vulnReference")
public class VulnReferenceController extends BaseController {
    @Autowired
    private IVulnReferenceService vulnReferenceService;

    /**
     * 查询漏洞参考链接列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping("/list")
    public TableDataInfo list(VulnReference vulnReference) {
        startPage();
        List<VulnReference> list = vulnReferenceService.selectVulnReferenceList(vulnReference);
        return getDataTable(list);
    }

    /**
     * 导出漏洞参考链接列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞参考链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VulnReference vulnReference) {
        List<VulnReference> list = vulnReferenceService.selectVulnReferenceList(vulnReference);
        ExcelUtil<VulnReference> util = new ExcelUtil<VulnReference>(VulnReference.class);
        util.exportExcel(response, list, "漏洞参考链接数据");
    }

    /**
     * 获取漏洞参考链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(vulnReferenceService.selectVulnReferenceById(id));
    }

    /**
     * 新增漏洞参考链接
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞参考链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VulnReference vulnReference) {
        return toAjax(vulnReferenceService.insertVulnReference(vulnReference));
    }

    /**
     * 修改漏洞参考链接
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞参考链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VulnReference vulnReference) {
        return toAjax(vulnReferenceService.updateVulnReference(vulnReference));
    }

    /**
     * 删除漏洞参考链接
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞参考链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(vulnReferenceService.deleteVulnReferenceByIds(ids));
    }
    
    /**
     * 根据漏洞ID查询相关参考链接
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping(value = "/vuln/{vulnId}")
    public AjaxResult getVulnReferenceByVulnId(@PathVariable("vulnId") Integer vulnId) {
        return success(vulnReferenceService.selectVulnReferenceByVulnId(vulnId));
    }
}
