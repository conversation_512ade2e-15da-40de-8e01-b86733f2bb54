package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.PatchAffectProduct;
import com.wiwj.securio.intelmgr.service.IPatchAffectProductService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 补丁影响产品Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patchAffectProduct")
public class PatchAffectProductController extends BaseController {
    @Autowired
    private IPatchAffectProductService patchAffectProductService;

    /**
     * 查询补丁影响产品列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping("/list")
    public TableDataInfo list(PatchAffectProduct patchAffectProduct) {
        startPage();
        List<PatchAffectProduct> list = patchAffectProductService.selectPatchAffectProductList(patchAffectProduct);
        return getDataTable(list);
    }

    /**
     * 导出补丁影响产品列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁影响产品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatchAffectProduct patchAffectProduct) {
        List<PatchAffectProduct> list = patchAffectProductService.selectPatchAffectProductList(patchAffectProduct);
        ExcelUtil<PatchAffectProduct> util = new ExcelUtil<PatchAffectProduct>(PatchAffectProduct.class);
        util.exportExcel(response, list, "补丁影响产品数据");
    }

    /**
     * 获取补丁影响产品详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(patchAffectProductService.selectPatchAffectProductById(id));
    }

    /**
     * 新增补丁影响产品
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁影响产品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatchAffectProduct patchAffectProduct) {
        return toAjax(patchAffectProductService.insertPatchAffectProduct(patchAffectProduct));
    }

    /**
     * 修改补丁影响产品
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁影响产品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatchAffectProduct patchAffectProduct) {
        return toAjax(patchAffectProductService.updatePatchAffectProduct(patchAffectProduct));
    }

    /**
     * 删除补丁影响产品
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁影响产品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patchAffectProductService.deletePatchAffectProductByIds(ids));
    }
}
