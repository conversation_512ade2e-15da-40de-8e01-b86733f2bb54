package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.Patch;
import com.wiwj.securio.intelmgr.domain.PatchTrait;
import com.wiwj.securio.intelmgr.domain.PatchAffectProduct;
import com.wiwj.securio.intelmgr.domain.PatchAffectPackage;
import com.wiwj.securio.intelmgr.domain.PatchCve;
import com.wiwj.securio.intelmgr.domain.PatchReference;
import com.wiwj.securio.intelmgr.domain.PatchSuperseded;
import com.wiwj.securio.intelmgr.domain.PatchSuperseding;
import com.wiwj.securio.intelmgr.service.IPatchService;
import com.wiwj.securio.intelmgr.service.IPatchTraitService;
import com.wiwj.securio.intelmgr.service.IPatchAffectProductService;
import com.wiwj.securio.intelmgr.service.IPatchAffectPackageService;
import com.wiwj.securio.intelmgr.service.IPatchCveService;
import com.wiwj.securio.intelmgr.service.IPatchReferenceService;
import com.wiwj.securio.intelmgr.service.IPatchSupersededService;
import com.wiwj.securio.intelmgr.service.IPatchSupersedingService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;
import java.util.stream.Collectors;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.text.ParseException;

/**
 * 补丁基本信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patch")
public class PatchController extends BaseController {
    @Autowired
    private IPatchService patchService;

    @Autowired
    private IPatchTraitService patchTraitService;
    
    @Autowired
    private IPatchAffectProductService patchAffectProductService;
    
    @Autowired
    private IPatchAffectPackageService patchAffectPackageService;
    
    @Autowired
    private IPatchCveService patchCveService;
    
    @Autowired
    private IPatchReferenceService patchReferenceService;
    
    @Autowired
    private IPatchSupersededService patchSupersededService;
    
    @Autowired
    private IPatchSupersedingService patchSupersedingService;

    /**
     * 查询补丁基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping("/list")
    public TableDataInfo list(Patch patch) {
        startPage();
        List<Patch> list = patchService.selectPatchList(patch);
        
        // 获取补丁特性信息
        if (list != null && !list.isEmpty()) {
            // 获取所有补丁ID
            List<String> patchIds = list.stream().map(Patch::getId).collect(Collectors.toList());
            
            // 获取补丁特性信息
            List<PatchTrait> allTraits = patchTraitService.selectPatchTraitByPatchIds(patchIds);
            
            // 将补丁特性信息按补丁ID分组
            Map<String, List<PatchTrait>> traitMap = allTraits.stream()
                    .collect(Collectors.groupingBy(PatchTrait::getPatchId));
            
            // 设置补丁特性
            for (Patch p : list) {
                List<PatchTrait> traits = traitMap.get(p.getId());
                if (traits != null && !traits.isEmpty()) {
                    // 将补丁特性信息添加到feature字段，多个特性用逗号分隔
                    String feature = traits.stream()
                            .map(PatchTrait::getTrait)
                            .collect(Collectors.joining(","));
                    p.setFeature(feature);
                }
            }
        }
        
        return getDataTable(list);
    }

    /**
     * 导出补丁基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Patch patch) {
        List<Patch> list = patchService.selectPatchList(patch);
        ExcelUtil<Patch> util = new ExcelUtil<Patch>(Patch.class);
        util.exportExcel(response, list, "补丁基本信息数据");
    }

    /**
     * 获取补丁基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        Patch patch = patchService.selectPatchById(id);
        if (patch != null) {
            // 查询补丁特性信息
            PatchTrait traitQuery = new PatchTrait();
            traitQuery.setPatchId(id);
            List<PatchTrait> traits = patchTraitService.selectPatchTraitList(traitQuery);
            if (traits != null && !traits.isEmpty()) {
                // 查找是否有reboot特性
                for (PatchTrait trait : traits) {
                    if ("reboot".equals(trait.getTrait())) {
                        patch.setFeature("reboot");
                        break;
                    }
                }
            }
        }
        return success(patch);
    }

    /**
     * 新增补丁基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Patch patch) {
        return toAjax(patchService.insertPatch(patch));
    }

    /**
     * 修改补丁基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Patch patch) {
        return toAjax(patchService.updatePatch(patch));
    }

    /**
     * 删除补丁基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(patchService.deletePatchByIds(ids));
    }

    /**
     * 获取补丁详细信息（包含关联数据）
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping(value = "/detail/{id}")
    public AjaxResult getDetail(@PathVariable("id") String id) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取补丁基本信息
        Patch patch = patchService.selectPatchById(id);
        if (patch == null) {
            return AjaxResult.error("补丁信息不存在");
        }
        result.put("basic", patch);
        
        // 获取补丁特性信息
        PatchTrait traitQuery = new PatchTrait();
        traitQuery.setPatchId(id);
        List<PatchTrait> traits = patchTraitService.selectPatchTraitList(traitQuery);
        result.put("traits", traits);
        
        // 获取补丁影响产品信息
        PatchAffectProduct productQuery = new PatchAffectProduct();
        productQuery.setPatchId(id);
        List<PatchAffectProduct> products = patchAffectProductService.selectPatchAffectProductList(productQuery);
        result.put("affectProducts", products);
        
        // 获取补丁影响包信息
        PatchAffectPackage packageQuery = new PatchAffectPackage();
        packageQuery.setPatchId(id);
        List<PatchAffectPackage> packages = patchAffectPackageService.selectPatchAffectPackageList(packageQuery);
        result.put("affectPackages", packages);
        
        // 获取补丁CVE信息
        PatchCve cveQuery = new PatchCve();
        cveQuery.setPatchId(id);
        List<PatchCve> cves = patchCveService.selectPatchCveList(cveQuery);
        result.put("cves", cves);
        
        // 获取补丁参考链接信息
        PatchReference referenceQuery = new PatchReference();
        referenceQuery.setPatchId(id);
        List<PatchReference> references = patchReferenceService.selectPatchReferenceList(referenceQuery);
        result.put("references", references);
        
        // 获取被替代的补丁信息
        PatchSuperseded supersededQuery = new PatchSuperseded();
        supersededQuery.setPatchId(id);
        List<PatchSuperseded> superseded = patchSupersededService.selectPatchSupersededList(supersededQuery);
        result.put("superseded", superseded);
        
        // 获取替代补丁信息
        PatchSuperseding supersedingQuery = new PatchSuperseding();
        supersedingQuery.setPatchId(id);
        List<PatchSuperseding> superseding = patchSupersedingService.selectPatchSupersedingList(supersedingQuery);
        result.put("superseding", superseding);
        
        return success(result);
    }
    
    /**
     * 新增补丁信息（聚合操作）
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁信息", businessType = BusinessType.INSERT)
    @PostMapping("/aggregate")
    public AjaxResult addAggregate(@RequestBody Map<String, Object> data) {
        try {
            // 1. 提取并新增补丁基本信息
            Patch patch = new Patch();
            Map<String, Object> patchData = (Map<String, Object>) data.get("patch");
            if (patchData != null) {
                if (patchData.get("level") != null) patch.setLevel(Integer.valueOf(patchData.get("level").toString()));
                patch.setType((String) patchData.get("type"));
                patch.setNumber((String) patchData.get("number"));
                patch.setName((String) patchData.get("name"));
                patch.setOs((String) patchData.get("os"));
                if (patchData.get("publishTime") != null) {
                    if (patchData.get("publishTime") instanceof Date) {
                        patch.setPublishTime((Date) patchData.get("publishTime"));
                    } else if (patchData.get("publishTime") instanceof String) {
                        // 处理日期字符串转换
                        String dateStr = (String) patchData.get("publishTime");
                        try {
                            // 先尝试使用完整格式解析
                            SimpleDateFormat fullFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            patch.setPublishTime(fullFormat.parse(dateStr));
                        } catch (ParseException e) {
                            try {
                                // 如果失败，尝试使用日期格式解析
                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                patch.setPublishTime(dateFormat.parse(dateStr));
                            } catch (ParseException e2) {
                                // 记录错误但继续执行
                                e2.printStackTrace();
                            }
                        }
                    }
                } else {
                    patch.setPublishTime(null);
                }
                patch.setDescription((String) patchData.get("description"));
                patch.setSolution((String) patchData.get("solution"));
            }
            
            int result = patchService.insertPatch(patch);
            if (result <= 0) {
                return AjaxResult.error("新增补丁基本信息失败");
            }
            
            String patchId = patch.getId();
            
            // 2. 处理特性信息
            if (patchData != null && "reboot".equals(patchData.get("feature"))) {
                PatchTrait trait = new PatchTrait();
                trait.setPatchId(patchId);
                trait.setTrait("reboot");
                patchTraitService.insertPatchTrait(trait);
            }
            
            // 3. 处理CVE信息
            List<Map<String, Object>> cveList = (List<Map<String, Object>>) data.get("cves");
            if (cveList != null && !cveList.isEmpty()) {
                for (Map<String, Object> cveData : cveList) {
                    PatchCve cve = new PatchCve();
                    cve.setPatchId(patchId);
                    cve.setCve((String) cveData.get("cve"));
                    patchCveService.insertPatchCve(cve);
                }
            }
            
            // 4. 处理影响产品信息
            List<Map<String, Object>> productList = (List<Map<String, Object>>) data.get("affectProducts");
            if (productList != null && !productList.isEmpty()) {
                for (Map<String, Object> productData : productList) {
                    PatchAffectProduct product = new PatchAffectProduct();
                    product.setPatchId(patchId);
                    product.setProduct((String) productData.get("product"));
                    patchAffectProductService.insertPatchAffectProduct(product);
                }
            }
            
            // 5. 处理影响包信息
            List<Map<String, Object>> packageList = (List<Map<String, Object>>) data.get("affectPackages");
            if (packageList != null && !packageList.isEmpty()) {
                for (Map<String, Object> packageData : packageList) {
                    PatchAffectPackage pkg = new PatchAffectPackage();
                    pkg.setPatchId(patchId);
                    pkg.setPackageName((String) packageData.get("packageName"));
                    patchAffectPackageService.insertPatchAffectPackage(pkg);
                }
            }
            
            // 6. 处理被替代补丁信息
            List<Map<String, Object>> supersededList = (List<Map<String, Object>>) data.get("superseded");
            if (supersededList != null && !supersededList.isEmpty()) {
                for (Map<String, Object> supersededData : supersededList) {
                    PatchSuperseded superseded = new PatchSuperseded();
                    superseded.setPatchId(patchId);
                    superseded.setSupersededId((String) supersededData.get("supersededId"));
                    superseded.setTitle((String) supersededData.get("title"));
                    patchSupersededService.insertPatchSuperseded(superseded);
                }
            }
            
            // 7. 处理替代补丁信息
            List<Map<String, Object>> supersedingList = (List<Map<String, Object>>) data.get("superseding");
            if (supersedingList != null && !supersedingList.isEmpty()) {
                for (Map<String, Object> supersedingData : supersedingList) {
                    PatchSuperseding superseding = new PatchSuperseding();
                    superseding.setPatchId(patchId);
                    superseding.setSupersedingId((String) supersedingData.get("supersedingId"));
                    superseding.setTitle((String) supersedingData.get("title"));
                    patchSupersedingService.insertPatchSuperseding(superseding);
                }
            }
            
            // 8. 处理参考链接信息
            List<Map<String, Object>> referenceList = (List<Map<String, Object>>) data.get("references");
            if (referenceList != null && !referenceList.isEmpty()) {
                for (Map<String, Object> referenceData : referenceList) {
                    PatchReference reference = new PatchReference();
                    reference.setPatchId(patchId);
                    reference.setSource((String) referenceData.get("source"));
                    reference.setTitle((String) referenceData.get("title"));
                    reference.setLink((String) referenceData.get("link"));
                    patchReferenceService.insertPatchReference(reference);
                }
            }
            
            return success(patch);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("新增补丁信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 修改补丁信息（聚合操作）
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁信息", businessType = BusinessType.UPDATE)
    @PostMapping("/update-aggregate")
    public AjaxResult updateAggregate(@RequestBody Map<String, Object> data) {
        try {
            // 1. 提取并更新补丁基本信息
            Map<String, Object> patchData = (Map<String, Object>) data.get("patch");
            if (patchData == null || patchData.get("id") == null) {
                return AjaxResult.error("补丁ID不能为空");
            }
            
            String patchId = (String) patchData.get("id");
            Patch patch = patchService.selectPatchById(patchId);
            if (patch == null) {
                return AjaxResult.error("补丁信息不存在");
            }
            
            if (patchData.get("level") != null) patch.setLevel(Integer.valueOf(patchData.get("level").toString()));
            patch.setType((String) patchData.get("type"));
            patch.setNumber((String) patchData.get("number"));
            patch.setName((String) patchData.get("name"));
            patch.setOs((String) patchData.get("os"));
            if (patchData.get("publishTime") != null) {
                if (patchData.get("publishTime") instanceof Date) {
                    patch.setPublishTime((Date) patchData.get("publishTime"));
                } else if (patchData.get("publishTime") instanceof String) {
                    // 处理日期字符串转换
                    String dateStr = (String) patchData.get("publishTime");
                    try {
                        // 先尝试使用完整格式解析
                        SimpleDateFormat fullFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        patch.setPublishTime(fullFormat.parse(dateStr));
                    } catch (ParseException e) {
                        try {
                            // 如果失败，尝试使用日期格式解析
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            patch.setPublishTime(dateFormat.parse(dateStr));
                        } catch (ParseException e2) {
                            // 记录错误但继续执行
                            e2.printStackTrace();
                        }
                    }
                }
            } else {
                patch.setPublishTime(null);
            }
            patch.setDescription((String) patchData.get("description"));
            patch.setSolution((String) patchData.get("solution"));
            
            patchService.updatePatch(patch);
            
            // 2. 处理特性信息 - 先删除现有特性，再添加新特性
            PatchTrait traitQuery = new PatchTrait();
            traitQuery.setPatchId(patchId);
            List<PatchTrait> existingTraits = patchTraitService.selectPatchTraitList(traitQuery);
            for (PatchTrait trait : existingTraits) {
                patchTraitService.deletePatchTraitById(trait.getId());
            }
            
            if (patchData != null && "reboot".equals(patchData.get("feature"))) {
                PatchTrait trait = new PatchTrait();
                trait.setPatchId(patchId);
                trait.setTrait("reboot");
                patchTraitService.insertPatchTrait(trait);
            }
            
            // 3. 处理CVE信息 - 先删除现有CVE，再添加新CVE
            PatchCve cveQuery = new PatchCve();
            cveQuery.setPatchId(patchId);
            List<PatchCve> existingCves = patchCveService.selectPatchCveList(cveQuery);
            for (PatchCve cve : existingCves) {
                patchCveService.deletePatchCveById(cve.getId());
            }
            
            List<Map<String, Object>> cveList = (List<Map<String, Object>>) data.get("cves");
            if (cveList != null && !cveList.isEmpty()) {
                for (Map<String, Object> cveData : cveList) {
                    PatchCve cve = new PatchCve();
                    cve.setPatchId(patchId);
                    cve.setCve((String) cveData.get("cve"));
                    patchCveService.insertPatchCve(cve);
                }
            }
            
            // 4. 处理影响产品信息 - 先删除现有影响产品，再添加新影响产品
            PatchAffectProduct productQuery = new PatchAffectProduct();
            productQuery.setPatchId(patchId);
            List<PatchAffectProduct> existingProducts = patchAffectProductService.selectPatchAffectProductList(productQuery);
            for (PatchAffectProduct product : existingProducts) {
                patchAffectProductService.deletePatchAffectProductById(product.getId());
            }
            
            List<Map<String, Object>> productList = (List<Map<String, Object>>) data.get("affectProducts");
            if (productList != null && !productList.isEmpty()) {
                for (Map<String, Object> productData : productList) {
                    PatchAffectProduct product = new PatchAffectProduct();
                    product.setPatchId(patchId);
                    product.setProduct((String) productData.get("product"));
                    patchAffectProductService.insertPatchAffectProduct(product);
                }
            }
            
            // 5. 处理影响包信息 - 先删除现有影响包，再添加新影响包
            PatchAffectPackage packageQuery = new PatchAffectPackage();
            packageQuery.setPatchId(patchId);
            List<PatchAffectPackage> existingPackages = patchAffectPackageService.selectPatchAffectPackageList(packageQuery);
            for (PatchAffectPackage pkg : existingPackages) {
                patchAffectPackageService.deletePatchAffectPackageById(pkg.getId());
            }
            
            List<Map<String, Object>> packageList = (List<Map<String, Object>>) data.get("affectPackages");
            if (packageList != null && !packageList.isEmpty()) {
                for (Map<String, Object> packageData : packageList) {
                    PatchAffectPackage pkg = new PatchAffectPackage();
                    pkg.setPatchId(patchId);
                    pkg.setPackageName((String) packageData.get("packageName"));
                    patchAffectPackageService.insertPatchAffectPackage(pkg);
                }
            }
            
            // 6. 处理被替代补丁信息 - 先删除现有被替代补丁，再添加新被替代补丁
            PatchSuperseded supersededQuery = new PatchSuperseded();
            supersededQuery.setPatchId(patchId);
            List<PatchSuperseded> existingSuperseded = patchSupersededService.selectPatchSupersededList(supersededQuery);
            for (PatchSuperseded superseded : existingSuperseded) {
                patchSupersededService.deletePatchSupersededById(superseded.getId());
            }
            
            List<Map<String, Object>> supersededList = (List<Map<String, Object>>) data.get("superseded");
            if (supersededList != null && !supersededList.isEmpty()) {
                for (Map<String, Object> supersededData : supersededList) {
                    PatchSuperseded superseded = new PatchSuperseded();
                    superseded.setPatchId(patchId);
                    superseded.setSupersededId((String) supersededData.get("supersededId"));
                    superseded.setTitle((String) supersededData.get("title"));
                    patchSupersededService.insertPatchSuperseded(superseded);
                }
            }
            
            // 7. 处理替代补丁信息 - 先删除现有替代补丁，再添加新替代补丁
            PatchSuperseding supersedingQuery = new PatchSuperseding();
            supersedingQuery.setPatchId(patchId);
            List<PatchSuperseding> existingSuperseding = patchSupersedingService.selectPatchSupersedingList(supersedingQuery);
            for (PatchSuperseding superseding : existingSuperseding) {
                patchSupersedingService.deletePatchSupersedingById(superseding.getId());
            }
            
            List<Map<String, Object>> supersedingList = (List<Map<String, Object>>) data.get("superseding");
            if (supersedingList != null && !supersedingList.isEmpty()) {
                for (Map<String, Object> supersedingData : supersedingList) {
                    PatchSuperseding superseding = new PatchSuperseding();
                    superseding.setPatchId(patchId);
                    superseding.setSupersedingId((String) supersedingData.get("supersedingId"));
                    superseding.setTitle((String) supersedingData.get("title"));
                    patchSupersedingService.insertPatchSuperseding(superseding);
                }
            }
            
            // 8. 处理参考链接信息 - 先删除现有参考链接，再添加新参考链接
            PatchReference referenceQuery = new PatchReference();
            referenceQuery.setPatchId(patchId);
            List<PatchReference> existingReferences = patchReferenceService.selectPatchReferenceList(referenceQuery);
            for (PatchReference reference : existingReferences) {
                patchReferenceService.deletePatchReferenceById(reference.getId());
            }
            
            List<Map<String, Object>> referenceList = (List<Map<String, Object>>) data.get("references");
            if (referenceList != null && !referenceList.isEmpty()) {
                for (Map<String, Object> referenceData : referenceList) {
                    PatchReference reference = new PatchReference();
                    reference.setPatchId(patchId);
                    reference.setSource((String) referenceData.get("source"));
                    reference.setTitle((String) referenceData.get("title"));
                    reference.setLink((String) referenceData.get("link"));
                    patchReferenceService.insertPatchReference(reference);
                }
            }
            
            return success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("修改补丁信息失败：" + e.getMessage());
        }
    }
}
