package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.PatchSuperseding;
import com.wiwj.securio.intelmgr.service.IPatchSupersedingService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 替代补丁Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patchSuperseding")
public class PatchSupersedingController extends BaseController {
    @Autowired
    private IPatchSupersedingService patchSupersedingService;

    /**
     * 查询替代补丁列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping("/list")
    public TableDataInfo list(PatchSuperseding patchSuperseding) {
        startPage();
        List<PatchSuperseding> list = patchSupersedingService.selectPatchSupersedingList(patchSuperseding);
        return getDataTable(list);
    }

    /**
     * 导出替代补丁列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "替代补丁", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatchSuperseding patchSuperseding) {
        List<PatchSuperseding> list = patchSupersedingService.selectPatchSupersedingList(patchSuperseding);
        ExcelUtil<PatchSuperseding> util = new ExcelUtil<PatchSuperseding>(PatchSuperseding.class);
        util.exportExcel(response, list, "替代补丁数据");
    }

    /**
     * 获取替代补丁详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(patchSupersedingService.selectPatchSupersedingById(id));
    }

    /**
     * 新增替代补丁
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "替代补丁", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatchSuperseding patchSuperseding) {
        return toAjax(patchSupersedingService.insertPatchSuperseding(patchSuperseding));
    }

    /**
     * 修改替代补丁
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "替代补丁", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatchSuperseding patchSuperseding) {
        return toAjax(patchSupersedingService.updatePatchSuperseding(patchSuperseding));
    }

    /**
     * 删除替代补丁
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "替代补丁", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patchSupersedingService.deletePatchSupersedingByIds(ids));
    }
}
