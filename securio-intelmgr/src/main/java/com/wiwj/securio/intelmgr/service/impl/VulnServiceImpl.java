package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.VulnMapper;
import com.wiwj.securio.intelmgr.domain.Vuln;
import com.wiwj.securio.intelmgr.service.IVulnService;

/**
 * 漏洞基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class VulnServiceImpl implements IVulnService {
    @Autowired
    private VulnMapper vulnMapper;

    /**
     * 查询漏洞基本信息
     *
     * @param id 漏洞基本信息主键
     * @return 漏洞基本信息
     */
    @Override
    public Vuln selectVulnById(Integer id) {
        return vulnMapper.selectVulnById(id);
    }

    /**
     * 查询漏洞基本信息列表
     *
     * @param vuln 漏洞基本信息
     * @return 漏洞基本信息
     */
    @Override
    public List<Vuln> selectVulnList(Vuln vuln) {
        return vulnMapper.selectVulnList(vuln);
    }

    /**
     * 新增漏洞基本信息
     *
     * @param vuln 漏洞基本信息
     * @return 结果
     */
    @Override
    public int insertVuln(Vuln vuln) {
        return vulnMapper.insertVuln(vuln);
    }

    /**
     * 修改漏洞基本信息
     *
     * @param vuln 漏洞基本信息
     * @return 结果
     */
    @Override
    public int updateVuln(Vuln vuln) {
        return vulnMapper.updateVuln(vuln);
    }

    /**
     * 批量删除漏洞基本信息
     *
     * @param ids 需要删除的漏洞基本信息主键
     * @return 结果
     */
    @Override
    public int deleteVulnByIds(Integer[] ids) {
        return vulnMapper.deleteVulnByIds(ids);
    }

    /**
     * 删除漏洞基本信息信息
     *
     * @param id 漏洞基本信息主键
     * @return 结果
     */
    @Override
    public int deleteVulnById(Integer id) {
        return vulnMapper.deleteVulnById(id);
    }
}
