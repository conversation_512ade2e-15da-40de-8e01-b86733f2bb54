package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.PatchSuperseding;

/**
 * 替代补丁Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IPatchSupersedingService {
    /**
     * 查询替代补丁
     *
     * @param id 替代补丁主键
     * @return 替代补丁
     */
    public PatchSuperseding selectPatchSupersedingById(Integer id);

    /**
     * 查询替代补丁列表
     *
     * @param patchSuperseding 替代补丁
     * @return 替代补丁集合
     */
    public List<PatchSuperseding> selectPatchSupersedingList(PatchSuperseding patchSuperseding);

    /**
     * 新增替代补丁
     *
     * @param patchSuperseding 替代补丁
     * @return 结果
     */
    public int insertPatchSuperseding(PatchSuperseding patchSuperseding);

    /**
     * 修改替代补丁
     *
     * @param patchSuperseding 替代补丁
     * @return 结果
     */
    public int updatePatchSuperseding(PatchSuperseding patchSuperseding);

    /**
     * 批量删除替代补丁
     *
     * @param ids 需要删除的替代补丁主键集合
     * @return 结果
     */
    public int deletePatchSupersedingByIds(Integer[] ids);

    /**
     * 删除替代补丁信息
     *
     * @param id 替代补丁主键
     * @return 结果
     */
    public int deletePatchSupersedingById(Integer id);
}
