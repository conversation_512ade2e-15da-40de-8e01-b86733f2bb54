package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.Vuln;

/**
 * 漏洞基本信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IVulnService {
    /**
     * 查询漏洞基本信息
     *
     * @param id 漏洞基本信息主键
     * @return 漏洞基本信息
     */
    public Vuln selectVulnById(Integer id);

    /**
     * 查询漏洞基本信息列表
     *
     * @param vuln 漏洞基本信息
     * @return 漏洞基本信息集合
     */
    public List<Vuln> selectVulnList(Vuln vuln);

    /**
     * 新增漏洞基本信息
     *
     * @param vuln 漏洞基本信息
     * @return 结果
     */
    public int insertVuln(Vuln vuln);

    /**
     * 修改漏洞基本信息
     *
     * @param vuln 漏洞基本信息
     * @return 结果
     */
    public int updateVuln(Vuln vuln);

    /**
     * 批量删除漏洞基本信息
     *
     * @param ids 需要删除的漏洞基本信息主键集合
     * @return 结果
     */
    public int deleteVulnByIds(Integer[] ids);

    /**
     * 删除漏洞基本信息信息
     *
     * @param id 漏洞基本信息主键
     * @return 结果
     */
    public int deleteVulnById(Integer id);
}
