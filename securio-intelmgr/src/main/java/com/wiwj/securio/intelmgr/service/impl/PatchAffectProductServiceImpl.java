package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.PatchAffectProductMapper;
import com.wiwj.securio.intelmgr.domain.PatchAffectProduct;
import com.wiwj.securio.intelmgr.service.IPatchAffectProductService;

/**
 * 补丁影响产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class PatchAffectProductServiceImpl implements IPatchAffectProductService {
    @Autowired
    private PatchAffectProductMapper patchAffectProductMapper;

    /**
     * 查询补丁影响产品
     *
     * @param id 补丁影响产品主键
     * @return 补丁影响产品
     */
    @Override
    public PatchAffectProduct selectPatchAffectProductById(Integer id) {
        return patchAffectProductMapper.selectPatchAffectProductById(id);
    }

    /**
     * 查询补丁影响产品列表
     *
     * @param patchAffectProduct 补丁影响产品
     * @return 补丁影响产品
     */
    @Override
    public List<PatchAffectProduct> selectPatchAffectProductList(PatchAffectProduct patchAffectProduct) {
        return patchAffectProductMapper.selectPatchAffectProductList(patchAffectProduct);
    }

    /**
     * 新增补丁影响产品
     *
     * @param patchAffectProduct 补丁影响产品
     * @return 结果
     */
    @Override
    public int insertPatchAffectProduct(PatchAffectProduct patchAffectProduct) {
        return patchAffectProductMapper.insertPatchAffectProduct(patchAffectProduct);
    }

    /**
     * 修改补丁影响产品
     *
     * @param patchAffectProduct 补丁影响产品
     * @return 结果
     */
    @Override
    public int updatePatchAffectProduct(PatchAffectProduct patchAffectProduct) {
        return patchAffectProductMapper.updatePatchAffectProduct(patchAffectProduct);
    }

    /**
     * 批量删除补丁影响产品
     *
     * @param ids 需要删除的补丁影响产品主键
     * @return 结果
     */
    @Override
    public int deletePatchAffectProductByIds(Integer[] ids) {
        return patchAffectProductMapper.deletePatchAffectProductByIds(ids);
    }

    /**
     * 删除补丁影响产品信息
     *
     * @param id 补丁影响产品主键
     * @return 结果
     */
    @Override
    public int deletePatchAffectProductById(Integer id) {
        return patchAffectProductMapper.deletePatchAffectProductById(id);
    }
}
