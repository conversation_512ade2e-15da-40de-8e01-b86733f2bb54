package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.VulnExploitInfo;

/**
 * 漏洞利用情报Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IVulnExploitInfoService {
    /**
     * 查询漏洞利用情报
     *
     * @param id 漏洞利用情报主键
     * @return 漏洞利用情报
     */
    public VulnExploitInfo selectVulnExploitInfoById(Integer id);

    /**
     * 查询漏洞利用情报列表
     *
     * @param vulnExploitInfo 漏洞利用情报
     * @return 漏洞利用情报集合
     */
    public List<VulnExploitInfo> selectVulnExploitInfoList(VulnExploitInfo vulnExploitInfo);

    /**
     * 新增漏洞利用情报
     *
     * @param vulnExploitInfo 漏洞利用情报
     * @return 结果
     */
    public int insertVulnExploitInfo(VulnExploitInfo vulnExploitInfo);

    /**
     * 修改漏洞利用情报
     *
     * @param vulnExploitInfo 漏洞利用情报
     * @return 结果
     */
    public int updateVulnExploitInfo(VulnExploitInfo vulnExploitInfo);

    /**
     * 批量删除漏洞利用情报
     *
     * @param ids 需要删除的漏洞利用情报主键集合
     * @return 结果
     */
    public int deleteVulnExploitInfoByIds(Integer[] ids);

    /**
     * 删除漏洞利用情报信息
     *
     * @param id 漏洞利用情报主键
     * @return 结果
     */
    public int deleteVulnExploitInfoById(Integer id);

    /**
     * 根据漏洞ID查询相关利用情报
     *
     * @param vulnId 漏洞ID
     * @return 漏洞利用情报集合
     */
    public List<VulnExploitInfo> selectVulnExploitInfoByVulnId(Integer vulnId);
}
