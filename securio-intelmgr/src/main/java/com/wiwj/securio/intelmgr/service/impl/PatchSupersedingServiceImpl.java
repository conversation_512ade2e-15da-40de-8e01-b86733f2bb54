package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.PatchSupersedingMapper;
import com.wiwj.securio.intelmgr.domain.PatchSuperseding;
import com.wiwj.securio.intelmgr.service.IPatchSupersedingService;

/**
 * 替代补丁Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class PatchSupersedingServiceImpl implements IPatchSupersedingService {
    @Autowired
    private PatchSupersedingMapper patchSupersedingMapper;

    /**
     * 查询替代补丁
     *
     * @param id 替代补丁主键
     * @return 替代补丁
     */
    @Override
    public PatchSuperseding selectPatchSupersedingById(Integer id) {
        return patchSupersedingMapper.selectPatchSupersedingById(id);
    }

    /**
     * 查询替代补丁列表
     *
     * @param patchSuperseding 替代补丁
     * @return 替代补丁
     */
    @Override
    public List<PatchSuperseding> selectPatchSupersedingList(PatchSuperseding patchSuperseding) {
        return patchSupersedingMapper.selectPatchSupersedingList(patchSuperseding);
    }

    /**
     * 新增替代补丁
     *
     * @param patchSuperseding 替代补丁
     * @return 结果
     */
    @Override
    public int insertPatchSuperseding(PatchSuperseding patchSuperseding) {
        return patchSupersedingMapper.insertPatchSuperseding(patchSuperseding);
    }

    /**
     * 修改替代补丁
     *
     * @param patchSuperseding 替代补丁
     * @return 结果
     */
    @Override
    public int updatePatchSuperseding(PatchSuperseding patchSuperseding) {
        return patchSupersedingMapper.updatePatchSuperseding(patchSuperseding);
    }

    /**
     * 批量删除替代补丁
     *
     * @param ids 需要删除的替代补丁主键
     * @return 结果
     */
    @Override
    public int deletePatchSupersedingByIds(Integer[] ids) {
        return patchSupersedingMapper.deletePatchSupersedingByIds(ids);
    }

    /**
     * 删除替代补丁信息
     *
     * @param id 替代补丁主键
     * @return 结果
     */
    @Override
    public int deletePatchSupersedingById(Integer id) {
        return patchSupersedingMapper.deletePatchSupersedingById(id);
    }
}
