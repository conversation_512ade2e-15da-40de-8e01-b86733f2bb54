package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.PatchAffectPackageMapper;
import com.wiwj.securio.intelmgr.domain.PatchAffectPackage;
import com.wiwj.securio.intelmgr.service.IPatchAffectPackageService;

/**
 * 补丁影响包Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class PatchAffectPackageServiceImpl implements IPatchAffectPackageService {
    @Autowired
    private PatchAffectPackageMapper patchAffectPackageMapper;

    /**
     * 查询补丁影响包
     *
     * @param id 补丁影响包主键
     * @return 补丁影响包
     */
    @Override
    public PatchAffectPackage selectPatchAffectPackageById(Integer id) {
        return patchAffectPackageMapper.selectPatchAffectPackageById(id);
    }

    /**
     * 查询补丁影响包列表
     *
     * @param patchAffectPackage 补丁影响包
     * @return 补丁影响包
     */
    @Override
    public List<PatchAffectPackage> selectPatchAffectPackageList(PatchAffectPackage patchAffectPackage) {
        return patchAffectPackageMapper.selectPatchAffectPackageList(patchAffectPackage);
    }

    /**
     * 新增补丁影响包
     *
     * @param patchAffectPackage 补丁影响包
     * @return 结果
     */
    @Override
    public int insertPatchAffectPackage(PatchAffectPackage patchAffectPackage) {
        return patchAffectPackageMapper.insertPatchAffectPackage(patchAffectPackage);
    }

    /**
     * 修改补丁影响包
     *
     * @param patchAffectPackage 补丁影响包
     * @return 结果
     */
    @Override
    public int updatePatchAffectPackage(PatchAffectPackage patchAffectPackage) {
        return patchAffectPackageMapper.updatePatchAffectPackage(patchAffectPackage);
    }

    /**
     * 批量删除补丁影响包
     *
     * @param ids 需要删除的补丁影响包主键
     * @return 结果
     */
    @Override
    public int deletePatchAffectPackageByIds(Integer[] ids) {
        return patchAffectPackageMapper.deletePatchAffectPackageByIds(ids);
    }

    /**
     * 删除补丁影响包信息
     *
     * @param id 补丁影响包主键
     * @return 结果
     */
    @Override
    public int deletePatchAffectPackageById(Integer id) {
        return patchAffectPackageMapper.deletePatchAffectPackageById(id);
    }
}
