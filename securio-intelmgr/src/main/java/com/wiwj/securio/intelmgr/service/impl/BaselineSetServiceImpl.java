package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.BaselineSetMapper;
import com.wiwj.securio.intelmgr.domain.BaselineSet;
import com.wiwj.securio.intelmgr.service.IBaselineSetService;

/**
 * 基线分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class BaselineSetServiceImpl implements IBaselineSetService {
    @Autowired
    private BaselineSetMapper baselineSetMapper;

    /**
     * 查询基线分类
     *
     * @param id 基线分类主键
     * @return 基线分类
     */
    @Override
    public BaselineSet selectBaselineSetById(String id) {
        return baselineSetMapper.selectBaselineSetById(id);
    }

    /**
     * 查询基线分类列表
     *
     * @param baselineSet 基线分类
     * @return 基线分类
     */
    @Override
    public List<BaselineSet> selectBaselineSetList(BaselineSet baselineSet) {
        return baselineSetMapper.selectBaselineSetList(baselineSet);
    }

    /**
     * 新增基线分类
     *
     * @param baselineSet 基线分类
     * @return 结果
     */
    @Override
    public int insertBaselineSet(BaselineSet baselineSet) {
        return baselineSetMapper.insertBaselineSet(baselineSet);
    }

    /**
     * 修改基线分类
     *
     * @param baselineSet 基线分类
     * @return 结果
     */
    @Override
    public int updateBaselineSet(BaselineSet baselineSet) {
        return baselineSetMapper.updateBaselineSet(baselineSet);
    }

    /**
     * 批量删除基线分类
     *
     * @param ids 需要删除的基线分类主键
     * @return 结果
     */
    @Override
    public int deleteBaselineSetByIds(String[] ids) {
        return baselineSetMapper.deleteBaselineSetByIds(ids);
    }

    /**
     * 删除基线分类信息
     *
     * @param id 基线分类主键
     * @return 结果
     */
    @Override
    public int deleteBaselineSetById(String id) {
        return baselineSetMapper.deleteBaselineSetById(id);
    }
}
