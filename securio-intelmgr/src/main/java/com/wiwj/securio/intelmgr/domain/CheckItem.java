package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 核查项配置对象 check_item
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public class CheckItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 核查项ID */
    private String id;

    /** 核查项名称 */
    @Excel(name = "核查项名称")
    private String name;

    /** 核查项类型 */
    @Excel(name = "核查项类型")
    private String type;

    /** 核查项级别 */
    @Excel(name = "核查项级别")
    private Long level;

    /** 标签列表 */
    @Excel(name = "标签列表")
    private String tags;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 审计方法 */
    @Excel(name = "审计方法")
    private String audit;

    /** 风险说明 */
    @Excel(name = "风险说明")
    private String risk;

    /** 修复方法 */
    @Excel(name = "修复方法")
    private String remediation;

    /** 额外信息 */
    @Excel(name = "额外信息")
    private String extra;

    /** 超时时间 */
    @Excel(name = "超时时间")
    private Long timeout;

    /** 是否系统内置 */
    @Excel(name = "是否系统内置")
    private Integer isSystem;

    /** 参数模式 */
    @Excel(name = "参数模式")
    private String argsSchema;

    /** 操作系统匹配规则 */
    @Excel(name = "操作系统匹配规则")
    private String osMatcher;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private Long createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(String id){
        this.id = id;
    }

    public String getId(){
        return id;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }
    public void setType(String type){
        this.type = type;
    }

    public String getType(){
        return type;
    }
    public void setLevel(Long level){
        this.level = level;
    }

    public Long getLevel(){
        return level;
    }
    public void setTags(String tags){
        this.tags = tags;
    }

    public String getTags(){
        return tags;
    }
    public void setDescription(String description){
        this.description = description;
    }

    public String getDescription(){
        return description;
    }
    public void setAudit(String audit){
        this.audit = audit;
    }

    public String getAudit(){
        return audit;
    }
    public void setRisk(String risk){
        this.risk = risk;
    }

    public String getRisk(){
        return risk;
    }
    public void setRemediation(String remediation){
        this.remediation = remediation;
    }

    public String getRemediation(){
        return remediation;
    }
    public void setExtra(String extra){
        this.extra = extra;
    }

    public String getExtra(){
        return extra;
    }
    public void setTimeout(Long timeout){
        this.timeout = timeout;
    }

    public Long getTimeout(){
        return timeout;
    }
    public void setIsSystem(Integer isSystem){
        this.isSystem = isSystem;
    }

    public Integer getIsSystem(){
        return isSystem;
    }
    public void setArgsSchema(String argsSchema){
        this.argsSchema = argsSchema;
    }

    public String getArgsSchema(){
        return argsSchema;
    }
    public void setOsMatcher(String osMatcher){
        this.osMatcher = osMatcher;
    }

    public String getOsMatcher(){
        return osMatcher;
    }
    public void setCreatedAt(Long createdAt){
        this.createdAt = createdAt;
    }

    public Long getCreatedAt(){
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt){
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("type", getType())
            .append("level", getLevel())
            .append("tags", getTags())
            .append("description", getDescription())
            .append("audit", getAudit())
            .append("risk", getRisk())
            .append("remediation", getRemediation())
            .append("extra", getExtra())
            .append("timeout", getTimeout())
            .append("isSystem", getIsSystem())
            .append("argsSchema", getArgsSchema())
            .append("osMatcher", getOsMatcher())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
