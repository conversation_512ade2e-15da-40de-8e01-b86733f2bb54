package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.VulnDetail;
import com.wiwj.securio.intelmgr.service.IVulnDetailService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 漏洞详情Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/intelmgr/vulnDetail")
public class VulnDetailController extends BaseController {
    @Autowired
    private IVulnDetailService vulnDetailService;

    /**
     * 查询漏洞详情列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping("/list")
    public TableDataInfo list(VulnDetail vulnDetail) {
        startPage();
        List<VulnDetail> list = vulnDetailService.selectVulnDetailList(vulnDetail);
        return getDataTable(list);
    }

    /**
     * 导出漏洞详情列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VulnDetail vulnDetail) {
        List<VulnDetail> list = vulnDetailService.selectVulnDetailList(vulnDetail);
        ExcelUtil<VulnDetail> util = new ExcelUtil<VulnDetail>(VulnDetail.class);
        util.exportExcel(response, list, "漏洞详情数据");
    }

    /**
     * 获取漏洞详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(vulnDetailService.selectVulnDetailById(id));
    }

    /**
     * 新增漏洞详情
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VulnDetail vulnDetail) {
        return toAjax(vulnDetailService.insertVulnDetail(vulnDetail));
    }

    /**
     * 修改漏洞详情
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VulnDetail vulnDetail) {
        return toAjax(vulnDetailService.updateVulnDetail(vulnDetail));
    }

    /**
     * 删除漏洞详情
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(vulnDetailService.deleteVulnDetailByIds(ids));
    }
}
