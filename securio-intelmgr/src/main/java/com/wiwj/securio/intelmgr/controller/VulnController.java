package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.Vuln;
import com.wiwj.securio.intelmgr.domain.VulnDetail;
import com.wiwj.securio.intelmgr.domain.VulnReference;
import com.wiwj.securio.intelmgr.domain.VulnExploitInfo;
import com.wiwj.securio.intelmgr.service.IVulnService;
import com.wiwj.securio.intelmgr.service.IVulnDetailService;
import com.wiwj.securio.intelmgr.service.IVulnReferenceService;
import com.wiwj.securio.intelmgr.service.IVulnExploitInfoService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

import java.util.Map;

/**
 * 漏洞基本信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/intelmgr/vuln")
public class VulnController extends BaseController {
    @Autowired
    private IVulnService vulnService;
    
    @Autowired
    private IVulnDetailService vulnDetailService;
    
    @Autowired
    private IVulnReferenceService vulnReferenceService;
    
    @Autowired
    private IVulnExploitInfoService vulnExploitInfoService;

    /**
     * 查询漏洞基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping("/list")
    public TableDataInfo list(Vuln vuln) {
        startPage();
        List<Vuln> list = vulnService.selectVulnList(vuln);
        return getDataTable(list);
    }

    /**
     * 导出漏洞基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Vuln vuln) {
        List<Vuln> list = vulnService.selectVulnList(vuln);
        ExcelUtil<Vuln> util = new ExcelUtil<Vuln>(Vuln.class);
        util.exportExcel(response, list, "漏洞基本信息数据");
    }

    /**
     * 获取漏洞基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(vulnService.selectVulnById(id));
    }

    /**
     * 新增漏洞基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Vuln vuln) {
        int rows = vulnService.insertVuln(vuln);
        return rows > 0 ? success(vuln.getId()) : error("添加漏洞基本信息失败");
    }
    
    /**
     * 新增漏洞聚合信息（包含基本信息、详情、参考链接、利用情报）
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞聚合信息", businessType = BusinessType.INSERT)
    @PostMapping("/aggregate")
    public AjaxResult addAggregate(@RequestBody Map<String, Object> aggregateData) {
        try {
            // 1. 处理基本信息
            Vuln vuln = new Vuln();
            Map<String, Object> vulnData = (Map<String, Object>) aggregateData.get("vuln");
            if (vulnData != null) {
                // 设置漏洞基本信息属性
                if (vulnData.get("level") != null) {
                    vuln.setLevel(Integer.valueOf(vulnData.get("level").toString()));
                }
                vuln.setName((String) vulnData.get("name"));
                if (vulnData.get("cvssScore") != null) {
                    vuln.setCvssScore(new java.math.BigDecimal(vulnData.get("cvssScore").toString()));
                }
                vuln.setCve((String) vulnData.get("cve"));
                vuln.setCnnvd((String) vulnData.get("cnnvd"));
                vuln.setCnvd((String) vulnData.get("cnvd"));
                vuln.setBid((String) vulnData.get("bid"));
                vuln.setCwe((String) vulnData.get("cwe"));
                if (vulnData.get("type") != null) {
                    try {
                        vuln.setType(Integer.valueOf(vulnData.get("type").toString()));
                    } catch (NumberFormatException e) {
                        // 处理类型转换异常，设置默认值或记录日志
                    }
                }
                vuln.setCvssVector((String) vulnData.get("cvssVector"));
                vuln.setCvssAccessVector((String) vulnData.get("cvssAccessVector"));
                if (vulnData.get("hasPoc") != null) {
                    vuln.setHasPoc(Integer.valueOf(vulnData.get("hasPoc").toString()));
                }
                vuln.setPublishDate(parseDate(vulnData.get("publishDate")));
                vuln.setModifyDate(parseDate(vulnData.get("modifyDate")));
            }
            
            // 保存漏洞基本信息
            int rows = vulnService.insertVuln(vuln);
            if (rows <= 0) {
                return error("添加漏洞基本信息失败");
            }
            
            // 获取生成的主键ID
            Integer vulnId = vuln.getId();
            if (vulnId == null) {
                return error("获取漏洞ID失败");
            }
            
            // 2. 处理详情信息
            Map<String, Object> detailData = (Map<String, Object>) aggregateData.get("detail");
            if (detailData != null) {
                VulnDetail vulnDetail = new VulnDetail();
                vulnDetail.setId(vulnId);
                vulnDetail.setDescription((String) detailData.get("description"));
                vulnDetail.setSolution((String) detailData.get("solution"));
                vulnDetail.setAssesInfo((String) detailData.get("assesInfo"));
                vulnDetail.setCvssAccessComplexity((String) detailData.get("cvssAccessComplexity"));
                vulnDetail.setCvssAuthentication((String) detailData.get("cvssAuthentication"));
                vulnDetail.setCvssConfidentialityImpact((String) detailData.get("cvssConfidentialityImpact"));
                vulnDetail.setCvssIntegrityImpact((String) detailData.get("cvssIntegrityImpact"));
                vulnDetail.setCvssAvailabilityImpact((String) detailData.get("cvssAvailabilityImpact"));
                
                vulnDetailService.insertVulnDetail(vulnDetail);
            }
            
            // 3. 处理参考链接
            List<Map<String, Object>> references = (List<Map<String, Object>>) aggregateData.get("references");
            if (references != null && !references.isEmpty()) {
                for (Map<String, Object> ref : references) {
                    // 如果参考链接名称和URL都为空，则跳过
                    String name = (String) ref.get("name");
                    String url = (String) ref.get("url");
                    if ((name == null || name.trim().isEmpty()) && (url == null || url.trim().isEmpty())) {
                        continue;
                    }
                    
                    VulnReference vulnReference = new VulnReference();
                    vulnReference.setVulnId(vulnId);
                    vulnReference.setName(name);
                    vulnReference.setUrl(url);
                    
                    vulnReferenceService.insertVulnReference(vulnReference);
                }
            }
            
            // 4. 处理利用情报
            List<Map<String, Object>> exploitInfos = (List<Map<String, Object>>) aggregateData.get("exploitInfos");
            if (exploitInfos != null && !exploitInfos.isEmpty()) {
                for (Map<String, Object> info : exploitInfos) {
                    // 如果情报来源、标题和链接都为空，则跳过
                    String source = (String) info.get("source");
                    String title = (String) info.get("title");
                    String link = (String) info.get("link");
                    if ((source == null || source.trim().isEmpty()) && 
                        (title == null || title.trim().isEmpty()) && 
                        (link == null || link.trim().isEmpty())) {
                        continue;
                    }
                    
                    VulnExploitInfo vulnExploitInfo = new VulnExploitInfo();
                    vulnExploitInfo.setVulnId(vulnId);
                    vulnExploitInfo.setSource(source);
                    vulnExploitInfo.setTitle(title);
                    vulnExploitInfo.setLink(link);
                    
                    vulnExploitInfoService.insertVulnExploitInfo(vulnExploitInfo);
                }
            }
            
            // 返回成功结果和漏洞ID
            return success(vulnId);
        } catch (Exception e) {
            logger.error("添加漏洞聚合信息异常", e);
            return error("添加漏洞聚合信息失败：" + e.getMessage());
        }
    }

    /**
     * 修改漏洞基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Vuln vuln) {
        return toAjax(vulnService.updateVuln(vuln));
    }

    /**
     * 删除漏洞基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        try {
            // 1. 删除关联的参考链接
            for (Integer id : ids) {
                // 构造查询对象
                VulnReference reference = new VulnReference();
                reference.setVulnId(id);
                // 获取关联的参考链接
                List<VulnReference> references = vulnReferenceService.selectVulnReferenceList(reference);
                // 删除关联的参考链接
                for (VulnReference ref : references) {
                    vulnReferenceService.deleteVulnReferenceById(ref.getId());
                }
                
                // 2. 删除关联的利用情报
                VulnExploitInfo exploitInfo = new VulnExploitInfo();
                exploitInfo.setVulnId(id);
                // 获取关联的利用情报
                List<VulnExploitInfo> exploitInfos = vulnExploitInfoService.selectVulnExploitInfoList(exploitInfo);
                // 删除关联的利用情报
                for (VulnExploitInfo info : exploitInfos) {
                    vulnExploitInfoService.deleteVulnExploitInfoById(info.getId());
                }
                
                // 3. 删除关联的详细信息
                vulnDetailService.deleteVulnDetailById(id);
            }
            
            // 4. 删除漏洞基本信息
            return toAjax(vulnService.deleteVulnByIds(ids));
        } catch (Exception e) {
            logger.error("删除漏洞信息异常", e);
            return error("删除漏洞信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 解析日期字符串
     */
    private java.util.Date parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }
        
        try {
            if (dateObj instanceof String) {
                String dateStr = (String) dateObj;
                if (dateStr.trim().isEmpty()) {
                    return null;
                }
                
                // 使用日期工具类解析日期
                return com.wiwj.common.utils.DateUtils.parseDate(dateStr);
            }
        } catch (Exception e) {
            logger.error("日期解析错误", e);
        }
        
        return null;
    }

    /**
     * 修改漏洞聚合信息（包含基本信息、详情、参考链接、利用情报）
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞聚合信息", businessType = BusinessType.UPDATE)
    @PostMapping("/update-aggregate")
    public AjaxResult updateAggregate(@RequestBody Map<String, Object> aggregateData) {
        try {
            // 1. 处理基本信息
            Vuln vuln = new Vuln();
            Map<String, Object> vulnData = (Map<String, Object>) aggregateData.get("vuln");
            if (vulnData != null) {
                // 获取漏洞ID
                Integer vulnId = null;
                if (vulnData.get("id") != null) {
                    try {
                        vulnId = Integer.valueOf(vulnData.get("id").toString());
                        vuln.setId(vulnId);
                    } catch (NumberFormatException e) {
                        return error("漏洞ID格式错误");
                    }
                } else {
                    return error("漏洞ID不能为空");
                }
                
                // 设置漏洞基本信息属性
                if (vulnData.get("level") != null) {
                    vuln.setLevel(Integer.valueOf(vulnData.get("level").toString()));
                }
                vuln.setName((String) vulnData.get("name"));
                if (vulnData.get("cvssScore") != null) {
                    vuln.setCvssScore(new java.math.BigDecimal(vulnData.get("cvssScore").toString()));
                }
                vuln.setCve((String) vulnData.get("cve"));
                vuln.setCnnvd((String) vulnData.get("cnnvd"));
                vuln.setCnvd((String) vulnData.get("cnvd"));
                vuln.setBid((String) vulnData.get("bid"));
                vuln.setCwe((String) vulnData.get("cwe"));
                if (vulnData.get("type") != null) {
                    try {
                        vuln.setType(Integer.valueOf(vulnData.get("type").toString()));
                    } catch (NumberFormatException e) {
                        // 处理类型转换异常，设置默认值或记录日志
                    }
                }
                vuln.setCvssVector((String) vulnData.get("cvssVector"));
                vuln.setCvssAccessVector((String) vulnData.get("cvssAccessVector"));
                if (vulnData.get("hasPoc") != null) {
                    vuln.setHasPoc(Integer.valueOf(vulnData.get("hasPoc").toString()));
                }
                vuln.setPublishDate(parseDate(vulnData.get("publishDate")));
                vuln.setModifyDate(parseDate(vulnData.get("modifyDate")));
                
                // 更新漏洞基本信息
                int rows = vulnService.updateVuln(vuln);
                if (rows <= 0) {
                    return error("更新漏洞基本信息失败");
                }
                
                // 2. 处理详情信息
                Map<String, Object> detailData = (Map<String, Object>) aggregateData.get("detail");
                if (detailData != null) {
                    VulnDetail vulnDetail = vulnDetailService.selectVulnDetailById(vulnId);
                    boolean isInsert = false;
                    
                    // 如果详情不存在，则创建新的详情
                    if (vulnDetail == null) {
                        vulnDetail = new VulnDetail();
                        vulnDetail.setId(vulnId);
                        isInsert = true;
                    }
                    
                    // 设置详情属性
                    vulnDetail.setDescription((String) detailData.get("description"));
                    vulnDetail.setSolution((String) detailData.get("solution"));
                    vulnDetail.setAssesInfo((String) detailData.get("assesInfo"));
                    vulnDetail.setCvssAccessComplexity((String) detailData.get("cvssAccessComplexity"));
                    vulnDetail.setCvssAuthentication((String) detailData.get("cvssAuthentication"));
                    vulnDetail.setCvssConfidentialityImpact((String) detailData.get("cvssConfidentialityImpact"));
                    vulnDetail.setCvssIntegrityImpact((String) detailData.get("cvssIntegrityImpact"));
                    vulnDetail.setCvssAvailabilityImpact((String) detailData.get("cvssAvailabilityImpact"));
                    
                    // 保存或更新
                    if (isInsert) {
                        vulnDetailService.insertVulnDetail(vulnDetail);
                    } else {
                        vulnDetailService.updateVulnDetail(vulnDetail);
                    }
                }
                
                // 3. 处理参考链接
                List<Map<String, Object>> references = (List<Map<String, Object>>) aggregateData.get("references");
                if (references != null) {
                    // 先删除旧的参考链接
                    List<VulnReference> oldReferences = vulnReferenceService.selectVulnReferenceByVulnId(vulnId);
                    for (VulnReference oldRef : oldReferences) {
                        vulnReferenceService.deleteVulnReferenceById(oldRef.getId());
                    }
                    
                    // 添加新的参考链接
                    for (Map<String, Object> ref : references) {
                        // 如果参考链接名称和URL都为空，则跳过
                        String name = (String) ref.get("name");
                        String url = (String) ref.get("url");
                        if ((name == null || name.trim().isEmpty()) && (url == null || url.trim().isEmpty())) {
                            continue;
                        }
                        
                        VulnReference vulnReference = new VulnReference();
                        vulnReference.setVulnId(vulnId);
                        vulnReference.setName(name);
                        vulnReference.setUrl(url);
                        
                        vulnReferenceService.insertVulnReference(vulnReference);
                    }
                }
                
                // 4. 处理利用情报
                List<Map<String, Object>> exploitInfos = (List<Map<String, Object>>) aggregateData.get("exploitInfos");
                if (exploitInfos != null) {
                    // 先删除旧的利用情报
                    List<VulnExploitInfo> oldExploitInfos = vulnExploitInfoService.selectVulnExploitInfoByVulnId(vulnId);
                    for (VulnExploitInfo oldInfo : oldExploitInfos) {
                        vulnExploitInfoService.deleteVulnExploitInfoById(oldInfo.getId());
                    }
                    
                    // 添加新的利用情报
                    for (Map<String, Object> info : exploitInfos) {
                        // 如果情报来源、标题和链接都为空，则跳过
                        String source = (String) info.get("source");
                        String title = (String) info.get("title");
                        String link = (String) info.get("link");
                        if ((source == null || source.trim().isEmpty()) && 
                            (title == null || title.trim().isEmpty()) && 
                            (link == null || link.trim().isEmpty())) {
                            continue;
                        }
                        
                        VulnExploitInfo vulnExploitInfo = new VulnExploitInfo();
                        vulnExploitInfo.setVulnId(vulnId);
                        vulnExploitInfo.setSource(source);
                        vulnExploitInfo.setTitle(title);
                        vulnExploitInfo.setLink(link);
                        
                        vulnExploitInfoService.insertVulnExploitInfo(vulnExploitInfo);
                    }
                }
                
                // 返回成功结果
                return success();
            } else {
                return error("漏洞基本信息不能为空");
            }
        } catch (Exception e) {
            logger.error("更新漏洞聚合信息异常", e);
            return error("更新漏洞聚合信息失败：" + e.getMessage());
        }
    }
}
