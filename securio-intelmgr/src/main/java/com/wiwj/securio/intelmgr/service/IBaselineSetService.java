package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.BaselineSet;

/**
 * 基线分类Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IBaselineSetService {
    /**
     * 查询基线分类
     *
     * @param id 基线分类主键
     * @return 基线分类
     */
    public BaselineSet selectBaselineSetById(String id);

    /**
     * 查询基线分类列表
     *
     * @param baselineSet 基线分类
     * @return 基线分类集合
     */
    public List<BaselineSet> selectBaselineSetList(BaselineSet baselineSet);

    /**
     * 新增基线分类
     *
     * @param baselineSet 基线分类
     * @return 结果
     */
    public int insertBaselineSet(BaselineSet baselineSet);

    /**
     * 修改基线分类
     *
     * @param baselineSet 基线分类
     * @return 结果
     */
    public int updateBaselineSet(BaselineSet baselineSet);

    /**
     * 批量删除基线分类
     *
     * @param ids 需要删除的基线分类主键集合
     * @return 结果
     */
    public int deleteBaselineSetByIds(String[] ids);

    /**
     * 删除基线分类信息
     *
     * @param id 基线分类主键
     * @return 结果
     */
    public int deleteBaselineSetById(String id);
}
