package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.PatchAffectPackage;

/**
 * 补丁影响包Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IPatchAffectPackageService {
    /**
     * 查询补丁影响包
     *
     * @param id 补丁影响包主键
     * @return 补丁影响包
     */
    public PatchAffectPackage selectPatchAffectPackageById(Integer id);

    /**
     * 查询补丁影响包列表
     *
     * @param patchAffectPackage 补丁影响包
     * @return 补丁影响包集合
     */
    public List<PatchAffectPackage> selectPatchAffectPackageList(PatchAffectPackage patchAffectPackage);

    /**
     * 新增补丁影响包
     *
     * @param patchAffectPackage 补丁影响包
     * @return 结果
     */
    public int insertPatchAffectPackage(PatchAffectPackage patchAffectPackage);

    /**
     * 修改补丁影响包
     *
     * @param patchAffectPackage 补丁影响包
     * @return 结果
     */
    public int updatePatchAffectPackage(PatchAffectPackage patchAffectPackage);

    /**
     * 批量删除补丁影响包
     *
     * @param ids 需要删除的补丁影响包主键集合
     * @return 结果
     */
    public int deletePatchAffectPackageByIds(Integer[] ids);

    /**
     * 删除补丁影响包信息
     *
     * @param id 补丁影响包主键
     * @return 结果
     */
    public int deletePatchAffectPackageById(Integer id);
}
