package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.PatchCve;
import com.wiwj.securio.intelmgr.service.IPatchCveService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 补丁CVEController
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patchCve")
public class PatchCveController extends BaseController {
    @Autowired
    private IPatchCveService patchCveService;

    /**
     * 查询补丁CVE列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping("/list")
    public TableDataInfo list(PatchCve patchCve) {
        startPage();
        List<PatchCve> list = patchCveService.selectPatchCveList(patchCve);
        return getDataTable(list);
    }

    /**
     * 导出补丁CVE列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁CVE", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatchCve patchCve) {
        List<PatchCve> list = patchCveService.selectPatchCveList(patchCve);
        ExcelUtil<PatchCve> util = new ExcelUtil<PatchCve>(PatchCve.class);
        util.exportExcel(response, list, "补丁CVE数据");
    }

    /**
     * 获取补丁CVE详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(patchCveService.selectPatchCveById(id));
    }

    /**
     * 新增补丁CVE
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁CVE", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatchCve patchCve) {
        return toAjax(patchCveService.insertPatchCve(patchCve));
    }

    /**
     * 修改补丁CVE
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁CVE", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatchCve patchCve) {
        return toAjax(patchCveService.updatePatchCve(patchCve));
    }

    /**
     * 删除补丁CVE
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch')")
    @Log(title = "补丁CVE", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patchCveService.deletePatchCveByIds(ids));
    }
}
