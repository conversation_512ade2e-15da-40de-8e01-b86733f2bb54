package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 补丁特性对象 patch_trait
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public class PatchTrait extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    /** 补丁ID */
    @Excel(name = "补丁ID")
    private String patchId;

    /** 特性 */
    @Excel(name = "特性")
    private String trait;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    public void setId(Integer id){
        this.id = id;
    }

    public Integer getId(){
        return id;
    }
    public void setPatchId(String patchId){
        this.patchId = patchId;
    }

    public String getPatchId(){
        return patchId;
    }
    public void setTrait(String trait){
        this.trait = trait;
    }

    public String getTrait(){
        return trait;
    }
    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("patchId", getPatchId())
            .append("trait", getTrait())
            .append("createdAt", getCreatedAt())
            .toString();
    }
}
