package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.CheckItemMapper;
import com.wiwj.securio.intelmgr.domain.CheckItem;
import com.wiwj.securio.intelmgr.service.ICheckItemService;

/**
 * 核查项配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class CheckItemServiceImpl implements ICheckItemService {
    @Autowired
    private CheckItemMapper checkItemMapper;

    /**
     * 查询核查项配置
     *
     * @param id 核查项配置主键
     * @return 核查项配置
     */
    @Override
    public CheckItem selectCheckItemById(String id) {
        return checkItemMapper.selectCheckItemById(id);
    }

    /**
     * 查询核查项配置列表
     *
     * @param checkItem 核查项配置
     * @return 核查项配置
     */
    @Override
    public List<CheckItem> selectCheckItemList(CheckItem checkItem) {
        return checkItemMapper.selectCheckItemList(checkItem);
    }

    /**
     * 新增核查项配置
     *
     * @param checkItem 核查项配置
     * @return 结果
     */
    @Override
    public int insertCheckItem(CheckItem checkItem) {
        return checkItemMapper.insertCheckItem(checkItem);
    }

    /**
     * 修改核查项配置
     *
     * @param checkItem 核查项配置
     * @return 结果
     */
    @Override
    public int updateCheckItem(CheckItem checkItem) {
        return checkItemMapper.updateCheckItem(checkItem);
    }

    /**
     * 批量删除核查项配置
     *
     * @param ids 需要删除的核查项配置主键
     * @return 结果
     */
    @Override
    public int deleteCheckItemByIds(String[] ids) {
        return checkItemMapper.deleteCheckItemByIds(ids);
    }

    /**
     * 删除核查项配置信息
     *
     * @param id 核查项配置主键
     * @return 结果
     */
    @Override
    public int deleteCheckItemById(String id) {
        return checkItemMapper.deleteCheckItemById(id);
    }
}
