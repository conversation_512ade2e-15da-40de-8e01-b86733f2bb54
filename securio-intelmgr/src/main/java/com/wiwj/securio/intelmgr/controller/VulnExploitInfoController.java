package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.VulnExploitInfo;
import com.wiwj.securio.intelmgr.service.IVulnExploitInfoService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 漏洞利用情报Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/intelmgr/vulnExploitInfo")
public class VulnExploitInfoController extends BaseController {
    @Autowired
    private IVulnExploitInfoService vulnExploitInfoService;

    /**
     * 查询漏洞利用情报列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping("/list")
    public TableDataInfo list(VulnExploitInfo vulnExploitInfo) {
        startPage();
        List<VulnExploitInfo> list = vulnExploitInfoService.selectVulnExploitInfoList(vulnExploitInfo);
        return getDataTable(list);
    }

    /**
     * 导出漏洞利用情报列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞利用情报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VulnExploitInfo vulnExploitInfo) {
        List<VulnExploitInfo> list = vulnExploitInfoService.selectVulnExploitInfoList(vulnExploitInfo);
        ExcelUtil<VulnExploitInfo> util = new ExcelUtil<VulnExploitInfo>(VulnExploitInfo.class);
        util.exportExcel(response, list, "漏洞利用情报数据");
    }

    /**
     * 获取漏洞利用情报详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(vulnExploitInfoService.selectVulnExploitInfoById(id));
    }

    /**
     * 新增漏洞利用情报
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞利用情报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VulnExploitInfo vulnExploitInfo) {
        return toAjax(vulnExploitInfoService.insertVulnExploitInfo(vulnExploitInfo));
    }

    /**
     * 修改漏洞利用情报
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞利用情报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VulnExploitInfo vulnExploitInfo) {
        return toAjax(vulnExploitInfoService.updateVulnExploitInfo(vulnExploitInfo));
    }

    /**
     * 删除漏洞利用情报
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @Log(title = "漏洞利用情报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(vulnExploitInfoService.deleteVulnExploitInfoByIds(ids));
    }
    
    /**
     * 根据漏洞ID查询相关利用情报
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:vuln')")
    @GetMapping(value = "/vuln/{vulnId}")
    public AjaxResult getVulnExploitInfoByVulnId(@PathVariable("vulnId") Integer vulnId) {
        return success(vulnExploitInfoService.selectVulnExploitInfoByVulnId(vulnId));
    }
}
