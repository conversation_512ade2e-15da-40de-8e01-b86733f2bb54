package com.wiwj.securio.intelmgr.mapper;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.PatchReference;

/**
 * 补丁参考链接Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface PatchReferenceMapper {
    /**
     * 查询补丁参考链接
     *
     * @param id 补丁参考链接主键
     * @return 补丁参考链接
     */
    public PatchReference selectPatchReferenceById(Integer id);

    /**
     * 查询补丁参考链接列表
     *
     * @param patchReference 补丁参考链接
     * @return 补丁参考链接集合
     */
    public List<PatchReference> selectPatchReferenceList(PatchReference patchReference);

    /**
     * 新增补丁参考链接
     *
     * @param patchReference 补丁参考链接
     * @return 结果
     */
    public int insertPatchReference(PatchReference patchReference);

    /**
     * 修改补丁参考链接
     *
     * @param patchReference 补丁参考链接
     * @return 结果
     */
    public int updatePatchReference(PatchReference patchReference);

    /**
     * 删除补丁参考链接
     *
     * @param id 补丁参考链接主键
     * @return 结果
     */
    public int deletePatchReferenceById(Integer id);

    /**
     * 批量删除补丁参考链接
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatchReferenceByIds(Integer[] ids);
}
