package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.PatchSupersededMapper;
import com.wiwj.securio.intelmgr.domain.PatchSuperseded;
import com.wiwj.securio.intelmgr.service.IPatchSupersededService;

/**
 * 被替代的补丁Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class PatchSupersededServiceImpl implements IPatchSupersededService {
    @Autowired
    private PatchSupersededMapper patchSupersededMapper;

    /**
     * 查询被替代的补丁
     *
     * @param id 被替代的补丁主键
     * @return 被替代的补丁
     */
    @Override
    public PatchSuperseded selectPatchSupersededById(Integer id) {
        return patchSupersededMapper.selectPatchSupersededById(id);
    }

    /**
     * 查询被替代的补丁列表
     *
     * @param patchSuperseded 被替代的补丁
     * @return 被替代的补丁
     */
    @Override
    public List<PatchSuperseded> selectPatchSupersededList(PatchSuperseded patchSuperseded) {
        return patchSupersededMapper.selectPatchSupersededList(patchSuperseded);
    }

    /**
     * 新增被替代的补丁
     *
     * @param patchSuperseded 被替代的补丁
     * @return 结果
     */
    @Override
    public int insertPatchSuperseded(PatchSuperseded patchSuperseded) {
        return patchSupersededMapper.insertPatchSuperseded(patchSuperseded);
    }

    /**
     * 修改被替代的补丁
     *
     * @param patchSuperseded 被替代的补丁
     * @return 结果
     */
    @Override
    public int updatePatchSuperseded(PatchSuperseded patchSuperseded) {
        return patchSupersededMapper.updatePatchSuperseded(patchSuperseded);
    }

    /**
     * 批量删除被替代的补丁
     *
     * @param ids 需要删除的被替代的补丁主键
     * @return 结果
     */
    @Override
    public int deletePatchSupersededByIds(Integer[] ids) {
        return patchSupersededMapper.deletePatchSupersededByIds(ids);
    }

    /**
     * 删除被替代的补丁信息
     *
     * @param id 被替代的补丁主键
     * @return 结果
     */
    @Override
    public int deletePatchSupersededById(Integer id) {
        return patchSupersededMapper.deletePatchSupersededById(id);
    }
}
