package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.Patch;

/**
 * 补丁基本信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IPatchService {
    /**
     * 查询补丁基本信息
     *
     * @param id 补丁基本信息主键
     * @return 补丁基本信息
     */
    public Patch selectPatchById(String id);

    /**
     * 查询补丁基本信息列表
     *
     * @param patch 补丁基本信息
     * @return 补丁基本信息集合
     */
    public List<Patch> selectPatchList(Patch patch);

    /**
     * 新增补丁基本信息
     *
     * @param patch 补丁基本信息
     * @return 结果
     */
    public int insertPatch(Patch patch);

    /**
     * 修改补丁基本信息
     *
     * @param patch 补丁基本信息
     * @return 结果
     */
    public int updatePatch(Patch patch);

    /**
     * 批量删除补丁基本信息
     *
     * @param ids 需要删除的补丁基本信息主键集合
     * @return 结果
     */
    public int deletePatchByIds(String[] ids);

    /**
     * 删除补丁基本信息信息
     *
     * @param id 补丁基本信息主键
     * @return 结果
     */
    public int deletePatchById(String id);
}
