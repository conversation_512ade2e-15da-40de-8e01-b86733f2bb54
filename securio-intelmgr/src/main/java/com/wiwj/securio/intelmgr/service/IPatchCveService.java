package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.PatchCve;

/**
 * 补丁CVEService接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IPatchCveService {
    /**
     * 查询补丁CVE
     *
     * @param id 补丁CVE主键
     * @return 补丁CVE
     */
    public PatchCve selectPatchCveById(Integer id);

    /**
     * 查询补丁CVE列表
     *
     * @param patchCve 补丁CVE
     * @return 补丁CVE集合
     */
    public List<PatchCve> selectPatchCveList(PatchCve patchCve);

    /**
     * 新增补丁CVE
     *
     * @param patchCve 补丁CVE
     * @return 结果
     */
    public int insertPatchCve(PatchCve patchCve);

    /**
     * 修改补丁CVE
     *
     * @param patchCve 补丁CVE
     * @return 结果
     */
    public int updatePatchCve(PatchCve patchCve);

    /**
     * 批量删除补丁CVE
     *
     * @param ids 需要删除的补丁CVE主键集合
     * @return 结果
     */
    public int deletePatchCveByIds(Integer[] ids);

    /**
     * 删除补丁CVE信息
     *
     * @param id 补丁CVE主键
     * @return 结果
     */
    public int deletePatchCveById(Integer id);
}
