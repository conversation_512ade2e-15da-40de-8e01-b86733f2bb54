package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.PatchTraitMapper;
import com.wiwj.securio.intelmgr.domain.PatchTrait;
import com.wiwj.securio.intelmgr.service.IPatchTraitService;

/**
 * 补丁特性Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class PatchTraitServiceImpl implements IPatchTraitService {
    @Autowired
    private PatchTraitMapper patchTraitMapper;

    /**
     * 查询补丁特性
     *
     * @param id 补丁特性主键
     * @return 补丁特性
     */
    @Override
    public PatchTrait selectPatchTraitById(Integer id) {
        return patchTraitMapper.selectPatchTraitById(id);
    }

    /**
     * 查询补丁特性列表
     *
     * @param patchTrait 补丁特性
     * @return 补丁特性
     */
    @Override
    public List<PatchTrait> selectPatchTraitList(PatchTrait patchTrait) {
        return patchTraitMapper.selectPatchTraitList(patchTrait);
    }

    /**
     * 新增补丁特性
     *
     * @param patchTrait 补丁特性
     * @return 结果
     */
    @Override
    public int insertPatchTrait(PatchTrait patchTrait) {
        return patchTraitMapper.insertPatchTrait(patchTrait);
    }

    /**
     * 修改补丁特性
     *
     * @param patchTrait 补丁特性
     * @return 结果
     */
    @Override
    public int updatePatchTrait(PatchTrait patchTrait) {
        return patchTraitMapper.updatePatchTrait(patchTrait);
    }

    /**
     * 批量删除补丁特性
     *
     * @param ids 需要删除的补丁特性主键
     * @return 结果
     */
    @Override
    public int deletePatchTraitByIds(Integer[] ids) {
        return patchTraitMapper.deletePatchTraitByIds(ids);
    }

    /**
     * 删除补丁特性信息
     *
     * @param id 补丁特性主键
     * @return 结果
     */
    @Override
    public int deletePatchTraitById(Integer id) {
        return patchTraitMapper.deletePatchTraitById(id);
    }

    /**
     * 根据补丁ID查询补丁特性
     *
     * @param patchIds 补丁ID列表
     * @return 补丁特性列表
     */
    @Override
    public List<PatchTrait> selectPatchTraitByPatchIds(List<String> patchIds) {
        if (patchIds == null || patchIds.isEmpty()) {
            return new ArrayList<>();
        }
        return patchTraitMapper.selectPatchTraitByPatchIds(patchIds);
    }
}
