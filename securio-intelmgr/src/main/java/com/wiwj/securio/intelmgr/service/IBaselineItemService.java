package com.wiwj.securio.intelmgr.service;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.BaselineItem;

/**
 * 基线条目Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IBaselineItemService {
    /**
     * 查询基线条目
     *
     * @param id 基线条目主键
     * @return 基线条目
     */
    public BaselineItem selectBaselineItemById(String id);

    /**
     * 查询基线条目列表
     *
     * @param baselineItem 基线条目
     * @return 基线条目集合
     */
    public List<BaselineItem> selectBaselineItemList(BaselineItem baselineItem);

    /**
     * 新增基线条目
     *
     * @param baselineItem 基线条目
     * @return 结果
     */
    public int insertBaselineItem(BaselineItem baselineItem);

    /**
     * 修改基线条目
     *
     * @param baselineItem 基线条目
     * @return 结果
     */
    public int updateBaselineItem(BaselineItem baselineItem);

    /**
     * 批量删除基线条目
     *
     * @param ids 需要删除的基线条目主键集合
     * @return 结果
     */
    public int deleteBaselineItemByIds(String[] ids);

    /**
     * 删除基线条目信息
     *
     * @param id 基线条目主键
     * @return 结果
     */
    public int deleteBaselineItemById(String id);
}
