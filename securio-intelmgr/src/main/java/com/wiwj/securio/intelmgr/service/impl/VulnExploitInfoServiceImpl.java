package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.VulnExploitInfoMapper;
import com.wiwj.securio.intelmgr.domain.VulnExploitInfo;
import com.wiwj.securio.intelmgr.service.IVulnExploitInfoService;

/**
 * 漏洞利用情报Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class VulnExploitInfoServiceImpl implements IVulnExploitInfoService {
    @Autowired
    private VulnExploitInfoMapper vulnExploitInfoMapper;

    /**
     * 查询漏洞利用情报
     *
     * @param id 漏洞利用情报主键
     * @return 漏洞利用情报
     */
    @Override
    public VulnExploitInfo selectVulnExploitInfoById(Integer id) {
        return vulnExploitInfoMapper.selectVulnExploitInfoById(id);
    }

    /**
     * 查询漏洞利用情报列表
     *
     * @param vulnExploitInfo 漏洞利用情报
     * @return 漏洞利用情报
     */
    @Override
    public List<VulnExploitInfo> selectVulnExploitInfoList(VulnExploitInfo vulnExploitInfo) {
        return vulnExploitInfoMapper.selectVulnExploitInfoList(vulnExploitInfo);
    }

    /**
     * 新增漏洞利用情报
     *
     * @param vulnExploitInfo 漏洞利用情报
     * @return 结果
     */
    @Override
    public int insertVulnExploitInfo(VulnExploitInfo vulnExploitInfo) {
        return vulnExploitInfoMapper.insertVulnExploitInfo(vulnExploitInfo);
    }

    /**
     * 修改漏洞利用情报
     *
     * @param vulnExploitInfo 漏洞利用情报
     * @return 结果
     */
    @Override
    public int updateVulnExploitInfo(VulnExploitInfo vulnExploitInfo) {
        return vulnExploitInfoMapper.updateVulnExploitInfo(vulnExploitInfo);
    }

    /**
     * 批量删除漏洞利用情报
     *
     * @param ids 需要删除的漏洞利用情报主键
     * @return 结果
     */
    @Override
    public int deleteVulnExploitInfoByIds(Integer[] ids) {
        return vulnExploitInfoMapper.deleteVulnExploitInfoByIds(ids);
    }

    /**
     * 删除漏洞利用情报信息
     *
     * @param id 漏洞利用情报主键
     * @return 结果
     */
    @Override
    public int deleteVulnExploitInfoById(Integer id) {
        return vulnExploitInfoMapper.deleteVulnExploitInfoById(id);
    }

    /**
     * 根据漏洞ID查询相关利用情报
     *
     * @param vulnId 漏洞ID
     * @return 漏洞利用情报集合
     */
    @Override
    public List<VulnExploitInfo> selectVulnExploitInfoByVulnId(Integer vulnId) {
        return vulnExploitInfoMapper.selectVulnExploitInfoByVulnId(vulnId);
    }
}
