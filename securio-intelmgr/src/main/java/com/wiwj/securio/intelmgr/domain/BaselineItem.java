package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 基线条目对象 baseline_item
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public class BaselineItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 基线条目ID */
    private String id;

    /** 所属基线分类ID */
    @Excel(name = "所属基线分类ID")
    private String setId;

    /** 基线条目名称 */
    @Excel(name = "基线条目名称")
    private String name;

    /** 基线条目类型 */
    @Excel(name = "基线条目类型")
    private String type;

    /** 基线条目级别 */
    @Excel(name = "基线条目级别")
    private Long level;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Integer enable;

    /** 标签列表 */
    @Excel(name = "标签列表")
    private String tags;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 审计方法 */
    @Excel(name = "审计方法")
    private String audit;

    /** 风险说明 */
    @Excel(name = "风险说明")
    private String risk;

    /** 修复方法 */
    @Excel(name = "修复方法")
    private String remediation;

    /** 额外信息 */
    @Excel(name = "额外信息")
    private String extra;

    /** 超时时间 */
    @Excel(name = "超时时间")
    private Long timeout;

    /** 是否系统内置 */
    @Excel(name = "是否系统内置")
    private Integer isSystem;

    /** 参数模式 */
    @Excel(name = "参数模式")
    private String argsSchema;

    /** 参数值 */
    @Excel(name = "参数值")
    private String args;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(String id){
        this.id = id;
    }

    public String getId(){
        return id;
    }
    public void setSetId(String setId){
        this.setId = setId;
    }

    public String getSetId(){
        return setId;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }
    public void setType(String type){
        this.type = type;
    }

    public String getType(){
        return type;
    }
    public void setLevel(Long level){
        this.level = level;
    }

    public Long getLevel(){
        return level;
    }
    public void setEnable(Integer enable){
        this.enable = enable;
    }

    public Integer getEnable(){
        return enable;
    }
    public void setTags(String tags){
        this.tags = tags;
    }

    public String getTags(){
        return tags;
    }
    public void setDescription(String description){
        this.description = description;
    }

    public String getDescription(){
        return description;
    }
    public void setAudit(String audit){
        this.audit = audit;
    }

    public String getAudit(){
        return audit;
    }
    public void setRisk(String risk){
        this.risk = risk;
    }

    public String getRisk(){
        return risk;
    }
    public void setRemediation(String remediation){
        this.remediation = remediation;
    }

    public String getRemediation(){
        return remediation;
    }
    public void setExtra(String extra){
        this.extra = extra;
    }

    public String getExtra(){
        return extra;
    }
    public void setTimeout(Long timeout){
        this.timeout = timeout;
    }

    public Long getTimeout(){
        return timeout;
    }
    public void setIsSystem(Integer isSystem){
        this.isSystem = isSystem;
    }

    public Integer getIsSystem(){
        return isSystem;
    }
    public void setArgsSchema(String argsSchema){
        this.argsSchema = argsSchema;
    }

    public String getArgsSchema(){
        return argsSchema;
    }
    public void setArgs(String args){
        this.args = args;
    }

    public String getArgs(){
        return args;
    }
    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt){
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("setId", getSetId())
            .append("name", getName())
            .append("type", getType())
            .append("level", getLevel())
            .append("enable", getEnable())
            .append("tags", getTags())
            .append("description", getDescription())
            .append("audit", getAudit())
            .append("risk", getRisk())
            .append("remediation", getRemediation())
            .append("extra", getExtra())
            .append("timeout", getTimeout())
            .append("isSystem", getIsSystem())
            .append("argsSchema", getArgsSchema())
            .append("args", getArgs())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
