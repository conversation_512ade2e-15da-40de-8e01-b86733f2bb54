package com.wiwj.securio.intelmgr.mapper;

import java.util.List;
import com.wiwj.securio.intelmgr.domain.VulnReference;

/**
 * 漏洞参考链接Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface VulnReferenceMapper {
    /**
     * 查询漏洞参考链接
     *
     * @param id 漏洞参考链接主键
     * @return 漏洞参考链接
     */
    public VulnReference selectVulnReferenceById(Integer id);

    /**
     * 查询漏洞参考链接列表
     *
     * @param vulnReference 漏洞参考链接
     * @return 漏洞参考链接集合
     */
    public List<VulnReference> selectVulnReferenceList(VulnReference vulnReference);

    /**
     * 新增漏洞参考链接
     *
     * @param vulnReference 漏洞参考链接
     * @return 结果
     */
    public int insertVulnReference(VulnReference vulnReference);

    /**
     * 修改漏洞参考链接
     *
     * @param vulnReference 漏洞参考链接
     * @return 结果
     */
    public int updateVulnReference(VulnReference vulnReference);

    /**
     * 删除漏洞参考链接
     *
     * @param id 漏洞参考链接主键
     * @return 结果
     */
    public int deleteVulnReferenceById(Integer id);

    /**
     * 批量删除漏洞参考链接
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVulnReferenceByIds(Integer[] ids);

    /**
     * 根据漏洞ID查询相关参考链接
     *
     * @param vulnId 漏洞ID
     * @return 漏洞参考链接集合
     */
    public List<VulnReference> selectVulnReferenceByVulnId(Integer vulnId);
}
