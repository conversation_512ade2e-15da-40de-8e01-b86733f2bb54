package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 漏洞利用情报对象 vuln_exploit_info
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public class VulnExploitInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    /** 漏洞ID */
    @Excel(name = "漏洞ID")
    private Integer vulnId;

    /** 情报来源 */
    @Excel(name = "情报来源")
    private String source;

    /** 情报标题 */
    @Excel(name = "情报标题")
    private String title;

    /** 情报链接 */
    @Excel(name = "情报链接")
    private String link;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    public void setId(Integer id){
        this.id = id;
    }

    public Integer getId(){
        return id;
    }
    public void setVulnId(Integer vulnId){
        this.vulnId = vulnId;
    }

    public Integer getVulnId(){
        return vulnId;
    }
    public void setSource(String source){
        this.source = source;
    }

    public String getSource(){
        return source;
    }
    public void setTitle(String title){
        this.title = title;
    }

    public String getTitle(){
        return title;
    }
    public void setLink(String link){
        this.link = link;
    }

    public String getLink(){
        return link;
    }
    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("vulnId", getVulnId())
            .append("source", getSource())
            .append("title", getTitle())
            .append("link", getLink())
            .append("createdAt", getCreatedAt())
            .toString();
    }
}
