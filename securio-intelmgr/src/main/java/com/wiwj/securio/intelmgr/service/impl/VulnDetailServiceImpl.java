package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.VulnDetailMapper;
import com.wiwj.securio.intelmgr.domain.VulnDetail;
import com.wiwj.securio.intelmgr.service.IVulnDetailService;

/**
 * 漏洞详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class VulnDetailServiceImpl implements IVulnDetailService {
    @Autowired
    private VulnDetailMapper vulnDetailMapper;

    /**
     * 查询漏洞详情
     *
     * @param id 漏洞详情主键
     * @return 漏洞详情
     */
    @Override
    public VulnDetail selectVulnDetailById(Integer id) {
        return vulnDetailMapper.selectVulnDetailById(id);
    }

    /**
     * 查询漏洞详情列表
     *
     * @param vulnDetail 漏洞详情
     * @return 漏洞详情
     */
    @Override
    public List<VulnDetail> selectVulnDetailList(VulnDetail vulnDetail) {
        return vulnDetailMapper.selectVulnDetailList(vulnDetail);
    }

    /**
     * 新增漏洞详情
     *
     * @param vulnDetail 漏洞详情
     * @return 结果
     */
    @Override
    public int insertVulnDetail(VulnDetail vulnDetail) {
        return vulnDetailMapper.insertVulnDetail(vulnDetail);
    }

    /**
     * 修改漏洞详情
     *
     * @param vulnDetail 漏洞详情
     * @return 结果
     */
    @Override
    public int updateVulnDetail(VulnDetail vulnDetail) {
        return vulnDetailMapper.updateVulnDetail(vulnDetail);
    }

    /**
     * 批量删除漏洞详情
     *
     * @param ids 需要删除的漏洞详情主键
     * @return 结果
     */
    @Override
    public int deleteVulnDetailByIds(Integer[] ids) {
        return vulnDetailMapper.deleteVulnDetailByIds(ids);
    }

    /**
     * 删除漏洞详情信息
     *
     * @param id 漏洞详情主键
     * @return 结果
     */
    @Override
    public int deleteVulnDetailById(Integer id) {
        return vulnDetailMapper.deleteVulnDetailById(id);
    }
}
