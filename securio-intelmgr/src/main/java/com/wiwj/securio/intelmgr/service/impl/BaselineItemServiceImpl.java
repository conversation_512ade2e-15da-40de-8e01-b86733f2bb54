package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.BaselineItemMapper;
import com.wiwj.securio.intelmgr.domain.BaselineItem;
import com.wiwj.securio.intelmgr.service.IBaselineItemService;

/**
 * 基线条目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class BaselineItemServiceImpl implements IBaselineItemService {
    @Autowired
    private BaselineItemMapper baselineItemMapper;

    /**
     * 查询基线条目
     *
     * @param id 基线条目主键
     * @return 基线条目
     */
    @Override
    public BaselineItem selectBaselineItemById(String id) {
        return baselineItemMapper.selectBaselineItemById(id);
    }

    /**
     * 查询基线条目列表
     *
     * @param baselineItem 基线条目
     * @return 基线条目
     */
    @Override
    public List<BaselineItem> selectBaselineItemList(BaselineItem baselineItem) {
        return baselineItemMapper.selectBaselineItemList(baselineItem);
    }

    /**
     * 新增基线条目
     *
     * @param baselineItem 基线条目
     * @return 结果
     */
    @Override
    public int insertBaselineItem(BaselineItem baselineItem) {
        // 生成UUID作为主键
        baselineItem.setId(UUID.randomUUID().toString());
        return baselineItemMapper.insertBaselineItem(baselineItem);
    }

    /**
     * 修改基线条目
     *
     * @param baselineItem 基线条目
     * @return 结果
     */
    @Override
    public int updateBaselineItem(BaselineItem baselineItem) {
        return baselineItemMapper.updateBaselineItem(baselineItem);
    }

    /**
     * 批量删除基线条目
     *
     * @param ids 需要删除的基线条目主键
     * @return 结果
     */
    @Override
    public int deleteBaselineItemByIds(String[] ids) {
        return baselineItemMapper.deleteBaselineItemByIds(ids);
    }

    /**
     * 删除基线条目信息
     *
     * @param id 基线条目主键
     * @return 结果
     */
    @Override
    public int deleteBaselineItemById(String id) {
        return baselineItemMapper.deleteBaselineItemById(id);
    }
}
