package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 基线分类对象 baseline_set
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public class BaselineSet extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 基线分类ID */
    private String id;

    /** 基线分类名称 */
    @Excel(name = "基线分类名称")
    private String name;

    /** 备注说明 */
    @Excel(name = "备注说明")
    private String comment;

    /** 是否系统内置 */
    @Excel(name = "是否系统内置")
    private Integer isSystem;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private Long createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(String id){
        this.id = id;
    }

    public String getId(){
        return id;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }
    public void setComment(String comment){
        this.comment = comment;
    }

    public String getComment(){
        return comment;
    }
    public void setIsSystem(Integer isSystem){
        this.isSystem = isSystem;
    }

    public Integer getIsSystem(){
        return isSystem;
    }
    public void setCreatedAt(Long createdAt){
        this.createdAt = createdAt;
    }

    public Long getCreatedAt(){
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt){
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("comment", getComment())
            .append("isSystem", getIsSystem())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
