package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.BaselineItem;
import com.wiwj.securio.intelmgr.service.IBaselineItemService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 基线条目Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/intelmgr/baselineItem")
public class BaselineItemController extends BaseController {
    @Autowired
    private IBaselineItemService baselineItemService;

    /**
     * 查询基线条目列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @GetMapping("/list")
    public TableDataInfo list(BaselineItem baselineItem) {
        startPage();
        List<BaselineItem> list = baselineItemService.selectBaselineItemList(baselineItem);
        return getDataTable(list);
    }

    /**
     * 根据基线分类ID查询基线条目列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @GetMapping("/list/set/{setId}")
    public TableDataInfo listBySetId(@PathVariable("setId") String setId) {
        startPage();
        BaselineItem baselineItem = new BaselineItem();
        baselineItem.setSetId(setId);
        List<BaselineItem> list = baselineItemService.selectBaselineItemList(baselineItem);
        return getDataTable(list);
    }

    /**
     * 导出基线条目列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @Log(title = "基线条目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaselineItem baselineItem) {
        List<BaselineItem> list = baselineItemService.selectBaselineItemList(baselineItem);
        ExcelUtil<BaselineItem> util = new ExcelUtil<BaselineItem>(BaselineItem.class);
        util.exportExcel(response, list, "基线条目数据");
    }

    /**
     * 获取基线条目详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(baselineItemService.selectBaselineItemById(id));
    }

    /**
     * 新增基线条目
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @Log(title = "基线条目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaselineItem baselineItem) {
        return toAjax(baselineItemService.insertBaselineItem(baselineItem));
    }

    /**
     * 修改基线条目
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @Log(title = "基线条目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaselineItem baselineItem) {
        return toAjax(baselineItemService.updateBaselineItem(baselineItem));
    }

    /**
     * 删除基线条目
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:baseline')")
    @Log(title = "基线条目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(baselineItemService.deleteBaselineItemByIds(ids));
    }
}
