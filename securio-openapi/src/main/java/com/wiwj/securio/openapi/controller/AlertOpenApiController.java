package com.wiwj.securio.openapi.controller;

import com.alibaba.fastjson2.JSON;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.service.IAlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 告警开放接口Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/openapi/alert")
public class AlertOpenApiController {

    @Autowired
    private IAlertService alertService;

    /**
     * 接收告警
     *
     * @param alert 告警对象
     * @return 处理结果
     */
    @PostMapping("/push")
    public AjaxResult receiveAlert(@RequestBody Alert alert) {
        log.info("接收到告警: {}", JSON.toJSONString(alert));

        try {
            // 接收告警
            Map<String, Object> resultData = alertService.receiveAlert(alert);

            // 返回成功结果
            return AjaxResult.success("成功接收告警", resultData);
        } catch (Exception e) {
            log.error("接收告警时发生错误: {}", e.getMessage(), e);
            return AjaxResult.error("处理告警失败: " + e.getMessage());
        }
    }

    /**
     * 批量接收告警
     *
     * @param alerts 告警对象数组
     * @return 处理结果
     */
    @PostMapping("/batch-receive")
    public AjaxResult receiveBatchAlerts(@RequestBody Alert[] alerts) {
        log.info("接收到批量告警, 数量: {}", alerts.length);

        try {
            // 批量接收告警
            Map<String, Object> resultData = alertService.receiveBatchAlerts(alerts);

            // 返回成功结果
            return AjaxResult.success("成功接收批量告警", resultData);
        } catch (Exception e) {
            log.error("批量接收告警时发生错误: {}", e.getMessage(), e);
            return AjaxResult.error("处理告警失败: " + e.getMessage());
        }
    }

    /**
     * 开发接口用于预览数据
     *
     * @param json
     * @param type
     * @return
     */
    @PostMapping("/testPush/{type}")
    public AjaxResult testPush(@RequestBody String json, @PathVariable String type) {
        log.info("接收 {} 类型测试告警: {}", type, json);

        return AjaxResult.success();
    }
}
