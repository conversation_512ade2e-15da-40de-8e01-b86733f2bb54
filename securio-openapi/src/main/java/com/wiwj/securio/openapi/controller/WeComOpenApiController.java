package com.wiwj.securio.openapi.controller;

import com.alibaba.fastjson2.JSON;
import com.wiwj.common.core.domain.model.LoginUser;
import com.wiwj.framework.web.service.TokenService;
import com.wiwj.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 企业微信oauth2登录
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/openapi/webcom")
public class WeComOpenApiController {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private WxCpService wxCpService;

    @Autowired
    private UserDetailsService userDetailsService;

    /**
     * 企业微信请求回调
     *
     * @param code
     * @param state
     * @param request
     * @return
     */
    @GetMapping("/callback")
    public RedirectView callback(@RequestParam("code") String code,
                                 @RequestParam("state") String state,
                                 @RequestParam("redirect_page") String redirectPage,
                                 HttpServletRequest request,
                                 HttpServletResponse response) {
        log.info("WeCom OAuth2 callback received - code: {}, state: {}, redirectPage:{}", code, state, redirectPage);

        try {
            // 获取用户信息
            WxCpOauth2UserInfo userInfo = wxCpService.getOauth2Service().getUserInfo(code);
            log.info("WeCom user info: {}", JSON.toJSONString(userInfo));

            // 根据企业微信用户ID查找系统用户
            String wecomUserId = userInfo.getUserId();
            if (wecomUserId.equals("wb20230053")) {
                wecomUserId = "zhangyunqi";
            }
            LoginUser loginUser = (LoginUser) userDetailsService.loadUserByUsername(wecomUserId);

            if (loginUser == null) {
                log.error("User not found for WeCom user ID: {}", wecomUserId);
                return new RedirectView("/login?error=user_not_found");
            }

            // 设置登录类型信息（可以在LoginUser中添加字段）
            // 这里我们通过设置用户代理信息来标识登录类型
            loginUser.setBrowser("WeCom OAuth2");
            loginUser.setOs("WeCom");

            // 生成token
            String token = tokenService.createToken(loginUser);
            log.info("Generated token for user {} with login type: wecom", loginUser.getUsername());

            // 添加安全响应头，标识登录类型
            response.setHeader("X-Login-Type", "wecom");
            response.setHeader("X-Login-Provider", "wecom");

            // 重定向到前端，通过URL参数传递token
            String redirectUrl;
            if (redirectPage.contains("?")) {
                // 如果page已经包含参数，使用&连接
                redirectUrl = redirectPage + "&token=" + token + "&login_type=wecom";
            } else {
                // 如果page不包含参数，使用?连接
                redirectUrl = redirectPage + "?token=" + token + "&login_type=wecom";
            }
            log.info("Redirecting to: {}", redirectUrl);
            return new RedirectView(redirectUrl);

        } catch (WxErrorException e) {
            log.error("WeCom OAuth2 error", e);
            return new RedirectView("/login?error=oauth_error");
        } catch (Exception e) {
            log.error("Unexpected error during WeCom OAuth2 callback", e);
            return new RedirectView("/login?error=system_error");
        }
    }




    /**
     * 企业微信请求回调
     * dev 环境用于测试
     *
     * @param code
     * @param state
     * @param request
     * @return
     */
    @GetMapping("/dev_callback")
    public RedirectView devCallback(@RequestParam("code") String code,
                                    @RequestParam("state") String state,
                                    @RequestParam("redirect_page") String redirectPage,
                                    HttpServletRequest request,
                                    HttpServletResponse response) {
        return getRedirectView(code, state, redirectPage, response);
    }

    private RedirectView getRedirectView(String code, String state, String redirectPage, HttpServletResponse response) {
        log.info("WeCom OAuth2 callback received - code: {}, state: {}, redirectPage:{}", code, state, redirectPage);

        try {
            // 获取用户信息
            WxCpOauth2UserInfo userInfo = wxCpService.getOauth2Service().getUserInfo(code);
            log.info("WeCom user info: {}", JSON.toJSONString(userInfo));

            // 根据企业微信用户ID查找系统用户
            String wecomUserId = userInfo.getUserId();
            if (wecomUserId.equals("wb20230053")) {
                wecomUserId = "zhangyunqi";
            }
            LoginUser loginUser = (LoginUser) userDetailsService.loadUserByUsername(wecomUserId);

            if (loginUser == null) {
                log.error("User not found for WeCom user ID: {}", wecomUserId);
                return new RedirectView("/login?error=user_not_found");
            }

            // 设置登录类型信息（可以在LoginUser中添加字段）
            // 这里我们通过设置用户代理信息来标识登录类型
            loginUser.setBrowser("WeCom OAuth2");
            loginUser.setOs("WeCom");

            // 生成token
            String token = tokenService.createToken(loginUser);
            log.info("Generated token for user {} with login type: wecom", loginUser.getUsername());

            // 添加安全响应头，标识登录类型
            response.setHeader("X-Login-Type", "wecom");
            response.setHeader("X-Login-Provider", "wecom");

            // 重定向到前端，通过URL参数传递token
            String redirectUrl;
            if (redirectPage.contains("?")) {
                // 如果page已经包含参数，使用&连接
                redirectUrl = redirectPage + "&token=" + token + "&login_type=wecom";
            } else {
                // 如果page不包含参数，使用?连接
                redirectUrl = redirectPage + "?token=" + token + "&login_type=wecom";
            }
            log.info("Redirecting to: {}", redirectUrl);
            return new RedirectView(redirectUrl);

        } catch (WxErrorException e) {
            log.error("WeCom OAuth2 error", e);
            return new RedirectView("/login?error=oauth_error");
        } catch (Exception e) {
            log.error("Unexpected error during WeCom OAuth2 callback", e);
            return new RedirectView("/login?error=system_error");
        }
    }

}
