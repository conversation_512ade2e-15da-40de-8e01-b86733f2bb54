package com.wiwj.securio.openapi.config;

import com.wiwj.securio.openapi.interceptor.OpenApiAuthInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new OpenApiAuthInterceptor())
                .addPathPatterns("/openapi/**").excludePathPatterns("/openapi/webcom/callback");
    }
}
