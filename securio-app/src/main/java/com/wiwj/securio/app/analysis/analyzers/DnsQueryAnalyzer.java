package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.logmgr.util.TimeFormatUtil;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * DNS查询分析器
 * 负责分析特定IP的DNS查询行为，包括查询域名分布、DNS服务器使用情况等
 *
 * <AUTHOR>
 */
@Component
public class DnsQueryAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(DnsQueryAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Override
    public String getName() {
        return "DNS查询行为分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.DNS_QUERY_ANALYSIS;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 支持所有IP类型的DNS查询分析
        return ipType != IpType.UNKNOWN && ipType != IpType.VPN_USER && ipType != IpType.PUBLIC;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行DNS查询分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建时间查询条件
            String startTimeStr = null;
            String endTimeStr = null;
            
            if (context.getStartTime() != null) {
                startTimeStr = context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            if (context.getEndTime() != null) {
                endTimeStr = context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            
            String timeQuery = TimeFormatUtil.buildTimeQuery(
                startTimeStr, 
                endTimeStr, 
                context.getTimeRange()
            );
            
            // 构建查询语句
            String query = String.format("%s and stream:\"AUDITLOG_DNS_BIND_QUERY\" and src_ip:\"%s\"", 
                timeQuery, context.getHostIp());
            
            logger.info("DNS查询分析查询语句: {}", query);
            
            // 使用facets接口获取统计数据
            Map<String, Object> facetsData = getFacetsStatistics(query, context.getHostIp());
            
            // 独立构建桑基图数据
            Map<String, Object> sankeyData = buildSankeyData(context.getHostIp(), timeQuery);
            
            // 分析DNS查询数据（不需要详细记录，只需要统计）
            Map<String, Object> dnsAnalysis = analyzeDnsData(null, facetsData, context.getHostIp());
            
            // 构建分析结果
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("targetIp", context.getHostIp());
            // 移除dnsRecords，数据量太大，使用日志组件展示
            // analysisData.put("dnsRecords", dnsRecords);
            analysisData.put("totalRecords", 0); // 从facets统计中获取总数
            analysisData.put("dnsAnalysis", dnsAnalysis);
            analysisData.put("facetsData", facetsData);
            analysisData.put("sankeyData", sankeyData); // 独立的桑基图数据
            
            // 添加时间信息供前端组件构建LogQL使用
            if (context.getStartTime() != null) {
                analysisData.put("startTime", context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getEndTime() != null) {
                analysisData.put("endTime", context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getTimeRange() != null) {
                analysisData.put("timeRange", context.getTimeRange());
            }
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(context.getHostIp(), null, dnsAnalysis);
            analysisData.put("analysisSummary", summary);
            
            // 时间范围描述信息
            analysisData.put("timeRangeDescription", getTimeRangeDescription(context));
            
            logger.info("DNS查询分析完成，主机IP: {}, 记录数: {}", 
                context.getHostIp(), 0);
                
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("DNS查询分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "DNS查询分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // DNS查询分析优先级为4
        return 4;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间8秒
        return 8000;
    }
    
    /**
     * 使用facets接口获取统计数据
     */
    private Map<String, Object> getFacetsStatistics(String baseQuery, String targetIp) {
        Map<String, Object> facetsData = new HashMap<>();
        
        try {
            // 调用facets接口获取统计数据
            String facetsResult = victoriaLogsService.facets(baseQuery, 50, null);
            
            if (facetsResult != null && !facetsResult.trim().isEmpty()) {
                JSONObject facetsJson = JSON.parseObject(facetsResult);
                
                if (facetsJson != null && facetsJson.containsKey("facets")) {
                    JSONArray facets = facetsJson.getJSONArray("facets");
                    
                    // 解析感兴趣的字段
                    for (int i = 0; i < facets.size(); i++) {
                        JSONObject facet = facets.getJSONObject(i);
                        String fieldName = facet.getString("field_name");
                        
                        if ("dst_ip".equals(fieldName)) {
                            facetsData.put("dnsServers", parseFacetValues(facet));
                        } else if ("query_domain".equals(fieldName)) {
                            facetsData.put("queryDomains", parseFacetValues(facet));
                        } else if ("query_type".equals(fieldName)) {
                            facetsData.put("queryTypes", parseFacetValues(facet));
                        } else if ("event_time".equals(fieldName)) {
                            facetsData.put("timeDistribution", parseFacetValues(facet));
                        }
                    }
                }
            }
            
            logger.info("获取到DNS查询facets统计数据，目标IP: {}", targetIp);
            
        } catch (Exception e) {
            logger.error("获取DNS查询facets统计数据失败", e);
        }
        
        return facetsData;
    }
    
    /**
     * 解析facet字段值
     */
    private List<Map<String, Object>> parseFacetValues(JSONObject facet) {
        List<Map<String, Object>> values = new ArrayList<>();
        
        try {
            JSONArray valuesArray = facet.getJSONArray("values");
            for (int i = 0; i < valuesArray.size(); i++) {
                JSONObject valueObj = valuesArray.getJSONObject(i);
                Map<String, Object> valueMap = new HashMap<>();
                valueMap.put("name", valueObj.getString("field_value"));
                valueMap.put("value", valueObj.getInteger("hits"));
                values.add(valueMap);
            }
        } catch (Exception e) {
            logger.debug("解析facet值失败", e);
        }
        
        return values;
    }
    
    /**
     * 分析DNS查询数据
     */
    private Map<String, Object> analyzeDnsData(List<Map<String, Object>> records, 
                                               Map<String, Object> facetsData, String targetIp) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 从facets数据中获取统计信息
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> dnsServers = (List<Map<String, Object>>) facetsData.get("dnsServers");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> queryDomains = (List<Map<String, Object>>) facetsData.get("queryDomains");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> queryTypes = (List<Map<String, Object>>) facetsData.get("queryTypes");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> timeDistribution = (List<Map<String, Object>>) facetsData.get("timeDistribution");
        
        // 计算总查询次数
        int totalQueries = 0;
        if (queryDomains != null) {
            totalQueries = queryDomains.stream().mapToInt(item -> (Integer) item.get("value")).sum();
        }
        
        analysis.put("totalRecords", records != null ? records.size() : totalQueries);
        analysis.put("totalQueries", totalQueries);
        
        // 计算唯一值数量
        analysis.put("uniqueDnsServers", dnsServers != null ? dnsServers.size() : 0);
        analysis.put("uniqueQueryDomains", queryDomains != null ? queryDomains.size() : 0);
        analysis.put("uniqueQueryTypes", queryTypes != null ? queryTypes.size() : 0);
        
        // 设置各种统计数据
        analysis.put("dnsServers", dnsServers != null ? dnsServers : new ArrayList<>());
        analysis.put("queryDomains", queryDomains != null ? queryDomains : new ArrayList<>());
        analysis.put("queryTypes", queryTypes != null ? queryTypes : new ArrayList<>());
        analysis.put("timeDistribution", timeDistribution != null ? timeDistribution : new ArrayList<>());
        
        // 分析查询行为特征
        analysis.put("behaviorCharacteristics", analyzeBehaviorCharacteristics(queryDomains, queryTypes));
        
        return analysis;
    }
    
    /**
     * 分析查询行为特征
     */
    private Map<String, Object> analyzeBehaviorCharacteristics(List<Map<String, Object>> queryDomains,
                                                               List<Map<String, Object>> queryTypes) {
        Map<String, Object> characteristics = new HashMap<>();
        
        if (queryTypes != null && !queryTypes.isEmpty()) {
            // 分析查询类型分布
            Map<String, Object> typeAnalysis = new HashMap<>();
            for (Map<String, Object> type : queryTypes) {
                typeAnalysis.put((String) type.get("name"), type.get("value"));
            }
            characteristics.put("queryTypeDistribution", typeAnalysis);
        }
        
        return characteristics;
    }
    
    /**
     * 生成分析摘要
     */
    private String generateAnalysisSummary(String targetIp, List<Map<String, Object>> records, 
                                           Map<String, Object> analysis) {
        Integer totalQueries = (Integer) analysis.get("totalQueries");
        if (totalQueries == null || totalQueries == 0) {
            return String.format("未发现IP %s 的DNS查询记录。可能该IP在查询时间范围内未进行DNS查询活动。", targetIp);
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("在指定时间范围内，IP %s 共进行了 %d 次DNS查询。", 
            targetIp, totalQueries));
        
        Integer uniqueDomains = (Integer) analysis.get("uniqueQueryDomains");
        Integer uniqueServers = (Integer) analysis.get("uniqueDnsServers");
        Integer uniqueTypes = (Integer) analysis.get("uniqueQueryTypes");
        
        if (uniqueDomains != null && uniqueDomains > 0) {
            summary.append(String.format(" 涉及 %d 个不同的域名", uniqueDomains));
        }
        
        if (uniqueServers != null && uniqueServers > 0) {
            summary.append(String.format("，使用了 %d 个DNS服务器", uniqueServers));
        }
        
        if (uniqueTypes != null && uniqueTypes > 0) {
            summary.append(String.format("，查询类型包括 %d 种", uniqueTypes));
        }
        
        summary.append("。");
        
        return summary.toString();
    }
    
    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(AnalysisContext context) {
        if (context.getTimeRange() != null) {
            return "最近" + context.getTimeRange();
        } else if (context.getStartTime() != null && context.getEndTime() != null) {
            return String.format("%s 至 %s", 
                context.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                context.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            return "未指定时间范围";
        }
    }
    
    /**
     * 构建DNS查询桑基图数据
     * 展示DNS查询流向：来源IP -> 分析IP -> DNS服务器 以及 反向查询
     */
    private Map<String, Object> buildSankeyData(String targetIp, String baseTimeQuery) {
        Map<String, Object> sankeyData = new HashMap<>();
        List<Map<String, Object>> nodes = new ArrayList<>();
        List<Map<String, Object>> links = new ArrayList<>();
        
        try {
            // 1. 查询该IP作为源IP发起的DNS查询（到哪些DNS服务器）
            String outboundQuery = String.format("%s and stream:\"AUDITLOG_DNS_BIND_QUERY\" and src_ip:\"%s\" | stats by (dst_ip) count() cnt | sort by (cnt desc) | limit 10", 
                baseTimeQuery, targetIp);
            
            logger.debug("DNS桑基图出站查询: {}", outboundQuery);
            String outboundResult = victoriaLogsService.queryLogs(outboundQuery, null, null, null);
            
            // 2. 查询该IP作为DNS服务器被查询的情况（哪些IP查询它）
            String inboundQuery = String.format("%s and stream:\"AUDITLOG_DNS_BIND_QUERY\" and dst_ip:\"%s\" | stats by (src_ip) count() cnt | sort by (cnt desc) | limit 10", 
                baseTimeQuery, targetIp);
            
            logger.debug("DNS桑基图入站查询: {}", inboundQuery);
            String inboundResult = victoriaLogsService.queryLogs(inboundQuery, null, null, null);
            
            // 添加中心节点（分析的目标IP）
            Map<String, Object> centerNode = new HashMap<>();
            centerNode.put("name", targetIp); // 只保留IP地址
            centerNode.put("type", "target");
            nodes.add(centerNode);
            
            // 解析出站查询结果（该IP查询的DNS服务器）
            if (outboundResult != null && !outboundResult.trim().isEmpty()) {
                String[] outboundLines = outboundResult.trim().split("\n");
                for (String line : outboundLines) {
                    try {
                        JSONObject outboundRecord = JSON.parseObject(line);
                        String dnsServerIp = outboundRecord.getString("dst_ip");
                        Integer count = Integer.parseInt(outboundRecord.getString("cnt"));
                        
                        if (dnsServerIp != null && !dnsServerIp.equals(targetIp)) {
                            // 添加DNS服务器节点
                            Map<String, Object> dnsServerNode = new HashMap<>();
                            dnsServerNode.put("name", dnsServerIp); // 只保留IP地址
                            dnsServerNode.put("type", "upstream");
                            nodes.add(dnsServerNode);
                            
                            // 添加连接（分析IP -> DNS服务器）
                            Map<String, Object> link = new HashMap<>();
                            link.put("source", centerNode.get("name"));
                            link.put("target", dnsServerNode.get("name"));
                            link.put("value", count);
                            links.add(link);
                        }
                    } catch (Exception e) {
                        logger.debug("解析DNS出站查询记录失败: {}", line, e);
                    }
                }
            }
            
            // 解析入站查询结果（查询该IP作为DNS服务器的来源IP）
            if (inboundResult != null && !inboundResult.trim().isEmpty()) {
                String[] inboundLines = inboundResult.trim().split("\n");
                for (String line : inboundLines) {
                    try {
                        JSONObject inboundRecord = JSON.parseObject(line);
                        String sourceIp = inboundRecord.getString("src_ip");
                        Integer count = Integer.parseInt(inboundRecord.getString("cnt"));
                        
                        if (sourceIp != null && !sourceIp.equals(targetIp)) {
                            // 添加来源IP节点
                            Map<String, Object> sourceNode = new HashMap<>();
                            sourceNode.put("name", sourceIp); // 只保留IP地址
                            sourceNode.put("type", "source");
                            nodes.add(sourceNode);
                            
                            // 添加连接（来源IP -> 分析IP）
                            Map<String, Object> link = new HashMap<>();
                            link.put("source", sourceNode.get("name"));
                            link.put("target", centerNode.get("name"));
                            link.put("value", count);
                            links.add(link);
                        }
                    } catch (Exception e) {
                        logger.debug("解析DNS入站查询记录失败: {}", line, e);
                    }
                }
            }
            
            sankeyData.put("nodes", nodes);
            sankeyData.put("links", links);
            
            logger.info("构建DNS桑基图数据完成，节点数: {}, 连接数: {}", nodes.size(), links.size());
            
        } catch (Exception e) {
            logger.error("构建DNS桑基图数据失败", e);
        }
        
        return sankeyData;
    }
} 