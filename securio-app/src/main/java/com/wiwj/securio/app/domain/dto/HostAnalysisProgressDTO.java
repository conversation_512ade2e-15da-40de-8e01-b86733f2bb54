package com.wiwj.securio.app.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 主机分析进度DTO
 *
 * <AUTHOR>
 */
@ApiModel(description = "主机分析进度信息")
public class HostAnalysisProgressDTO {

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "当前步骤")
    private String currentStep;

    @ApiModelProperty(value = "步骤描述")
    private String stepDescription;

    @ApiModelProperty(value = "进度百分比 (0-100)")
    private Integer progress;

    @ApiModelProperty(value = "状态: RUNNING, COMPLETED, ERROR")
    private String status;

    @ApiModelProperty(value = "消息内容")
    private String message;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "时间戳")
    private Long timestamp;

    @ApiModelProperty(value = "分析结果（仅在完成时返回）")
    private HostAnalysisResponseDTO result;

    public HostAnalysisProgressDTO() {
        this.timestamp = System.currentTimeMillis();
    }

    public HostAnalysisProgressDTO(String taskId, String currentStep, String stepDescription, Integer progress, String status, String message) {
        this();
        this.taskId = taskId;
        this.currentStep = currentStep;
        this.stepDescription = stepDescription;
        this.progress = progress;
        this.status = status;
        this.message = message;
    }

    // Getters and Setters
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getCurrentStep() {
        return currentStep;
    }

    public void setCurrentStep(String currentStep) {
        this.currentStep = currentStep;
    }

    public String getStepDescription() {
        return stepDescription;
    }

    public void setStepDescription(String stepDescription) {
        this.stepDescription = stepDescription;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public HostAnalysisResponseDTO getResult() {
        return result;
    }

    public void setResult(HostAnalysisResponseDTO result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "HostAnalysisProgressDTO{" +
                "taskId='" + taskId + '\'' +
                ", currentStep='" + currentStep + '\'' +
                ", stepDescription='" + stepDescription + '\'' +
                ", progress=" + progress +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
} 