package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.app.domain.dto.HostAnalysisResponseDTO;
import com.wiwj.securio.app.dto.IpLocationInfo;
import com.wiwj.securio.app.service.HostAnalysisService;
import com.wiwj.securio.app.service.IpLocationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 主机基本信息分析器
 * 负责获取和分析主机的基本信息
 *
 * <AUTHOR>
 */
@Component
public class HostBasicInfoAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(HostBasicInfoAnalyzer.class);
    
    @Autowired
    private HostAnalysisService hostAnalysisService;
    
    @Autowired
    private IpLocationService ipLocationService;
    
    @Override
    public String getName() {
        return "主机基本信息分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.HOST_BASIC_INFO;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 办公区客户端和VPN用户不执行主机基本信息分析，改用AC访问日志分析
        return ipType != IpType.UNKNOWN && ipType != IpType.OFFICE_CLIENT && ipType != IpType.VPN_USER;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行主机基本信息分析，主机IP: {}，IP类型: {}", context.getHostIp(), context.getIpType());
        
        try {
            // 构建包含IP归属信息的结果
            Map<String, Object> enhancedResult = new HashMap<>();
            
            // 添加IP类型信息到结果中
            enhancedResult.put("ipType", context.getIpType().getCode());
            
            // 根据IP类型决定处理逻辑
            if (context.getIpType() == IpType.PUBLIC) {
                // 外网IP：只获取和返回IP归属信息
                logger.info("检测到外网IP，仅获取IP归属信息: {}", context.getHostIp());
                try {
                    IpLocationInfo locationInfo = ipLocationService.getIpLocation(context.getHostIp());
                    if (locationInfo != null) {
                        enhancedResult.put("ipLocationInfo", locationInfo);
                        logger.info("成功获取外网IP归属信息: {}, 国家: {}, 城市: {}, ISP: {}", 
                                context.getHostIp(), 
                                locationInfo.getCountry(), 
                                locationInfo.getCity(), 
                                locationInfo.getIsp());
                    } else {
                        logger.warn("未能获取外网IP归属信息: {}", context.getHostIp());
                        enhancedResult.put("ipLocationInfo", null);
                        enhancedResult.put("ipLocationError", "获取IP归属信息失败");
                    }
                } catch (Exception e) {
                    logger.error("获取外网IP归属信息时发生异常: {}", context.getHostIp(), e);
                    enhancedResult.put("ipLocationInfo", null);
                    enhancedResult.put("ipLocationError", "获取IP归属信息失败: " + e.getMessage());
                }
                
                // 外网IP不设置hostBasicInfo
                enhancedResult.put("hostBasicInfo", null);
                
            } else {
                // 内网IP：获取完整的主机基本信息
                logger.info("检测到内网IP，获取完整主机基本信息: {}", context.getHostIp());
                
                HostAnalysisResponseDTO.HostBasicInfoDTO basicInfo = 
                    hostAnalysisService.getHostBasicInfo(context.getHostIp());
                enhancedResult.put("hostBasicInfo", basicInfo);
                
                // 对于内网IP，创建基本的位置信息标识
                IpLocationInfo internalIpInfo = new IpLocationInfo(context.getHostIp());
                internalIpInfo.setCountry("内网");
                internalIpInfo.setRegionName("内网");
                internalIpInfo.setCity("内网");
                internalIpInfo.setApiSource("internal");
                enhancedResult.put("ipLocationInfo", internalIpInfo);
            }
            
            // 将结果缓存到上下文中，供其他分析器使用
            context.cacheResult("enhancedHostInfo", enhancedResult);
            
            logger.info("主机基本信息分析完成，主机IP: {}，IP类型: {}", context.getHostIp(), context.getIpType());
            return AnalysisResult.success(getType(), getName(), enhancedResult);
            
        } catch (Exception e) {
            logger.error("主机基本信息分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "获取主机基本信息失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // 基本信息分析优先级最高，因为其他分析器可能依赖这些信息
        return 1;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间5秒（增加IP归属信息查询时间）
        return 5000;
    }
} 