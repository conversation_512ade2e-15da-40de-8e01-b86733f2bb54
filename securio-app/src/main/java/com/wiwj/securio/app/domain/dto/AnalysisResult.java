package com.wiwj.securio.app.domain.dto;

import com.wiwj.securio.app.analysis.core.AnalyzerType;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 分析结果
 * 单个分析器的执行结果
 *
 * <AUTHOR>
 */
public class AnalysisResult {
    
    /** 分析器类型 */
    private AnalyzerType analyzerType;
    
    /** 分析器名称 */
    private String analyzerName;
    
    /** 是否成功 */
    private boolean success;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 分析数据 */
    private Object data;
    
    /** 执行时间（毫秒） */
    private long executionTime;
    
    /** 开始时间 */
    private LocalDateTime startTime;
    
    /** 结束时间 */
    private LocalDateTime endTime;
    
    /** 扩展信息 */
    private Map<String, Object> metadata;
    
    public AnalysisResult() {
    }
    
    public AnalysisResult(AnalyzerType analyzerType, String analyzerName) {
        this.analyzerType = analyzerType;
        this.analyzerName = analyzerName;
        this.startTime = LocalDateTime.now();
    }
    
    /**
     * 创建成功结果
     */
    public static AnalysisResult success(AnalyzerType analyzerType, String analyzerName, Object data) {
        AnalysisResult result = new AnalysisResult(analyzerType, analyzerName);
        result.success = true;
        result.data = data;
        result.endTime = LocalDateTime.now();
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static AnalysisResult failure(AnalyzerType analyzerType, String analyzerName, String errorMessage) {
        AnalysisResult result = new AnalysisResult(analyzerType, analyzerName);
        result.success = false;
        result.errorMessage = errorMessage;
        result.endTime = LocalDateTime.now();
        return result;
    }
    
    /**
     * 计算执行时间
     */
    public void calculateExecutionTime() {
        if (startTime != null && endTime != null) {
            this.executionTime = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }
    
    // Getters and Setters
    public AnalyzerType getAnalyzerType() {
        return analyzerType;
    }
    
    public void setAnalyzerType(AnalyzerType analyzerType) {
        this.analyzerType = analyzerType;
    }
    
    public String getAnalyzerName() {
        return analyzerName;
    }
    
    public void setAnalyzerName(String analyzerName) {
        this.analyzerName = analyzerName;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    
    public long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        calculateExecutionTime();
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
} 