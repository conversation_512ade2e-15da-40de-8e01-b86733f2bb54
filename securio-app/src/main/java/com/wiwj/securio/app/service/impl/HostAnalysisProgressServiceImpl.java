package com.wiwj.securio.app.service.impl;

import com.alibaba.fastjson2.JSON;
import com.wiwj.securio.app.domain.dto.HostAnalysisProgressDTO;
import com.wiwj.securio.app.service.HostAnalysisProgressService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 主机分析进度服务实现类
 *
 * <AUTHOR>
 */
@Service
public class HostAnalysisProgressServiceImpl implements HostAnalysisProgressService {

    private static final Logger logger = LoggerFactory.getLogger(HostAnalysisProgressServiceImpl.class);

    // 存储SSE连接
    private final ConcurrentHashMap<String, SseEmitter> connections = new ConcurrentHashMap<>();
    
    // 存储进度历史
    private final ConcurrentHashMap<String, List<HostAnalysisProgressDTO>> progressHistory = new ConcurrentHashMap<>();

    @Override
    public SseEmitter createConnection(String taskId) {
        logger.info("创建SSE连接，任务ID: {}", taskId);
        
        // 创建SSE连接，设置超时时间为45分钟（增加缓冲时间）
        SseEmitter emitter = new SseEmitter(45 * 60 * 1000L);
        
        // 设置连接完成和超时回调
        emitter.onCompletion(() -> {
            logger.info("SSE连接完成，任务ID: {}", taskId);
            connections.remove(taskId);
            // 清理历史记录以节省内存
            progressHistory.remove(taskId);
        });
        
        emitter.onTimeout(() -> {
            logger.warn("SSE连接超时，任务ID: {}", taskId);
            connections.remove(taskId);
            try {
                // 尝试发送超时消息
                SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                    .name("timeout")
                    .data("{\"error\":\"连接超时\",\"taskId\":\"" + taskId + "\"}")
                    .reconnectTime(3000);
                emitter.send(eventBuilder);
            } catch (Exception e) {
                logger.warn("发送超时消息失败: {}", e.getMessage());
            }
        });
        
        emitter.onError((throwable) -> {
            logger.error("SSE连接错误，任务ID: {}, 错误: {}", taskId, throwable.getMessage());
            connections.remove(taskId);
        });
        
        // 存储连接
        connections.put(taskId, emitter);
        
        // 初始化进度历史
        progressHistory.put(taskId, new CopyOnWriteArrayList<>());
        
        try {
            // 发送连接成功消息
            HostAnalysisProgressDTO initProgress = new HostAnalysisProgressDTO(
                taskId, "INIT", "连接建立成功", 0, "RUNNING", "SSE连接已建立，准备开始分析..."
            );
            
            // 发送SSE事件，确保正确的格式和刷新
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                .name("progress")
                .data(JSON.toJSONString(initProgress))
                .reconnectTime(3000)
                .comment("连接初始化"); // 添加注释以确保数据流
            
            emitter.send(eventBuilder);
            
            // 立即发送一个心跳消息确保连接稳定
            sendHeartbeat(emitter, taskId);
            
            // 保存到历史记录
            progressHistory.get(taskId).add(initProgress);
            
        } catch (IOException e) {
            logger.error("发送初始化消息失败，任务ID: {}, 错误: {}", taskId, e.getMessage());
            connections.remove(taskId);
            progressHistory.remove(taskId);
            throw new RuntimeException("SSE连接初始化失败", e);
        }
        
        return emitter;
    }

    @Override
    public void sendProgress(String taskId, HostAnalysisProgressDTO progress) {
        SseEmitter emitter = connections.get(taskId);
        if (emitter == null) {
            logger.warn("未找到SSE连接，任务ID: {}", taskId);
            return;
        }
        
        try {
            logger.debug("发送进度更新，任务ID: {}, 步骤: {}, 进度: {}%", 
                taskId, progress.getCurrentStep(), progress.getProgress());
            
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                .name("progress")
                .data(JSON.toJSONString(progress))
                .reconnectTime(3000);
            
            emitter.send(eventBuilder);
            
            // 保存到历史记录
            List<HostAnalysisProgressDTO> history = progressHistory.get(taskId);
            if (history != null) {
                history.add(progress);
            }
            
        } catch (IOException e) {
            logger.error("发送进度更新失败，任务ID: {}, 错误: {}", taskId, e.getMessage());
            connections.remove(taskId);
        }
    }

    @Override
    public void completeTask(String taskId, HostAnalysisProgressDTO progress) {
        SseEmitter emitter = connections.get(taskId);
        if (emitter == null) {
            logger.warn("未找到SSE连接，任务ID: {}", taskId);
            return;
        }
        
        try {
            logger.info("任务完成，任务ID: {}", taskId);
            
            progress.setStatus("COMPLETED");
            progress.setProgress(100);
            
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                .name("complete")
                .data(JSON.toJSONString(progress))
                .reconnectTime(3000);
            
            emitter.send(eventBuilder);
            
            // 保存到历史记录
            List<HostAnalysisProgressDTO> history = progressHistory.get(taskId);
            if (history != null) {
                history.add(progress);
            }
            
            // 完成连接
            emitter.complete();
            
        } catch (IOException e) {
            logger.error("发送完成消息失败，任务ID: {}, 错误: {}", taskId, e.getMessage());
        } finally {
            connections.remove(taskId);
        }
    }

    @Override
    public void errorTask(String taskId, HostAnalysisProgressDTO progress) {
        SseEmitter emitter = connections.get(taskId);
        if (emitter == null) {
            logger.warn("未找到SSE连接，任务ID: {}", taskId);
            return;
        }
        
        try {
            logger.error("任务出错，任务ID: {}, 错误: {}", taskId, progress.getErrorMessage());
            
            progress.setStatus("ERROR");
            
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                .name("error")
                .data(JSON.toJSONString(progress))
                .reconnectTime(3000);
            
            emitter.send(eventBuilder);
            
            // 保存到历史记录
            List<HostAnalysisProgressDTO> history = progressHistory.get(taskId);
            if (history != null) {
                history.add(progress);
            }
            
            // 完成连接
            emitter.complete();
            
        } catch (IOException e) {
            logger.error("发送错误消息失败，任务ID: {}, 错误: {}", taskId, e.getMessage());
        } finally {
            connections.remove(taskId);
        }
    }

    @Override
    public void removeConnection(String taskId) {
        logger.info("移除SSE连接，任务ID: {}", taskId);
        
        SseEmitter emitter = connections.remove(taskId);
        if (emitter != null) {
            try {
                emitter.complete();
            } catch (Exception e) {
                logger.warn("关闭SSE连接时出错，任务ID: {}, 错误: {}", taskId, e.getMessage());
            }
        }
    }

    @Override
    public List<HostAnalysisProgressDTO> getProgressHistory(String taskId) {
        List<HostAnalysisProgressDTO> history = progressHistory.get(taskId);
        return history != null ? new ArrayList<>(history) : new ArrayList<>();
    }
    
    /**
     * 发送心跳消息以确保SSE连接稳定
     */
    private void sendHeartbeat(SseEmitter emitter, String taskId) {
        try {
            SseEmitter.SseEventBuilder heartbeatEvent = SseEmitter.event()
                .name("heartbeat")
                .data("{\"type\":\"heartbeat\",\"taskId\":\"" + taskId + "\",\"timestamp\":" + System.currentTimeMillis() + "}")
                .comment("心跳检测");
            emitter.send(heartbeatEvent);
            logger.debug("发送心跳消息，任务ID: {}", taskId);
        } catch (IOException e) {
            logger.warn("发送心跳消息失败，任务ID: {}, 错误: {}", taskId, e.getMessage());
        }
    }
} 