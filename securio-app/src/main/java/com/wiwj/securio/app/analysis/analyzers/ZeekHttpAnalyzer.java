package com.wiwj.securio.app.analysis.analyzers;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.agent.mapper.InventoryPortInfoMapper;
import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.app.domain.dto.IpBehaviorAnalysisResponseDTO;
import com.wiwj.securio.logmgr.util.TimeFormatUtil;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Zeek HTTP连接分析器
 * 负责分析IP的HTTP连接链路，构建桑基图数据
 *
 * <AUTHOR>
 */
@Component
public class ZeekHttpAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(ZeekHttpAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Autowired
    private InventoryPortInfoMapper inventoryPortInfoMapper;
    
    @Override
    public String getName() {
        return "Zeek HTTP连接分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.ZEEK_HTTP_ANALYSIS;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 支持除UNKNOWN外的所有IP类型
        return ipType != IpType.UNKNOWN;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行Zeek HTTP连接分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建分析结果
            IpBehaviorAnalysisResponseDTO.SankeyChartData sankeyData = buildSankeyData(context);
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(sankeyData, context.getHostIp());
            
            // 构建完整的响应数据
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("sankeyData", sankeyData);
            analysisData.put("analysisSummary", summary);
            analysisData.put("targetIp", context.getHostIp());
            analysisData.put("timeRange", getTimeRangeDescription(context));
            
            // 添加时间信息供前端组件构建LogQL使用
            if (context.getStartTime() != null) {
                analysisData.put("startTime", context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getEndTime() != null) {
                analysisData.put("endTime", context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getTimeRange() != null) {
                analysisData.put("timeRange", context.getTimeRange());
            }
            
            logger.info("Zeek HTTP连接分析完成，主机IP: {}, 节点数: {}, 连接数: {}", 
                context.getHostIp(), 
                sankeyData.getNodes().size(), 
                sankeyData.getLinks().size());
                
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("Zeek HTTP连接分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "Zeek HTTP连接分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // HTTP连接分析优先级为4
        return 4;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间12秒
        return 12000;
    }
    
    /**
     * 构建桑基图数据
     */
    private IpBehaviorAnalysisResponseDTO.SankeyChartData buildSankeyData(AnalysisContext context) {
        IpBehaviorAnalysisResponseDTO.SankeyChartData sankeyData = new IpBehaviorAnalysisResponseDTO.SankeyChartData();
        
        List<IpBehaviorAnalysisResponseDTO.SankeyNode> nodes = new ArrayList<>();
        List<IpBehaviorAnalysisResponseDTO.SankeyLink> links = new ArrayList<>();

        try {
            // 1. 获取出站HTTP连接数据（该IP作为源IP）
            List<Map<String, Object>> outboundData = getOutboundData(context);
            
            // 2. 获取入站HTTP连接数据（该IP作为目标IP）
            List<Map<String, Object>> inboundData = getInboundData(context);

            // 3. 收集所有IP地址用于批量查询hostname
            Set<String> allIps = new HashSet<>();
            allIps.add(context.getHostIp()); // 目标IP
            
            // 收集出站连接的目标IP
            for (Map<String, Object> item : outboundData) {
                String dstIp = (String) item.get("dst_ip");
                if (dstIp != null) {
                    allIps.add(dstIp);
                }
            }
            
            // 收集入站连接的源IP
            for (Map<String, Object> item : inboundData) {
                String srcIp = (String) item.get("src_ip");
                if (srcIp != null) {
                    allIps.add(srcIp);
                }
            }

            // 4. 批量查询IP对应的hostname
            Map<String, String> ipHostnameMap = getIpHostnameMap(allIps);

            // 5. 构建节点和连接
            Set<String> nodeNames = new HashSet<>();
            
            // 添加中心节点（分析的目标IP）
            String targetDisplayName = getDisplayName(context.getHostIp(), ipHostnameMap);
            nodes.add(new IpBehaviorAnalysisResponseDTO.SankeyNode(targetDisplayName, "target"));
            nodeNames.add(targetDisplayName);

            // 处理入站连接数据（其他IP -> 目标IP）
            for (Map<String, Object> item : inboundData) {
                String srcIp = (String) item.get("src_ip");
                Long connectionCount = (Long) item.get("connection_count");

                if (srcIp != null) {
                    String srcDisplayName = getDisplayName(srcIp, ipHostnameMap);
                    if (!nodeNames.contains(srcDisplayName)) {
                        nodes.add(new IpBehaviorAnalysisResponseDTO.SankeyNode(srcDisplayName, "source"));
                        nodeNames.add(srcDisplayName);
                    }
                    links.add(new IpBehaviorAnalysisResponseDTO.SankeyLink(srcDisplayName, targetDisplayName, connectionCount));
                }
            }

            // 处理出站连接数据（目标IP -> 其他IP）
            for (Map<String, Object> item : outboundData) {
                String dstIp = (String) item.get("dst_ip");
                Long connectionCount = (Long) item.get("connection_count");

                if (dstIp != null) {
                    String destDisplayName = getDisplayName(dstIp, ipHostnameMap);
                    if (!nodeNames.contains(destDisplayName)) {
                        nodes.add(new IpBehaviorAnalysisResponseDTO.SankeyNode(destDisplayName, "upstream"));
                        nodeNames.add(destDisplayName);
                    }
                    links.add(new IpBehaviorAnalysisResponseDTO.SankeyLink(targetDisplayName, destDisplayName, connectionCount));
                }
            }

            sankeyData.setNodes(nodes);
            sankeyData.setLinks(links);

            logger.info("Zeek HTTP桑基图数据构建完成，节点数: {}, 连接数: {}, hostname映射数: {}", 
                       nodes.size(), links.size(), ipHostnameMap.size());

        } catch (Exception e) {
            logger.error("构建Zeek HTTP桑基图数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("构建Zeek HTTP桑基图数据失败: " + e.getMessage(), e);
        }

        return sankeyData;
    }
    
    /**
     * 获取出站HTTP连接数据（该IP作为源IP）
     */
    private List<Map<String, Object>> getOutboundData(AnalysisContext context) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 构建时间查询
            String timeQuery = buildTimeQuery(context);
            
            // 构建查询语句
            String query = String.format("%s and stream:\"SECURIO_ZEEK_HTTP\" and src_ip:\"%s\" | stats by (dst_ip) count() cnt | sort by (cnt desc) | limit 10",
                    timeQuery, context.getHostIp());
            
            logger.debug("Zeek HTTP出站连接查询: {}", query);
            
            String queryResult = victoriaLogsService.queryLogs(query, null, null, "vmlog2");
            logger.debug("Zeek HTTP出站连接查询结果: {}", queryResult);
            
            if (queryResult != null && !queryResult.trim().isEmpty()) {
                String[] lines = queryResult.trim().split("\n");
                for (String line : lines) {
                    try {
                        JSONObject record = JSON.parseObject(line);
                        String dstIp = record.getString("dst_ip");
                        Long count = Long.parseLong(record.getString("cnt"));
                        
                        Map<String, Object> item = new HashMap<>();
                        item.put("dst_ip", dstIp);
                        item.put("connection_count", count);
                        result.add(item);
                        
                        logger.debug("出站HTTP连接: {} -> {}, 次数: {}", context.getHostIp(), dstIp, count);
                    } catch (Exception e) {
                        logger.debug("解析出站HTTP连接记录失败: {}", line, e);
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("获取出站HTTP连接数据失败", e);
        }

        return result;
    }
    
    /**
     * 获取入站HTTP连接数据（该IP作为目标IP）
     */
    private List<Map<String, Object>> getInboundData(AnalysisContext context) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 构建时间查询
            String timeQuery = buildTimeQuery(context);
            
            // 构建查询语句
            String query = String.format("%s and stream:\"SECURIO_ZEEK_HTTP\" and dst_ip:\"%s\" | stats by (src_ip) count() cnt | sort by (cnt desc) | limit 10",
                    timeQuery, context.getHostIp());
            
            logger.debug("Zeek HTTP入站连接查询: {}", query);
            
            String queryResult = victoriaLogsService.queryLogs(query, null, null, "vmlog2");
            logger.debug("Zeek HTTP入站连接查询结果: {}", queryResult);
            
            if (queryResult != null && !queryResult.trim().isEmpty()) {
                String[] lines = queryResult.trim().split("\n");
                for (String line : lines) {
                    try {
                        JSONObject record = JSON.parseObject(line);
                        String srcIp = record.getString("src_ip");
                        Long count = Long.parseLong(record.getString("cnt"));
                        
                        Map<String, Object> item = new HashMap<>();
                        item.put("src_ip", srcIp);
                        item.put("connection_count", count);
                        result.add(item);
                        
                        logger.debug("入站HTTP连接: {} -> {}, 次数: {}", srcIp, context.getHostIp(), count);
                    } catch (Exception e) {
                        logger.debug("解析入站HTTP连接记录失败: {}", line, e);
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("获取入站HTTP连接数据失败", e);
        }

        return result;
    }
    
    /**
     * 构建时间查询条件
     */
    private String buildTimeQuery(AnalysisContext context) {
        String startTimeStr = null;
        String endTimeStr = null;
        
        if (context.getStartTime() != null) {
            startTimeStr = context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
        if (context.getEndTime() != null) {
            endTimeStr = context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
        
        return TimeFormatUtil.buildTimeQuery(startTimeStr, endTimeStr, context.getTimeRange());
    }
    
    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(AnalysisContext context) {
        if (context.getTimeRange() != null) {
            return "最近" + context.getTimeRange();
        } else if (context.getStartTime() != null && context.getEndTime() != null) {
            return String.format("%s 至 %s", 
                context.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                context.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            return "未指定时间范围";
        }
    }
    
    /**
     * 生成分析摘要
     */
    private String generateAnalysisSummary(IpBehaviorAnalysisResponseDTO.SankeyChartData sankeyData, String targetIp) {
        if (sankeyData.getNodes().isEmpty() || sankeyData.getLinks().isEmpty()) {
            return String.format("在指定时间范围内，未发现IP %s 的HTTP连接记录。", targetIp);
        }

        StringBuilder summary = new StringBuilder();
        
        // 统计不同类型的连接
        long totalConnections = sankeyData.getLinks().stream().mapToLong(IpBehaviorAnalysisResponseDTO.SankeyLink::getValue).sum();
        long sourceNodes = sankeyData.getNodes().stream().filter(node -> "source".equals(node.getType())).count();
        long upstreamNodes = sankeyData.getNodes().stream().filter(node -> "upstream".equals(node.getType())).count();
        
        summary.append(String.format("在指定时间范围内，IP %s 共产生了 %d 个HTTP连接。", targetIp, totalConnections));
        
        if (sourceNodes > 0) {
            summary.append(String.format(" 接收来自 %d 个不同IP的HTTP请求", sourceNodes));
        }
        
        if (upstreamNodes > 0) {
            summary.append(String.format("，向 %d 个不同的目标服务器发起HTTP请求", upstreamNodes));
        }
        
        summary.append("。这反映了该IP的HTTP通信活跃度和网络访问模式。");
        
        return summary.toString();
    }
    
    /**
     * 批量查询IP对应的hostname
     */
    private Map<String, String> getIpHostnameMap(Set<String> ips) {
        Map<String, String> ipHostnameMap = new HashMap<>();
        
        try {
            // 批量查询主机信息
            List<Map<String, Object>> hostInfoList = inventoryPortInfoMapper.selectHostnameByIpList(ips);
            
            // 构建IP到hostname的映射
            for (Map<String, Object> hostInfo : hostInfoList) {
                String ip = (String) hostInfo.get("ip");
                String hostname = (String) hostInfo.get("hostname");
                if (ip != null && hostname != null && !hostname.trim().isEmpty()) {
                    ipHostnameMap.put(ip, hostname);
                }
            }
            
            logger.debug("批量查询得到 {} 个IP的hostname信息", ipHostnameMap.size());
        } catch (Exception e) {
            logger.warn("批量查询IP hostname信息失败", e);
        }
        
        return ipHostnameMap;
    }
    
    /**
     * 获取显示名称（hostname + IP 或 仅IP）
     */
    private String getDisplayName(String ip, Map<String, String> ipHostnameMap) {
        String hostname = ipHostnameMap.get(ip);
        if (hostname != null && !hostname.trim().isEmpty()) {
            return String.format("%s (%s)", hostname, ip);
        }
        return ip;
    }
} 