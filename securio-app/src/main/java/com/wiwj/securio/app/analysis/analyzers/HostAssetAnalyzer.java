package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.app.domain.dto.HostAnalysisResponseDTO;
import com.wiwj.securio.app.service.HostAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 主机资产分析器
 * 用于分析内网主机的资产信息，包括进程、端口、软件等
 *
 * <AUTHOR>
 */
@Component
public class HostAssetAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(HostAssetAnalyzer.class);
    
    @Autowired
    private HostAnalysisService hostAnalysisService;
    
    @Override
    public String getName() {
        return "主机资产分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.HOST_ASSET;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 支持服务器IP的资产分析，办公区客户端改用AC访问日志分析
        return ipType == IpType.SERVER;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行主机资产分析，主机IP: {}，IP类型: {}", context.getHostIp(), context.getIpType());
        
        try {
            // 调用主机分析服务获取资产信息
            HostAnalysisResponseDTO.HostAssetInfoDTO assetInfo = hostAnalysisService.getHostAssetInfo(context.getHostIp());
            
            if (assetInfo == null) {
                logger.warn("未获取到主机资产数据 - IP: {}", context.getHostIp());
                return AnalysisResult.failure(getType(), getName(), "未获取到主机资产数据");
            }

            // 构建分析结果
            Map<String, Object> result = new HashMap<>();
            result.put("hostIp", context.getHostIp());
            result.put("ipType", context.getIpType().getCode()); // 使用分析上下文中的IP类型
            
            // 构建资产统计数据
            Map<String, Object> assetStats = new HashMap<>();
            assetStats.put("users", assetInfo.getUserCount() != null ? assetInfo.getUserCount() : 0);
            assetStats.put("processes", assetInfo.getProcessCount() != null ? assetInfo.getProcessCount() : 0);
            assetStats.put("ports", assetInfo.getPortCount() != null ? assetInfo.getPortCount() : 0);
            assetStats.put("software", assetInfo.getSoftwareCount() != null ? assetInfo.getSoftwareCount() : 0);
            assetStats.put("startups", assetInfo.getStartupCount() != null ? assetInfo.getStartupCount() : 0);
            assetStats.put("scheduledTasks", assetInfo.getScheduledTaskCount() != null ? assetInfo.getScheduledTaskCount() : 0);
            assetStats.put("kernelModules", assetInfo.getKernelModuleCount() != null ? assetInfo.getKernelModuleCount() : 0);
            assetStats.put("networkInterfaces", assetInfo.getNetworkInterfaceCount() != null ? assetInfo.getNetworkInterfaceCount() : 0);
            
            // 以下字段在当前HostAssetInfoDTO中不存在，设为默认值0
            assetStats.put("dockerImages", 0);
            assetStats.put("dockerContainers", 0);
            assetStats.put("dockerNetworks", 0);
            assetStats.put("risks", 0);
            
            result.put("assetStats", assetStats);
            
            // 计算总资产数
            int totalAssets = calculateTotalAssets(assetStats);
            result.put("totalAssets", totalAssets);
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(context.getHostIp(), assetStats, totalAssets);
            result.put("analysisSummary", summary);
            
            // 添加详细信息
            if (assetInfo.getDetailInfo() != null) {
                result.put("detailInfo", assetInfo.getDetailInfo());
            }
            
            logger.info("主机资产分析完成 - IP: {}, 总资产数: {}", context.getHostIp(), totalAssets);
            return AnalysisResult.success(getType(), getName(), result);
            
        } catch (Exception e) {
            logger.error("主机资产分析失败 - IP: {}, 错误: {}", context.getHostIp(), e.getMessage(), e);
            return AnalysisResult.failure(getType(), getName(), "主机资产分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // 优先级中等，在基本信息分析之后执行
        return 5;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间3秒
        return 3000;
    }
    
    /**
     * 计算总资产数
     */
    private int calculateTotalAssets(Map<String, Object> assetStats) {
        int total = 0;
        
        // 统计各类资产数量
        total += getIntValue(assetStats, "users");
        total += getIntValue(assetStats, "processes");
        total += getIntValue(assetStats, "ports");
        total += getIntValue(assetStats, "software");
        total += getIntValue(assetStats, "startups");
        total += getIntValue(assetStats, "scheduledTasks");
        total += getIntValue(assetStats, "kernelModules");
        total += getIntValue(assetStats, "networkInterfaces");
        total += getIntValue(assetStats, "dockerImages");
        total += getIntValue(assetStats, "dockerContainers");
        total += getIntValue(assetStats, "dockerNetworks");
        
        return total;
    }
    
    /**
     * 生成分析摘要
     */
    private String generateAnalysisSummary(String hostIp, Map<String, Object> assetStats, int totalAssets) {
        StringBuilder summary = new StringBuilder();
        summary.append("主机 ").append(hostIp).append(" 的资产分析结果：");
        summary.append("共发现 ").append(totalAssets).append(" 项资产。");
        
        // 详细统计
        if (totalAssets > 0) {
            summary.append("其中包括：");
            appendAssetCount(summary, assetStats, "users", "用户");
            appendAssetCount(summary, assetStats, "processes", "进程");
            appendAssetCount(summary, assetStats, "ports", "网络连接");
            appendAssetCount(summary, assetStats, "software", "软件");
            appendAssetCount(summary, assetStats, "startups", "启动项");
            appendAssetCount(summary, assetStats, "scheduledTasks", "计划任务");
            appendAssetCount(summary, assetStats, "kernelModules", "内核模块");
            appendAssetCount(summary, assetStats, "networkInterfaces", "网络接口");
            appendAssetCount(summary, assetStats, "dockerImages", "Docker镜像");
            appendAssetCount(summary, assetStats, "dockerContainers", "Docker容器");
            appendAssetCount(summary, assetStats, "dockerNetworks", "Docker网络");
            
            // 移除最后的"、"
            if (summary.toString().endsWith("、")) {
                summary.setLength(summary.length() - 1);
                summary.append("。");
            }
        }
        
        // 风险评估
        int riskCount = getIntValue(assetStats, "risks");
        if (riskCount > 0) {
            summary.append("发现 ").append(riskCount).append(" 项风险需要关注。");
        } else {
            summary.append("未发现明显风险项。");
        }
        
        return summary.toString();
    }
    
    /**
     * 添加资产数量描述
     */
    private void appendAssetCount(StringBuilder summary, Map<String, Object> assetStats, String key, String label) {
        int count = getIntValue(assetStats, key);
        if (count > 0) {
            summary.append(count).append("个").append(label).append("、");
        }
    }
    
    /**
     * 安全获取整数值
     */
    private int getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }
} 