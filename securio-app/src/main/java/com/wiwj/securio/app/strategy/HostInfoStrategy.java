package com.wiwj.securio.app.strategy;

import com.wiwj.securio.app.domain.dto.HostAnalysisResponseDTO.HostBasicInfoDTO;

/**
 * 主机信息获取策略接口
 *
 * <AUTHOR>
 */
public interface HostInfoStrategy {
    
    /**
     * 获取主机基本信息
     *
     * @param hostIp 主机IP地址
     * @return 主机基本信息
     */
    HostBasicInfoDTO getHostBasicInfo(String hostIp);
    
    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();
} 