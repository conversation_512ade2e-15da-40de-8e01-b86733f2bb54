package com.wiwj.securio.app.analysis.core;

/**
 * IP类型枚举
 * 根据IP地址段判断IP的类型，用于分发到不同的分析器组
 *
 * <AUTHOR>
 */
public enum IpType {
    
    /** 服务器IP - 10开头但不是10.1开头 */
    SERVER("服务器", "server"),
    
    /** 办公区客户端IP - 10.1开头 */
    OFFICE_CLIENT("办公区客户端", "office_client"),
    
    /** VPN用户IP - 10.250和10.251开头 */
    VPN_USER("VPN用户", "vpn_user"),
    
    /** 公网IP - 其他IP */
    PUBLIC("公网IP", "public"),
    
    /** 未知类型 */
    UNKNOWN("未知类型", "unknown");
    
    private final String description;
    private final String code;
    
    IpType(String description, String code) {
        this.description = description;
        this.code = code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getCode() {
        return code;
    }
    
    /**
     * 根据IP地址判断IP类型
     * @param ip IP地址
     * @return IP类型
     */
    public static IpType fromIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        try {
            if (ip.startsWith("10.1.")) {
                return OFFICE_CLIENT;
            } else if (ip.startsWith("10.250.") || ip.startsWith("10.251.")) {
                return VPN_USER;
            } else if (ip.startsWith("10.")) {
                return SERVER;
            } else {
                return PUBLIC;
            }
        } catch (Exception e) {
            return UNKNOWN;
        }
    }
} 