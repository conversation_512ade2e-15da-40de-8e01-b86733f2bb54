package com.wiwj.securio.app.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * IP行为分析响应DTO
 *
 * <AUTHOR>
 */
@ApiModel(description = "IP行为分析结果")
public class IpBehaviorAnalysisResponseDTO {

    @ApiModelProperty(value = "目标IP地址")
    private String targetIp;

    @ApiModelProperty(value = "分析时间范围")
    private String timeRange;

    @ApiModelProperty(value = "桑基图数据")
    private SankeyChartData sankeyData;

    @ApiModelProperty(value = "分析摘要")
    private String analysisSummary;

    @ApiModelProperty(value = "生成时间")
    private String generateTime;

    public IpBehaviorAnalysisResponseDTO() {
    }

    public String getTargetIp() {
        return targetIp;
    }

    public void setTargetIp(String targetIp) {
        this.targetIp = targetIp;
    }

    public String getTimeRange() {
        return timeRange;
    }

    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }

    public SankeyChartData getSankeyData() {
        return sankeyData;
    }

    public void setSankeyData(SankeyChartData sankeyData) {
        this.sankeyData = sankeyData;
    }

    public String getAnalysisSummary() {
        return analysisSummary;
    }

    public void setAnalysisSummary(String analysisSummary) {
        this.analysisSummary = analysisSummary;
    }

    public String getGenerateTime() {
        return generateTime;
    }

    public void setGenerateTime(String generateTime) {
        this.generateTime = generateTime;
    }

    /**
     * 桑基图数据
     */
    @ApiModel(description = "桑基图数据")
    public static class SankeyChartData {
        @ApiModelProperty(value = "节点列表")
        private List<SankeyNode> nodes;

        @ApiModelProperty(value = "连接列表")
        private List<SankeyLink> links;

        public SankeyChartData() {
        }

        public List<SankeyNode> getNodes() {
            return nodes;
        }

        public void setNodes(List<SankeyNode> nodes) {
            this.nodes = nodes;
        }

        public List<SankeyLink> getLinks() {
            return links;
        }

        public void setLinks(List<SankeyLink> links) {
            this.links = links;
        }
    }

    /**
     * 桑基图节点
     */
    @ApiModel(description = "桑基图节点")
    public static class SankeyNode {
        @ApiModelProperty(value = "节点名称")
        private String name;

        @ApiModelProperty(value = "节点类型", notes = "source, target, upstream")
        private String type;

        public SankeyNode() {
        }

        public SankeyNode(String name, String type) {
            this.name = name;
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }

    /**
     * 桑基图连接
     */
    @ApiModel(description = "桑基图连接")
    public static class SankeyLink {
        @ApiModelProperty(value = "源节点")
        private String source;

        @ApiModelProperty(value = "目标节点")
        private String target;

        @ApiModelProperty(value = "连接值")
        private Long value;

        public SankeyLink() {
        }

        public SankeyLink(String source, String target, Long value) {
            this.source = source;
            this.target = target;
            this.value = value;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public String getTarget() {
            return target;
        }

        public void setTarget(String target) {
            this.target = target;
        }

        public Long getValue() {
            return value;
        }

        public void setValue(Long value) {
            this.value = value;
        }
    }
} 