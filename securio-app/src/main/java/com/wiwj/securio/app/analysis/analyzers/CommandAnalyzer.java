package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.logmgr.util.TimeFormatUtil;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 主机命令分析器
 * 负责分析特定主机的命令执行日志，包括命令类型分布、执行频率等
 *
 * <AUTHOR>
 */
@Component
public class CommandAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(CommandAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Override
    public String getName() {
        return "主机命令行为分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.COMMAND_ANALYSIS;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 主要支持服务器IP的命令分析
        return ipType == IpType.SERVER;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行主机命令分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建时间查询条件
            String startTimeStr = null;
            String endTimeStr = null;
            
            if (context.getStartTime() != null) {
                startTimeStr = context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            if (context.getEndTime() != null) {
                endTimeStr = context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            
            String timeQuery = TimeFormatUtil.buildTimeQuery(
                startTimeStr, 
                endTimeStr, 
                context.getTimeRange()
            );
            
            // 构建查询语句 - 注意使用dst_ip字段
            String query = String.format("%s and stream:\"AUDITLOG_SYSLOG_COMMAND\" and agent_hostip:\"%s\"", 
                timeQuery, context.getHostIp());
            
            logger.info("主机命令分析查询语句: {}", query);
            
            // 使用facets接口获取统计数据
            Map<String, Object> facetsData = getFacetsStatistics(query, context.getHostIp());
            
            // 分析命令数据（只做统计分析，不返回原始日志）
            Map<String, Object> commandAnalysis = analyzeCommandData(null, facetsData, context.getHostIp());
            
            // 构建分析结果
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("targetIp", context.getHostIp());
            analysisData.put("totalRecords", 0); // 从facets统计中获取总数
            analysisData.put("commandAnalysis", commandAnalysis);
            analysisData.put("facetsData", facetsData);
            
            // 添加时间信息供前端组件构建LogQL使用
            if (context.getStartTime() != null) {
                analysisData.put("startTime", context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getEndTime() != null) {
                analysisData.put("endTime", context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getTimeRange() != null) {
                analysisData.put("timeRange", context.getTimeRange());
            }
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(context.getHostIp(), null, commandAnalysis);
            analysisData.put("analysisSummary", summary);
            
            // 时间范围描述信息
            analysisData.put("timeRangeDescription", getTimeRangeDescription(context));
            
            logger.info("主机命令分析完成，主机IP: {}", context.getHostIp());
                
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("主机命令分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "主机命令分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // 命令分析优先级为3
        return 3;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间6秒
        return 6000;
    }
    
    /**
     * 使用facets接口获取统计数据
     */
    private Map<String, Object> getFacetsStatistics(String baseQuery, String targetIp) {
        Map<String, Object> facetsData = new HashMap<>();
        
        try {
            // 调用facets接口获取统计数据
            String facetsResult = victoriaLogsService.facets(baseQuery, 50, null);
            
            if (facetsResult != null && !facetsResult.trim().isEmpty()) {
                JSONObject facetsJson = JSON.parseObject(facetsResult);
                
                if (facetsJson != null && facetsJson.containsKey("facets")) {
                    JSONArray facets = facetsJson.getJSONArray("facets");
                    
                    // 解析感兴趣的字段
                    for (int i = 0; i < facets.size(); i++) {
                        JSONObject facet = facets.getJSONObject(i);
                        String fieldName = facet.getString("field_name");
                        
                        if ("command".equals(fieldName)) {
                            facetsData.put("commands", parseFacetValues(facet));
                        } else if ("user".equals(fieldName)) {
                            facetsData.put("users", parseFacetValues(facet));
                        } else if ("hostname".equals(fieldName)) {
                            facetsData.put("hostnames", parseFacetValues(facet));
                        } else if ("event_time".equals(fieldName)) {
                            facetsData.put("timeDistribution", parseFacetValues(facet));
                        }
                    }
                }
            }
            
            logger.info("获取到主机命令facets统计数据，目标IP: {}", targetIp);
            
        } catch (Exception e) {
            logger.error("获取主机命令facets统计数据失败", e);
        }
        
        return facetsData;
    }
    
    /**
     * 解析facet字段值
     */
    private List<Map<String, Object>> parseFacetValues(JSONObject facet) {
        List<Map<String, Object>> values = new ArrayList<>();
        
        try {
            JSONArray valuesArray = facet.getJSONArray("values");
            for (int i = 0; i < valuesArray.size(); i++) {
                JSONObject valueObj = valuesArray.getJSONObject(i);
                Map<String, Object> valueMap = new HashMap<>();
                valueMap.put("name", valueObj.getString("field_value"));
                valueMap.put("value", valueObj.getInteger("hits"));
                values.add(valueMap);
            }
        } catch (Exception e) {
            logger.debug("解析facet值失败", e);
        }
        
        return values;
    }
    
    /**
     * 分析命令数据
     */
    private Map<String, Object> analyzeCommandData(List<Map<String, Object>> records, 
                                                   Map<String, Object> facetsData, String targetIp) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 从facets数据中获取统计信息
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> commands = (List<Map<String, Object>>) facetsData.get("commands");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> users = (List<Map<String, Object>>) facetsData.get("users");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> hostnames = (List<Map<String, Object>>) facetsData.get("hostnames");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> timeDistribution = (List<Map<String, Object>>) facetsData.get("timeDistribution");
        
        // 计算总命令执行次数
        int totalCommands = 0;
        if (commands != null) {
            totalCommands = commands.stream().mapToInt(item -> (Integer) item.get("value")).sum();
        }
        
        analysis.put("totalRecords", records != null ? records.size() : totalCommands);
        analysis.put("totalCommands", totalCommands);
        
        // 计算唯一值数量
        analysis.put("uniqueCommands", commands != null ? commands.size() : 0);
        analysis.put("uniqueUsers", users != null ? users.size() : 0);
        analysis.put("uniqueHostnames", hostnames != null ? hostnames.size() : 0);
        
        // 设置各种统计数据
        analysis.put("commands", commands != null ? commands : new ArrayList<>());
        analysis.put("users", users != null ? users : new ArrayList<>());
        analysis.put("hostnames", hostnames != null ? hostnames : new ArrayList<>());
        analysis.put("timeDistribution", timeDistribution != null ? timeDistribution : new ArrayList<>());
        
        // 分析命令行为特征
        analysis.put("behaviorCharacteristics", analyzeCommandBehavior(commands, users));
        
        return analysis;
    }
    
    /**
     * 分析命令行为特征
     */
    private Map<String, Object> analyzeCommandBehavior(List<Map<String, Object>> commands,
                                                       List<Map<String, Object>> users) {
        Map<String, Object> characteristics = new HashMap<>();
        
        if (commands != null && !commands.isEmpty()) {
            // 分析命令类型分布
            Map<String, Object> commandTypes = categorizeCommands(commands);
            characteristics.put("commandTypes", commandTypes);
        }
        
        if (users != null && !users.isEmpty()) {
            // 用户活动分布
            Map<String, Object> userActivity = new HashMap<>();
            for (Map<String, Object> user : users) {
                userActivity.put((String) user.get("name"), user.get("value"));
            }
            characteristics.put("userActivity", userActivity);
        }
        
        return characteristics;
    }
    
    /**
     * 命令分类
     */
    private Map<String, Object> categorizeCommands(List<Map<String, Object>> commands) {
        Map<String, Integer> categories = new HashMap<>();
        categories.put("系统管理", 0);
        categories.put("文件操作", 0);
        categories.put("网络操作", 0);
        categories.put("进程管理", 0);
        categories.put("其他", 0);
        
        for (Map<String, Object> command : commands) {
            String commandName = (String) command.get("name");
            Integer count = (Integer) command.get("value");
            String category = getCommandCategory(commandName);
            categories.put(category, categories.get(category) + count);
        }
        
        // 转换为Map<String, Object>格式
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Integer> entry : categories.entrySet()) {
            result.put(entry.getKey(), entry.getValue());
        }
        
        return result;
    }
    
    /**
     * 获取命令分类
     */
    private String getCommandCategory(String command) {
        if (command == null) return "其他";
        
        String lowerCommand = command.toLowerCase();
        
        if (lowerCommand.contains("systemctl") || lowerCommand.contains("service") || 
            lowerCommand.contains("crontab") || lowerCommand.contains("sudo")) {
            return "系统管理";
        } else if (lowerCommand.contains("ls") || lowerCommand.contains("cp") || 
                   lowerCommand.contains("mv") || lowerCommand.contains("rm") ||
                   lowerCommand.contains("chmod") || lowerCommand.contains("chown")) {
            return "文件操作";
        } else if (lowerCommand.contains("wget") || lowerCommand.contains("curl") || 
                   lowerCommand.contains("ssh") || lowerCommand.contains("scp") ||
                   lowerCommand.contains("netstat") || lowerCommand.contains("iptables")) {
            return "网络操作";
        } else if (lowerCommand.contains("ps") || lowerCommand.contains("kill") || 
                   lowerCommand.contains("top") || lowerCommand.contains("htop") ||
                   lowerCommand.contains("jobs") || lowerCommand.contains("nohup")) {
            return "进程管理";
        } else {
            return "其他";
        }
    }
    
    /**
     * 生成分析摘要
     */
    private String generateAnalysisSummary(String targetIp, List<Map<String, Object>> records, 
                                           Map<String, Object> analysis) {
        Integer totalCommands = (Integer) analysis.get("totalCommands");
        if (totalCommands == null || totalCommands == 0) {
            return String.format("未发现主机 %s 的命令执行记录。可能该主机在查询时间范围内未执行命令或日志收集异常。", targetIp);
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("在指定时间范围内，主机 %s 共执行了 %d 次命令。", 
            targetIp, totalCommands));
        
        Integer uniqueCommands = (Integer) analysis.get("uniqueCommands");
        Integer uniqueUsers = (Integer) analysis.get("uniqueUsers");
        
        if (uniqueCommands != null && uniqueCommands > 0) {
            summary.append(String.format(" 涉及 %d 种不同的命令", uniqueCommands));
        }
        
        if (uniqueUsers != null && uniqueUsers > 0) {
            summary.append(String.format("，由 %d 个用户执行", uniqueUsers));
        }
        
        summary.append("。");
        
        return summary.toString();
    }
    
    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(AnalysisContext context) {
        if (context.getTimeRange() != null) {
            return "最近" + context.getTimeRange();
        } else if (context.getStartTime() != null && context.getEndTime() != null) {
            return String.format("%s 至 %s", 
                context.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                context.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            return "未指定时间范围";
        }
    }
} 