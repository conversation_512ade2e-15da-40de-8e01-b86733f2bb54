package com.wiwj.securio.app.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.app.domain.dto.IpBehaviorAnalysisRequestDTO;
import com.wiwj.securio.app.domain.dto.IpBehaviorAnalysisResponseDTO;
import com.wiwj.securio.app.service.IpBehaviorAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * IP行为分析控制器
 *
 * <AUTHOR>
 */
@Api(tags = "IP行为分析", description = "IP行为分析相关接口")
@RestController
@RequestMapping("/app/ip-behavior-analysis")
@Validated
public class IpBehaviorAnalysisController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(IpBehaviorAnalysisController.class);

    @Autowired
    private IpBehaviorAnalysisService ipBehaviorAnalysisService;

    /**
     * 执行IP行为分析
     */
    @ApiOperation(value = "执行IP行为分析", notes = "根据目标IP和时间范围分析网络行为，生成桑基图")
    @PostMapping("/analyze")
    public AjaxResult analyzeIpBehavior(@ApiParam(value = "分析请求参数", required = true) @Valid @RequestBody IpBehaviorAnalysisRequestDTO request) {
        try {
            logger.info("收到IP行为分析请求，目标IP: {}, 时间范围: {} - {}", 
                       request.getTargetIp(), request.getStartTime(), request.getEndTime());

            IpBehaviorAnalysisResponseDTO response = ipBehaviorAnalysisService.analyzeIpBehavior(request);
            
            logger.info("IP行为分析完成，目标IP: {}", request.getTargetIp());
            return AjaxResult.success("IP行为分析完成", response);

        } catch (Exception e) {
            logger.error("IP行为分析失败，目标IP: {}, 错误: {}", request.getTargetIp(), e.getMessage(), e);
            return AjaxResult.error("IP行为分析失败: " + e.getMessage());
        }
    }
} 