package com.wiwj.securio.app.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.app.analysis.core.AnalysisDispatcher;
import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.*;
import com.wiwj.securio.app.service.HostAnalysisService;
import com.wiwj.securio.app.service.HostAnalysisProgressService;
import com.wiwj.securio.plugin.wukong.service.WukongCMDBService;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 主机综合分析控制器
 * 重新设计的架构，支持可扩展的分析器组合
 *
 * <AUTHOR>
 */
@Api(tags = "主机综合分析", description = "主机综合分析相关接口")
@RestController
@RequestMapping("/app/host-analysis")
@Validated
public class HostAnalysisController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(HostAnalysisController.class);

    @Autowired
    private HostAnalysisService hostAnalysisService;

    @Autowired
    private HostAnalysisProgressService progressService;

    @Autowired
    private AnalysisDispatcher analysisDispatcher;

    @Autowired
    private WukongCMDBService wukongCMDBService;

    /**
     * 执行主机综合分析（同步）
     * 直接返回完整的分析结果
     */
    @ApiOperation(value = "执行主机综合分析", notes = "根据主机IP和时间范围生成综合分析报告")
    @PostMapping("/analyze")
    public AjaxResult analyzeHost(@ApiParam(value = "分析请求参数", required = true) @Valid @RequestBody HostAnalysisRequestDTO request) {
        try {
            logger.info("收到主机综合分析请求，主机IP: {}, 时间范围: {} - {}", 
                       request.getHostIp(), request.getStartTime(), request.getEndTime());

            // 创建分析上下文
            AnalysisContext context = createAnalysisContext(request, UUID.randomUUID().toString());
            
            // 执行分析
            Map<AnalyzerType, AnalysisResult> analysisResults = analysisDispatcher.executeAnalysis(context);
            
            // 构建响应
            HostAnalysisResponseDTO response = buildAnalysisResponse(context, analysisResults);
            
            logger.info("主机综合分析完成，主机IP: {}, IP类型: {}", 
                request.getHostIp(), context.getIpType().getDescription());
            
            return AjaxResult.success("主机综合分析完成", response);

        } catch (Exception e) {
            logger.error("主机综合分析失败，主机IP: {}, 错误: {}", request.getHostIp(), e.getMessage(), e);
            return AjaxResult.error("主机综合分析失败: " + e.getMessage());
        }
    }

    /**
     * 启动主机综合分析（异步，带进度推送）
     */
    @ApiOperation(value = "启动主机综合分析", notes = "异步执行主机综合分析，返回任务ID用于SSE连接")
    @PostMapping("/analyze/start")
    public AjaxResult startAnalyzeHost(@ApiParam(value = "分析请求参数", required = true) @Valid @RequestBody HostAnalysisRequestDTO request) {
        try {
            // 生成任务ID
            String taskId = UUID.randomUUID().toString();
            
            logger.info("启动主机综合分析任务，主机IP: {}, 任务ID: {}, 时间范围: {} - {}", 
                       request.getHostIp(), taskId, request.getStartTime(), request.getEndTime());

            // 异步执行分析任务
            CompletableFuture.runAsync(() -> {
                try {
                    // 等待1秒确保SSE连接建立
                    Thread.sleep(1000);
                    
                    // 创建分析上下文
                    AnalysisContext context = createAnalysisContext(request, taskId);
                    
                    // 执行分析
                    Map<AnalyzerType, AnalysisResult> analysisResults = analysisDispatcher.executeAnalysis(context);
                    
                    // 构建响应
                    HostAnalysisResponseDTO response = buildAnalysisResponse(context, analysisResults);
                    
                    // 发送完成消息，包含分析结果
                    HostAnalysisProgressDTO completeProgress = new HostAnalysisProgressDTO(
                        taskId, "COMPLETED", "分析完成", 100, "COMPLETED", 
                        "主机综合分析已完成，生成时间: " + response.getGenerateTime()
                    );
                    completeProgress.setResult(response);
                    progressService.completeTask(taskId, completeProgress);
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.error("异步分析任务被中断，任务ID: {}", taskId);
                } catch (Exception e) {
                    logger.error("异步分析任务执行失败，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
                    
                    HostAnalysisProgressDTO errorProgress = new HostAnalysisProgressDTO(
                        taskId, "ERROR", "分析失败", 0, "ERROR", "分析过程中发生错误"
                    );
                    errorProgress.setErrorMessage("主机综合分析失败: " + e.getMessage());
                    progressService.errorTask(taskId, errorProgress);
                }
            });
            
            return AjaxResult.success("分析任务已启动", taskId);

        } catch (Exception e) {
            logger.error("启动主机综合分析失败，主机IP: {}, 错误: {}", request.getHostIp(), e.getMessage(), e);
            return AjaxResult.error("启动分析失败: " + e.getMessage());
        }
    }

    /**
     * 建立SSE连接获取分析进度
     */
    @ApiOperation(value = "建立SSE连接", notes = "建立SSE连接以实时获取分析进度")
    @GetMapping(value = "/analyze/progress/{taskId}", produces = "text/event-stream;charset=UTF-8")
    public SseEmitter getAnalysisProgress(@ApiParam(value = "任务ID", required = true) @PathVariable String taskId, 
                                         HttpServletResponse response) {
        // 设置SSE响应头
        response.setContentType("text/event-stream;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Headers", "Cache-Control, Content-Type");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("X-Accel-Buffering", "no");
        response.setHeader("Transfer-Encoding", "chunked");
        
        try {
            logger.info("建立SSE连接，任务ID: {}", taskId);
            return progressService.createConnection(taskId);
        } catch (Exception e) {
            logger.error("建立SSE连接失败，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            
            SseEmitter errorEmitter = new SseEmitter(5000L);
            try {
                SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                    .name("error")
                    .data("{\"error\":\"建立SSE连接失败: " + e.getMessage() + "\",\"taskId\":\"" + taskId + "\"}")
                    .reconnectTime(3000);
                errorEmitter.send(eventBuilder);
                errorEmitter.complete();
            } catch (Exception sendError) {
                logger.error("发送错误消息失败", sendError);
            }
            return errorEmitter;
        }
    }

    /**
     * 获取任务进度历史
     */
    @ApiOperation(value = "获取任务进度历史", notes = "获取指定任务的进度历史记录")
    @GetMapping("/analyze/history/{taskId}")
    public AjaxResult getProgressHistory(@ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        try {
            logger.info("获取任务进度历史，任务ID: {}", taskId);
            java.util.List<HostAnalysisProgressDTO> history = progressService.getProgressHistory(taskId);
            return AjaxResult.success("获取进度历史成功", history);
        } catch (Exception e) {
            logger.error("获取任务进度历史失败，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            return AjaxResult.error("获取进度历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取主机基本信息
     */
    @ApiOperation(value = "获取主机基本信息", notes = "获取指定主机的基本信息，包括IP归属信息")
    @GetMapping("/basic-info/{hostIp}")
    public AjaxResult getHostBasicInfo(@ApiParam(value = "主机IP", required = true) @PathVariable String hostIp) {
        try {
            logger.info("获取主机基本信息，主机IP: {}", hostIp);

            // 创建分析上下文
            AnalysisContext context = new AnalysisContext();
            context.setHostIp(hostIp);
            context.setIpType(IpType.fromIp(hostIp));
            
            // 获取主机基本信息分析器
            java.util.List<Analyzer> analyzers = analysisDispatcher.getSupportedAnalyzers(context.getIpType());
            Analyzer basicInfoAnalyzer = analyzers.stream()
                .filter(analyzer -> analyzer.getType() == AnalyzerType.HOST_BASIC_INFO)
                .findFirst()
                .orElse(null);
            
            if (basicInfoAnalyzer != null) {
                // 执行分析器获取增强结果
                AnalysisResult result = basicInfoAnalyzer.analyze(context);
                
                if (result.isSuccess()) {
                    logger.info("成功获取增强的主机基本信息: {}", hostIp);
                    return AjaxResult.success("获取主机基本信息成功", result.getData());
                } else {
                    logger.error("主机基本信息分析失败: {}, 错误: {}", hostIp, result.getErrorMessage());
                    return AjaxResult.error("获取主机基本信息失败: " + result.getErrorMessage());
                }
            } else {
                // 如果没有找到分析器，回退到原来的方法
                logger.warn("未找到主机基本信息分析器，使用原始方法: {}", hostIp);
                HostAnalysisResponseDTO.HostBasicInfoDTO basicInfo = hostAnalysisService.getHostBasicInfo(hostIp);
            return AjaxResult.success("获取主机基本信息成功", basicInfo);
            }

        } catch (Exception e) {
            logger.error("获取主机基本信息失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            return AjaxResult.error("获取主机基本信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的分析器列表
     */
    @ApiOperation(value = "获取支持的分析器列表", notes = "获取指定IP类型支持的分析器列表")
    @GetMapping("/analyzers/{hostIp}")
    public AjaxResult getSupportedAnalyzers(@ApiParam(value = "主机IP", required = true) @PathVariable String hostIp) {
        try {
            IpType ipType = IpType.fromIp(hostIp);
            java.util.List<Analyzer> supportedAnalyzers = analysisDispatcher.getSupportedAnalyzers(ipType);
            
            java.util.List<JSONObject> analyzerInfo = supportedAnalyzers.stream()
                .map(analyzer -> {
                    JSONObject info = new JSONObject();
                    info.put("name", analyzer.getName());
                    info.put("type", analyzer.getType().getCode());
                    info.put("description", analyzer.getType().getDescription());
                    info.put("priority", analyzer.getPriority());
                    info.put("estimatedTime", analyzer.getEstimatedExecutionTime());
                    return info;
                })
                .collect(java.util.stream.Collectors.toList());
            
            JSONObject result = new JSONObject();
            result.put("hostIp", hostIp);
            result.put("ipType", ipType.getCode());
            result.put("ipTypeDescription", ipType.getDescription());
            result.put("analyzers", analyzerInfo);
            
            return AjaxResult.success("获取分析器列表成功", result);
        } catch (Exception e) {
            logger.error("获取分析器列表失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            return AjaxResult.error("获取分析器列表失败: " + e.getMessage());
        }
    }

    /**
     * 测试CMDB API响应 - 临时调试接口
     */
    @GetMapping("/test-cmdb/{hostIp}")
    public AjaxResult testCmdbApi(@PathVariable String hostIp) {
        try {
            JSONObject serverInfo = wukongCMDBService.getServerInfoByIp(hostIp);
            return AjaxResult.success("CMDB测试成功", serverInfo);
        } catch (Exception e) {
            logger.error("测试CMDB API失败", e);
            return AjaxResult.error("测试CMDB API失败: " + e.getMessage());
        }
    }

    /**
     * 创建分析上下文
     */
    private AnalysisContext createAnalysisContext(HostAnalysisRequestDTO request, String taskId) {
        AnalysisContext context = new AnalysisContext();
        context.setTaskId(taskId);
        context.setHostIp(request.getHostIp());
        context.setTimeRange(request.getTimeRange());
        
        // 解析时间，支持多种格式
        if (request.getStartTime() != null && !request.getStartTime().isEmpty()) {
            context.setStartTime(parseDateTime(request.getStartTime()));
        }
        if (request.getEndTime() != null && !request.getEndTime().isEmpty()) {
            context.setEndTime(parseDateTime(request.getEndTime()));
        }
        
        // 可以根据需要添加其他参数
        
        return context;
    }
    
    /**
     * 解析时间字符串，支持多种格式
     */
    private LocalDateTime parseDateTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        
        timeStr = timeStr.trim();
        
        try {
            // 尝试ISO格式：2025-06-04T11:07:56
            if (timeStr.contains("T")) {
                return LocalDateTime.parse(timeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            
            // 尝试标准格式：2025-06-04 11:07:56
            if (timeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            // 尝试其他可能的格式
            if (timeStr.matches("\\d{4}/\\d{2}/\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
            }
            
            // 如果都不匹配，尝试ISO格式作为最后的尝试
            return LocalDateTime.parse(timeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            
        } catch (Exception e) {
            logger.error("无法解析时间字符串: {}, 错误: {}", timeStr, e.getMessage());
            throw new IllegalArgumentException("无效的时间格式: " + timeStr + "，支持的格式：yyyy-MM-dd HH:mm:ss 或 yyyy-MM-ddTHH:mm:ss");
        }
    }

    /**
     * 构建分析响应
     */
    private HostAnalysisResponseDTO buildAnalysisResponse(AnalysisContext context, 
                                                         Map<AnalyzerType, AnalysisResult> analysisResults) {
        HostAnalysisResponseDTO response = new HostAnalysisResponseDTO();
        response.setGenerateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 不再在根级别设置hostBasicInfo，避免数据重复
        // 所有分析器结果统一通过analyzerResults字段返回
        
        // 生成分析摘要
        response.setAnalysisSummary(generateAnalysisSummary(context, analysisResults));
        
        // 添加analyzerResults字段用于前端新架构展示
        Map<String, Object> analyzerResultsMap = new java.util.HashMap<>();
        for (Map.Entry<AnalyzerType, AnalysisResult> entry : analysisResults.entrySet()) {
            AnalyzerType analyzerType = entry.getKey();
            AnalysisResult result = entry.getValue();
            
            Map<String, Object> resultInfo = new java.util.HashMap<>();
            resultInfo.put("success", result.isSuccess());
            resultInfo.put("analyzerName", analyzerType.getDescription());
            resultInfo.put("data", result.getData());
            resultInfo.put("executionTime", result.getExecutionTime());
            
            if (!result.isSuccess()) {
                resultInfo.put("errorMessage", result.getErrorMessage());
            }
            
            analyzerResultsMap.put(analyzerType.getCode(), resultInfo);
        }
        
        response.setAnalyzerResults(analyzerResultsMap);
        
        return response;
    }

    /**
     * 生成分析摘要
     */
    private HostAnalysisResponseDTO.AnalysisSummaryDTO generateAnalysisSummary(
            AnalysisContext context, Map<AnalyzerType, AnalysisResult> analysisResults) {
        
        HostAnalysisResponseDTO.AnalysisSummaryDTO summary = new HostAnalysisResponseDTO.AnalysisSummaryDTO();
        
        long successCount = analysisResults.values().stream()
            .mapToLong(result -> result.isSuccess() ? 1 : 0)
            .sum();
        
        summary.setOverallAssessment(String.format(
            "针对主机 %s (%s) 执行了 %d 个分析器，其中 %d 个成功完成。", 
            context.getHostIp(), 
            context.getIpType().getDescription(),
            analysisResults.size(),
            successCount
        ));
        
        // 可以根据具体的分析结果生成更详细的摘要
        summary.setKeyFindings(java.util.Arrays.asList(
            "主机类型: " + context.getIpType().getDescription(),
            "分析时间范围: " + context.getTimeRange(),
            "执行的分析器数量: " + analysisResults.size()
        ));
        
        summary.setRecommendations(java.util.Arrays.asList(
            "定期监控主机状态",
            "注意安全事件报警",
            "保持系统更新"
        ));
        
        return summary;
    }
}