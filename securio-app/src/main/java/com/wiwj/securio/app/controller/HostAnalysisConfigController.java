package com.wiwj.securio.app.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.app.config.HostAnalysisConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 主机分析配置管理控制器
 *
 * <AUTHOR>
 */
@Api(tags = "主机分析配置管理")
@RestController
@RequestMapping("/app/hostAnalysis/config")
public class HostAnalysisConfigController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(HostAnalysisConfigController.class);
    
    @Resource
    private HostAnalysisConfig hostAnalysisConfig;
    
    /**
     * 获取主机分析配置
     */
    @ApiOperation("获取主机分析配置")
    @GetMapping
    public AjaxResult getConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("basicInfoSource", hostAnalysisConfig.getBasicInfoSource());
            config.put("timeout", hostAnalysisConfig.getTimeout());
            
            return AjaxResult.success("获取配置成功", config);
        } catch (Exception e) {
            logger.error("获取主机分析配置失败", e);
            return AjaxResult.error("获取配置失败: " + e.getMessage());
        }
    }
    
} 