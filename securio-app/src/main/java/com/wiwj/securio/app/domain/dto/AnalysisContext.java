package com.wiwj.securio.app.domain.dto;

import com.wiwj.securio.app.analysis.core.IpType;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分析上下文
 * 包含分析过程中需要的所有信息
 *
 * <AUTHOR>
 */
public class AnalysisContext {
    
    /** 任务ID */
    private String taskId;
    
    /** 目标IP地址 */
    private String hostIp;
    
    /** IP类型 */
    private IpType ipType;
    
    /** 开始时间 */
    private LocalDateTime startTime;
    
    /** 结束时间 */
    private LocalDateTime endTime;
    
    /** 时间范围字符串（如30m, 1h等） */
    private String timeRange;
    
    /** 扩展参数 */
    private Map<String, Object> parameters;
    
    /** 中间结果缓存 */
    private Map<String, Object> cache;
    
    public AnalysisContext() {
        this.parameters = new ConcurrentHashMap<>();
        this.cache = new ConcurrentHashMap<>();
    }
    
    public AnalysisContext(String taskId, String hostIp, LocalDateTime startTime, LocalDateTime endTime) {
        this();
        this.taskId = taskId;
        this.hostIp = hostIp;
        this.ipType = IpType.fromIp(hostIp);
        this.startTime = startTime;
        this.endTime = endTime;
    }
    
    // Getters and Setters
    public String getTaskId() {
        return taskId;
    }
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    public String getHostIp() {
        return hostIp;
    }
    
    public void setHostIp(String hostIp) {
        this.hostIp = hostIp;
        this.ipType = IpType.fromIp(hostIp);
    }
    
    public IpType getIpType() {
        return ipType;
    }
    
    public void setIpType(IpType ipType) {
        this.ipType = ipType;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public String getTimeRange() {
        return timeRange;
    }
    
    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }
    
    public Map<String, Object> getParameters() {
        return parameters;
    }
    
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }
    
    public Map<String, Object> getCache() {
        return cache;
    }
    
    public void setCache(Map<String, Object> cache) {
        this.cache = cache;
    }
    
    /**
     * 添加参数
     */
    public void addParameter(String key, Object value) {
        this.parameters.put(key, value);
    }
    
    /**
     * 获取参数
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, Class<T> type) {
        return (T) this.parameters.get(key);
    }
    
    /**
     * 缓存中间结果
     */
    public void cacheResult(String key, Object value) {
        this.cache.put(key, value);
    }
    
    /**
     * 获取缓存的中间结果
     */
    @SuppressWarnings("unchecked")
    public <T> T getCachedResult(String key, Class<T> type) {
        return (T) this.cache.get(key);
    }
} 