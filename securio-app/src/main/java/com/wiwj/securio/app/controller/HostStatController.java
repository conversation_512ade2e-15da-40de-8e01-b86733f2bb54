package com.wiwj.securio.app.controller;

import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.agent.domain.NetworkConnectionStatDTO;
import com.wiwj.securio.agent.service.IInventoryPortInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主机统计控制器
 * 提供主机相关的统计信息查询接口
 *
 * <AUTHOR>
 */
@Api(tags = "主机统计")
@RestController
@RequestMapping("/app/host/stat")
public class HostStatController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(HostStatController.class);

    @Autowired
    private IInventoryPortInfoService inventoryPortInfoService;

    /**
     * 获取主机端口统计信息
     *
     * @param hostIp 主机IP地址
     * @return 端口统计信息
     */
    @ApiOperation("获取主机端口统计信息")
    @Log(title = "主机端口统计", businessType = BusinessType.OTHER)
    @GetMapping("/port/{hostIp}")
    public AjaxResult getPortStatistics(
            @ApiParam(value = "主机IP地址", required = true)
            @PathVariable("hostIp") String hostIp) {
        
        logger.info("获取主机端口统计信息，主机IP: {}", hostIp);
        
        try {
            // 获取端口状态统计
            Map<String, Long> portStats = inventoryPortInfoService.selectPortStatisticsByHostIp(hostIp);
            
            // 获取网络连接统计
            List<NetworkConnectionStatDTO> networkStats = inventoryPortInfoService.selectNetworkConnectionStats(hostIp);
            
            // 获取本地监听端口列表
            List<Map<String, Object>> listeningPorts = inventoryPortInfoService.selectListeningPorts(hostIp);
            
            // 计算总端口数
            int totalPorts = portStats.values().stream().mapToInt(Long::intValue).sum();
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("portStats", portStats);
            result.put("networkStats", networkStats);
            result.put("listeningPorts", listeningPorts);
            result.put("totalPorts", totalPorts);
            
            logger.info("成功获取主机端口统计信息，主机IP: {}, 总端口数: {}, 监听端口数: {}", 
                hostIp, totalPorts, listeningPorts.size());
            return success(result);
            
        } catch (Exception e) {
            logger.error("获取主机端口统计信息失败，主机IP: {}", hostIp, e);
            return error("获取主机端口统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取主机外部连接信息
     *
     * @param hostIp 主机IP地址
     * @return 外部连接信息
     */
    @ApiOperation("获取主机外部连接信息")
    @Log(title = "主机外部连接", businessType = BusinessType.OTHER)
    @GetMapping("/connections/{hostIp}")
    public AjaxResult getExternalConnections(
            @ApiParam(value = "主机IP地址", required = true)
            @PathVariable("hostIp") String hostIp) {
        
        logger.info("获取主机外部连接信息，主机IP: {}", hostIp);
        
        try {
            List<Map<String, Object>> connections = inventoryPortInfoService.selectExternalConnections(hostIp);
            
            logger.info("成功获取主机外部连接信息，主机IP: {}, 连接数: {}", hostIp, connections.size());
            return success(connections);
            
        } catch (Exception e) {
            logger.error("获取主机外部连接信息失败，主机IP: {}", hostIp, e);
            return error("获取主机外部连接信息失败: " + e.getMessage());
        }
    }
} 