package com.wiwj.securio.app.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 主机分析配置类
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "app.host-analysis")
public class HostAnalysisConfig {
    
    /**
     * 主机基本信息数据源：cmdb 或 agent
     */
    private String basicInfoSource = "cmdb";
    
    /**
     * 数据获取超时时间（秒）
     */
    private int timeout = 30;
    
    public String getBasicInfoSource() {
        return basicInfoSource;
    }
    
    public void setBasicInfoSource(String basicInfoSource) {
        this.basicInfoSource = basicInfoSource;
    }
    

    
    public int getTimeout() {
        return timeout;
    }
    
    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
} 