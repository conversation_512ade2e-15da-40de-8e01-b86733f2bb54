package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 主机SYSLOG分析器
 * 负责分析特定主机的系统日志，主要用于日志查询和展示
 *
 * <AUTHOR>
 */
@Component
public class SyslogAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(SyslogAnalyzer.class);
    
    @Override
    public String getName() {
        return "主机系统日志分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.SYSLOG_ANALYSIS;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        return ipType == IpType.SERVER;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行主机SYSLOG分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建分析结果 - 后端逻辑留空，主要用于前端日志展示
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("targetIp", context.getHostIp());
            
            // 添加时间信息供前端组件构建LogQL使用
            if (context.getStartTime() != null) {
                analysisData.put("startTime", context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getEndTime() != null) {
                analysisData.put("endTime", context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getTimeRange() != null) {
                analysisData.put("timeRange", context.getTimeRange());
            }
            
            // 设置支持的日志流类型
            List<String> supportedStreams = Arrays.asList(
                "AUDITLOG_SYSTEM_AUDIT",
                "AUDITLOG_SYSLOG_COMMAND", 
                "AUDITLOG_SYSTEM_SECURE",
                "AUDITLOG_SYSLOG_MESSAGE",
                "AUDITLOG_SYSLOG_CRON"
            );
            analysisData.put("supportedStreams", supportedStreams);
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(context.getHostIp(), supportedStreams);
            analysisData.put("analysisSummary", summary);
            
            // 时间范围描述信息
            analysisData.put("timeRangeDescription", getTimeRangeDescription(context));
            
            logger.info("主机SYSLOG分析完成，主机IP: {}", context.getHostIp());
                
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("主机SYSLOG分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "主机SYSLOG分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // SYSLOG分析优先级为2
        return 2;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间2秒（主要是准备工作）
        return 2000;
    }
    
    /**
     * 生成分析摘要
     */
    private String generateAnalysisSummary(String targetIp, List<String> supportedStreams) {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("主机 %s 的系统日志分析已准备就绪。", targetIp));
        summary.append(String.format("支持查询 %d 种类型的系统日志：", supportedStreams.size()));
        summary.append("系统审计日志、命令执行日志、安全日志、系统消息日志、计划任务日志。");
        summary.append("请通过下方的Tab页面查看详细的日志记录。");
        
        return summary.toString();
    }
    
    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(AnalysisContext context) {
        if (context.getTimeRange() != null) {
            return "最近" + context.getTimeRange();
        } else if (context.getStartTime() != null && context.getEndTime() != null) {
            return String.format("%s 至 %s", 
                context.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                context.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            return "未指定时间范围";
        }
    }
} 