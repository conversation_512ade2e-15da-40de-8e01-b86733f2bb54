package com.wiwj.securio.app.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.app.domain.dto.HostAnalysisResponseDTO.HostBasicInfoDTO;
import com.wiwj.securio.app.strategy.HostInfoStrategy;
import com.wiwj.securio.plugin.wukong.service.WukongCMDBService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * CMDB主机信息获取策略实现
 * 直接返回CMDB原始数据，避免DTO转换的复杂性
 *
 * <AUTHOR>
 */
@Component("cmdbHostInfoStrategy")
public class CmdbHostInfoStrategy implements HostInfoStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(CmdbHostInfoStrategy.class);
    
    @Resource
    private WukongCMDBService wukongCMDBService;
    
    @Override
    public HostBasicInfoDTO getHostBasicInfo(String hostIp) {
        logger.info("使用CMDB策略获取主机基本信息，主机IP: {}", hostIp);
        
        HostBasicInfoDTO basicInfo = new HostBasicInfoDTO();
        basicInfo.setHostIp(hostIp);
        basicInfo.setDataSource("cmdb");
        
        try {
            // 收集所有CMDB原始数据
            Map<String, Object> rawData = new HashMap<>();
            
            // 1. 获取服务器基础信息
            try {
                JSONObject serverInfo = wukongCMDBService.getServerInfoByIp(hostIp);
                if (serverInfo != null) {
                    rawData.put("serverInfo", serverInfo);
                    logger.info("成功获取CMDB服务器基础信息");
                } else {
                    logger.warn("CMDB服务器基础信息获取失败或为空，主机IP: {}", hostIp);
                }
            } catch (Exception e) {
                logger.warn("获取CMDB服务器基础信息异常，主机IP: {}, 错误: {}", hostIp, e.getMessage());
            }
            
            // 2. 获取主机详细信息
            try {
                JSONObject hostDetailInfo = wukongCMDBService.getHostDetailInfo(hostIp);
                if (hostDetailInfo != null) {
                    rawData.put("hostDetailInfo", hostDetailInfo);
                    logger.info("成功获取CMDB主机详细信息");
                } else {
                    logger.warn("CMDB主机详细信息获取失败或为空，主机IP: {}", hostIp);
                }
            } catch (Exception e) {
                logger.warn("获取CMDB主机详细信息异常，主机IP: {}, 错误: {}", hostIp, e.getMessage());
            }
            
            // 3. 获取服务器关联信息
            try {
                JSONObject serverRelations = wukongCMDBService.getServerRelationsByIp(hostIp);
                if (serverRelations != null) {
                    rawData.put("serverRelations", serverRelations);
                    logger.info("成功获取CMDB服务器关联信息");
                } else {
                    logger.warn("CMDB服务器关联信息获取失败或为空，主机IP: {}", hostIp);
                }
            } catch (Exception e) {
                logger.warn("获取CMDB服务器关联信息异常，主机IP: {}, 错误: {}", hostIp, e.getMessage());
            }
            
            // 设置原始数据
            basicInfo.setRawData(rawData);
            
            logger.info("CMDB策略获取主机信息完成，主机IP: {}, 原始数据字段数: {}", hostIp, rawData.size());
            return basicInfo;
            
        } catch (Exception e) {
            logger.error("CMDB策略获取主机信息失败，主机IP: {}", hostIp, e);
            // 即使失败也返回基本结构，包含数据源信息
            basicInfo.setRawData(new HashMap<>());
            return basicInfo;
        }
    }
    
    @Override
    public String getStrategyName() {
        return "cmdb";
    }
} 