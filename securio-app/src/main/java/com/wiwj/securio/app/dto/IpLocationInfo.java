package com.wiwj.securio.app.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * IP归属信息数据传输对象
 */
public class IpLocationInfo {
    
    private String ip;
    private String country;
    private String countryCode;
    private String region;
    private String regionName;
    private String city;
    private String zipcode;
    private Double latitude;
    private Double longitude;
    private String timezone;
    private String isp;
    private String organization;
    private String asn;
    private String currency;
    private String countryFlag;
    private String apiSource; // 记录数据来源API
    
    public IpLocationInfo() {}
    
    public IpLocationInfo(String ip) {
        this.ip = ip;
    }
    
    // Getters and Setters
    public String getIp() {
        return ip;
    }
    
    public void setIp(String ip) {
        this.ip = ip;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getCountryCode() {
        return countryCode;
    }
    
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    
    public String getRegion() {
        return region;
    }
    
    public void setRegion(String region) {
        this.region = region;
    }
    
    public String getRegionName() {
        return regionName;
    }
    
    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getZipcode() {
        return zipcode;
    }
    
    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }
    
    public Double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
    
    public Double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
    
    public String getTimezone() {
        return timezone;
    }
    
    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }
    
    public String getIsp() {
        return isp;
    }
    
    public void setIsp(String isp) {
        this.isp = isp;
    }
    
    public String getOrganization() {
        return organization;
    }
    
    public void setOrganization(String organization) {
        this.organization = organization;
    }
    
    public String getAsn() {
        return asn;
    }
    
    public void setAsn(String asn) {
        this.asn = asn;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getCountryFlag() {
        return countryFlag;
    }
    
    public void setCountryFlag(String countryFlag) {
        this.countryFlag = countryFlag;
    }
    
    public String getApiSource() {
        return apiSource;
    }
    
    public void setApiSource(String apiSource) {
        this.apiSource = apiSource;
    }
    
    @Override
    public String toString() {
        return "IpLocationInfo{" +
                "ip='" + ip + '\'' +
                ", country='" + country + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", region='" + region + '\'' +
                ", regionName='" + regionName + '\'' +
                ", city='" + city + '\'' +
                ", zipcode='" + zipcode + '\'' +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", timezone='" + timezone + '\'' +
                ", isp='" + isp + '\'' +
                ", organization='" + organization + '\'' +
                ", asn='" + asn + '\'' +
                ", currency='" + currency + '\'' +
                ", countryFlag='" + countryFlag + '\'' +
                ", apiSource='" + apiSource + '\'' +
                '}';
    }
} 