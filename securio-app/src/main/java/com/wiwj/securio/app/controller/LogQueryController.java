package com.wiwj.securio.app.controller;

import com.wiwj.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeFormatter;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 日志查询控制器
 */
@RestController
@RequestMapping("/app/log-query")
public class LogQueryController {
    
    /**
     * 构建LogQL查询语句
     */
    @PostMapping("/build-logql")
    public AjaxResult buildLogQL(@RequestBody LogQLRequest request) {
        try {
            String logQL = buildLogQLQuery(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("logQL", logQL);
            result.put("timeRange", request.getTimeRange());
            result.put("startTime", request.getStartTime());
            result.put("endTime", request.getEndTime());
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("构建LogQL查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建LogQL查询语句的核心方法
     */
    private String buildLogQLQuery(LogQLRequest request) {
        StringBuilder queryBuilder = new StringBuilder();
        
        // 1. 添加时间过滤条件
        String timeFilter = buildTimeFilter(request);
        if (timeFilter != null && !timeFilter.isEmpty()) {
            queryBuilder.append(timeFilter);
        }
        
        // 2. 添加流过滤条件
        if (request.getStream() != null && !request.getStream().isEmpty()) {
            if (queryBuilder.length() > 0) {
                queryBuilder.append(" and ");
            }
            queryBuilder.append("stream:\"").append(request.getStream()).append("\"");
        }
        
        // 3. 添加源IP过滤条件
        if (request.getSrcIp() != null && !request.getSrcIp().isEmpty()) {
            if (queryBuilder.length() > 0) {
                queryBuilder.append(" and ");
            }
            queryBuilder.append("src_ip:\"").append(request.getSrcIp()).append("\"");
        }
        
        // 4. 添加目标IP过滤条件
        if (request.getdstIp() != null && !request.getdstIp().isEmpty()) {
            if (queryBuilder.length() > 0) {
                queryBuilder.append(" and ");
            }
            queryBuilder.append("dst_ip:\"").append(request.getdstIp()).append("\"");
        }
        
        // 5. 添加关键字过滤条件
        if (request.getKeyword() != null && !request.getKeyword().isEmpty()) {
            if (queryBuilder.length() > 0) {
                queryBuilder.append(" and ");
            }
            queryBuilder.append(request.getKeyword());
        }
        
        // 6. 添加自定义过滤条件
        if (request.getCustomFilters() != null && !request.getCustomFilters().isEmpty()) {
            for (Map<String, String> filter : request.getCustomFilters()) {
                String field = filter.get("field");
                String operator = filter.get("operator");
                String value = filter.get("value");
                
                if (field != null && !field.isEmpty() && value != null && !value.isEmpty()) {
                    if (queryBuilder.length() > 0) {
                        queryBuilder.append(" and ");
                    }
                    
                    String logQLOperator = convertOperatorToLogQL(operator);
                    queryBuilder.append(field).append(logQLOperator).append("\"").append(value).append("\"");
                }
            }
        }
        
        return queryBuilder.toString();
    }
    
    /**
     * 构建时间过滤条件
     */
    private String buildTimeFilter(LogQLRequest request) {
        // 优先检查是否有明确的开始和结束时间
        if (request.getStartTime() != null && !request.getStartTime().isEmpty() &&
            request.getEndTime() != null && !request.getEndTime().isEmpty()) {
            
            // 转换为VmLogs支持的时间格式
            String startTimeFormatted = formatTimeForVmLogs(request.getStartTime());
            String endTimeFormatted = formatTimeForVmLogs(request.getEndTime());
            
            // 使用VmLogs的时间范围格式：_time:[startTime,endTime]
            return "_time:[" + startTimeFormatted + "," + endTimeFormatted + "]";
        }
        
        // 检查timeRange是否包含时间范围（使用 ~ 分隔符）
        if (request.getTimeRange() != null && !request.getTimeRange().isEmpty()) {
            String timeRange = request.getTimeRange().trim();
            
            // 检查是否是时间范围格式：startTime ~ endTime
            if (timeRange.contains(" ~ ")) {
                String[] timeParts = timeRange.split(" ~ ");
                if (timeParts.length == 2) {
                    String startTime = timeParts[0].trim();
                    String endTime = timeParts[1].trim();
                    
                    // 转换为VmLogs支持的时间格式
                    String startTimeFormatted = formatTimeForVmLogs(startTime);
                    String endTimeFormatted = formatTimeForVmLogs(endTime);
                    
                    // 使用VmLogs的时间范围格式：_time:[startTime,endTime]
                    return "_time:[" + startTimeFormatted + "," + endTimeFormatted + "]";
                }
            }
            
            // 否则作为快速时间选择处理
            String normalizedTimeRange = normalizeTimeRange(timeRange);
            return "_time:" + normalizedTimeRange;
        }
        
        return null;
    }
    
    /**
     * 标准化时间范围表达式
     */
    private String normalizeTimeRange(String timeRange) {
        // 移除中文前缀，如"最近30m" -> "30m"
        if (timeRange.startsWith("最近")) {
            return timeRange.substring(2);
        }
        
        // 如果已经是标准格式，直接返回
        if (timeRange.matches("^\\d+[mhd]$")) {
            return timeRange;
        }
        
        // 处理其他可能的格式
        switch (timeRange) {
            case "5分钟":
            case "5 分钟":
                return "5m";
            case "15分钟":
            case "15 分钟":
                return "15m";
            case "30分钟":
            case "30 分钟":
                return "30m";
            case "1小时":
            case "1 小时":
                return "1h";
            case "6小时":
            case "6 小时":
                return "6h";
            case "1天":
            case "1 天":
                return "1d";
            case "10天":
            case "10 天":
                return "10d";
            case "30天":
            case "30 天":
                return "30d";
            default:
                return timeRange;
        }
    }
    
    /**
     * 格式化时间为VmLogs支持的格式
     * 将东八区时间转换为UTC时间
     */
    private String formatTimeForVmLogs(String timeStr) {
        try {
            // 如果已经是UTC格式且包含Z，直接返回
            if (timeStr.endsWith("Z") && timeStr.contains("T")) {
                return timeStr;
            }
            
            LocalDateTime localDateTime = null;
            
            // 处理 "yyyy-MM-dd HH:mm:ss" 格式
            if (timeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                localDateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            // 处理 "yyyy/MM/dd HH:mm:ss" 格式
            else if (timeStr.matches("\\d{4}/\\d{2}/\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                localDateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
            }
            // 处理ISO格式但没有Z
            else if (timeStr.contains("T") && !timeStr.endsWith("Z")) {
                localDateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            // 其他格式尝试作为ISO格式解析
            else {
                localDateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            
            if (localDateTime != null) {
                // 将本地时间视为东八区时间
                ZonedDateTime chinaTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
                // 转换为UTC时间
                ZonedDateTime utcTime = chinaTime.withZoneSameInstant(ZoneId.of("UTC"));
                // 格式化为ISO格式并添加Z
                return utcTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + "Z";
            }
            
        } catch (Exception e) {
            // 如果解析失败，返回原始字符串
            System.err.println("时间格式转换失败: " + timeStr + ", 错误: " + e.getMessage());
        }
        
        return timeStr;
    }
    
    /**
     * 转换操作符为LogQL格式
     */
    private String convertOperatorToLogQL(String operator) {
        if (operator == null) {
            return ":";
        }
        
        switch (operator) {
            case "=":
                return ":";
            case "~=":
                return ":~";
            case "^=":
                return ":^";
            case "$=":
                return ":$";
            default:
                return ":";
        }
    }
    
    /**
     * LogQL请求对象
     */
    public static class LogQLRequest {
        private String timeRange;
        private String startTime;
        private String endTime;
        private String stream;
        private String srcIp;
        private String dstIp;
        private String keyword;
        private java.util.List<Map<String, String>> customFilters;
        
        // Getters and Setters
        public String getTimeRange() {
            return timeRange;
        }
        
        public void setTimeRange(String timeRange) {
            this.timeRange = timeRange;
        }
        
        public String getStartTime() {
            return startTime;
        }
        
        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }
        
        public String getEndTime() {
            return endTime;
        }
        
        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }
        
        public String getStream() {
            return stream;
        }
        
        public void setStream(String stream) {
            this.stream = stream;
        }
        
        public String getSrcIp() {
            return srcIp;
        }
        
        public void setSrcIp(String srcIp) {
            this.srcIp = srcIp;
        }
        
        public String getdstIp() {
            return dstIp;
        }
        
        public void setdstIp(String dstIp) {
            this.dstIp = dstIp;
        }
        
        public String getKeyword() {
            return keyword;
        }
        
        public void setKeyword(String keyword) {
            this.keyword = keyword;
        }
        
        public java.util.List<Map<String, String>> getCustomFilters() {
            return customFilters;
        }
        
        public void setCustomFilters(java.util.List<Map<String, String>> customFilters) {
            this.customFilters = customFilters;
        }
    }
} 