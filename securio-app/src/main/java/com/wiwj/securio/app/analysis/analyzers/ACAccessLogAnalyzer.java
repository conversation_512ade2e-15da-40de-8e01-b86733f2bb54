package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AC访问日志分析器
 * 负责分析办公区客户端的AC访问日志，识别真实用户身份
 *
 * <AUTHOR>
 */
@Component
public class ACAccessLogAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(ACAccessLogAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    // AC日志解析正则表达式
    private static final Pattern AC_LOG_PATTERN = Pattern.compile(
        "Client MAC:\\s*([^,]+),\\s*" +
        "IP:\\s*([^,]+),\\s*" +
        ".*?" +
        "Username:\\s*([^,]+),\\s*" +
        "AP name:\\s*([^,]+),\\s*" +
        ".*?" +
        "SSID:\\s*([^,]+),\\s*" +
        "BSSID:\\s*([^.,]+)"
    );
    
    @Override
    public String getName() {
        return "AC访问日志分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.AC_ACCESS_LOG;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 支持办公区客户端IP和VPN用户IP的AC访问日志分析
        return ipType == IpType.OFFICE_CLIENT || ipType == IpType.VPN_USER;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行AC访问日志分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建查询语句，固定查询最近1天的数据
            String query = String.format("_time:3d and stream:\"AUDITLOG_AC\" and \"%s\" | limit 10", 
                context.getHostIp());
            
            logger.info("AC访问日志查询语句: {}", query);
            
            // 调用VictoriaLogs查询日志
            String logsResult = victoriaLogsService.queryLogs(query, 10, "30s", null);
            
            // 解析日志结果
            List<Map<String, Object>> acRecords = parseACLogs(logsResult, context.getHostIp());
            
            // 构建分析结果
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("targetIp", context.getHostIp());
            analysisData.put("acRecords", acRecords);
            analysisData.put("totalRecords", acRecords.size());
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(context.getHostIp(), acRecords);
            analysisData.put("analysisSummary", summary);
            
            // 统计信息
            Map<String, Object> statistics = generateStatistics(acRecords);
            analysisData.put("statistics", statistics);
            
            logger.info("AC访问日志分析完成，主机IP: {}, 记录数: {}", 
                context.getHostIp(), acRecords.size());
                
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("AC访问日志分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "AC访问日志分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // AC访问日志分析优先级为2，在基本信息之后执行
        return 2;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间8秒
        return 8000;
    }
    
    /**
     * 解析AC访问日志
     * @param logsResult 日志查询结果
     * @param targetIp 目标IP
     * @return 解析后的AC记录列表
     */
    private List<Map<String, Object>> parseACLogs(String logsResult, String targetIp) {
        List<Map<String, Object>> records = new ArrayList<>();
        
        if (logsResult == null || logsResult.trim().isEmpty()) {
            logger.warn("AC访问日志查询结果为空，IP: {}", targetIp);
            return records;
        }
        
        try {
            // 尝试解析为JSON格式（VictoriaLogs标准返回格式）
            try {
                JSONObject jsonResult = JSON.parseObject(logsResult);
                if (jsonResult != null && jsonResult.containsKey("data")) {
                    // 处理JSON格式的返回结果
                    return parseJSONLogs(jsonResult, targetIp);
                }
            } catch (Exception e) {
                logger.debug("无法解析为JSON格式，尝试按行解析: {}", e.getMessage());
            }
            
            // 按行分割日志（原始文本格式）
            String[] lines = logsResult.split("\n");
            
            for (String line : lines) {
                if (line.trim().isEmpty()) {
                    continue;
                }
                
                try {
                    // 解析每行日志
                    Map<String, Object> record = parseACLogLine(line);
                    if (record != null && record.containsKey("clientIP") && 
                        targetIp.equals(record.get("clientIP"))) {
                        records.add(record);
                    }
                } catch (Exception e) {
                    logger.debug("解析AC日志行失败: {}, 错误: {}", line, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("解析AC访问日志异常", e);
        }
        
        return records;
    }
    
    /**
     * 解析JSON格式的VictoriaLogs返回结果
     * @param jsonResult JSON结果
     * @param targetIp 目标IP
     * @return 解析后的AC记录列表
     */
    private List<Map<String, Object>> parseJSONLogs(JSONObject jsonResult, String targetIp) {
        List<Map<String, Object>> records = new ArrayList<>();
        
        try {
            // 检查是否为VictoriaLogs的标准JSON格式
            if (jsonResult.containsKey("data")) {
                // 如果data是字符串，按行分割处理
                Object data = jsonResult.get("data");
                if (data instanceof String) {
                    String[] lines = ((String) data).split("\n");
                    for (String line : lines) {
                        if (line.trim().isEmpty()) {
                            continue;
                        }
                        
                        try {
                            // 尝试解析每行为JSON
                            JSONObject logEntry = JSON.parseObject(line);
                            Map<String, Object> record = parseJSONLogEntry(logEntry, targetIp);
                            if (record != null) {
                                records.add(record);
                            }
                        } catch (Exception e) {
                            // 如果不是JSON格式，按普通文本处理
                            Map<String, Object> record = parseACLogLine(line);
                            if (record != null && record.containsKey("clientIP") && 
                                targetIp.equals(record.get("clientIP"))) {
                                records.add(record);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析JSON格式AC日志异常", e);
        }
        
        return records;
    }
    
    /**
     * 解析单个JSON日志条目
     * @param logEntry JSON日志条目
     * @param targetIp 目标IP
     * @return 解析后的记录，如果不匹配返回null
     */
    private Map<String, Object> parseJSONLogEntry(JSONObject logEntry, String targetIp) {
        try {
            // 获取日志消息内容
            String message = logEntry.getString("_msg");
            String timestamp = logEntry.getString("_time");
            
            if (message == null || !message.contains("Client MAC:") || !message.contains("Username:")) {
                return null;
            }
            
            // 使用现有的解析方法处理消息内容
            Map<String, Object> record = parseACLogLine(message);
            if (record != null && record.containsKey("clientIP") && 
                targetIp.equals(record.get("clientIP"))) {
                
                // 添加时间戳信息
                if (timestamp != null) {
                    record.put("timestamp", timestamp);
                }
                
                return record;
            }
        } catch (Exception e) {
            logger.debug("解析JSON日志条目异常: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 解析单行AC日志
     * @param logLine 日志行
     * @return 解析后的记录，如果解析失败返回null
     */
    private Map<String, Object> parseACLogLine(String logLine) {
        try {
            // 查找包含AC日志特征的部分
            if (!logLine.contains("Client MAC:") || !logLine.contains("Username:")) {
                return null;
            }
            
            // 使用正则表达式提取关键字段
            Matcher matcher = AC_LOG_PATTERN.matcher(logLine);
            if (matcher.find()) {
                Map<String, Object> record = new HashMap<>();
                record.put("clientMAC", matcher.group(1).trim());
                record.put("clientIP", matcher.group(2).trim());
                record.put("username", matcher.group(3).trim());
                record.put("apName", matcher.group(4).trim());
                record.put("ssid", matcher.group(5).trim());
                record.put("bssid", matcher.group(6).trim());
                
                // 提取时间戳（从日志行开头提取）
                String timestamp = extractTimestamp(logLine);
                if (timestamp != null) {
                    record.put("timestamp", timestamp);
                }
                
                // 保存原始日志行用于详情展示
                record.put("rawLog", logLine);
                
                return record;
            }
            
        } catch (Exception e) {
            logger.debug("解析AC日志行异常: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 从日志行中提取时间戳
     * @param logLine 日志行
     * @return 时间戳字符串
     */
    private String extractTimestamp(String logLine) {
        try {
            // AC日志通常以时间戳开头，格式如：Jun  4 14:44:33
            Pattern timestampPattern = Pattern.compile("^(\\w{3}\\s+\\d{1,2}\\s+\\d{2}:\\d{2}:\\d{2})");
            Matcher matcher = timestampPattern.matcher(logLine);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            logger.debug("提取时间戳失败: {}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 生成分析摘要
     * @param targetIp 目标IP
     * @param records AC记录列表
     * @return 分析摘要
     */
    private String generateAnalysisSummary(String targetIp, List<Map<String, Object>> records) {
        if (records.isEmpty()) {
            return String.format("未发现IP %s 的AC访问日志记录。可能该IP未通过无线网络接入，或者不在查询时间范围内。", targetIp);
        }
        
        // 统计唯一用户名
        Set<String> uniqueUsers = new HashSet<>();
        Set<String> uniqueAPs = new HashSet<>();
        Set<String> uniqueSSIDs = new HashSet<>();
        
        for (Map<String, Object> record : records) {
            String username = (String) record.get("username");
            String apName = (String) record.get("apName");
            String ssid = (String) record.get("ssid");
            
            if (username != null && !username.trim().isEmpty() && !"-NA-".equals(username)) {
                uniqueUsers.add(username);
            }
            if (apName != null && !apName.trim().isEmpty()) {
                uniqueAPs.add(apName);
            }
            if (ssid != null && !ssid.trim().isEmpty()) {
                uniqueSSIDs.add(ssid);
            }
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("在最近3天内，IP %s 的AC访问日志中发现 %d 条记录。", targetIp, records.size()));
        
        if (!uniqueUsers.isEmpty()) {
            summary.append(String.format(" 涉及 %d 个唯一用户：%s。", 
                uniqueUsers.size(), String.join(", ", uniqueUsers)));
        }
        
        if (!uniqueAPs.isEmpty()) {
            summary.append(String.format(" 主要接入点：%s。", String.join(", ", uniqueAPs)));
        }
        
        if (!uniqueSSIDs.isEmpty()) {
            summary.append(String.format(" 使用的无线网络：%s。", String.join(", ", uniqueSSIDs)));
        }
        
        return summary.toString();
    }
    
    /**
     * 生成统计信息
     * @param records AC记录列表
     * @return 统计信息
     */
    private Map<String, Object> generateStatistics(List<Map<String, Object>> records) {
        Map<String, Object> stats = new HashMap<>();
        
        if (records.isEmpty()) {
            stats.put("totalRecords", 0);
            stats.put("uniqueUsers", 0);
            stats.put("uniqueAPs", 0);
            stats.put("uniqueSSIDs", 0);
            return stats;
        }
        
        Set<String> users = new HashSet<>();
        Set<String> aps = new HashSet<>();
        Set<String> ssids = new HashSet<>();
        Map<String, Integer> userCounts = new HashMap<>();
        Map<String, Integer> apCounts = new HashMap<>();
        Map<String, Integer> ssidCounts = new HashMap<>();
        
        for (Map<String, Object> record : records) {
            String username = (String) record.get("username");
            String apName = (String) record.get("apName");
            String ssid = (String) record.get("ssid");
            
            if (username != null && !username.trim().isEmpty() && !"-NA-".equals(username)) {
                users.add(username);
                userCounts.put(username, userCounts.getOrDefault(username, 0) + 1);
            }
            if (apName != null && !apName.trim().isEmpty()) {
                aps.add(apName);
                apCounts.put(apName, apCounts.getOrDefault(apName, 0) + 1);
            }
            if (ssid != null && !ssid.trim().isEmpty()) {
                ssids.add(ssid);
                ssidCounts.put(ssid, ssidCounts.getOrDefault(ssid, 0) + 1);
            }
        }
        
        stats.put("totalRecords", records.size());
        stats.put("uniqueUsers", users.size());
        stats.put("uniqueAPs", aps.size());
        stats.put("uniqueSSIDs", ssids.size());
        stats.put("userCounts", userCounts);
        stats.put("apCounts", apCounts);
        stats.put("ssidCounts", ssidCounts);
        
        return stats;
    }
} 