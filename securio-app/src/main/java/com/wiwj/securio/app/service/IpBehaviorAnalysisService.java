package com.wiwj.securio.app.service;

import com.wiwj.securio.app.domain.dto.IpBehaviorAnalysisRequestDTO;
import com.wiwj.securio.app.domain.dto.IpBehaviorAnalysisResponseDTO;

/**
 * IP行为分析服务接口
 *
 * <AUTHOR>
 */
public interface IpBehaviorAnalysisService {

    /**
     * 执行IP行为分析
     *
     * @param request 分析请求参数
     * @return IP行为分析结果
     */
    IpBehaviorAnalysisResponseDTO analyzeIpBehavior(IpBehaviorAnalysisRequestDTO request);
} 