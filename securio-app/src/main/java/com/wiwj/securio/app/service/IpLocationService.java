package com.wiwj.securio.app.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wiwj.securio.app.dto.IpLocationInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.regex.Pattern;

/**
 * IP归属信息查询服务
 */
@Service
public class IpLocationService {
    
    private static final Logger log = LoggerFactory.getLogger(IpLocationService.class);
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    // IP地址格式验证正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$"
    );
    
    // 内网IP地址范围
    private static final List<Pattern> PRIVATE_IP_PATTERNS = Arrays.asList(
        Pattern.compile("^10\\..*"),
        Pattern.compile("^172\\.(1[6-9]|2[0-9]|3[0-1])\\..*"),
        Pattern.compile("^192\\.168\\..*"),
        Pattern.compile("^127\\..*"),
        Pattern.compile("^169\\.254\\..*")
    );
    
    // API配置列表
    private static final List<ApiConfig> API_CONFIGS = Arrays.asList(
        new ApiConfig("ipgeolocation", "https://api.ipgeolocation.io/ipgeo?apiKey=********************************&ip={ip}")
        //new ApiConfig("ip-api", "http://ip-api.com/json/{ip}"),
        //new ApiConfig("freeipapi", "https://freeipapi.com/api/json/{ip}")
    );
    
    private static final int MAX_RETRY_COUNT = 5;
    private final Random random = new Random();
    
    /**
     * 获取IP归属信息
     * @param ip IP地址
     * @return IP归属信息
     */
    public IpLocationInfo getIpLocation(String ip) {
        // 验证IP地址格式
        if (!isValidIp(ip)) {
            log.warn("无效的IP地址格式: {}", ip);
            return null;
        }
        
        // 检查是否为内网IP
        if (isPrivateIp(ip)) {
            log.info("内网IP地址不需要查询归属信息: {}", ip);
            return createPrivateIpInfo(ip);
        }
        
        // 随机选择API并重试
        for (int attempt = 0; attempt < MAX_RETRY_COUNT; attempt++) {
            ApiConfig apiConfig = getRandomApiConfig();
            try {
                log.info("第{}次尝试查询IP归属信息: {}, 使用API: {}", attempt + 1, ip, apiConfig.getName());
                IpLocationInfo result = queryIpLocation(ip, apiConfig);
                if (result != null) {
                    log.info("成功获取IP归属信息: {}, API: {}", ip, apiConfig.getName());
                    return result;
                }
            } catch (Exception e) {
                log.warn("查询IP归属信息失败: {}, API: {}, 错误: {}", ip, apiConfig.getName(), e.getMessage());
                if (attempt < MAX_RETRY_COUNT - 1) {
                    // 等待一段时间后重试
                    try {
                        Thread.sleep(1000 * (attempt + 1)); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        log.error("所有API都查询失败，无法获取IP归属信息: {}", ip);
        return null;
    }
    
    /**
     * 查询单个API的IP归属信息
     */
    private IpLocationInfo queryIpLocation(String ip, ApiConfig apiConfig) {
        try {
            String url = apiConfig.getUrl().replace("{ip}", ip);
            String response = restTemplate.getForObject(url, String.class);
            
            if (response == null || response.trim().isEmpty()) {
                log.warn("API返回空响应: {}", apiConfig.getName());
                return null;
            }
            
            return parseApiResponse(response, apiConfig.getName(), ip);
            
        } catch (Exception e) {
            log.error("调用IP归属API失败: {}, 错误: {}", apiConfig.getName(), e.getMessage());
            throw e;
        }
    }
    
    /**
     * 解析API响应
     */
    private IpLocationInfo parseApiResponse(String response, String apiName, String ip) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response);
            IpLocationInfo info = new IpLocationInfo(ip);
            info.setApiSource(apiName);
            
            switch (apiName) {
                case "ipgeolocation":
                    parseIpGeolocationResponse(jsonNode, info);
                    break;
                case "ip-api":
                    parseIpApiResponse(jsonNode, info);
                    break;
                case "freeipapi":
                    parseIpApiCoResponse(jsonNode, info);
                    break;
                default:
                    log.warn("未知的API类型: {}", apiName);
                    return null;
            }
            
            return info;
            
        } catch (Exception e) {
            log.error("解析API响应失败: {}, 错误: {}", apiName, e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析 ipgeolocation.io API 响应
     */
    private void parseIpGeolocationResponse(JsonNode jsonNode, IpLocationInfo info) {
        info.setCountry(getTextValue(jsonNode, "country_name"));
        info.setCountryCode(getTextValue(jsonNode, "country_code2"));
        info.setRegion(getTextValue(jsonNode, "state_code"));
        info.setRegionName(getTextValue(jsonNode, "state_prov"));
        info.setCity(getTextValue(jsonNode, "city"));
        info.setZipcode(getTextValue(jsonNode, "zipcode"));
        info.setLatitude(getDoubleValue(jsonNode, "latitude"));
        info.setLongitude(getDoubleValue(jsonNode, "longitude"));
        info.setTimezone(getTextValue(jsonNode, "time_zone", "name"));
        info.setIsp(getTextValue(jsonNode, "isp"));
        info.setOrganization(getTextValue(jsonNode, "organization"));
        info.setCountryFlag(getTextValue(jsonNode, "country_flag"));
        
        // 处理货币信息
        JsonNode currency = jsonNode.get("currency");
        if (currency != null) {
            info.setCurrency(getTextValue(currency, "code"));
        }
    }
    
    /**
     * 解析 ip-api.com API 响应
     */
    private void parseIpApiResponse(JsonNode jsonNode, IpLocationInfo info) {
        // 检查状态
        if (!"success".equals(getTextValue(jsonNode, "status"))) {
            log.warn("ip-api.com返回失败状态: {}", getTextValue(jsonNode, "message"));
            return;
        }
        
        info.setCountry(getTextValue(jsonNode, "country"));
        info.setCountryCode(getTextValue(jsonNode, "countryCode"));
        info.setRegion(getTextValue(jsonNode, "region"));
        info.setRegionName(getTextValue(jsonNode, "regionName"));
        info.setCity(getTextValue(jsonNode, "city"));
        info.setZipcode(getTextValue(jsonNode, "zip"));
        info.setLatitude(getDoubleValue(jsonNode, "lat"));
        info.setLongitude(getDoubleValue(jsonNode, "lon"));
        info.setTimezone(getTextValue(jsonNode, "timezone"));
        info.setIsp(getTextValue(jsonNode, "isp"));
        info.setOrganization(getTextValue(jsonNode, "org"));
        info.setAsn(getTextValue(jsonNode, "as"));
    }
    
    /**
     * 解析 freeipapi.com API 响应
     */
    private void parseIpApiCoResponse(JsonNode jsonNode, IpLocationInfo info) {
        info.setCountry(getTextValue(jsonNode, "countryName"));
        info.setCountryCode(getTextValue(jsonNode, "countryCode"));
        info.setRegionName(getTextValue(jsonNode, "regionName"));
        info.setCity(getTextValue(jsonNode, "cityName"));
        info.setZipcode(getTextValue(jsonNode, "zipCode"));
        info.setLatitude(getDoubleValue(jsonNode, "latitude"));
        info.setLongitude(getDoubleValue(jsonNode, "longitude"));
        info.setTimezone(getTextValue(jsonNode, "timeZone"));
        
        // freeipapi返回的currency是个对象，提取code
        JsonNode currency = jsonNode.get("currency");
        if (currency != null) {
            info.setCurrency(getTextValue(currency, "code"));
        }
    }
    
    /**
     * 验证IP地址格式
     */
    private boolean isValidIp(String ip) {
        return ip != null && IP_PATTERN.matcher(ip).matches();
    }
    
    /**
     * 检查是否为内网IP
     */
    private boolean isPrivateIp(String ip) {
        return PRIVATE_IP_PATTERNS.stream().anyMatch(pattern -> pattern.matcher(ip).matches());
    }
    
    /**
     * 创建内网IP信息
     */
    private IpLocationInfo createPrivateIpInfo(String ip) {
        IpLocationInfo info = new IpLocationInfo(ip);
        info.setCountry("内网");
        info.setRegionName("内网");
        info.setCity("内网");
        info.setApiSource("internal");
        return info;
    }
    
    /**
     * 随机选择API配置
     */
    private ApiConfig getRandomApiConfig() {
        return API_CONFIGS.get(random.nextInt(API_CONFIGS.size()));
    }
    
    /**
     * 安全获取文本值
     */
    private String getTextValue(JsonNode node, String... paths) {
        JsonNode current = node;
        for (String path : paths) {
            if (current == null || !current.has(path)) {
                return null;
            }
            current = current.get(path);
        }
        return current != null && !current.isNull() ? current.asText() : null;
    }
    
    /**
     * 安全获取Double值
     */
    private Double getDoubleValue(JsonNode node, String path) {
        JsonNode valueNode = node.get(path);
        if (valueNode != null && !valueNode.isNull()) {
            try {
                return valueNode.asDouble();
            } catch (Exception e) {
                log.warn("无法解析Double值: {}", valueNode.asText());
            }
        }
        return null;
    }
    
    /**
     * API配置类
     */
    private static class ApiConfig {
        private final String name;
        private final String url;
        
        public ApiConfig(String name, String url) {
            this.name = name;
            this.url = url;
        }
        
        public String getName() {
            return name;
        }
        
        public String getUrl() {
            return url;
        }
    }
} 