package com.wiwj.securio.app.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.AssertTrue;

/**
 * 主机综合分析请求DTO
 *
 * <AUTHOR>
 */
@ApiModel(description = "主机综合分析请求参数")
public class HostAnalysisRequestDTO {

    @ApiModelProperty(value = "主机IP地址", required = true, example = "*********")
    @NotBlank(message = "主机IP不能为空")
    @Pattern(regexp = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$", 
             message = "主机IP格式不正确")
    private String hostIp;

    @ApiModelProperty(value = "开始时间", example = "2024-01-01T00:00:00Z")
    private String startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-01-01T23:59:59Z")
    private String endTime;

    @ApiModelProperty(value = "时间范围快捷选择", example = "1h")
    private String timeRange;

    @ApiModelProperty(value = "分析深度", example = "basic")
    private String analysisLevel = "basic";

    public HostAnalysisRequestDTO() {
    }

    /**
     * 自定义验证：要么提供精确时间（startTime和endTime），要么提供时间范围（timeRange）
     */
    @AssertTrue(message = "请提供时间参数：要么指定开始时间和结束时间，要么指定时间范围（如1h、30m等）")
    public boolean isValidTimeParameter() {
        // 检查是否提供了精确时间
        boolean hasExactTime = (startTime != null && !startTime.trim().isEmpty()) && 
                              (endTime != null && !endTime.trim().isEmpty());
        
        // 检查是否提供了相对时间
        boolean hasRelativeTime = (timeRange != null && !timeRange.trim().isEmpty()) &&
                                 !timeRange.equals("precise"); // 排除精确时间标识符
        
        // 至少要有一种时间参数
        return hasExactTime || hasRelativeTime;
    }

    /**
     * 判断是否为精确时间模式
     */
    public boolean isPreciseTimeMode() {
        return (startTime != null && !startTime.trim().isEmpty()) && 
               (endTime != null && !endTime.trim().isEmpty());
    }

    /**
     * 判断是否为相对时间模式
     */
    public boolean isRelativeTimeMode() {
        return (timeRange != null && !timeRange.trim().isEmpty()) &&
               !timeRange.equals("precise") && !isPreciseTimeMode();
    }

    public String getHostIp() {
        return hostIp;
    }

    public void setHostIp(String hostIp) {
        this.hostIp = hostIp;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getTimeRange() {
        return timeRange;
    }

    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }

    public String getAnalysisLevel() {
        return analysisLevel;
    }

    public void setAnalysisLevel(String analysisLevel) {
        this.analysisLevel = analysisLevel;
    }

    @Override
    public String toString() {
        return "HostAnalysisRequestDTO{" +
                "hostIp='" + hostIp + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", timeRange='" + timeRange + '\'' +
                ", analysisLevel='" + analysisLevel + '\'' +
                ", preciseTimeMode=" + isPreciseTimeMode() +
                ", relativeTimeMode=" + isRelativeTimeMode() +
                '}';
    }
} 