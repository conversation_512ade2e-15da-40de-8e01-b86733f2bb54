package com.wiwj.securio.app.analysis.core;

import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.app.domain.dto.HostAnalysisProgressDTO;
import com.wiwj.securio.app.service.HostAnalysisProgressService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 分析器调度器
 * 负责根据IP类型选择合适的分析器组，并协调执行分析任务
 *
 * <AUTHOR>
 */
@Component
public class AnalysisDispatcher {
    
    private static final Logger logger = LoggerFactory.getLogger(AnalysisDispatcher.class);
    
    @Autowired
    private List<Analyzer> analyzers;
    
    @Autowired
    private HostAnalysisProgressService progressService;
    
    /** 分析器执行线程池 */
    private ExecutorService executorService;
    
    /** 按IP类型分组的分析器 */
    private Map<IpType, List<Analyzer>> analyzerGroups;
    
    @PostConstruct
    public void init() {
        // 初始化线程池
        this.executorService = Executors.newFixedThreadPool(10);
        
        // 初始化分析器分组
        initAnalyzerGroups();
        
        logger.info("分析器调度器初始化完成，共加载 {} 个分析器", analyzers.size());
    }
    
    /**
     * 初始化分析器分组
     */
    private void initAnalyzerGroups() {
        analyzerGroups = new EnumMap<>(IpType.class);
        
        for (IpType ipType : IpType.values()) {
            List<Analyzer> supportedAnalyzers = analyzers.stream()
                    .filter(analyzer -> analyzer.supports(ipType))
                    .sorted(Comparator.comparingInt(Analyzer::getPriority))
                    .collect(Collectors.toList());
            
            analyzerGroups.put(ipType, supportedAnalyzers);
            
            logger.info("IP类型 {} 支持的分析器: {}", 
                ipType.getDescription(), 
                supportedAnalyzers.stream().map(Analyzer::getName).collect(Collectors.joining(", ")));
        }
    }
    
    /**
     * 执行综合分析
     * @param context 分析上下文
     * @return 综合分析结果
     */
    public Map<AnalyzerType, AnalysisResult> executeAnalysis(AnalysisContext context) {
        String taskId = context.getTaskId();
        IpType ipType = context.getIpType();
        
        logger.info("开始执行综合分析，任务ID: {}, 主机IP: {}, IP类型: {}", 
            taskId, context.getHostIp(), ipType.getDescription());
        
        // 发送开始分析进度
        sendProgress(taskId, "开始综合分析", 0, "RUNNING");
        
        // 获取支持的分析器
        List<Analyzer> supportedAnalyzers = analyzerGroups.getOrDefault(ipType, Collections.emptyList());
        
        if (supportedAnalyzers.isEmpty()) {
            logger.warn("没有找到支持IP类型 {} 的分析器", ipType.getDescription());
            sendProgress(taskId, "没有找到支持的分析器", 0, "ERROR");
            return Collections.emptyMap();
        }
        
        // 计算总的预估执行时间
        long totalEstimatedTime = supportedAnalyzers.stream()
                .mapToLong(Analyzer::getEstimatedExecutionTime)
                .sum();
        
        // 并行执行分析器
        Map<AnalyzerType, AnalysisResult> results = executeAnalyzersInParallel(
            context, supportedAnalyzers, totalEstimatedTime);
        
        // 发送完成进度
        long successCount = results.values().stream()
                .mapToLong(result -> result.isSuccess() ? 1 : 0)
                .sum();
        
        sendProgress(taskId, 
            String.format("综合分析完成，成功执行 %d/%d 个分析器", successCount, supportedAnalyzers.size()), 
            100, "COMPLETED");
        
        logger.info("综合分析完成，任务ID: {}, 成功: {}/{}", taskId, successCount, supportedAnalyzers.size());
        
        return results;
    }
    
    /**
     * 并行执行分析器
     */
    private Map<AnalyzerType, AnalysisResult> executeAnalyzersInParallel(
            AnalysisContext context, List<Analyzer> analyzers, long totalEstimatedTime) {
        
        String taskId = context.getTaskId();
        Map<AnalyzerType, AnalysisResult> results = new HashMap<>();
        
        // 创建并行任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        long accumulatedTime = 0;
        for (Analyzer analyzer : analyzers) {
            final long currentProgress = accumulatedTime * 90 / totalEstimatedTime; // 90%用于执行分析器
            accumulatedTime += analyzer.getEstimatedExecutionTime();
            
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 发送当前分析器开始进度
                    sendProgress(taskId, 
                        "正在执行: " + analyzer.getName(), 
                        (int) (10 + currentProgress), 
                        "RUNNING");
                    
                    // 执行分析
                    AnalysisResult result = analyzer.analyze(context);
                    
                    // 存储结果
                    synchronized (results) {
                        results.put(analyzer.getType(), result);
                    }
                    
                    if (result.isSuccess()) {
                        logger.info("分析器 {} 执行成功，耗时: {}ms", 
                            analyzer.getName(), result.getExecutionTime());
                    } else {
                        logger.error("分析器 {} 执行失败: {}", 
                            analyzer.getName(), result.getErrorMessage());
                    }
                    
                } catch (Exception e) {
                    logger.error("分析器 {} 执行异常", analyzer.getName(), e);
                    
                    // 创建失败结果
                    AnalysisResult failureResult = AnalysisResult.failure(
                        analyzer.getType(), analyzer.getName(), "执行异常: " + e.getMessage());
                    
                    synchronized (results) {
                        results.put(analyzer.getType(), failureResult);
                    }
                }
            }, executorService);
            
            futures.add(future);
        }
        
        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(5, TimeUnit.MINUTES); // 5分钟超时
        } catch (Exception e) {
            logger.error("等待分析器执行完成时发生异常，任务ID: {}", taskId, e);
            sendProgress(taskId, "分析器执行超时或异常", 0, "ERROR");
        }
        
        return results;
    }
    
    /**
     * 发送进度更新
     */
    private void sendProgress(String taskId, String message, int progress, String status) {
        try {
            HostAnalysisProgressDTO progressDTO = new HostAnalysisProgressDTO(
                taskId, status, message, progress, status, message
            );
            progressService.sendProgress(taskId, progressDTO);
        } catch (Exception e) {
            logger.error("发送进度更新失败，任务ID: {}", taskId, e);
        }
    }
    
    /**
     * 获取支持指定IP类型的分析器
     */
    public List<Analyzer> getSupportedAnalyzers(IpType ipType) {
        return analyzerGroups.getOrDefault(ipType, Collections.emptyList());
    }
    
    /**
     * 关闭调度器
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
} 