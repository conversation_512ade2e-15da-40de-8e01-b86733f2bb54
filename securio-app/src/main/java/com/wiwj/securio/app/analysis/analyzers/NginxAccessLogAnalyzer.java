package com.wiwj.securio.app.analysis.analyzers;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.agent.mapper.InventoryPortInfoMapper;
import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.app.domain.dto.IpBehaviorAnalysisResponseDTO;
import com.wiwj.securio.logmgr.util.TimeFormatUtil;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Nginx访问日志分析器
 * 负责分析IP的网络请求链路，构建桑基图数据
 *
 * <AUTHOR>
 */
@Component
public class NginxAccessLogAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(NginxAccessLogAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Autowired
    private InventoryPortInfoMapper inventoryPortInfoMapper;
    
    @Override
    public String getName() {
        return "Nginx访问日志分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.NGINX_ACCESS_LOG;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 支持所有IP类型的Nginx访问日志分析
        return ipType != IpType.UNKNOWN;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行Nginx访问日志分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建分析结果
            IpBehaviorAnalysisResponseDTO.SankeyChartData sankeyData = buildSankeyData(context);
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(sankeyData, context.getHostIp());
            
            // 构建完整的响应数据
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("sankeyData", sankeyData);
            analysisData.put("analysisSummary", summary);
            analysisData.put("targetIp", context.getHostIp());
            analysisData.put("timeRange", getTimeRangeDescription(context));
            
            logger.info("Nginx访问日志分析完成，主机IP: {}, 节点数: {}, 连接数: {}", 
                context.getHostIp(), 
                sankeyData.getNodes().size(), 
                sankeyData.getLinks().size());
                
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("Nginx访问日志分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "Nginx访问日志分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // 网络日志分析优先级为3，在基本信息之后执行
        return 3;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间10秒（涉及日志查询）
        return 10000;
    }
    
    /**
     * 构建桑基图数据
     */
    private IpBehaviorAnalysisResponseDTO.SankeyChartData buildSankeyData(AnalysisContext context) {
        IpBehaviorAnalysisResponseDTO.SankeyChartData sankeyData = new IpBehaviorAnalysisResponseDTO.SankeyChartData();
        
        List<IpBehaviorAnalysisResponseDTO.SankeyNode> nodes = new ArrayList<>();
        List<IpBehaviorAnalysisResponseDTO.SankeyLink> links = new ArrayList<>();

        try {
            // 1. 获取客户端来源IP到目标IP的请求数据
            List<Map<String, Object>> sourceToTargetData = getSourceToTargetData(context);
            
            // 2. 获取目标IP到上游主机的请求数据
            List<Map<String, Object>> targetToUpstreamData = getTargetToUpstreamData(context);

            // 3. 收集所有IP地址用于批量查询hostname
            Set<String> allIps = new HashSet<>();
            allIps.add(context.getHostIp()); // 目标IP
            
            // 收集来源IP
            for (Map<String, Object> item : sourceToTargetData) {
                String sourceIp = (String) item.get("source_ip");
                if (sourceIp != null) {
                    allIps.add(sourceIp);
                }
            }
            
            // 收集上游IP
            for (Map<String, Object> item : targetToUpstreamData) {
                String upstreamIp = (String) item.get("upstream_ip");
                if (upstreamIp != null) {
                    allIps.add(upstreamIp);
                }
            }

            // 4. 批量查询IP对应的hostname
            Map<String, String> ipHostnameMap = getIpHostnameMap(allIps);

            // 5. 构建节点和连接
            Set<String> nodeNames = new HashSet<>();
            
            // 添加目标IP节点
            String targetDisplayName = getDisplayName(context.getHostIp(), ipHostnameMap);
            nodes.add(new IpBehaviorAnalysisResponseDTO.SankeyNode(targetDisplayName, "target"));
            nodeNames.add(targetDisplayName);

            // 处理来源IP数据
            for (Map<String, Object> item : sourceToTargetData) {
                String sourceIp = (String) item.get("source_ip");
                Long requestCount = (Long) item.get("request_count");

                if (sourceIp != null) {
                    String sourceDisplayName = getDisplayName(sourceIp, ipHostnameMap);
                    if (!nodeNames.contains(sourceDisplayName)) {
                        nodes.add(new IpBehaviorAnalysisResponseDTO.SankeyNode(sourceDisplayName, "source"));
                        nodeNames.add(sourceDisplayName);
                    }
                    links.add(new IpBehaviorAnalysisResponseDTO.SankeyLink(sourceDisplayName, targetDisplayName, requestCount));
                }
            }

            // 处理上游IP数据
            for (Map<String, Object> item : targetToUpstreamData) {
                String upstreamIp = (String) item.get("upstream_ip");
                Long requestCount = (Long) item.get("request_count");

                if (upstreamIp != null) {
                    String upstreamDisplayName = getDisplayName(upstreamIp, ipHostnameMap);
                    if (!nodeNames.contains(upstreamDisplayName)) {
                        nodes.add(new IpBehaviorAnalysisResponseDTO.SankeyNode(upstreamDisplayName, "upstream"));
                        nodeNames.add(upstreamDisplayName);
                    }
                    links.add(new IpBehaviorAnalysisResponseDTO.SankeyLink(targetDisplayName, upstreamDisplayName, requestCount));
                }
            }

            sankeyData.setNodes(nodes);
            sankeyData.setLinks(links);

            logger.info("桑基图数据构建完成，节点数: {}, 连接数: {}, hostname映射数: {}", 
                       nodes.size(), links.size(), ipHostnameMap.size());

        } catch (Exception e) {
            logger.error("构建桑基图数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("构建桑基图数据失败: " + e.getMessage(), e);
        }

        return sankeyData;
    }
    
    /**
     * 获取客户端来源IP到目标IP的请求数据
     */
    private List<Map<String, Object>> getSourceToTargetData(AnalysisContext context) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 构建时间查询
            String timeQuery = buildTimeQuery(context);
            
            // 构建查询语句
            String query = String.format("%s and stream:\"NGINX_ACCESS\" and dst_ip:\"%s\" | stats by (src_ip) count() cnt | sort by (cnt desc) ",
                    timeQuery, context.getHostIp());

            logger.info("执行来源IP查询: {}", query);

            // 调用VictoriaLogs API，指定vmlog2实例
            String queryResult = victoriaLogsService.queryLogs(query, 10, "60s", "vmlog2");
            
            // 解析查询结果
            if (queryResult != null && !queryResult.trim().isEmpty()) {
                String[] lines = queryResult.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;
                    
                    try {
                        JSONObject json = JSON.parseObject(line.trim());
                        String sourceIp = json.getString("src_ip");
                        String countStr = json.getString("cnt");
                        
                        if (sourceIp != null && countStr != null) {
                            Long count = Long.parseLong(countStr);
                            Map<String, Object> item = new HashMap<>();
                            item.put("source_ip", sourceIp);
                            item.put("request_count", count);
                            result.add(item);
                            logger.debug("解析来源IP数据: {} -> {}", sourceIp, count);
                        }
                    } catch (Exception e) {
                        logger.warn("解析来源IP数据行失败: {}, 错误: {}", line, e.getMessage());
                    }
                }
            }

            logger.info("获取来源IP数据完成，数据条数: {}", result.size());

        } catch (Exception e) {
            logger.error("获取来源IP数据失败: {}", e.getMessage(), e);
        }

        return result;
    }
    
    /**
     * 获取目标IP到上游主机的请求数据
     */
    private List<Map<String, Object>> getTargetToUpstreamData(AnalysisContext context) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 构建时间查询
            String timeQuery = buildTimeQuery(context);
            
            // 构建查询语句
            String query = String.format("%s and stream:\"NGINX_ACCESS\" and src_ip:\"%s\" | stats by (dst_ip) count() cnt | sort by (cnt desc)",
                    timeQuery, context.getHostIp());

            logger.info("执行上游IP查询: {}", query);

            // 调用VictoriaLogs API，指定vmlog2实例
            String queryResult = victoriaLogsService.queryLogs(query, 10, "60s", "vmlog2");
            
            // 解析查询结果
            if (queryResult != null && !queryResult.trim().isEmpty()) {
                String[] lines = queryResult.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;
                    
                    try {
                        JSONObject json = JSON.parseObject(line.trim());
                        String upstreamIp = json.getString("dst_ip");
                        String countStr = json.getString("cnt");
                        
                        if (upstreamIp != null && countStr != null) {
                            Long count = Long.parseLong(countStr);
                            Map<String, Object> item = new HashMap<>();
                            item.put("upstream_ip", upstreamIp);
                            item.put("request_count", count);
                            result.add(item);
                            logger.debug("解析上游IP数据: {} -> {}", upstreamIp, count);
                        }
                    } catch (Exception e) {
                        logger.warn("解析上游IP数据行失败: {}, 错误: {}", line, e.getMessage());
                    }
                }
            }

            logger.info("获取上游IP数据完成，数据条数: {}", result.size());

        } catch (Exception e) {
            logger.error("获取上游IP数据失败: {}", e.getMessage(), e);
        }

        return result;
    }
    
    /**
     * 构建时间查询条件
     */
    private String buildTimeQuery(AnalysisContext context) {
        // 从上下文获取时间参数
        String startTimeStr = null;
        String endTimeStr = null;
        
        if (context.getStartTime() != null) {
            startTimeStr = context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
        if (context.getEndTime() != null) {
            endTimeStr = context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
        
        String timeRange = context.getTimeRange();
        
        // 使用TimeFormatUtil统一处理时间查询条件
        return TimeFormatUtil.buildTimeQuery(startTimeStr, endTimeStr, timeRange);
    }
    
    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(AnalysisContext context) {
        if (context.getStartTime() != null && context.getEndTime() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return context.getStartTime().format(formatter) + " ~ " + context.getEndTime().format(formatter);
        } else if (context.getTimeRange() != null) {
            return "最近 " + context.getTimeRange();
        } else {
            return "最近 30 分钟";
        }
    }
    
    /**
     * 生成分析摘要
     */
    private String generateAnalysisSummary(IpBehaviorAnalysisResponseDTO.SankeyChartData sankeyData, String targetIp) {
        StringBuilder summary = new StringBuilder();
        
        try {
            int sourceCount = 0;
            int upstreamCount = 0;
            long totalIncoming = 0;
            long totalOutgoing = 0;

            // 找到目标IP对应的显示名称
            String targetDisplayName = null;
            if (sankeyData.getNodes() != null) {
                for (IpBehaviorAnalysisResponseDTO.SankeyNode node : sankeyData.getNodes()) {
                    if ("target".equals(node.getType())) {
                        targetDisplayName = node.getName();
                        break;
                    }
                }
            }

            // 统计来源和上游节点数量
            if (sankeyData.getNodes() != null) {
                for (IpBehaviorAnalysisResponseDTO.SankeyNode node : sankeyData.getNodes()) {
                    if ("source".equals(node.getType())) {
                        sourceCount++;
                    } else if ("upstream".equals(node.getType())) {
                        upstreamCount++;
                    }
                }
            }

            // 统计连接流量（使用显示名称匹配）
            if (sankeyData.getLinks() != null && targetDisplayName != null) {
                for (IpBehaviorAnalysisResponseDTO.SankeyLink link : sankeyData.getLinks()) {
                    if (targetDisplayName.equals(link.getTarget())) {
                        totalIncoming += link.getValue();
                    } else if (targetDisplayName.equals(link.getSource())) {
                        totalOutgoing += link.getValue();
                    }
                }
            }

            // 提取原始IP用于显示（去掉hostname部分）
            String displayTargetIp = targetIp;
            if (targetDisplayName != null && targetDisplayName.contains("(") && targetDisplayName.contains(")")) {
                // 从显示名称中提取IP部分用于展示
                int startIndex = targetDisplayName.lastIndexOf("(") + 1;
                int endIndex = targetDisplayName.lastIndexOf(")");
                if (startIndex > 0 && endIndex > startIndex) {
                    displayTargetIp = targetDisplayName.substring(startIndex, endIndex);
                }
            }

            summary.append(String.format("IP %s 在分析时间段内共接收来自 %d 个来源IP的 %,d 次请求，", 
                          displayTargetIp, sourceCount, totalIncoming));
            summary.append(String.format("向 %d 个上游主机发起了 %,d 次请求。", upstreamCount, totalOutgoing));

            if (sourceCount > 5) {
                summary.append("来源IP数量较多，建议关注高频访问的客户端。");
            }

            if (upstreamCount > 5) {
                summary.append("上游主机数量较多，可能存在负载均衡或微服务架构。");
            }

            if (sourceCount == 0 && upstreamCount == 0) {
                summary.append("在指定时间范围内未发现相关的网络活动记录，请检查时间范围设置或IP地址是否正确。");
            }

        } catch (Exception e) {
            logger.error("生成分析摘要失败: {}", e.getMessage(), e);
            summary.append("分析摘要生成失败");
        }

        return summary.toString();
    }
    
    /**
     * 批量查询IP对应的hostname
     */
    private Map<String, String> getIpHostnameMap(Set<String> ips) {
        Map<String, String> ipHostnameMap = new HashMap<>();
        
        if (ips == null || ips.isEmpty()) {
            return ipHostnameMap;
        }

        try {
            logger.info("开始批量查询hostname，IP数量: {}", ips.size());
            
            // 批量查询hostname
            List<Map<String, Object>> hostnameList = inventoryPortInfoMapper.selectHostnameByIpList(ips);
            
            for (Map<String, Object> hostItem : hostnameList) {
                String ip = (String) hostItem.get("hostIp");
                String hostname = (String) hostItem.get("hostname");
                if (ip != null && hostname != null) {
                    ipHostnameMap.put(ip, hostname);
                    logger.debug("IP hostname映射: {} -> {}", ip, hostname);
                }
            }
            
            logger.info("批量查询hostname完成，成功映射数量: {}", ipHostnameMap.size());
            
        } catch (Exception e) {
            logger.error("批量查询hostname失败: {}", e.getMessage(), e);
        }

        return ipHostnameMap;
    }
    
    /**
     * 获取IP的显示名称（优先显示hostname，没有则显示IP）
     */
    private String getDisplayName(String ip, Map<String, String> ipHostnameMap) {
        if (ip == null) {
            return "";
        }
        
        String hostname = ipHostnameMap.get(ip);
        if (hostname != null && !hostname.trim().isEmpty()) {
            // 如果hostname太长，显示为 "hostname (ip)"
            if (hostname.length() > 20) {
                return String.format("%s (%s)", hostname.substring(0, 17) + "...", ip);
            } else {
                return String.format("%s (%s)", hostname, ip);
            }
        }
        
        // 没有hostname则直接返回IP
        return ip;
    }
} 