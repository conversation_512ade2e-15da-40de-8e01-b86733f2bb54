package com.wiwj.securio.app.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * IP行为分析请求DTO
 *
 * <AUTHOR>
 */
@ApiModel(description = "IP行为分析请求参数")
public class IpBehaviorAnalysisRequestDTO {

    @ApiModelProperty(value = "目标IP地址", required = true, example = "************")
    @NotBlank(message = "目标IP不能为空")
    @Pattern(regexp = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$", 
             message = "IP地址格式不正确")
    private String targetIp;

    @ApiModelProperty(value = "开始时间", example = "2024-01-01T00:00:00Z")
    private String startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-01-01T23:59:59Z")
    private String endTime;

    @ApiModelProperty(value = "时间范围快捷选择", example = "1h")
    private String timeRange;

    @ApiModelProperty(value = "分析深度", example = "basic")
    private String analysisDepth = "basic";

    @ApiModelProperty(value = "超时时间(秒)", example = "120")
    private Integer timeout = 120;

    public IpBehaviorAnalysisRequestDTO() {
    }

    public String getTargetIp() {
        return targetIp;
    }

    public void setTargetIp(String targetIp) {
        this.targetIp = targetIp;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getTimeRange() {
        return timeRange;
    }

    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }

    public String getAnalysisDepth() {
        return analysisDepth;
    }

    public void setAnalysisDepth(String analysisDepth) {
        this.analysisDepth = analysisDepth;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    @Override
    public String toString() {
        return "IpBehaviorAnalysisRequestDTO{" +
                "targetIp='" + targetIp + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", timeRange='" + timeRange + '\'' +
                ", analysisDepth='" + analysisDepth + '\'' +
                ", timeout=" + timeout +
                '}';
    }
} 