package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.agent.domain.NetworkConnectionStatDTO;
import com.wiwj.securio.agent.service.IInventoryPortInfoService;
import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主机网络连接分析器
 * 用于分析内网主机的网络连接信息，包括端口状态分布、监听端口和网络连接统计
 *
 * <AUTHOR>
 */
@Component
public class HostNetworkConnectionAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(HostNetworkConnectionAnalyzer.class);
    
    @Autowired
    private IInventoryPortInfoService inventoryPortInfoService;
    
    @Override
    public String getName() {
        return "主机网络连接分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.HOST_NETWORK_CONNECTION;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 支持服务器IP的网络连接分析，办公区客户端改用AC访问日志分析
        return ipType == IpType.SERVER;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行主机网络连接分析，主机IP: {}，IP类型: {}", context.getHostIp(), context.getIpType());
        
        try {
            String hostIp = context.getHostIp();
            Map<String, Object> analysisData = new HashMap<>();
            
            // 1. 获取端口状态统计
            Map<String, Long> portStats = inventoryPortInfoService.selectPortStatisticsByHostIp(hostIp);
            analysisData.put("portStats", portStats);
            
            // 2. 获取网络连接统计
            List<NetworkConnectionStatDTO> networkStats = inventoryPortInfoService.selectNetworkConnectionStats(hostIp);
            analysisData.put("networkStats", networkStats);
            
            // 3. 获取本地监听端口列表
            List<Map<String, Object>> listeningPorts = inventoryPortInfoService.selectListeningPorts(hostIp);
            analysisData.put("listeningPorts", listeningPorts);
            
            // 4. 计算汇总统计
            int totalPorts = portStats.values().stream().mapToInt(Long::intValue).sum();
            int listeningPortCount = listeningPorts.size();
            int externalConnectionCount = networkStats.size();
            
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalPorts", totalPorts);
            summary.put("listeningPortCount", listeningPortCount);
            summary.put("externalConnectionCount", externalConnectionCount);
            summary.put("hostIp", hostIp);
            summary.put("ipType", context.getIpType().getCode());
            
            analysisData.put("summary", summary);
            
            // 5. 生成分析摘要
            StringBuilder analysisSummary = new StringBuilder();
            analysisSummary.append(String.format("主机 %s 网络连接分析完成。", hostIp));
            analysisSummary.append(String.format("共检测到 %d 个端口连接，其中 %d 个处于监听状态。", totalPorts, listeningPortCount));
            if (externalConnectionCount > 0) {
                analysisSummary.append(String.format("发现 %d 个外部网络连接。", externalConnectionCount));
            } else {
                analysisSummary.append("未发现活跃的外部网络连接。");
            }
            
            analysisData.put("analysisSummary", analysisSummary.toString());
            
            logger.info("主机网络连接分析完成，主机IP: {}，总端口数: {}，监听端口数: {}，外部连接数: {}", 
                hostIp, totalPorts, listeningPortCount, externalConnectionCount);
            
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("主机网络连接分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "网络连接分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // 网络连接分析优先级设为中等
        return 5;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间3秒
        return 3000;
    }
} 