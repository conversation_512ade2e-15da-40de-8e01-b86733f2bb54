package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.logmgr.util.TimeFormatUtil;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Zeek SSH连接分析器
 * 负责分析主机的SSH连接情况，包括出站连接和入站连接
 *
 * <AUTHOR>
 */
@Component
public class ZeekSshAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(ZeekSshAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Override
    public String getName() {
        return "Zeek SSH连接分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.ZEEK_SSH_ANALYSIS;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 支持所有已知类型的IP，因为SSH连接可能发生在任意网络段之间
        return ipType != IpType.UNKNOWN;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行Zeek SSH连接分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建时间查询条件
            String startTimeStr = null;
            String endTimeStr = null;
            
            if (context.getStartTime() != null) {
                startTimeStr = context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            if (context.getEndTime() != null) {
                endTimeStr = context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            
            String timeQuery = TimeFormatUtil.buildTimeQuery(
                startTimeStr, 
                endTimeStr, 
                context.getTimeRange()
            );
            
            // 分析出站SSH连接 - 目标IP作为源IP
            Map<String, Object> outboundAnalysis = analyzeOutboundConnections(timeQuery, context.getHostIp());
            
            // 分析入站SSH连接 - 目标IP作为目标IP
            Map<String, Object> inboundAnalysis = analyzeInboundConnections(timeQuery, context.getHostIp());
            
            // 构建分析结果
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("targetIp", context.getHostIp());
            analysisData.put("outboundAnalysis", outboundAnalysis);
            analysisData.put("inboundAnalysis", inboundAnalysis);
            
            // 计算总体统计
            Map<String, Object> totalStats = calculateTotalStats(outboundAnalysis, inboundAnalysis);
            analysisData.put("totalStats", totalStats);
            
            // 添加时间信息供前端组件构建LogQL使用
            if (context.getStartTime() != null) {
                analysisData.put("startTime", context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getEndTime() != null) {
                analysisData.put("endTime", context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getTimeRange() != null) {
                analysisData.put("timeRange", context.getTimeRange());
            }
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(context.getHostIp(), outboundAnalysis, inboundAnalysis, totalStats);
            analysisData.put("analysisSummary", summary);
            
            // 时间范围描述信息
            analysisData.put("timeRangeDescription", getTimeRangeDescription(context));
            
            logger.info("Zeek SSH连接分析完成，主机IP: {}, 出站连接: {}个目标, 入站连接: {}个源", 
                context.getHostIp(), 
                ((List<?>) outboundAnalysis.getOrDefault("destinationList", Collections.emptyList())).size(),
                ((List<?>) inboundAnalysis.getOrDefault("sourceList", Collections.emptyList())).size());
                
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("Zeek SSH连接分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "Zeek SSH连接分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // SSH连接分析优先级为4
        return 4;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间12秒
        return 12000;
    }
    
    /**
     * 分析出站SSH连接（目标IP作为源IP）
     */
    private Map<String, Object> analyzeOutboundConnections(String timeQuery, String targetIp) {
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            // 构建查询语句 - 查询目标IP作为源IP的SSH连接
            String query = String.format("%s and stream:\"SECURIO_ZEEK_SSH\" and src_ip:\"%s\" | stats by (dst_ip) count() cnt | limit 20", 
                timeQuery, targetIp);
            
            logger.info("出站SSH连接查询语句: {}", query);
            
            // 执行查询
            String result = victoriaLogsService.queryLogs(query, 20, "60s", "vmlog2");
            
            // 解析结果
            List<Map<String, Object>> destinations = parseStatsResult(result, "dst_ip", "cnt");
            
            // 计算统计信息
            int totalConnections = destinations.stream().mapToInt(item -> (Integer) item.get("count")).sum();
            int uniqueDestinations = destinations.size();
            
            analysis.put("destinationList", destinations);
            analysis.put("totalOutboundConnections", totalConnections);
            analysis.put("uniqueDestinations", uniqueDestinations);
            
            logger.info("出站SSH连接分析完成，连接总数: {}, 唯一目标: {}", totalConnections, uniqueDestinations);
            
        } catch (Exception e) {
            logger.error("分析出站SSH连接失败", e);
            analysis.put("destinationList", new ArrayList<>());
            analysis.put("totalOutboundConnections", 0);
            analysis.put("uniqueDestinations", 0);
        }
        
        return analysis;
    }
    
    /**
     * 分析入站SSH连接（目标IP作为目标IP）
     */
    private Map<String, Object> analyzeInboundConnections(String timeQuery, String targetIp) {
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            // 构建查询语句 - 查询目标IP作为目标IP的SSH连接
            String query = String.format("%s and stream:\"SECURIO_ZEEK_SSH\" and dst_ip:\"%s\" | stats by (src_ip) count() cnt | limit 20", 
                timeQuery, targetIp);
            
            logger.info("入站SSH连接查询语句: {}", query);
            
            // 执行查询
            String result = victoriaLogsService.queryLogs(query, 20, "60s", "vmlog2");
            
            // 解析结果
            List<Map<String, Object>> sources = parseStatsResult(result, "src_ip", "cnt");
            
            // 计算统计信息
            int totalConnections = sources.stream().mapToInt(item -> (Integer) item.get("count")).sum();
            int uniqueSources = sources.size();
            
            analysis.put("sourceList", sources);
            analysis.put("totalInboundConnections", totalConnections);
            analysis.put("uniqueSources", uniqueSources);
            
            logger.info("入站SSH连接分析完成，连接总数: {}, 唯一源: {}", totalConnections, uniqueSources);
            
        } catch (Exception e) {
            logger.error("分析入站SSH连接失败", e);
            analysis.put("sourceList", new ArrayList<>());
            analysis.put("totalInboundConnections", 0);
            analysis.put("uniqueSources", 0);
        }
        
        return analysis;
    }
    
    /**
     * 解析stats查询结果
     */
    private List<Map<String, Object>> parseStatsResult(String result, String ipField, String countField) {
        List<Map<String, Object>> list = new ArrayList<>();
        
        if (result == null || result.trim().isEmpty()) {
            return list;
        }
        
        try {
            // 按行分割结果
            String[] lines = result.split("\n");
            
            for (String line : lines) {
                if (line.trim().isEmpty()) {
                    continue;
                }
                
                try {
                    // 尝试解析为JSON
                    JSONObject jsonObj = JSON.parseObject(line);
                    if (jsonObj != null && jsonObj.containsKey(ipField) && jsonObj.containsKey(countField)) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("ip", jsonObj.getString(ipField));
                        item.put("count", jsonObj.getInteger(countField));
                        list.add(item);
                    }
                } catch (Exception e) {
                    logger.debug("解析stats结果行失败: {}, 错误: {}", line, e.getMessage());
                }
            }
            
            // 按连接数量降序排序
            list.sort((a, b) -> Integer.compare((Integer) b.get("count"), (Integer) a.get("count")));
            
        } catch (Exception e) {
            logger.error("解析stats结果异常", e);
        }
        
        return list;
    }
    
    /**
     * 计算总体统计信息
     */
    private Map<String, Object> calculateTotalStats(Map<String, Object> outboundAnalysis, Map<String, Object> inboundAnalysis) {
        Map<String, Object> stats = new HashMap<>();
        
        int totalOutbound = (Integer) outboundAnalysis.getOrDefault("totalOutboundConnections", 0);
        int totalInbound = (Integer) inboundAnalysis.getOrDefault("totalInboundConnections", 0);
        int uniqueDestinations = (Integer) outboundAnalysis.getOrDefault("uniqueDestinations", 0);
        int uniqueSources = (Integer) inboundAnalysis.getOrDefault("uniqueSources", 0);
        
        stats.put("totalSshConnections", totalOutbound + totalInbound);
        stats.put("totalOutboundConnections", totalOutbound);
        stats.put("totalInboundConnections", totalInbound);
        stats.put("uniqueDestinations", uniqueDestinations);
        stats.put("uniqueSources", uniqueSources);
        stats.put("totalUniqueHosts", uniqueDestinations + uniqueSources);
        
        return stats;
    }
    
    /**
     * 生成分析摘要
     */
    private String generateAnalysisSummary(String targetIp, Map<String, Object> outboundAnalysis, 
                                         Map<String, Object> inboundAnalysis, Map<String, Object> totalStats) {
        
        int totalOutbound = (Integer) outboundAnalysis.getOrDefault("totalOutboundConnections", 0);
        int totalInbound = (Integer) inboundAnalysis.getOrDefault("totalInboundConnections", 0);
        int uniqueDestinations = (Integer) outboundAnalysis.getOrDefault("uniqueDestinations", 0);
        int uniqueSources = (Integer) inboundAnalysis.getOrDefault("uniqueSources", 0);
        
        if (totalOutbound == 0 && totalInbound == 0) {
            return String.format("在指定时间范围内，主机 %s 未发现任何SSH连接记录。", targetIp);
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("在指定时间范围内，主机 %s 的SSH连接活动分析：", targetIp));
        
        if (totalOutbound > 0) {
            summary.append(String.format(" 发起 %d 次出站SSH连接，连接到 %d 个不同的目标服务器；", 
                totalOutbound, uniqueDestinations));
        }
        
        if (totalInbound > 0) {
            summary.append(String.format(" 接收 %d 次入站SSH连接，来自 %d 个不同的源主机；", 
                totalInbound, uniqueSources));
        }
        
        // 添加Top连接信息
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> topDestinations = (List<Map<String, Object>>) outboundAnalysis.get("destinationList");
        if (topDestinations != null && !topDestinations.isEmpty()) {
            summary.append(" 主要连接目标包括：");
            for (int i = 0; i < Math.min(3, topDestinations.size()); i++) {
                Map<String, Object> dest = topDestinations.get(i);
                summary.append(dest.get("ip")).append("(").append(dest.get("count")).append("次)");
                if (i < Math.min(2, topDestinations.size() - 1)) {
                    summary.append("、");
                }
            }
            summary.append("；");
        }
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> topSources = (List<Map<String, Object>>) inboundAnalysis.get("sourceList");
        if (topSources != null && !topSources.isEmpty()) {
            summary.append(" 主要连接来源包括：");
            for (int i = 0; i < Math.min(3, topSources.size()); i++) {
                Map<String, Object> src = topSources.get(i);
                summary.append(src.get("ip")).append("(").append(src.get("count")).append("次)");
                if (i < Math.min(2, topSources.size() - 1)) {
                    summary.append("、");
                }
            }
            summary.append("。");
        }
        
        return summary.toString();
    }
    
    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(AnalysisContext context) {
        if (context.getStartTime() != null && context.getEndTime() != null) {
            return String.format("%s ~ %s", 
                context.getStartTime().toString(), 
                context.getEndTime().toString());
        } else if (context.getTimeRange() != null) {
            return "最近 " + context.getTimeRange();
        } else {
            return "未指定时间范围";
        }
    }
} 