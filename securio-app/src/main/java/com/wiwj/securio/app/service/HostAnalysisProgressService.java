package com.wiwj.securio.app.service;

import com.wiwj.securio.app.domain.dto.HostAnalysisProgressDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 主机分析进度服务接口
 *
 * <AUTHOR>
 */
public interface HostAnalysisProgressService {

    /**
     * 创建SSE连接
     *
     * @param taskId 任务ID
     * @return SseEmitter
     */
    SseEmitter createConnection(String taskId);

    /**
     * 发送进度更新
     *
     * @param taskId 任务ID
     * @param progress 进度信息
     */
    void sendProgress(String taskId, HostAnalysisProgressDTO progress);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param progress 最终进度信息
     */
    void completeTask(String taskId, HostAnalysisProgressDTO progress);

    /**
     * 任务出错
     *
     * @param taskId 任务ID
     * @param progress 错误进度信息
     */
    void errorTask(String taskId, HostAnalysisProgressDTO progress);

    /**
     * 移除连接
     *
     * @param taskId 任务ID
     */
    void removeConnection(String taskId);

    /**
     * 获取任务进度历史
     *
     * @param taskId 任务ID
     * @return 进度历史列表
     */
    java.util.List<HostAnalysisProgressDTO> getProgressHistory(String taskId);
} 