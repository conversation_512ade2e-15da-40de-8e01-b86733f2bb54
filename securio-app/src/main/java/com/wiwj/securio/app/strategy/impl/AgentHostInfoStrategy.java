package com.wiwj.securio.app.strategy.impl;

import com.alibaba.fastjson2.JSON;
import com.wiwj.securio.app.domain.dto.HostAnalysisResponseDTO.HostBasicInfoDTO;
import com.wiwj.securio.app.strategy.HostInfoStrategy;
import com.wiwj.securio.agent.domain.AgentInfo;
import com.wiwj.securio.agent.service.IAgentInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Agent主机信息获取策略实现
 * 直接返回Agent原始数据，避免DTO转换的复杂性
 *
 * <AUTHOR>
 */
@Component("agentHostInfoStrategy")
public class AgentHostInfoStrategy implements HostInfoStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(AgentHostInfoStrategy.class);
    
    @Resource
    private IAgentInfoService agentInfoService;
    
    @Override
    public HostBasicInfoDTO getHostBasicInfo(String hostIp) {
        logger.info("使用Agent策略获取主机基本信息，主机IP: {}", hostIp);
        
        HostBasicInfoDTO basicInfo = new HostBasicInfoDTO();
        basicInfo.setHostIp(hostIp);
        basicInfo.setDataSource("agent");
        
        try {
            // 从数据库获取Agent信息
            AgentInfo agentInfo = agentInfoService.selectAgentInfoByHostIp(hostIp);
            if (agentInfo != null) {
                // 构建Agent数据结构
                Map<String, Object> agentData = new HashMap<>();
                agentData.put("ip", agentInfo.getHostIp());
                agentData.put("hostname", agentInfo.getHostname());
                agentData.put("osName", agentInfo.getOsName());
                agentData.put("osVersion", agentInfo.getOsVersion());
                agentData.put("cpuName", agentInfo.getCpuName());
                agentData.put("cpuCores", agentInfo.getCpuCores());
                agentData.put("ramTotal", agentInfo.getRamTotal());
                agentData.put("ramFree", agentInfo.getRamFree());
                agentData.put("status", agentInfo.getStatus());
                agentData.put("version", agentInfo.getVersion());
                agentData.put("agentId", agentInfo.getAgentId());
                agentData.put("boardSerial", agentInfo.getBoardSerial());
                agentData.put("cpuMhz", agentInfo.getCpuMhz());
                
                // 时间字段
                if (agentInfo.getStartTime() != null) {
                    agentData.put("startTime", agentInfo.getStartTime().toString());
                }
                if (agentInfo.getHeartbeatTime() != null) {
                    agentData.put("heartbeatTime", agentInfo.getHeartbeatTime().toString());
                }
                
                // 构建hostBaseInfo JSON字符串（模拟Agent上报的系统信息）
                Map<String, Object> hostBaseInfo = new HashMap<>();
                hostBaseInfo.put("hostname", agentInfo.getHostname());
                hostBaseInfo.put("platform", "linux"); // 默认值，可以根据osName判断
                hostBaseInfo.put("platformVersion", agentInfo.getOsVersion());
                hostBaseInfo.put("kernelVersion", "未知"); // Agent表中没有这个字段
                hostBaseInfo.put("kernelArch", "x86_64"); // 默认值
                hostBaseInfo.put("virtualizationRole", "guest"); // 默认值
                
                agentData.put("hostBaseInfo", JSON.toJSONString(hostBaseInfo));
                
                // 设置Agent信息
                basicInfo.setAgentInfo(agentData);
                
                logger.info("成功从Agent获取主机基本信息，主机: {}", agentInfo.getHostname());
            } else {
                logger.warn("未找到对应的Agent信息，主机IP: {}", hostIp);
                basicInfo.setAgentInfo(new HashMap<>());
            }
            
            logger.info("Agent策略获取主机信息完成，主机IP: {}", hostIp);
            return basicInfo;
            
        } catch (Exception e) {
            logger.error("Agent策略获取主机信息失败，主机IP: {}", hostIp, e);
            // 即使失败也返回基本结构，包含数据源信息
            basicInfo.setAgentInfo(new HashMap<>());
            return basicInfo;
        }
    }
    
    @Override
    public String getStrategyName() {
        return "agent";
    }
} 