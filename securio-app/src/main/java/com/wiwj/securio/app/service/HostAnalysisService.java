package com.wiwj.securio.app.service;

import com.wiwj.securio.app.domain.dto.HostAnalysisRequestDTO;
import com.wiwj.securio.app.domain.dto.HostAnalysisResponseDTO;

/**
 * 主机综合分析服务接口
 *
 * <AUTHOR>
 */
public interface HostAnalysisService {

    /**
     * 执行主机综合分析
     *
     * @param request 分析请求参数
     * @return 综合分析报告
     */
    HostAnalysisResponseDTO analyzeHost(HostAnalysisRequestDTO request);

    /**
     * 获取主机基本信息
     *
     * @param hostIp 主机IP
     * @return 主机基本信息
     */
    HostAnalysisResponseDTO.HostBasicInfoDTO getHostBasicInfo(String hostIp);

    /**
     * 获取主机资产信息
     *
     * @param hostIp 主机IP
     * @return 主机资产信息
     */
    HostAnalysisResponseDTO.HostAssetInfoDTO getHostAssetInfo(String hostIp);

    /**
     * 获取主机事件信息
     *
     * @param hostIp 主机IP
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 主机事件信息
     */
    HostAnalysisResponseDTO.HostEventInfoDTO getHostEventInfo(String hostIp, String startTime, String endTime);

    /**
     * 获取主机风险信息
     *
     * @param hostIp 主机IP
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 主机风险信息
     */
    HostAnalysisResponseDTO.HostRiskInfoDTO getHostRiskInfo(String hostIp, String startTime, String endTime);

    /**
     * 生成分析摘要
     *
     * @param hostBasicInfo 主机基本信息
     * @param hostAssetInfo 主机资产信息
     * @param hostEventInfo 主机事件信息
     * @param hostRiskInfo 主机风险信息
     * @param analysisTimeRange 分析时间范围
     * @return 分析摘要
     */
    HostAnalysisResponseDTO.AnalysisSummaryDTO generateAnalysisSummary(
            HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo,
            HostAnalysisResponseDTO.HostAssetInfoDTO hostAssetInfo,
            HostAnalysisResponseDTO.HostEventInfoDTO hostEventInfo,
            HostAnalysisResponseDTO.HostRiskInfoDTO hostRiskInfo,
            String analysisTimeRange);
} 