package com.wiwj.securio.app.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.app.domain.dto.HostAnalysisRequestDTO;
import com.wiwj.securio.app.domain.dto.HostAnalysisResponseDTO;
import com.wiwj.securio.app.domain.dto.HostAnalysisProgressDTO;
import com.wiwj.securio.app.service.HostAnalysisService;
import com.wiwj.securio.app.service.HostAnalysisProgressService;
import com.wiwj.securio.app.strategy.HostInfoStrategyFactory;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import com.wiwj.securio.agent.service.IAgentInfoService;
import com.wiwj.securio.agent.service.IAgentInventoryService;
import com.wiwj.securio.agent.service.IInventoryNetworkInfoService;
import com.wiwj.securio.agent.domain.AgentInfo;
import com.wiwj.securio.agent.domain.InventoryNetworkInfo;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZoneOffset;
import java.util.*;
import java.util.HashMap;

/**
 * 主机综合分析服务实现类
 *
 * <AUTHOR>
 */
@Service
public class HostAnalysisServiceImpl implements HostAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(HostAnalysisServiceImpl.class);

    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Autowired
    private HostInfoStrategyFactory hostInfoStrategyFactory;
    
    @Autowired
    private IAgentInfoService agentInfoService;
    
    @Autowired
    private IAgentInventoryService agentInventoryService;
    
    @Autowired
    private IInventoryNetworkInfoService inventoryNetworkInfoService;
    
    @Autowired
    private HostAnalysisProgressService progressService;

    @Override
    public HostAnalysisResponseDTO analyzeHost(HostAnalysisRequestDTO request) {
        return analyzeHost(request, null);
    }
    
    /**
     * 执行主机综合分析（带进度推送）
     *
     * @param request 分析请求参数
     * @param taskId 任务ID（用于进度推送）
     * @return 综合分析报告
     */
    public HostAnalysisResponseDTO analyzeHost(HostAnalysisRequestDTO request, String taskId) {
        logger.info("开始执行主机综合分析，主机IP: {}, 请求参数: {}, 任务ID: {}", 
                   request.getHostIp(), request.toString(), taskId);

        // 处理时间参数 - 支持相对时间和精确时间
        String actualStartTime;
        String actualEndTime;
        String timeRangeDescription;
        
        if (request.isRelativeTimeMode()) {
            // 相对时间模式：如30m、1h等
            logger.info("使用相对时间模式，时间范围: {}", request.getTimeRange());
            String[] timeRange = calculateTimeRangeFromRelative(request.getTimeRange());
            actualStartTime = timeRange[0];
            actualEndTime = timeRange[1];
            timeRangeDescription = String.format("最近%s (%s ~ %s)", request.getTimeRange(), actualStartTime, actualEndTime);
        } else if (request.isPreciseTimeMode()) {
            // 精确时间模式
            logger.info("使用精确时间模式，开始时间: {}, 结束时间: {}", request.getStartTime(), request.getEndTime());
            actualStartTime = request.getStartTime();
            actualEndTime = request.getEndTime();
            timeRangeDescription = String.format("%s ~ %s", actualStartTime, actualEndTime);
        } else {
            // 这种情况不应该发生，因为DTO验证应该已经拦截了
            throw new IllegalArgumentException("无效的时间参数：必须提供相对时间范围或精确时间");
        }
        
        logger.info("实际分析时间范围: {}", timeRangeDescription);

        HostAnalysisResponseDTO response = new HostAnalysisResponseDTO();

        try {
            // 发送开始分析进度
            sendProgress(taskId, "START", "开始主机综合分析", 5, "RUNNING", 
                "正在初始化分析任务，主机IP: " + request.getHostIp() + "，时间范围: " + timeRangeDescription);

            // 1. 获取主机基本信息
            sendProgress(taskId, "BASIC_INFO", "获取主机基本信息", 15, "RUNNING", 
                "正在从数据源获取主机基本信息...");
            HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo = getHostBasicInfo(request.getHostIp());
            response.setHostBasicInfo(hostBasicInfo);
            sendProgress(taskId, "BASIC_INFO", "主机基本信息获取完成", 25, "RUNNING", 
                "主机基本信息获取成功，数据源: " + (hostBasicInfo != null ? hostBasicInfo.getDataSource() : "未知"));

            // 2. 获取主机资产信息
            sendProgress(taskId, "ASSET_INFO", "获取主机资产信息", 35, "RUNNING", 
                "正在统计主机资产清单信息...");
            HostAnalysisResponseDTO.HostAssetInfoDTO hostAssetInfo = getHostAssetInfo(request.getHostIp());
            response.setHostAssetInfo(hostAssetInfo);
            sendProgress(taskId, "ASSET_INFO", "主机资产信息获取完成", 45, "RUNNING", 
                "主机资产统计完成，发现 " + (hostAssetInfo != null ? 
                (hostAssetInfo.getUserCount() + hostAssetInfo.getProcessCount() + hostAssetInfo.getPortCount()) : 0) + " 项资产");

            // 3. 获取主机事件信息（使用处理后的时间参数）
            sendProgress(taskId, "EVENT_INFO", "获取主机事件信息", 55, "RUNNING", 
                "正在分析主机事件日志，时间范围: " + timeRangeDescription);
            HostAnalysisResponseDTO.HostEventInfoDTO hostEventInfo = 
                getHostEventInfo(request.getHostIp(), actualStartTime, actualEndTime);
            response.setHostEventInfo(hostEventInfo);
            sendProgress(taskId, "EVENT_INFO", "主机事件信息获取完成", 70, "RUNNING", 
                "事件日志分析完成，发现审计日志 " + 
                (hostEventInfo != null && hostEventInfo.getAuditLogStat() != null ? 
                hostEventInfo.getAuditLogStat().getTotalCount() : 0) + " 条");

            // 4. 获取主机风险信息（使用处理后的时间参数）
            sendProgress(taskId, "RISK_INFO", "获取主机风险信息", 80, "RUNNING", 
                "正在进行主机风险评估...");
            HostAnalysisResponseDTO.HostRiskInfoDTO hostRiskInfo = 
                getHostRiskInfo(request.getHostIp(), actualStartTime, actualEndTime);
            response.setHostRiskInfo(hostRiskInfo);
            sendProgress(taskId, "RISK_INFO", "主机风险信息获取完成", 85, "RUNNING", 
                "风险评估完成，风险等级: " + (hostRiskInfo != null ? hostRiskInfo.getRiskLevel() : "未知"));

            // 5. 生成分析摘要（使用处理后的时间描述）
            sendProgress(taskId, "SUMMARY", "生成分析摘要", 90, "RUNNING", 
                "正在生成综合分析摘要...");
            HostAnalysisResponseDTO.AnalysisSummaryDTO analysisSummary = 
                generateAnalysisSummary(hostBasicInfo, hostAssetInfo, hostEventInfo, hostRiskInfo, timeRangeDescription);
            response.setAnalysisSummary(analysisSummary);

            // 6. 设置生成时间
            response.setGenerateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 发送完成进度
            // sendProgress(taskId, "COMPLETED", "分析完成", 100, "COMPLETED", 
            //     "主机综合分析已完成，生成时间: " + response.getGenerateTime());

            logger.info("主机综合分析完成，主机IP: {}, 任务ID: {}", request.getHostIp(), taskId);
            return response;

        } catch (Exception e) {
            logger.error("主机综合分析失败，主机IP: {}, 任务ID: {}, 错误: {}", request.getHostIp(), taskId, e.getMessage(), e);
            
            // 发送错误进度
            sendErrorProgress(taskId, "ERROR", "分析失败", 
                "主机综合分析失败: " + e.getMessage());
            
            throw new RuntimeException("主机综合分析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将相对时间转换为具体的开始和结束时间
     * 
     * @param relativeTime 相对时间，如"30m"、"1h"、"1d"等
     * @return String数组，[0]为开始时间，[1]为结束时间
     */
    private String[] calculateTimeRangeFromRelative(String relativeTime) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = now;
        LocalDateTime startTime;
        
        // 解析相对时间
        if (relativeTime.endsWith("m")) {
            // 分钟
            int minutes = Integer.parseInt(relativeTime.substring(0, relativeTime.length() - 1));
            startTime = now.minusMinutes(minutes);
        } else if (relativeTime.endsWith("h")) {
            // 小时
            int hours = Integer.parseInt(relativeTime.substring(0, relativeTime.length() - 1));
            startTime = now.minusHours(hours);
        } else if (relativeTime.endsWith("d")) {
            // 天
            int days = Integer.parseInt(relativeTime.substring(0, relativeTime.length() - 1));
            startTime = now.minusDays(days);
        } else {
            throw new IllegalArgumentException("不支持的相对时间格式: " + relativeTime + "。支持的格式：30m（分钟）、1h（小时）、1d（天）");
        }
        
        // 格式化为ISO 8601格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTimeStr = startTime.format(formatter);
        String endTimeStr = endTime.format(formatter);
        
        logger.info("相对时间 {} 转换为: {} ~ {}", relativeTime, startTimeStr, endTimeStr);
        
        return new String[]{startTimeStr, endTimeStr};
    }
    
    /**
     * 发送进度更新
     */
    private void sendProgress(String taskId, String step, String description, int progress, String status, String message) {
        if (taskId != null && progressService != null) {
            HostAnalysisProgressDTO progressDTO = new HostAnalysisProgressDTO(
                taskId, step, description, progress, status, message
            );
            progressService.sendProgress(taskId, progressDTO);
        }
    }
    
    /**
     * 发送错误进度
     */
    private void sendErrorProgress(String taskId, String step, String description, String errorMessage) {
        if (taskId != null && progressService != null) {
            HostAnalysisProgressDTO progressDTO = new HostAnalysisProgressDTO(
                taskId, step, description, 0, "ERROR", "分析过程中发生错误"
            );
            progressDTO.setErrorMessage(errorMessage);
            progressService.errorTask(taskId, progressDTO);
        }
    }

    @Override
    public HostAnalysisResponseDTO.HostBasicInfoDTO getHostBasicInfo(String hostIp) {
        logger.info("获取主机基本信息，主机IP: {}", hostIp);
        
        try {
            // 使用策略工厂获取主机基本信息
            return hostInfoStrategyFactory.getHostBasicInfo(hostIp);
        } catch (Exception e) {
            logger.error("获取主机基本信息失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            
            // 返回默认信息
            HostAnalysisResponseDTO.HostBasicInfoDTO basicInfo = new HostAnalysisResponseDTO.HostBasicInfoDTO();
            basicInfo.setHostIp(hostIp);
            basicInfo.setDataSource("error");
            basicInfo.setRawData(new HashMap<>());
            basicInfo.setAgentInfo(new HashMap<>());
            return basicInfo;
        }
    }

    @Override
    public HostAnalysisResponseDTO.HostAssetInfoDTO getHostAssetInfo(String hostIp) {
        logger.info("获取主机资产信息，主机IP: {}", hostIp);

        HostAnalysisResponseDTO.HostAssetInfoDTO assetInfo = new HostAnalysisResponseDTO.HostAssetInfoDTO();

        try {
            // 从 securio-agentmgr 模块获取资产清单统计信息
            Map<String, Object> inventoryStats = getHostInventoryStats(hostIp);
            if (inventoryStats != null) {
                assetInfo.setUserCount(getIntegerValue(inventoryStats, "users"));
                assetInfo.setProcessCount(getIntegerValue(inventoryStats, "processes"));
                assetInfo.setPortCount(getIntegerValue(inventoryStats, "ports"));
                assetInfo.setSoftwareCount(getIntegerValue(inventoryStats, "software"));
                assetInfo.setStartupCount(getIntegerValue(inventoryStats, "startups"));
                assetInfo.setScheduledTaskCount(getIntegerValue(inventoryStats, "scheduledTasks"));
                assetInfo.setKernelModuleCount(getIntegerValue(inventoryStats, "kernelModules"));
                
                // 获取网络接口数量
                List<InventoryNetworkInfo> networkInfoList = getHostNetworkInfo(hostIp);
                if (networkInfoList != null) {
                    assetInfo.setNetworkInterfaceCount(networkInfoList.size());
                }

                // 设置详细信息
                assetInfo.setDetailInfo(inventoryStats);
            }

            // 设置默认值
            if (assetInfo.getUserCount() == null) assetInfo.setUserCount(0);
            if (assetInfo.getProcessCount() == null) assetInfo.setProcessCount(0);
            if (assetInfo.getPortCount() == null) assetInfo.setPortCount(0);
            if (assetInfo.getSoftwareCount() == null) assetInfo.setSoftwareCount(0);
            if (assetInfo.getStartupCount() == null) assetInfo.setStartupCount(0);
            if (assetInfo.getScheduledTaskCount() == null) assetInfo.setScheduledTaskCount(0);
            if (assetInfo.getKernelModuleCount() == null) assetInfo.setKernelModuleCount(0);
            if (assetInfo.getNetworkInterfaceCount() == null) assetInfo.setNetworkInterfaceCount(0);

        } catch (Exception e) {
            logger.error("获取主机资产信息失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            // 设置默认值
            assetInfo.setUserCount(0);
            assetInfo.setProcessCount(0);
            assetInfo.setPortCount(0);
            assetInfo.setSoftwareCount(0);
            assetInfo.setStartupCount(0);
            assetInfo.setScheduledTaskCount(0);
            assetInfo.setKernelModuleCount(0);
            assetInfo.setNetworkInterfaceCount(0);
        }

        return assetInfo;
    }

    @Override
    public HostAnalysisResponseDTO.HostEventInfoDTO getHostEventInfo(String hostIp, String startTime, String endTime) {
        logger.info("获取主机事件信息，主机IP: {}, 时间范围: {} - {}", hostIp, startTime, endTime);

        HostAnalysisResponseDTO.HostEventInfoDTO eventInfo = new HostAnalysisResponseDTO.HostEventInfoDTO();

        try {
            // 1. 获取审计日志统计
            HostAnalysisResponseDTO.LogStatDTO auditLogStat = getAuditLogStats(hostIp, startTime, endTime);
            eventInfo.setAuditLogStat(auditLogStat);

            // 2. 获取 Nginx 访问日志统计
            HostAnalysisResponseDTO.LogStatDTO nginxLogStat = getNginxLogStats(hostIp, startTime, endTime);
            eventInfo.setNginxLogStat(nginxLogStat);

            // 3. 获取 Zeek 网络日志统计
            HostAnalysisResponseDTO.LogStatDTO zeekLogStat = getZeekLogStats(hostIp, startTime, endTime);
            eventInfo.setZeekLogStat(zeekLogStat);

            // 4. 获取网络流量分析
            HostAnalysisResponseDTO.NetworkFlowAnalysisDTO networkFlowAnalysis = 
                getNetworkFlowAnalysis(hostIp, startTime, endTime);
            eventInfo.setNetworkFlowAnalysis(networkFlowAnalysis);

            // 5. 获取访问关系图数据
            List<Map<String, Object>> accessRelationData = getAccessRelationData(hostIp, startTime, endTime);
            eventInfo.setAccessRelationData(accessRelationData);

        } catch (Exception e) {
            logger.error("获取主机事件信息失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            // 设置默认值
            eventInfo.setAuditLogStat(new HostAnalysisResponseDTO.LogStatDTO());
            eventInfo.setNginxLogStat(new HostAnalysisResponseDTO.LogStatDTO());
            eventInfo.setZeekLogStat(new HostAnalysisResponseDTO.LogStatDTO());
            eventInfo.setNetworkFlowAnalysis(new HostAnalysisResponseDTO.NetworkFlowAnalysisDTO());
            eventInfo.setAccessRelationData(new ArrayList<>());
        }

        return eventInfo;
    }

    @Override
    public HostAnalysisResponseDTO.HostRiskInfoDTO getHostRiskInfo(String hostIp, String startTime, String endTime) {
        logger.info("获取主机风险信息，主机IP: {}, 时间范围: {} - {}", hostIp, startTime, endTime);

        HostAnalysisResponseDTO.HostRiskInfoDTO riskInfo = new HostAnalysisResponseDTO.HostRiskInfoDTO();

        try {
            // 暂时设置为低风险，因为风险数据还没有准备好
            riskInfo.setRiskLevel("低");
            riskInfo.setRiskEventCount(0);
            riskInfo.setRiskEvents(new ArrayList<>());
            
            List<String> suggestions = new ArrayList<>();
            suggestions.add("定期更新系统补丁");
            suggestions.add("加强访问控制管理");
            suggestions.add("监控异常网络连接");
            suggestions.add("定期检查系统日志");
            riskInfo.setRiskSuggestions(suggestions);

        } catch (Exception e) {
            logger.error("获取主机风险信息失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            riskInfo.setRiskLevel("未知");
            riskInfo.setRiskEventCount(0);
            riskInfo.setRiskEvents(new ArrayList<>());
            riskInfo.setRiskSuggestions(new ArrayList<>());
        }

        return riskInfo;
    }

    @Override
    public HostAnalysisResponseDTO.AnalysisSummaryDTO generateAnalysisSummary(
            HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo,
            HostAnalysisResponseDTO.HostAssetInfoDTO hostAssetInfo,
            HostAnalysisResponseDTO.HostEventInfoDTO hostEventInfo,
            HostAnalysisResponseDTO.HostRiskInfoDTO hostRiskInfo,
            String analysisTimeRange) {

        HostAnalysisResponseDTO.AnalysisSummaryDTO summary = new HostAnalysisResponseDTO.AnalysisSummaryDTO();

        try {
            // 总体评估
            String overallAssessment = generateOverallAssessment(hostBasicInfo, hostAssetInfo, hostEventInfo, hostRiskInfo);
            summary.setOverallAssessment(overallAssessment);

            // 关键发现
            List<String> keyFindings = generateKeyFindings(hostBasicInfo, hostAssetInfo, hostEventInfo, hostRiskInfo);
            summary.setKeyFindings(keyFindings);

            // 建议措施
            List<String> recommendations = generateRecommendations(hostBasicInfo, hostAssetInfo, hostEventInfo, hostRiskInfo);
            summary.setRecommendations(recommendations);

            // 分析时间范围
            summary.setAnalysisTimeRange(analysisTimeRange);

        } catch (Exception e) {
            logger.error("生成分析摘要失败，错误: {}", e.getMessage(), e);
            summary.setOverallAssessment("分析摘要生成失败");
            summary.setKeyFindings(new ArrayList<>());
            summary.setRecommendations(new ArrayList<>());
            summary.setAnalysisTimeRange(analysisTimeRange);
        }

        return summary;
    }

    // 私有辅助方法

    /**
     * 获取主机 Agent 信息
     */
    private AgentInfo getHostAgentInfo(String hostIp) {
        try {
            return agentInfoService.selectAgentInfoByHostIp(hostIp);
        } catch (Exception e) {
            logger.warn("获取主机 Agent 信息失败，主机IP: {}, 错误: {}", hostIp, e.getMessage());
            return null;
        }
    }

    /**
     * 获取主机资产清单统计信息
     */
    private Map<String, Object> getHostInventoryStats(String hostIp) {
        try {
            // 首先根据主机IP获取AgentInfo
            AgentInfo agentInfo = agentInfoService.selectAgentInfoByHostIp(hostIp);
            if (agentInfo != null) {
                return agentInventoryService.getAgentInventoryStats(agentInfo.getAgentId());
            }
            return null;
        } catch (Exception e) {
            logger.warn("获取主机资产清单统计信息失败，主机IP: {}, 错误: {}", hostIp, e.getMessage());
            return null;
        }
    }

    /**
     * 获取主机网络接口信息
     */
    private List<InventoryNetworkInfo> getHostNetworkInfo(String hostIp) {
        try {
            InventoryNetworkInfo queryParam = new InventoryNetworkInfo();
            queryParam.setHostIp(hostIp);
            return inventoryNetworkInfoService.selectInventoryNetworkInfoList(queryParam);
        } catch (Exception e) {
            logger.warn("获取主机网络接口信息失败，主机IP: {}, 错误: {}", hostIp, e.getMessage());
            return null;
        }
    }

    /**
     * 获取审计日志统计
     */
    private HostAnalysisResponseDTO.LogStatDTO getAuditLogStats(String hostIp, String startTime, String endTime) {
        HostAnalysisResponseDTO.LogStatDTO logStat = new HostAnalysisResponseDTO.LogStatDTO();
        
        try {
            // 转换时间格式为ISO 8601
            String isoStartTime = convertToISO8601(startTime);
            String isoEndTime = convertToISO8601(endTime);
            
            // 构建查询语句，使用正确的时间范围格式
            String timeRange = String.format("_time:[%s,%s]", isoStartTime, isoEndTime);
            String query = String.format("%s and _stream: {stream=\"AUDITLOG_SYSTEM_AUDIT\"} AND host_ip:\"%s\"", timeRange, hostIp);
            
            // 查询总数
            String countQuery = query + " | stats count(*) as total";
            String countResult = victoriaLogsService.queryLogs(countQuery, 1, null);
            
            if (countResult != null && !countResult.trim().isEmpty()) {
                String[] lines = countResult.trim().split("\n");
                if (lines.length > 0) {
                    try {
                        JSONObject countData = JSON.parseObject(lines[0]);
                        Long total = countData.getLong("total");
                        logStat.setTotalCount(total != null ? total : 0L);
                    } catch (Exception e) {
                        logStat.setTotalCount(0L);
                    }
                }
            } else {
                logStat.setTotalCount(0L);
            }

            // 时间分布统计
            String timeQuery = query + " | stats by (_time:1h) count(*) as count";
            String timeResult = victoriaLogsService.queryLogs(timeQuery,  100, null);
            List<Map<String, Object>> timeDistribution = parseTimeDistribution(timeResult);
            logStat.setTimeDistribution(timeDistribution);

            // TOP 统计
            String topQuery = query + " | stats by (user) count(*) as count | sort by (count desc) | limit 10";
            String topResult = victoriaLogsService.queryLogs(topQuery, 10, null);
            List<Map<String, Object>> topStats = parseTopStats(topResult);
            logStat.setTopStats(topStats);

        } catch (Exception e) {
            logger.error("获取审计日志统计失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            logStat.setTotalCount(0L);
            logStat.setTimeDistribution(new ArrayList<>());
            logStat.setTopStats(new ArrayList<>());
        }

        return logStat;
    }

    /**
     * 获取 Nginx 日志统计
     */
    private HostAnalysisResponseDTO.LogStatDTO getNginxLogStats(String hostIp, String startTime, String endTime) {
        HostAnalysisResponseDTO.LogStatDTO logStat = new HostAnalysisResponseDTO.LogStatDTO();
        
        try {
            // 转换时间格式为ISO 8601
            String isoStartTime = convertToISO8601(startTime);
            String isoEndTime = convertToISO8601(endTime);
            
            // 构建查询语句，将时间范围嵌入到查询中
            String timeRange = String.format("_time:[%s,%s]", isoStartTime, isoEndTime);
            String query = String.format("%s and _stream: {stream=\"NGINX_ACCESS\"} AND (message.remote_addr:\"%s\" OR message.server_addr:\"%s\")", timeRange, hostIp, hostIp);
            
            // 查询总数
            String countQuery = query + " | stats count(*) as total";
            String countResult = victoriaLogsService.queryLogs(countQuery, 1, null, "vmlog2");
            
            if (countResult != null && !countResult.trim().isEmpty()) {
                String[] lines = countResult.trim().split("\n");
                if (lines.length > 0) {
                    try {
                        JSONObject countData = JSON.parseObject(lines[0]);
                        Long total = countData.getLong("total");
                        logStat.setTotalCount(total != null ? total : 0L);
                    } catch (Exception e) {
                        logStat.setTotalCount(0L);
                    }
                }
            } else {
                logStat.setTotalCount(0L);
            }

            // 时间分布统计
            String timeQuery = query + " | stats by (_time:1h) count(*) as count";
            String timeResult = victoriaLogsService.queryLogs(timeQuery, 100, null, "vmlog2");
            List<Map<String, Object>> timeDistribution = parseTimeDistribution(timeResult);
            logStat.setTimeDistribution(timeDistribution);

            // TOP 统计 - 访问最多的 URL
            String topQuery = query + " | stats by (message.request_uri) count(*) as count | sort by (count desc) | limit 10";
            String topResult = victoriaLogsService.queryLogs(topQuery, 10, null, "vmlog2");
            List<Map<String, Object>> topStats = parseTopStats(topResult);
            logStat.setTopStats(topStats);

        } catch (Exception e) {
            logger.error("获取 Nginx 日志统计失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            logStat.setTotalCount(0L);
            logStat.setTimeDistribution(new ArrayList<>());
            logStat.setTopStats(new ArrayList<>());
        }

        return logStat;
    }

    /**
     * 获取 Zeek 日志统计
     */
    private HostAnalysisResponseDTO.LogStatDTO getZeekLogStats(String hostIp, String startTime, String endTime) {
        HostAnalysisResponseDTO.LogStatDTO logStat = new HostAnalysisResponseDTO.LogStatDTO();
        
        try {
            // 转换时间格式为ISO 8601
            String isoStartTime = convertToISO8601(startTime);
            String isoEndTime = convertToISO8601(endTime);
            
            // 构建查询语句，将时间范围嵌入到查询中
            String timeRange = String.format("_time:[%s,%s]", isoStartTime, isoEndTime);
            String query = String.format("%s and _stream: {stream=~\"SECURIO_ZEEK.*\"} AND (id_orig_h:\"%s\" OR id_resp_h:\"%s\")", timeRange, hostIp, hostIp);
            
            // 查询总数
            String countQuery = query + " | stats count(*) as total";
            String countResult = victoriaLogsService.queryLogs(countQuery, 1, null);
            
            if (countResult != null && !countResult.trim().isEmpty()) {
                String[] lines = countResult.trim().split("\n");
                if (lines.length > 0) {
                    try {
                        JSONObject countData = JSON.parseObject(lines[0]);
                        Long total = countData.getLong("total");
                        logStat.setTotalCount(total != null ? total : 0L);
                    } catch (Exception e) {
                        logStat.setTotalCount(0L);
                    }
                }
            } else {
                logStat.setTotalCount(0L);
            }

            // 时间分布统计
            String timeQuery = query + " | stats by (_time:1h) count(*) as count";
            String timeResult = victoriaLogsService.queryLogs(timeQuery, 100, null);
            List<Map<String, Object>> timeDistribution = parseTimeDistribution(timeResult);
            logStat.setTimeDistribution(timeDistribution);

            // TOP 统计 - 最活跃的连接
            String topQuery = query + " | stats by (id_resp_h) count(*) as count | sort by (count desc) | limit 10";
            String topResult = victoriaLogsService.queryLogs(topQuery, 10, null);
            List<Map<String, Object>> topStats = parseTopStats(topResult);
            logStat.setTopStats(topStats);

        } catch (Exception e) {
            logger.error("获取 Zeek 日志统计失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            logStat.setTotalCount(0L);
            logStat.setTimeDistribution(new ArrayList<>());
            logStat.setTopStats(new ArrayList<>());
        }

        return logStat;
    }

    /**
     * 获取网络流量分析
     */
    private HostAnalysisResponseDTO.NetworkFlowAnalysisDTO getNetworkFlowAnalysis(String hostIp, String startTime, String endTime) {
        HostAnalysisResponseDTO.NetworkFlowAnalysisDTO flowAnalysis = new HostAnalysisResponseDTO.NetworkFlowAnalysisDTO();
        
        try {
            // 转换时间格式为ISO 8601
            String isoStartTime = convertToISO8601(startTime);
            String isoEndTime = convertToISO8601(endTime);
            String timeRange = String.format("_time:[%s,%s]", isoStartTime, isoEndTime);
            
            // 入站流量统计
            String inboundQuery = String.format("%s and _stream: {stream=~\"SECURIO_ZEEK.*\"} AND id_resp_h:\"%s\" | stats by (id_orig_h) count(*) as count | sort by (count desc) | limit 10", timeRange, hostIp);
            String inboundResult = victoriaLogsService.queryLogs(inboundQuery, 10, null);
            List<Map<String, Object>> inboundTraffic = parseTopStats(inboundResult);
            flowAnalysis.setInboundTraffic(inboundTraffic);

            // 出站流量统计
            String outboundQuery = String.format("%s and _stream: {stream=~\"SECURIO_ZEEK.*\"} AND id_orig_h:\"%s\" | stats by (id_resp_h) count(*) as count | sort by (count desc) | limit 10", timeRange, hostIp);
            String outboundResult = victoriaLogsService.queryLogs(outboundQuery, 10, null);
            List<Map<String, Object>> outboundTraffic = parseTopStats(outboundResult);
            flowAnalysis.setOutboundTraffic(outboundTraffic);

            // 协议分布
            String protocolQuery = String.format("%s and _stream: {stream=~\"SECURIO_ZEEK.*\"} AND (id_orig_h:\"%s\" OR id_resp_h:\"%s\") | stats by (proto) count(*) as count | sort by (count desc)", timeRange, hostIp, hostIp);
            String protocolResult = victoriaLogsService.queryLogs(protocolQuery, 20, null);
            List<Map<String, Object>> protocolDistribution = parseTopStats(protocolResult);
            flowAnalysis.setProtocolDistribution(protocolDistribution);

            // 端口分布
            String portQuery = String.format("%s and _stream: {stream=~\"SECURIO_ZEEK.*\"} AND (id_orig_h:\"%s\" OR id_resp_h:\"%s\") | stats by (id_resp_p) count(*) as count | sort by (count desc) | limit 20", timeRange, hostIp, hostIp);
            String portResult = victoriaLogsService.queryLogs(portQuery, 20, null);
            List<Map<String, Object>> portDistribution = parseTopStats(portResult);
            flowAnalysis.setPortDistribution(portDistribution);

        } catch (Exception e) {
            logger.error("获取网络流量分析失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
            flowAnalysis.setInboundTraffic(new ArrayList<>());
            flowAnalysis.setOutboundTraffic(new ArrayList<>());
            flowAnalysis.setProtocolDistribution(new ArrayList<>());
            flowAnalysis.setPortDistribution(new ArrayList<>());
        }

        return flowAnalysis;
    }

    /**
     * 获取访问关系图数据
     */
    private List<Map<String, Object>> getAccessRelationData(String hostIp, String startTime, String endTime) {
        List<Map<String, Object>> relationData = new ArrayList<>();
        
        try {
            // 转换时间格式为ISO 8601
            String isoStartTime = convertToISO8601(startTime);
            String isoEndTime = convertToISO8601(endTime);
            String timeRange = String.format("_time:[%s,%s]", isoStartTime, isoEndTime);
            
            // 基于 Nginx 访问日志构建访问关系
            String nginxQuery = String.format("%s and _stream: {stream=\"NGINX_ACCESS\"} AND (message.remote_addr:\"%s\" OR message.server_addr:\"%s\") | stats by (message.remote_addr, message.server_addr) count(*) as count | sort by (count desc) | limit 50", timeRange, hostIp, hostIp);
            String nginxResult = victoriaLogsService.queryLogs(nginxQuery, 50, null, "vmlog2");
            
            if (nginxResult != null && !nginxResult.trim().isEmpty()) {
                String[] lines = nginxResult.trim().split("\n");
                for (String line : lines) {
                    try {
                        JSONObject data = JSON.parseObject(line);
                        Map<String, Object> relation = new HashMap<>();
                        relation.put("source", data.getString("message.remote_addr"));
                        relation.put("target", data.getString("message.server_addr"));
                        relation.put("value", data.getLong("count"));
                        relation.put("type", "http");
                        relationData.add(relation);
                    } catch (Exception e) {
                        // 忽略解析错误的行
                    }
                }
            }

            // 基于 Zeek 网络日志构建网络连接关系
            String zeekQuery = String.format("%s and _stream: {stream=~\"SECURIO_ZEEK.*\"} AND (id_orig_h:\"%s\" OR id_resp_h:\"%s\") | stats by (id_orig_h, id_resp_h, proto) count(*) as count | sort by (count desc) | limit 50", timeRange, hostIp, hostIp);
            String zeekResult = victoriaLogsService.queryLogs(zeekQuery, 50, null);
            
            if (zeekResult != null && !zeekResult.trim().isEmpty()) {
                String[] lines = zeekResult.trim().split("\n");
                for (String line : lines) {
                    try {
                        JSONObject data = JSON.parseObject(line);
                        Map<String, Object> relation = new HashMap<>();
                        relation.put("source", data.getString("id_orig_h"));
                        relation.put("target", data.getString("id_resp_h"));
                        relation.put("value", data.getLong("count"));
                        relation.put("type", data.getString("proto"));
                        relationData.add(relation);
                    } catch (Exception e) {
                        // 忽略解析错误的行
                    }
                }
            }

        } catch (Exception e) {
            logger.error("获取访问关系图数据失败，主机IP: {}, 错误: {}", hostIp, e.getMessage(), e);
        }

        return relationData;
    }

    /**
     * 解析时间分布数据
     */
    private List<Map<String, Object>> parseTimeDistribution(String result) {
        List<Map<String, Object>> timeDistribution = new ArrayList<>();
        
        if (result != null && !result.trim().isEmpty()) {
            String[] lines = result.trim().split("\n");
            for (String line : lines) {
                try {
                    JSONObject data = JSON.parseObject(line);
                    Map<String, Object> timePoint = new HashMap<>();
                    timePoint.put("time", data.getString("_time"));
                    timePoint.put("count", data.getLong("count"));
                    timeDistribution.add(timePoint);
                } catch (Exception e) {
                    // 忽略解析错误的行
                }
            }
        }
        
        return timeDistribution;
    }

    /**
     * 解析 TOP 统计数据
     */
    private List<Map<String, Object>> parseTopStats(String result) {
        List<Map<String, Object>> topStats = new ArrayList<>();
        
        if (result != null && !result.trim().isEmpty()) {
            String[] lines = result.trim().split("\n");
            for (String line : lines) {
                try {
                    JSONObject data = JSON.parseObject(line);
                    Map<String, Object> stat = new HashMap<>();
                    
                    // 获取除了 count 之外的第一个字段作为 key
                    for (String key : data.keySet()) {
                        if (!"count".equals(key)) {
                            stat.put("name", data.getString(key));
                            break;
                        }
                    }
                    stat.put("value", data.getLong("count"));
                    topStats.add(stat);
                } catch (Exception e) {
                    // 忽略解析错误的行
                }
            }
        }
        
        return topStats;
    }

    /**
     * 生成总体评估
     */
    private String generateOverallAssessment(
            HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo,
            HostAnalysisResponseDTO.HostAssetInfoDTO hostAssetInfo,
            HostAnalysisResponseDTO.HostEventInfoDTO hostEventInfo,
            HostAnalysisResponseDTO.HostRiskInfoDTO hostRiskInfo) {
        
        StringBuilder assessment = new StringBuilder();
        
        // 从原始数据中提取主机名和系统信息，如果没有则使用默认值
        String hostname = extractHostname(hostBasicInfo);
        String osName = extractOsName(hostBasicInfo);
        String onlineStatus = extractOnlineStatus(hostBasicInfo);
        
        assessment.append("主机 ").append(hostBasicInfo.getHostIp()).append(" (").append(hostname).append(") ");
        assessment.append("运行 ").append(osName).append(" 系统，");
        assessment.append("当前状态为 ").append(onlineStatus).append("。");
        
        assessment.append("该主机共有 ").append(hostAssetInfo.getProcessCount()).append(" 个进程，");
        assessment.append(hostAssetInfo.getPortCount()).append(" 个开放端口，");
        assessment.append(hostAssetInfo.getSoftwareCount()).append(" 个已安装软件。");
        
        Long totalEvents = 0L;
        if (hostEventInfo.getAuditLogStat() != null && hostEventInfo.getAuditLogStat().getTotalCount() != null) {
            totalEvents += hostEventInfo.getAuditLogStat().getTotalCount();
        }
        if (hostEventInfo.getNginxLogStat() != null && hostEventInfo.getNginxLogStat().getTotalCount() != null) {
            totalEvents += hostEventInfo.getNginxLogStat().getTotalCount();
        }
        if (hostEventInfo.getZeekLogStat() != null && hostEventInfo.getZeekLogStat().getTotalCount() != null) {
            totalEvents += hostEventInfo.getZeekLogStat().getTotalCount();
        }
        
        assessment.append("在分析时间段内共产生 ").append(totalEvents).append(" 条日志事件，");
        assessment.append("风险等级为 ").append(hostRiskInfo.getRiskLevel()).append("。");
        
        return assessment.toString();
    }

    /**
     * 生成关键发现
     */
    private List<String> generateKeyFindings(
            HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo,
            HostAnalysisResponseDTO.HostAssetInfoDTO hostAssetInfo,
            HostAnalysisResponseDTO.HostEventInfoDTO hostEventInfo,
            HostAnalysisResponseDTO.HostRiskInfoDTO hostRiskInfo) {
        
        List<String> findings = new ArrayList<>();
        
        // 基于资产信息的发现
        if (hostAssetInfo.getProcessCount() > 100) {
            findings.add("主机运行进程数量较多 (" + hostAssetInfo.getProcessCount() + " 个)，建议检查是否有异常进程");
        }
        
        if (hostAssetInfo.getPortCount() > 50) {
            findings.add("主机开放端口数量较多 (" + hostAssetInfo.getPortCount() + " 个)，建议检查是否有不必要的开放端口");
        }
        
        // 基于事件信息的发现
        if (hostEventInfo.getAuditLogStat() != null && hostEventInfo.getAuditLogStat().getTotalCount() > 1000) {
            findings.add("审计日志数量较多 (" + hostEventInfo.getAuditLogStat().getTotalCount() + " 条)，系统活动频繁");
        }
        
        if (hostEventInfo.getNginxLogStat() != null && hostEventInfo.getNginxLogStat().getTotalCount() > 500) {
            findings.add("Web 访问量较大 (" + hostEventInfo.getNginxLogStat().getTotalCount() + " 次)，建议关注访问模式");
        }
        
        if (hostEventInfo.getZeekLogStat() != null && hostEventInfo.getZeekLogStat().getTotalCount() > 1000) {
            findings.add("网络连接数量较多 (" + hostEventInfo.getZeekLogStat().getTotalCount() + " 次)，网络活动活跃");
        }
        
        // 基于主机状态的发现
        String onlineStatus = extractOnlineStatus(hostBasicInfo);
        if ("离线".equals(onlineStatus) || "异常".equals(onlineStatus)) {
            findings.add("主机状态异常 (" + onlineStatus + ")，需要立即检查");
        }
        
        if (findings.isEmpty()) {
            findings.add("主机运行状态正常，未发现明显异常");
        }
        
        return findings;
    }

    /**
     * 生成建议措施
     */
    private List<String> generateRecommendations(
            HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo,
            HostAnalysisResponseDTO.HostAssetInfoDTO hostAssetInfo,
            HostAnalysisResponseDTO.HostEventInfoDTO hostEventInfo,
            HostAnalysisResponseDTO.HostRiskInfoDTO hostRiskInfo) {
        
        List<String> recommendations = new ArrayList<>();
        
        recommendations.add("定期检查系统安全更新和补丁");
        recommendations.add("监控系统资源使用情况，及时清理不必要的进程和服务");
        recommendations.add("定期审查网络连接和访问日志，识别异常行为");
        recommendations.add("建立完善的备份策略，确保数据安全");
        recommendations.add("加强访问控制，限制不必要的网络端口开放");
        
        // 基于具体情况的建议
        if (hostAssetInfo.getPortCount() > 50) {
            recommendations.add("检查并关闭不必要的网络端口，减少攻击面");
        }
        
        if (hostEventInfo.getNginxLogStat() != null && hostEventInfo.getNginxLogStat().getTotalCount() > 500) {
            recommendations.add("配置 Web 应用防火墙，防护 Web 应用安全");
        }
        
        return recommendations;
    }

    /**
     * 格式化内存大小
     */
    private String formatMemorySize(Long bytes) {
        if (bytes == null) return "未知";
        
        double gb = bytes / (1024.0 * 1024.0 * 1024.0);
        if (gb >= 1) {
            return String.format("%.1f GB", gb);
        }
        
        double mb = bytes / (1024.0 * 1024.0);
        return String.format("%.1f MB", mb);
    }

    /**
     * 将时间格式转换为VictoriaLogs期望的ISO 8601格式
     * 
     * @param timeStr 时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @return ISO 8601格式的时间字符串，格式为 "yyyy-MM-ddTHH:mm:ssZ"
     */
    private String convertToISO8601(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 解析输入的时间格式
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.parse(timeStr, inputFormatter);
            
            // 将本地时间转换为ISO 8601格式（假设输入是本地时间，转换为UTC）
            // 注意：这里假设输入的时间是系统默认时区的时间
            return localDateTime.atZone(java.time.ZoneId.systemDefault())
                               .withZoneSameInstant(ZoneOffset.UTC)
                               .format(DateTimeFormatter.ISO_INSTANT);
        } catch (Exception e) {
            logger.warn("时间格式转换失败，原始时间: {}, 错误: {}", timeStr, e.getMessage());
            
            // 如果转换失败，尝试简单的字符串替换
            try {
                return timeStr.replace(" ", "T") + "Z";
            } catch (Exception e2) {
                logger.warn("简单时间格式转换也失败，返回原始时间: {}", timeStr);
                return timeStr; // 如果都失败，返回原始时间字符串
            }
        }
    }
    
    /**
     * 安全地从Map中获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    // 新增辅助方法

    private String extractHostname(HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo) {
        // 从CMDB原始数据中提取主机名
        if ("cmdb".equals(hostBasicInfo.getDataSource()) && hostBasicInfo.getRawData() != null) {
            Map<String, Object> serverInfo = (Map<String, Object>) hostBasicInfo.getRawData().get("serverInfo");
            if (serverInfo != null) {
                Object hostname = serverInfo.get("hostname");
                if (hostname != null) {
                    return hostname.toString();
                }
            }
        }
        
        // 从Agent数据中提取主机名
        if ("agent".equals(hostBasicInfo.getDataSource()) && hostBasicInfo.getAgentInfo() != null) {
            if (hostBasicInfo.getAgentInfo() instanceof Map) {
                Map<String, Object> agentData = (Map<String, Object>) hostBasicInfo.getAgentInfo();
                Object hostname = agentData.get("hostname");
                if (hostname != null) {
                    return hostname.toString();
                }
            }
        }
        
        return "未知主机";
    }

    private String extractOsName(HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo) {
        // 从CMDB原始数据中提取操作系统信息
        if ("cmdb".equals(hostBasicInfo.getDataSource()) && hostBasicInfo.getRawData() != null) {
            Map<String, Object> serverInfo = (Map<String, Object>) hostBasicInfo.getRawData().get("serverInfo");
            if (serverInfo != null) {
                Object osType = serverInfo.get("os_type");
                if (osType != null) {
                    return osType.toString();
                }
            }
        }
        
        // 从Agent数据中提取操作系统信息
        if ("agent".equals(hostBasicInfo.getDataSource()) && hostBasicInfo.getAgentInfo() != null) {
            if (hostBasicInfo.getAgentInfo() instanceof Map) {
                Map<String, Object> agentData = (Map<String, Object>) hostBasicInfo.getAgentInfo();
                Object osName = agentData.get("osName");
                if (osName != null) {
                    return osName.toString();
                }
            }
        }
        
        return "未知操作系统";
    }

    private String extractOnlineStatus(HostAnalysisResponseDTO.HostBasicInfoDTO hostBasicInfo) {
        // 从CMDB原始数据中提取在线状态
        if ("cmdb".equals(hostBasicInfo.getDataSource()) && hostBasicInfo.getRawData() != null) {
            Map<String, Object> serverInfo = (Map<String, Object>) hostBasicInfo.getRawData().get("serverInfo");
            if (serverInfo != null) {
                Object onlineStatus = serverInfo.get("host_online_status");
                if (onlineStatus != null) {
                    return onlineStatus.toString();
                }
            }
        }
        
        // 从Agent数据中提取在线状态
        if ("agent".equals(hostBasicInfo.getDataSource()) && hostBasicInfo.getAgentInfo() != null) {
            if (hostBasicInfo.getAgentInfo() instanceof Map) {
                Map<String, Object> agentData = (Map<String, Object>) hostBasicInfo.getAgentInfo();
                Object status = agentData.get("status");
                if (status != null) {
                    String statusStr = status.toString();
                    if ("running".equals(statusStr)) {
                        return "在线";
                    } else if ("stopped".equals(statusStr)) {
                        return "离线";
                    } else {
                        return statusStr;
                    }
                }
            }
        }
        
        return "未知状态";
    }
} 