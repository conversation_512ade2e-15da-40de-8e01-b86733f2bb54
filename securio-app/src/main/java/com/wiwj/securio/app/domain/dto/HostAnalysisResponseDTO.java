package com.wiwj.securio.app.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 主机综合分析响应DTO
 *
 * <AUTHOR>
 */
@ApiModel(description = "主机综合分析报告")
public class HostAnalysisResponseDTO {

    @ApiModelProperty(value = "主机基本信息")
    private HostBasicInfoDTO hostBasicInfo;

    @ApiModelProperty(value = "主机资产信息")
    private HostAssetInfoDTO hostAssetInfo;

    @ApiModelProperty(value = "主机事件信息")
    private HostEventInfoDTO hostEventInfo;

    @ApiModelProperty(value = "主机风险信息")
    private HostRiskInfoDTO hostRiskInfo;

    @ApiModelProperty(value = "分析摘要")
    private AnalysisSummaryDTO analysisSummary;

    @ApiModelProperty(value = "生成时间")
    private String generateTime;

    @ApiModelProperty(value = "分析器执行结果", notes = "新架构下的分析器结果集合")
    private Map<String, Object> analyzerResults;

    public HostAnalysisResponseDTO() {
    }

    public HostBasicInfoDTO getHostBasicInfo() {
        return hostBasicInfo;
    }

    public void setHostBasicInfo(HostBasicInfoDTO hostBasicInfo) {
        this.hostBasicInfo = hostBasicInfo;
    }

    public HostAssetInfoDTO getHostAssetInfo() {
        return hostAssetInfo;
    }

    public void setHostAssetInfo(HostAssetInfoDTO hostAssetInfo) {
        this.hostAssetInfo = hostAssetInfo;
    }

    public HostEventInfoDTO getHostEventInfo() {
        return hostEventInfo;
    }

    public void setHostEventInfo(HostEventInfoDTO hostEventInfo) {
        this.hostEventInfo = hostEventInfo;
    }

    public HostRiskInfoDTO getHostRiskInfo() {
        return hostRiskInfo;
    }

    public void setHostRiskInfo(HostRiskInfoDTO hostRiskInfo) {
        this.hostRiskInfo = hostRiskInfo;
    }

    public AnalysisSummaryDTO getAnalysisSummary() {
        return analysisSummary;
    }

    public void setAnalysisSummary(AnalysisSummaryDTO analysisSummary) {
        this.analysisSummary = analysisSummary;
    }

    public String getGenerateTime() {
        return generateTime;
    }

    public void setGenerateTime(String generateTime) {
        this.generateTime = generateTime;
    }

    public Map<String, Object> getAnalyzerResults() {
        return analyzerResults;
    }

    public void setAnalyzerResults(Map<String, Object> analyzerResults) {
        this.analyzerResults = analyzerResults;
    }

    /**
     * 主机基本信息DTO
     */
    @ApiModel(description = "主机基本信息")
    public static class HostBasicInfoDTO {
        @ApiModelProperty(value = "主机IP")
        private String hostIp;

        @ApiModelProperty(value = "数据源类型", notes = "cmdb或agent")
        private String dataSource;

        @ApiModelProperty(value = "原始数据", notes = "来自CMDB的原始JSON数据")
        private Map<String, Object> rawData;

        @ApiModelProperty(value = "Agent信息", notes = "来自Agent的原始数据")
        private Object agentInfo;

        // getter和setter方法
        public String getHostIp() {
            return hostIp;
        }

        public void setHostIp(String hostIp) {
            this.hostIp = hostIp;
        }

        public String getDataSource() {
            return dataSource;
        }

        public void setDataSource(String dataSource) {
            this.dataSource = dataSource;
        }

        public Map<String, Object> getRawData() {
            return rawData;
        }

        public void setRawData(Map<String, Object> rawData) {
            this.rawData = rawData;
        }

        public Object getAgentInfo() {
            return agentInfo;
        }

        public void setAgentInfo(Object agentInfo) {
            this.agentInfo = agentInfo;
        }
    }

    /**
     * 主机资产信息DTO
     */
    @ApiModel(description = "主机资产信息")
    public static class HostAssetInfoDTO {
        @ApiModelProperty(value = "用户数量")
        private Integer userCount;

        @ApiModelProperty(value = "进程数量")
        private Integer processCount;

        @ApiModelProperty(value = "端口数量")
        private Integer portCount;

        @ApiModelProperty(value = "软件数量")
        private Integer softwareCount;

        @ApiModelProperty(value = "启动项数量")
        private Integer startupCount;

        @ApiModelProperty(value = "计划任务数量")
        private Integer scheduledTaskCount;

        @ApiModelProperty(value = "内核模块数量")
        private Integer kernelModuleCount;

        @ApiModelProperty(value = "网络接口数量")
        private Integer networkInterfaceCount;

        @ApiModelProperty(value = "详细资产信息")
        private Map<String, Object> detailInfo;

        // getter和setter方法
        public Integer getUserCount() {
            return userCount;
        }

        public void setUserCount(Integer userCount) {
            this.userCount = userCount;
        }

        public Integer getProcessCount() {
            return processCount;
        }

        public void setProcessCount(Integer processCount) {
            this.processCount = processCount;
        }

        public Integer getPortCount() {
            return portCount;
        }

        public void setPortCount(Integer portCount) {
            this.portCount = portCount;
        }

        public Integer getSoftwareCount() {
            return softwareCount;
        }

        public void setSoftwareCount(Integer softwareCount) {
            this.softwareCount = softwareCount;
        }

        public Integer getStartupCount() {
            return startupCount;
        }

        public void setStartupCount(Integer startupCount) {
            this.startupCount = startupCount;
        }

        public Integer getScheduledTaskCount() {
            return scheduledTaskCount;
        }

        public void setScheduledTaskCount(Integer scheduledTaskCount) {
            this.scheduledTaskCount = scheduledTaskCount;
        }

        public Integer getKernelModuleCount() {
            return kernelModuleCount;
        }

        public void setKernelModuleCount(Integer kernelModuleCount) {
            this.kernelModuleCount = kernelModuleCount;
        }

        public Integer getNetworkInterfaceCount() {
            return networkInterfaceCount;
        }

        public void setNetworkInterfaceCount(Integer networkInterfaceCount) {
            this.networkInterfaceCount = networkInterfaceCount;
        }

        public Map<String, Object> getDetailInfo() {
            return detailInfo;
        }

        public void setDetailInfo(Map<String, Object> detailInfo) {
            this.detailInfo = detailInfo;
        }
    }

    /**
     * 主机事件信息DTO
     */
    @ApiModel(description = "主机事件信息")
    public static class HostEventInfoDTO {
        @ApiModelProperty(value = "审计日志统计")
        private LogStatDTO auditLogStat;

        @ApiModelProperty(value = "Nginx访问日志统计")
        private LogStatDTO nginxLogStat;

        @ApiModelProperty(value = "Zeek网络日志统计")
        private LogStatDTO zeekLogStat;

        @ApiModelProperty(value = "网络流量分析")
        private NetworkFlowAnalysisDTO networkFlowAnalysis;

        @ApiModelProperty(value = "访问关系图数据")
        private List<Map<String, Object>> accessRelationData;

        // getter和setter方法
        public LogStatDTO getAuditLogStat() {
            return auditLogStat;
        }

        public void setAuditLogStat(LogStatDTO auditLogStat) {
            this.auditLogStat = auditLogStat;
        }

        public LogStatDTO getNginxLogStat() {
            return nginxLogStat;
        }

        public void setNginxLogStat(LogStatDTO nginxLogStat) {
            this.nginxLogStat = nginxLogStat;
        }

        public LogStatDTO getZeekLogStat() {
            return zeekLogStat;
        }

        public void setZeekLogStat(LogStatDTO zeekLogStat) {
            this.zeekLogStat = zeekLogStat;
        }

        public NetworkFlowAnalysisDTO getNetworkFlowAnalysis() {
            return networkFlowAnalysis;
        }

        public void setNetworkFlowAnalysis(NetworkFlowAnalysisDTO networkFlowAnalysis) {
            this.networkFlowAnalysis = networkFlowAnalysis;
        }

        public List<Map<String, Object>> getAccessRelationData() {
            return accessRelationData;
        }

        public void setAccessRelationData(List<Map<String, Object>> accessRelationData) {
            this.accessRelationData = accessRelationData;
        }
    }

    /**
     * 主机风险信息DTO
     */
    @ApiModel(description = "主机风险信息")
    public static class HostRiskInfoDTO {
        @ApiModelProperty(value = "风险等级")
        private String riskLevel;

        @ApiModelProperty(value = "风险事件数量")
        private Integer riskEventCount;

        @ApiModelProperty(value = "风险事件列表")
        private List<Map<String, Object>> riskEvents;

        @ApiModelProperty(value = "风险建议")
        private List<String> riskSuggestions;

        // getter和setter方法
        public String getRiskLevel() {
            return riskLevel;
        }

        public void setRiskLevel(String riskLevel) {
            this.riskLevel = riskLevel;
        }

        public Integer getRiskEventCount() {
            return riskEventCount;
        }

        public void setRiskEventCount(Integer riskEventCount) {
            this.riskEventCount = riskEventCount;
        }

        public List<Map<String, Object>> getRiskEvents() {
            return riskEvents;
        }

        public void setRiskEvents(List<Map<String, Object>> riskEvents) {
            this.riskEvents = riskEvents;
        }

        public List<String> getRiskSuggestions() {
            return riskSuggestions;
        }

        public void setRiskSuggestions(List<String> riskSuggestions) {
            this.riskSuggestions = riskSuggestions;
        }
    }

    /**
     * 日志统计DTO
     */
    @ApiModel(description = "日志统计信息")
    public static class LogStatDTO {
        @ApiModelProperty(value = "日志总数")
        private Long totalCount;

        @ApiModelProperty(value = "时间分布统计")
        private List<Map<String, Object>> timeDistribution;

        @ApiModelProperty(value = "TOP统计")
        private List<Map<String, Object>> topStats;

        // getter和setter方法
        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }

        public List<Map<String, Object>> getTimeDistribution() {
            return timeDistribution;
        }

        public void setTimeDistribution(List<Map<String, Object>> timeDistribution) {
            this.timeDistribution = timeDistribution;
        }

        public List<Map<String, Object>> getTopStats() {
            return topStats;
        }

        public void setTopStats(List<Map<String, Object>> topStats) {
            this.topStats = topStats;
        }
    }

    /**
     * 网络流量分析DTO
     */
    @ApiModel(description = "网络流量分析")
    public static class NetworkFlowAnalysisDTO {
        @ApiModelProperty(value = "入站流量统计")
        private List<Map<String, Object>> inboundTraffic;

        @ApiModelProperty(value = "出站流量统计")
        private List<Map<String, Object>> outboundTraffic;

        @ApiModelProperty(value = "协议分布")
        private List<Map<String, Object>> protocolDistribution;

        @ApiModelProperty(value = "端口分布")
        private List<Map<String, Object>> portDistribution;

        // getter和setter方法
        public List<Map<String, Object>> getInboundTraffic() {
            return inboundTraffic;
        }

        public void setInboundTraffic(List<Map<String, Object>> inboundTraffic) {
            this.inboundTraffic = inboundTraffic;
        }

        public List<Map<String, Object>> getOutboundTraffic() {
            return outboundTraffic;
        }

        public void setOutboundTraffic(List<Map<String, Object>> outboundTraffic) {
            this.outboundTraffic = outboundTraffic;
        }

        public List<Map<String, Object>> getProtocolDistribution() {
            return protocolDistribution;
        }

        public void setProtocolDistribution(List<Map<String, Object>> protocolDistribution) {
            this.protocolDistribution = protocolDistribution;
        }

        public List<Map<String, Object>> getPortDistribution() {
            return portDistribution;
        }

        public void setPortDistribution(List<Map<String, Object>> portDistribution) {
            this.portDistribution = portDistribution;
        }
    }

    /**
     * 分析摘要DTO
     */
    @ApiModel(description = "分析摘要")
    public static class AnalysisSummaryDTO {
        @ApiModelProperty(value = "总体评估")
        private String overallAssessment;

        @ApiModelProperty(value = "关键发现")
        private List<String> keyFindings;

        @ApiModelProperty(value = "建议措施")
        private List<String> recommendations;

        @ApiModelProperty(value = "分析时间范围")
        private String analysisTimeRange;

        // getter和setter方法
        public String getOverallAssessment() {
            return overallAssessment;
        }

        public void setOverallAssessment(String overallAssessment) {
            this.overallAssessment = overallAssessment;
        }

        public List<String> getKeyFindings() {
            return keyFindings;
        }

        public void setKeyFindings(List<String> keyFindings) {
            this.keyFindings = keyFindings;
        }

        public List<String> getRecommendations() {
            return recommendations;
        }

        public void setRecommendations(List<String> recommendations) {
            this.recommendations = recommendations;
        }

        public String getAnalysisTimeRange() {
            return analysisTimeRange;
        }

        public void setAnalysisTimeRange(String analysisTimeRange) {
            this.analysisTimeRange = analysisTimeRange;
        }
    }
} 