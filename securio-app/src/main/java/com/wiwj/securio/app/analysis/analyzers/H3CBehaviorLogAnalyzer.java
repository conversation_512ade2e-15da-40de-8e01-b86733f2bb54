package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.Analyzer;
import com.wiwj.securio.app.analysis.core.AnalyzerType;
import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.logmgr.util.TimeFormatUtil;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * H3C上网行为分析器
 * 负责分析办公区客户端的H3C上网行为日志，分析用户上网行为模式
 *
 * <AUTHOR>
 */
@Component
public class H3CBehaviorLogAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(H3CBehaviorLogAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Override
    public String getName() {
        return "H3C上网行为分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.H3C_BEHAVIOR_LOG;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 支持办公区客户端IP和VPN用户IP的H3C上网行为分析
        return ipType == IpType.OFFICE_CLIENT || ipType == IpType.VPN_USER;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行H3C上网行为分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建时间查询条件
            String startTimeStr = null;
            String endTimeStr = null;
            
            if (context.getStartTime() != null) {
                startTimeStr = context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            if (context.getEndTime() != null) {
                endTimeStr = context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
            
            String timeQuery = TimeFormatUtil.buildTimeQuery(
                startTimeStr, 
                endTimeStr, 
                context.getTimeRange()
            );
            
            // 构建查询语句
            String query = String.format("%s and stream:\"AUDITLOG_H3C_BEHAVIOR\" and src_ip:\"%s\"", 
                timeQuery, context.getHostIp());
            
            logger.info("H3C上网行为查询语句: {}", query);
            
            // 获取详细日志记录
            String behaviorQuery = query + " | limit 100";
            String logsResult = victoriaLogsService.queryLogs(behaviorQuery, 100, "60s", null);
            List<Map<String, Object>> behaviorRecords = parseH3CBehaviorLogs(logsResult, context.getHostIp());
            
            // 使用facets接口获取统计数据
            Map<String, Object> facetsData = getFacetsStatistics(query, context.getHostIp());
            
            // 分析行为数据
            Map<String, Object> behaviorAnalysis = analyzeBehaviorData(behaviorRecords, facetsData, context.getHostIp());
            
            // 构建分析结果
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("targetIp", context.getHostIp());
            analysisData.put("behaviorRecords", behaviorRecords);
            analysisData.put("totalRecords", behaviorRecords.size());
            analysisData.put("behaviorAnalysis", behaviorAnalysis);
            analysisData.put("facetsData", facetsData);
            
            // 添加时间信息供前端组件构建LogQL使用
            if (context.getStartTime() != null) {
                analysisData.put("startTime", context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getEndTime() != null) {
                analysisData.put("endTime", context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getTimeRange() != null) {
                analysisData.put("timeRange", context.getTimeRange());
            }
            
            // 生成分析摘要
            String summary = generateAnalysisSummary(context.getHostIp(), behaviorRecords, behaviorAnalysis);
            analysisData.put("analysisSummary", summary);
            
            // 时间范围描述信息
            analysisData.put("timeRangeDescription", getTimeRangeDescription(context));
            
            logger.info("H3C上网行为分析完成，主机IP: {}, 记录数: {}", 
                context.getHostIp(), behaviorRecords.size());
                
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("H3C上网行为分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "H3C上网行为分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        // H3C上网行为分析优先级为3
        return 3;
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        // 预估执行时间10秒
        return 10000;
    }
    
    /**
     * 使用facets接口获取统计数据
     */
    private Map<String, Object> getFacetsStatistics(String baseQuery, String targetIp) {
        Map<String, Object> facetsData = new HashMap<>();
        
        try {
            // 调用facets接口获取统计数据
            String facetsResult = victoriaLogsService.facets(baseQuery, 50, null);
            
            if (facetsResult != null && !facetsResult.trim().isEmpty()) {
                JSONObject facetsJson = JSON.parseObject(facetsResult);
                
                if (facetsJson != null && facetsJson.containsKey("facets")) {
                    JSONArray facets = facetsJson.getJSONArray("facets");
                    
                    // 解析感兴趣的字段
                    for (int i = 0; i < facets.size(); i++) {
                        JSONObject facet = facets.getJSONObject(i);
                        String fieldName = facet.getString("field_name");
                        
                        if ("url_category".equals(fieldName)) {
                            facetsData.put("urlCategories", parseFacetValues(facet));
                        } else if ("url_domain".equals(fieldName)) {
                            facetsData.put("urlDomains", parseFacetValues(facet));
                        } else if ("dst_ip".equals(fieldName) || "dst_ip".equals(fieldName)) {
                            facetsData.put("dstIps", parseFacetValues(facet));
                        } else if ("user_group_name".equals(fieldName)) {
                            facetsData.put("userGroups", parseFacetValues(facet));
                        } else if ("app_name".equals(fieldName)) {
                            facetsData.put("appNames", parseFacetValues(facet));
                        } else if ("log_type".equals(fieldName)) {
                            facetsData.put("logTypes", parseFacetValues(facet));
                        } else if ("handle_action".equals(fieldName)) {
                            facetsData.put("handleActions", parseFacetValues(facet));
                        }
                    }
                }
            }
            
            logger.info("获取到H3C上网行为facets统计数据，目标IP: {}", targetIp);
            
        } catch (Exception e) {
            logger.error("获取H3C上网行为facets统计数据失败", e);
        }
        
        return facetsData;
    }
    
    /**
     * 解析facet字段值
     */
    private List<Map<String, Object>> parseFacetValues(JSONObject facet) {
        List<Map<String, Object>> values = new ArrayList<>();
        
        try {
            JSONArray valuesArray = facet.getJSONArray("values");
            for (int i = 0; i < valuesArray.size(); i++) {
                JSONObject valueObj = valuesArray.getJSONObject(i);
                Map<String, Object> valueMap = new HashMap<>();
                valueMap.put("name", valueObj.getString("field_value"));
                valueMap.put("value", valueObj.getInteger("hits"));
                values.add(valueMap);
            }
        } catch (Exception e) {
            logger.debug("解析facet值失败", e);
        }
        
        return values;
    }
    
    /**
     * 解析H3C上网行为日志
     * @param logsResult 日志查询结果
     * @param targetIp 目标IP
     * @return 解析后的行为记录列表
     */
    private List<Map<String, Object>> parseH3CBehaviorLogs(String logsResult, String targetIp) {
        List<Map<String, Object>> records = new ArrayList<>();
        
        if (logsResult == null || logsResult.trim().isEmpty()) {
            logger.warn("H3C上网行为日志查询结果为空，IP: {}", targetIp);
            return records;
        }
        
        try {
            // 尝试解析为JSON格式
            try {
                JSONObject jsonResult = JSON.parseObject(logsResult);
                if (jsonResult != null && jsonResult.containsKey("data")) {
                    return parseJSONLogs(jsonResult, targetIp);
                }
            } catch (Exception e) {
                logger.debug("无法解析为JSON格式，尝试按行解析: {}", e.getMessage());
            }
            
            // 按行分割日志（原始文本格式）
            String[] lines = logsResult.split("\n");
            
            for (String line : lines) {
                if (line.trim().isEmpty()) {
                    continue;
                }
                
                try {
                    Map<String, Object> record = parseH3CBehaviorLogLine(line);
                    if (record != null && record.containsKey("src_ip") && 
                        targetIp.equals(record.get("src_ip"))) {
                        records.add(record);
                    }
                } catch (Exception e) {
                    logger.debug("解析H3C行为日志行失败: {}, 错误: {}", line, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("解析H3C上网行为日志异常", e);
        }
        
        return records;
    }
    
    /**
     * 解析JSON格式的VictoriaLogs返回结果
     */
    private List<Map<String, Object>> parseJSONLogs(JSONObject jsonResult, String targetIp) {
        List<Map<String, Object>> records = new ArrayList<>();
        
        try {
            if (jsonResult.containsKey("data")) {
                Object data = jsonResult.get("data");
                if (data instanceof String) {
                    String[] lines = ((String) data).split("\n");
                    for (String line : lines) {
                        if (line.trim().isEmpty()) {
                            continue;
                        }
                        
                        try {
                            JSONObject logEntry = JSON.parseObject(line);
                            Map<String, Object> record = parseJSONLogEntry(logEntry, targetIp);
                            if (record != null) {
                                records.add(record);
                            }
                        } catch (Exception e) {
                            Map<String, Object> record = parseH3CBehaviorLogLine(line);
                            if (record != null && record.containsKey("src_ip") && 
                                targetIp.equals(record.get("src_ip"))) {
                                records.add(record);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析JSON格式H3C行为日志异常", e);
        }
        
        return records;
    }
    
    /**
     * 解析单个JSON日志条目
     */
    private Map<String, Object> parseJSONLogEntry(JSONObject logEntry, String targetIp) {
        try {
            String srcIp = logEntry.getString("src_ip");
            if (srcIp == null || !targetIp.equals(srcIp)) {
                return null;
            }
            
            Map<String, Object> record = new HashMap<>();
            
            // 提取关键字段
            record.put("timestamp", logEntry.getString("_time"));
            record.put("src_ip", logEntry.getString("src_ip"));
            record.put("user_name", logEntry.getString("user_name"));
            record.put("user_group_name", logEntry.getString("user_group_name"));
            record.put("src_mac", logEntry.getString("src_mac"));
            record.put("dst_ip", logEntry.getString("dst_ip"));
            record.put("dst_port", logEntry.getString("dst_port"));
            record.put("app_name", logEntry.getString("app_name"));
            record.put("app_cat_name", logEntry.getString("app_cat_name"));
            record.put("log_type", logEntry.getString("log_type"));
            record.put("handle_action", logEntry.getString("handle_action"));
            record.put("term_platform", logEntry.getString("term_platform"));
            record.put("term_device", logEntry.getString("term_device"));
            record.put("content", logEntry.getString("content"));
            record.put("account", logEntry.getString("account"));
            record.put("action_name", logEntry.getString("action_name"));
            record.put("message", logEntry.getString("message"));
            record.put("url", logEntry.getString("url"));
            record.put("url_category", logEntry.getString("url_category"));
            record.put("url_domain", logEntry.getString("url_domain"));
            
            return record;
        } catch (Exception e) {
            logger.debug("解析JSON行为日志条目异常: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 解析单行H3C行为日志（文本格式）
     */
    private Map<String, Object> parseH3CBehaviorLogLine(String logLine) {
        try {
            // 简单的字段提取逻辑，实际可以根据需要优化
            Map<String, Object> record = new HashMap<>();
            record.put("rawLog", logLine);
            
            // 从原始日志中提取关键信息
            if (logLine.contains("src_ip=")) {
                String srcIp = extractFieldValue(logLine, "src_ip=");
                record.put("src_ip", srcIp);
            }
            
            if (logLine.contains("user_name=")) {
                record.put("user_name", extractFieldValue(logLine, "user_name="));
            }
            
            if (logLine.contains("user_group_name=")) {
                record.put("user_group_name", extractFieldValue(logLine, "user_group_name="));
            }
            
            if (logLine.contains("app_name=")) {
                record.put("app_name", extractFieldValue(logLine, "app_name="));
            }
            
            if (logLine.contains("dst_ip=")) {
                record.put("dst_ip", extractFieldValue(logLine, "dst_ip="));
            }
            
            if (logLine.contains("url=")) {
                record.put("url", extractFieldValue(logLine, "url="));
            }
            
            if (logLine.contains("url_category=")) {
                record.put("url_category", extractFieldValue(logLine, "url_category="));
            }
            
            if (logLine.contains("url_domain=")) {
                record.put("url_domain", extractFieldValue(logLine, "url_domain="));
            }
            
            // 提取时间戳
            if (logLine.matches("^\\w{3}\\s+\\d{1,2}\\s+\\d{2}:\\d{2}:\\d{2}.*")) {
                String timestamp = logLine.substring(0, 15);
                record.put("timestamp", timestamp);
            }
            
            return record;
        } catch (Exception e) {
            logger.debug("解析H3C行为日志行异常: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 从日志行中提取字段值
     */
    private String extractFieldValue(String logLine, String fieldPrefix) {
        try {
            int startIndex = logLine.indexOf(fieldPrefix);
            if (startIndex == -1) {
                return null;
            }
            
            startIndex += fieldPrefix.length();
            int endIndex = logLine.indexOf(";", startIndex);
            if (endIndex == -1) {
                endIndex = logLine.length();
            }
            
            return logLine.substring(startIndex, endIndex).trim();
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 分析行为数据（结合facets统计数据）
     */
    private Map<String, Object> analyzeBehaviorData(List<Map<String, Object>> records, 
                                                   Map<String, Object> facetsData, String targetIp) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 从facets数据中获取统计信息
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> urlCategories = (List<Map<String, Object>>) facetsData.get("urlCategories");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> urlDomains = (List<Map<String, Object>>) facetsData.get("urlDomains");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> dstIps = (List<Map<String, Object>>) facetsData.get("dstIps");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> userGroups = (List<Map<String, Object>>) facetsData.get("userGroups");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> appNames = (List<Map<String, Object>>) facetsData.get("appNames");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> logTypes = (List<Map<String, Object>>) facetsData.get("logTypes");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> handleActions = (List<Map<String, Object>>) facetsData.get("handleActions");
        
        // 计算总访问次数
        int totalHits = 0;
        if (urlCategories != null) {
            totalHits = urlCategories.stream().mapToInt(item -> (Integer) item.get("value")).sum();
        }
        
        analysis.put("totalRecords", records.size());
        analysis.put("totalHits", totalHits);
        
        // 计算唯一值数量
        analysis.put("uniqueUrlCategories", urlCategories != null ? urlCategories.size() : 0);
        analysis.put("uniqueUrlDomains", urlDomains != null ? urlDomains.size() : 0);
        analysis.put("uniquedstIps", dstIps != null ? dstIps.size() : 0);
        analysis.put("uniqueUserGroups", userGroups != null ? userGroups.size() : 0);
        analysis.put("uniqueApps", appNames != null ? appNames.size() : 0);
        
        // 设置图表数据
        analysis.put("urlCategoriesChart", urlCategories != null ? urlCategories : new ArrayList<>());
        analysis.put("urlDomainsChart", urlDomains != null ? urlDomains.stream().limit(10).collect(Collectors.toList()) : new ArrayList<>());
        analysis.put("dstIpsChart", dstIps != null ? dstIps.stream().limit(10).collect(Collectors.toList()) : new ArrayList<>());
        analysis.put("userGroupsChart", userGroups != null ? userGroups : new ArrayList<>());
        analysis.put("appNamesChart", appNames != null ? appNames.stream().limit(10).collect(Collectors.toList()) : new ArrayList<>());
        analysis.put("logTypesChart", logTypes != null ? logTypes : new ArrayList<>());
        analysis.put("handleActionsChart", handleActions != null ? handleActions : new ArrayList<>());
        
        // 获取Top应用（从facets数据）
        if (appNames != null && !appNames.isEmpty()) {
            List<Map.Entry<String, Integer>> topApps = appNames.stream()
                .limit(10)
                .map(item -> new AbstractMap.SimpleEntry<>((String) item.get("name"), (Integer) item.get("value")))
                .collect(Collectors.toList());
            analysis.put("topApps", topApps);
        } else {
            analysis.put("topApps", new ArrayList<>());
        }
        
        return analysis;
    }
    
    /**
     * 生成分析摘要
     */
    private String generateAnalysisSummary(String targetIp, List<Map<String, Object>> records, Map<String, Object> analysis) {
        if (records.isEmpty() && (Integer) analysis.getOrDefault("totalHits", 0) == 0) {
            return String.format("未发现IP %s 的H3C上网行为日志记录。可能该IP在指定时间范围内无网络活动。", targetIp);
        }
        
        StringBuilder summary = new StringBuilder();
        int totalHits = (Integer) analysis.getOrDefault("totalHits", 0);
        int totalRecords = records.size();
        
        summary.append(String.format("在指定时间范围内，IP %s 的H3C上网行为分析发现 %d 条访问记录。", 
            targetIp, totalHits > 0 ? totalHits : totalRecords));
        
        int uniqueUrlCategories = (Integer) analysis.getOrDefault("uniqueUrlCategories", 0);
        int uniqueUrlDomains = (Integer) analysis.getOrDefault("uniqueUrlDomains", 0);
        int uniquedstIps = (Integer) analysis.getOrDefault("uniquedstIps", 0);
        int uniqueApps = (Integer) analysis.getOrDefault("uniqueApps", 0);
        
        if (uniqueUrlCategories > 0) {
            summary.append(String.format(" 涉及 %d 个网站分类，", uniqueUrlCategories));
        }
        if (uniqueUrlDomains > 0) {
            summary.append(String.format("访问了 %d 个不同域名，", uniqueUrlDomains));
        }
        if (uniqueApps > 0) {
            summary.append(String.format("使用了 %d 个不同应用，", uniqueApps));
        }
        if (uniquedstIps > 0) {
            summary.append(String.format("连接到 %d 个不同的目标服务器。", uniquedstIps));
        }
        
        @SuppressWarnings("unchecked")
        List<Map.Entry<String, Integer>> topApps = (List<Map.Entry<String, Integer>>) analysis.get("topApps");
        if (topApps != null && !topApps.isEmpty()) {
            summary.append(" 主要使用的应用包括：");
            for (int i = 0; i < Math.min(3, topApps.size()); i++) {
                Map.Entry<String, Integer> app = topApps.get(i);
                summary.append(app.getKey()).append("(").append(app.getValue()).append("次)");
                if (i < Math.min(2, topApps.size() - 1)) {
                    summary.append("、");
                }
            }
            summary.append("。");
        }
        
        return summary.toString();
    }
    
    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(AnalysisContext context) {
        if (context.getStartTime() != null && context.getEndTime() != null) {
            return String.format("%s ~ %s", 
                context.getStartTime().toString(), 
                context.getEndTime().toString());
        } else if (context.getTimeRange() != null) {
            return "最近 " + context.getTimeRange();
        } else {
            return "未指定时间范围";
        }
    }
} 