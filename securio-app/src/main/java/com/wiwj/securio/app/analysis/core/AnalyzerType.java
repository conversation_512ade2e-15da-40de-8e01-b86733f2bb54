package com.wiwj.securio.app.analysis.core;

/**
 * 分析器类型枚举
 *
 * <AUTHOR>
 */
public enum AnalyzerType {
    
    /** 主机基本信息分析器 */
    HOST_BASIC_INFO("主机基本信息分析", "host_basic_info"),
    
    /** DNS请求日志分析器 */
    DNS_LOG("DNS请求日志分析", "dns_log"),
    
    /** DNS查询行为分析器 */
    DNS_QUERY_ANALYSIS("DNS查询行为分析", "dns_query_analysis"),
    
    /** Nginx访问日志分析器 */
    NGINX_ACCESS_LOG("Nginx访问日志分析", "nginx_access_log"),
    
    /** Zeek网络连接日志分析器 */
    ZEEK_CONN_LOG("Zeek网络连接日志分析", "zeek_conn_log"),
    
    /** Zeek HTTP日志分析器 */
    ZEEK_HTTP_LOG("Zeek HTTP日志分析", "zeek_http_log"),
    
    /** Zeek文件传输日志分析器 */
    ZEEK_FILES_LOG("Zeek文件传输日志分析", "zeek_files_log"),
    
    /** 系统日志分析器 */
    SYSLOG("系统日志分析", "syslog"),
    
    /** 系统命令日志分析器 */
    SYS_COMMAND("系统命令日志分析", "sys_command"),
    
    /** H3C上网行为日志分析器 */
    H3C_WEB_BEHAVIOR("H3C上网行为日志分析", "h3c_web_behavior"),
    
    /** 主机资产分析器 */
    HOST_ASSET("主机资产分析", "host_asset"),
    
    /** 主机网络连接分析器 */
    HOST_NETWORK_CONNECTION("主机网络连接分析", "host_network_connection"),
    
    /** AC访问日志分析器 */
    AC_ACCESS_LOG("AC访问日志分析", "ac_access_log"),
    
    /** 主机命令分析器 */
    COMMAND_ANALYSIS("主机命令分析", "command_analysis"),
    
    /** 主机系统日志分析器 */
    SYSLOG_ANALYSIS("主机系统日志分析", "syslog_analysis"),
    
    /** H3C上网行为分析器 */
    H3C_BEHAVIOR_LOG("H3C上网行为分析", "h3c_behavior_log"),
    
    /** Zeek SSH连接分析器 */
    ZEEK_SSH_ANALYSIS("Zeek SSH连接分析", "zeek_ssh_analysis"),
    
    /** Zeek HTTP连接分析器 */
    ZEEK_HTTP_ANALYSIS("Zeek HTTP连接分析", "zeek_http_analysis"),
    
    /** Zeek网络连接分析器 */
    ZEEK_CONN_ANALYSIS("Zeek网络连接分析", "zeek_conn_analysis");
    
    private final String description;
    private final String code;
    
    AnalyzerType(String description, String code) {
        this.description = description;
        this.code = code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getCode() {
        return code;
    }
} 