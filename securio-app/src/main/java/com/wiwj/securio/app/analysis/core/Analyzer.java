package com.wiwj.securio.app.analysis.core;

import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;

/**
 * 分析器接口
 * 所有具体分析器都需要实现此接口
 *
 * <AUTHOR>
 */
public interface Analyzer {
    
    /**
     * 获取分析器名称
     * @return 分析器名称
     */
    String getName();
    
    /**
     * 获取分析器类型
     * @return 分析器类型
     */
    AnalyzerType getType();
    
    /**
     * 检查是否支持指定的IP类型
     * @param ipType IP类型
     * @return 是否支持
     */
    boolean supports(IpType ipType);
    
    /**
     * 执行分析
     * @param context 分析上下文
     * @return 分析结果
     */
    AnalysisResult analyze(AnalysisContext context);
    
    /**
     * 获取分析器优先级（数字越小优先级越高）
     * @return 优先级
     */
    int getPriority();
    
    /**
     * 获取预估执行时间（毫秒）
     * @return 预估执行时间
     */
    long getEstimatedExecutionTime();
} 