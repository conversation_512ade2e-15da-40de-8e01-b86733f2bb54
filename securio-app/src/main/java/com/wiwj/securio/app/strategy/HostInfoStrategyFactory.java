package com.wiwj.securio.app.strategy;

import com.wiwj.securio.app.config.HostAnalysisConfig;
import com.wiwj.securio.app.domain.dto.HostAnalysisResponseDTO.HostBasicInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 主机信息策略工厂
 *
 * <AUTHOR>
 */
@Component
public class HostInfoStrategyFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(HostInfoStrategyFactory.class);
    
    @Resource
    private HostAnalysisConfig hostAnalysisConfig;
    
    @Resource
    private Map<String, HostInfoStrategy> strategyMap;
    
    /**
     * 获取主机基本信息（根据配置选择策略）
     *
     * @param hostIp 主机IP地址
     * @return 主机基本信息
     */
    public HostBasicInfoDTO getHostBasicInfo(String hostIp) {
        String dataSource = hostAnalysisConfig.getBasicInfoSource();
        
        logger.info("获取主机基本信息，主机IP: {}, 数据源: {}", hostIp, dataSource);
        logger.info("可用策略列表: {}", strategyMap.keySet());
        
        // 使用指定的数据源获取信息
        HostBasicInfoDTO result = tryGetHostInfo(hostIp, dataSource);
        
        if (result != null) {
            logger.info("成功获取主机信息，数据源: {}, rawData字段数: {}, agentInfo存在: {}", 
                dataSource, 
                result.getRawData() != null ? result.getRawData().size() : 0,
                result.getAgentInfo() != null);
        } else {
            logger.warn("获取主机信息失败，使用默认信息，数据源: {}", dataSource);
        }
        
        return result != null ? result : createDefaultHostInfo(hostIp);
    }
    
    /**
     * 尝试使用指定策略获取主机信息
     */
    private HostBasicInfoDTO tryGetHostInfo(String hostIp, String strategyName) {
        try {
            String strategyBeanName = strategyName + "HostInfoStrategy";
            HostInfoStrategy strategy = strategyMap.get(strategyBeanName);
            
            if (strategy == null) {
                logger.warn("未找到策略实现: {}", strategyBeanName);
                return null;
            }
            
            return strategy.getHostBasicInfo(hostIp);
            
        } catch (Exception e) {
            logger.error("使用策略 {} 获取主机信息失败，主机IP: {}", strategyName, hostIp, e);
            return null;
        }
    }
    
    /**
     * 创建默认主机信息
     */
    private HostBasicInfoDTO createDefaultHostInfo(String hostIp) {
        HostBasicInfoDTO defaultInfo = new HostBasicInfoDTO();
        defaultInfo.setHostIp(hostIp);
        defaultInfo.setDataSource("unknown");
        defaultInfo.setRawData(new HashMap<>());
        defaultInfo.setAgentInfo(new HashMap<>());
        return defaultInfo;
    }
} 