package com.wiwj.securio.app.controller;

import com.wiwj.securio.app.dto.IpLocationInfo;
import com.wiwj.securio.app.service.IpLocationService;
import com.wiwj.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * IP归属信息查询控制器
 */
@RestController
@RequestMapping("/api/ip-location")
@Api(tags = "IP归属信息查询")
public class IpLocationController {
    
    private static final Logger log = LoggerFactory.getLogger(IpLocationController.class);
    
    @Autowired
    private IpLocationService ipLocationService;
    
    /**
     * 查询IP归属信息
     * @param ip IP地址
     * @return IP归属信息
     */
    @GetMapping("/query")
    @ApiOperation(value = "查询IP归属信息", notes = "根据IP地址查询其地理位置和运营商信息，支持多个API源随机选择和重试机制")
    public AjaxResult queryIpLocation(
            @ApiParam(value = "IP地址", required = true, example = "*******")
            @RequestParam String ip) {
        
        try {
            log.info("收到IP归属信息查询请求: {}", ip);
            
            IpLocationInfo locationInfo = ipLocationService.getIpLocation(ip);
            
            if (locationInfo != null) {
                log.info("成功查询IP归属信息: {}", ip);
                return AjaxResult.success("查询成功", locationInfo);
            } else {
                log.warn("未能查询到IP归属信息: {}", ip);
                return AjaxResult.error("未能查询到IP归属信息，请检查IP地址是否正确或稍后重试");
            }
            
        } catch (Exception e) {
            log.error("查询IP归属信息时发生异常: {}", ip, e);
            return AjaxResult.error("查询IP归属信息时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 批量查询IP归属信息
     * @param ips IP地址列表
     * @return IP归属信息列表
     */
    @PostMapping("/batch-query")
    @ApiOperation(value = "批量查询IP归属信息", notes = "批量查询多个IP地址的地理位置信息")
    public AjaxResult batchQueryIpLocation(
            @ApiParam(value = "IP地址列表", required = true)
            @RequestBody java.util.List<String> ips) {
        
        try {
            log.info("收到批量IP归属信息查询请求，数量: {}", ips.size());
            
            java.util.List<IpLocationInfo> results = new java.util.ArrayList<>();
            
            for (String ip : ips) {
                IpLocationInfo locationInfo = ipLocationService.getIpLocation(ip);
                if (locationInfo != null) {
                    results.add(locationInfo);
                } else {
                    // 对于查询失败的IP，添加一个标记信息
                    IpLocationInfo failedInfo = new IpLocationInfo(ip);
                    failedInfo.setApiSource("failed");
                    results.add(failedInfo);
                }
            }
            
            log.info("批量查询完成，成功查询: {}/{}", 
                    results.stream().filter(info -> !"failed".equals(info.getApiSource())).count(), 
                    ips.size());
            
            return AjaxResult.success("批量查询完成", results);
            
        } catch (Exception e) {
            log.error("批量查询IP归属信息时发生异常", e);
            return AjaxResult.error("批量查询IP归属信息时发生异常: " + e.getMessage());
        }
    }
} 