package com.wiwj.securio.app.analysis.core;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * IP类型识别测试
 */
public class IpTypeTest {

    @Test
    public void testVpnUserIpRecognition() {
        // 测试VPN用户IP识别
        assertEquals(IpType.VPN_USER, IpType.fromIp("************"));
        assertEquals(IpType.VPN_USER, IpType.fromIp("**************"));
        assertEquals(IpType.VPN_USER, IpType.fromIp("************"));
        assertEquals(IpType.VPN_USER, IpType.fromIp("*************"));
    }

    @Test
    public void testOfficeClientIpRecognition() {
        // 测试办公区客户端IP识别
        assertEquals(IpType.OFFICE_CLIENT, IpType.fromIp("**********"));
        assertEquals(IpType.OFFICE_CLIENT, IpType.fromIp("************"));
    }

    @Test
    public void testServerIpRecognition() {
        // 测试服务器IP识别
        assertEquals(IpType.SERVER, IpType.fromIp("**********"));
        assertEquals(IpType.SERVER, IpType.fromIp("************"));
        assertEquals(IpType.SERVER, IpType.fromIp("***********"));
        
        // 确保10.250和10.251不被识别为服务器IP
        assertNotEquals(IpType.SERVER, IpType.fromIp("************"));
        assertNotEquals(IpType.SERVER, IpType.fromIp("************"));
    }

    @Test
    public void testPublicIpRecognition() {
        // 测试公网IP识别
        assertEquals(IpType.PUBLIC, IpType.fromIp("*************"));
        assertEquals(IpType.PUBLIC, IpType.fromIp("************"));
        assertEquals(IpType.PUBLIC, IpType.fromIp("*******"));
        assertEquals(IpType.PUBLIC, IpType.fromIp("***************"));
    }

    @Test
    public void testUnknownIpRecognition() {
        // 测试未知IP识别
        assertEquals(IpType.UNKNOWN, IpType.fromIp(null));
        assertEquals(IpType.UNKNOWN, IpType.fromIp(""));
        assertEquals(IpType.UNKNOWN, IpType.fromIp("   "));
    }

    @Test
    public void testIpTypeDescription() {
        // 测试IP类型描述
        assertEquals("VPN用户", IpType.VPN_USER.getDescription());
        assertEquals("vpn_user", IpType.VPN_USER.getCode());
    }
} 