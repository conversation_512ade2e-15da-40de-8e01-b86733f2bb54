package com.wiwj.securio.app.util;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wiwj.securio.logmgr.util.TimeFormatUtil;

/**
 * TimeFormatUtil测试类
 * 测试时区转换功能
 *
 * <AUTHOR>
 */
public class TimeFormatUtilTest {

    private static final Logger logger = LoggerFactory.getLogger(TimeFormatUtilTest.class);

    @Test
    public void testTimeZoneConversion() {
        logger.info("开始测试时区转换功能");

        // 测试不同格式的时间字符串
        String[] testTimes = {
            "2024-12-31T10:00:00Z",      // ISO格式带Z
            "2024-12-31T10:00:00",       // ISO格式不带Z
            "2024-12-31 10:00:00",       // 简单格式
            "2024-12-31T10:00:00.123Z"   // 带毫秒的格式
        };

        for (String testTime : testTimes) {
            logger.info("\n=== 测试时间: {} ===", testTime);
            
            // 转换为UTC时间
            String utcTime = TimeFormatUtil.formatForVictoriaLogs(testTime);
            logger.info("输入时间（东八区）: {}", testTime);
            logger.info("转换后UTC时间: {}", utcTime);
            
            // 转换回东八区时间
            String chinaTime = TimeFormatUtil.convertUtcToChina(utcTime);
            logger.info("转换回东八区: {}", chinaTime);
            
            // 构建查询条件
            String query = TimeFormatUtil.buildTimeRangeQuery(testTime, testTime);
            logger.info("VictoriaLogs查询: {}", query);
        }

        // 测试时间范围查询
        logger.info("\n=== 测试时间范围查询 ===");
        String startTime = "2024-12-31T08:00:00";
        String endTime = "2024-12-31T10:00:00";
        String rangeQuery = TimeFormatUtil.buildTimeRangeQuery(startTime, endTime);
        logger.info("时间范围查询: 开始时间={}, 结束时间={}", startTime, endTime);
        logger.info("生成的查询条件: {}", rangeQuery);

        logger.info("时区转换测试完成");
    }

    @Test
    public void testCurrentTime() {
        logger.info("=== 当前时间测试 ===");
        logger.info("当前UTC时间: {}", TimeFormatUtil.getCurrentTimeUTC());
        logger.info("当前东八区时间: {}", TimeFormatUtil.getCurrentTimeChina());
        
        logger.info("=== 时间偏移测试 ===");
        logger.info("30分钟前UTC时间: {}", TimeFormatUtil.getTimeMinutesAgoUTC(30));
        logger.info("30分钟前东八区时间: {}", TimeFormatUtil.getTimeMinutesAgoChina(30));
    }

    @Test
    public void testTimeValidation() {
        logger.info("=== 时间格式验证测试 ===");
        
        String[] validTimes = {
            "2024-12-31T10:00:00Z",
            "2024-12-31T10:00:00",
            "2024-12-31 10:00:00"
        };
        
        String[] invalidTimes = {
            "invalid-time",
            "2024/12/31 10:00:00",
            "2024-13-31T10:00:00"
        };
        
        logger.info("有效时间格式测试:");
        for (String time : validTimes) {
            boolean valid = TimeFormatUtil.isValidTimeFormat(time);
            logger.info("{} -> {}", time, valid ? "有效" : "无效");
        }
        
        logger.info("无效时间格式测试:");
        for (String time : invalidTimes) {
            boolean valid = TimeFormatUtil.isValidTimeFormat(time);
            logger.info("{} -> {}", time, valid ? "有效" : "无效");
        }
    }

    /**
     * 测试快速时间选择功能
     */
    @Test
    public void testQuickTimeSelection() {
        logger.info("=== 快速时间选择测试 ===");
        
        // 测试有效的快速时间格式
        String[] validQuickTimes = {"1h", "30m", "1d", "2h", "5m", "7d"};
        for (String quickTime : validQuickTimes) {
            boolean isValid = TimeFormatUtil.isValidQuickTimeRange(quickTime);
            boolean isQuick = TimeFormatUtil.isQuickTimeSelection(quickTime);
            logger.info("快速时间 '{}': 格式有效={}, 是快速选择={}", quickTime, isValid, isQuick);
            
            String timeQuery = TimeFormatUtil.buildTimeQuery("", "", quickTime);
            logger.info("生成的查询条件: {}", timeQuery);
        }
        
        // 测试无效的快速时间格式
        String[] invalidQuickTimes = {"1x", "abc", "precise", "", null, "1hour"};
        for (String quickTime : invalidQuickTimes) {
            boolean isValid = TimeFormatUtil.isValidQuickTimeRange(quickTime);
            boolean isQuick = TimeFormatUtil.isQuickTimeSelection(quickTime);
            logger.info("无效时间 '{}': 格式有效={}, 是快速选择={}", quickTime, isValid, isQuick);
        }
        
        // 测试精确时间选择回退
        logger.info("\n=== 精确时间选择回退测试 ===");
        String startTime = "2024-12-31T10:00:00";
        String endTime = "2024-12-31T12:00:00";
        String timeQuery = TimeFormatUtil.buildTimeQuery(startTime, endTime, "precise");
        logger.info("精确时间查询: 开始={}, 结束={}, 生成条件={}", startTime, endTime, timeQuery);
        
        // 测试无效快速时间的回退
        timeQuery = TimeFormatUtil.buildTimeQuery(startTime, endTime, "invalid");
        logger.info("无效快速时间回退: 生成条件={}", timeQuery);
        
        logger.info("快速时间选择测试完成");
    }

    /**
     * 测试时间范围查询格式
     */
    @Test
    public void testTimeRangeQueryFormat() {
        logger.info("=== 时间范围查询格式测试 ===");
        
        String startTime = "2025-05-29T12:13:19Z";
        String endTime = "2025-05-29T12:43:19Z";
        
        // 测试精确时间范围查询
        String rangeQuery = TimeFormatUtil.buildTimeRangeQuery(startTime, endTime);
        logger.info("精确时间范围查询: {}", rangeQuery);
        
        // 验证格式应该包含Z
        if (rangeQuery.contains("Z")) {
            logger.info("✓ 时间格式正确，包含Z");
        } else {
            logger.error("✗ 时间格式错误，缺少Z");
        }
        
        // 测试快速时间与精确时间的区别
        String quickQuery = TimeFormatUtil.buildTimeQuery("", "", "1h");
        String preciseQuery = TimeFormatUtil.buildTimeQuery(startTime, endTime, "precise");
        
        logger.info("快速时间查询: {}", quickQuery);
        logger.info("精确时间查询: {}", preciseQuery);
        
        // 验证不同查询方式的格式
        if (quickQuery.equals("_time:1h")) {
            logger.info("✓ 快速时间查询格式正确");
        } else {
            logger.error("✗ 快速时间查询格式错误");
        }
        
        if (preciseQuery.contains("_time:[") && preciseQuery.contains("Z")) {
            logger.info("✓ 精确时间查询格式正确，包含时间范围和Z");
        } else {
            logger.error("✗ 精确时间查询格式错误");
        }
        
        logger.info("时间范围查询格式测试完成");
    }

    /**
     * 手动测试方法，可以在main方法中调用
     */
    public static void main(String[] args) {
        // 测试具体的时间转换
        TimeFormatUtil.testTimeConversion("2024-12-31T14:00:00Z");
        
        System.out.println("\n=== 实际应用场景测试 ===");
        
        // 模拟前端传递的时间（东八区下午2点）
        String frontendTime = "2024-12-31T14:00:00Z";
        System.out.println("前端传递时间（东八区下午2点）: " + frontendTime);
        
        // 转换为UTC时间（应该是上午6点）
        String utcTime = TimeFormatUtil.formatForVictoriaLogs(frontendTime);
        System.out.println("转换为UTC时间（应该是上午6点）: " + utcTime);
        
        // 构建VictoriaLogs查询
        String query = TimeFormatUtil.buildTimeRangeQuery(frontendTime, frontendTime);
        System.out.println("VictoriaLogs查询条件: " + query);
        
        System.out.println("\n说明：");
        System.out.println("- 前端传递 2024-12-31T14:00:00Z（实际是东八区时间）");
        System.out.println("- 后端转换为 UTC 时间应该是 2024-12-31T06:00:00Z");
        System.out.println("- 因为东八区比UTC快8小时，所以东八区14点 = UTC时间6点");
    }
} 