package com.wiwj.securio.app.analysis.analyzers;

import com.wiwj.securio.app.analysis.core.IpType;
import com.wiwj.securio.app.domain.dto.AnalysisContext;
import com.wiwj.securio.app.domain.dto.AnalysisResult;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * DNS查询分析器测试类
 *
 * <AUTHOR>
 */
public class DnsQueryAnalyzerTest {
    
    @Mock
    private VictoriaLogsService victoriaLogsService;
    
    @InjectMocks
    private DnsQueryAnalyzer dnsQueryAnalyzer;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    void testGetName() {
        assertEquals("DNS查询行为分析器", dnsQueryAnalyzer.getName());
    }
    
    @Test
    void testSupports() {
        assertTrue(dnsQueryAnalyzer.supports(IpType.SERVER));
        assertTrue(dnsQueryAnalyzer.supports(IpType.OFFICE_CLIENT));
        assertTrue(dnsQueryAnalyzer.supports(IpType.PUBLIC));
        assertFalse(dnsQueryAnalyzer.supports(IpType.UNKNOWN));
    }
    
    @Test
    void testAnalyze() {
        // 准备测试数据
        AnalysisContext context = new AnalysisContext();
        context.setHostIp("*********");
        context.setTimeRange("1d");
        context.setStartTime(LocalDateTime.now().minusDays(1));
        context.setEndTime(LocalDateTime.now());
        
        // 模拟VictoriaLogs服务返回的数据
        String mockLogsResult = "{\"event_time\":\"2025-06-04 16:06:24.33\",\"src_ip\":\"*********\",\"dst_ip\":\"*************\",\"query_domain\":\"obs.cn-north-4.myhuaweicloud.com\",\"query_type\":\"A\",\"src_port\":\"52745\"}";
        String mockFacetsResult = "{\"facets\":[{\"field_name\":\"dst_ip\",\"values\":[{\"field_value\":\"*************\",\"hits\":100}]},{\"field_name\":\"query_domain\",\"values\":[{\"field_value\":\"obs.cn-north-4.myhuaweicloud.com\",\"hits\":50}]},{\"field_name\":\"query_type\",\"values\":[{\"field_value\":\"A\",\"hits\":80}]}]}";
        
        when(victoriaLogsService.queryLogs(anyString(), anyInt(), anyString(), any()))
            .thenReturn(mockLogsResult);
        when(victoriaLogsService.facets(anyString(), anyInt(), any()))
            .thenReturn(mockFacetsResult);
        
        // 执行分析
        AnalysisResult result = dnsQueryAnalyzer.analyze(context);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
    }
    
    @Test
    void testAnalyzeWithException() {
        // 准备测试数据
        AnalysisContext context = new AnalysisContext();
        context.setHostIp("*********");
        context.setTimeRange("1d");
        
        // 模拟异常情况
        when(victoriaLogsService.queryLogs(anyString(), anyInt(), anyString(), any()))
            .thenThrow(new RuntimeException("查询失败"));
        
        // 执行分析
        AnalysisResult result = dnsQueryAnalyzer.analyze(context);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("DNS查询分析失败"));
    }
    
    @Test
    void testGetPriority() {
        assertEquals(4, dnsQueryAnalyzer.getPriority());
    }
    
    @Test
    void testGetEstimatedExecutionTime() {
        assertEquals(8000, dnsQueryAnalyzer.getEstimatedExecutionTime());
    }
} 