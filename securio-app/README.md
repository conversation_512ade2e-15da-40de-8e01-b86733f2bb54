# Securio-App 模块

## 模块简介

`securio-app` 是 Securio 安全运营平台的综合应用模块，专门用于构建直接和用户交互的综合应用功能。该模块整合了多个底层模块的能力，为用户提供高级的分析和管理功能。

## 主要功能

### 主机综合分析

提供完整的主机安全分析功能，包括：

- **主机基本信息**：从 CMDB 和 Agent 获取主机的基础信息
- **主机资产信息**：统计主机上的用户、进程、端口、软件等资产
- **主机事件信息**：分析审计日志、Nginx 访问日志、Zeek 网络日志
- **主机风险信息**：评估主机的安全风险等级
- **智能分析摘要**：生成总体评估、关键发现和建议措施

## 模块架构

### 依赖关系

```
securio-app
├── securio-common (通用工具)
├── securio-framework (核心框架)
├── securio-logmgr (日志管理)
├── securio-agentmgr (Agent管理)
└── securio-plugin (插件模块)
```

### 包结构

```
com.wiwj.securio.app
├── controller/          # 控制器层
├── service/            # 服务层
│   └── impl/          # 服务实现
├── domain/            # 领域对象
│   └── dto/          # 数据传输对象
└── config/           # 配置类
```

## API 接口

### 主机综合分析接口

- `POST /app/host-analysis/analyze` - 执行主机综合分析
- `GET /app/host-analysis/basic-info/{hostIp}` - 获取主机基本信息
- `GET /app/host-analysis/asset-info/{hostIp}` - 获取主机资产信息
- `GET /app/host-analysis/event-info/{hostIp}` - 获取主机事件信息
- `GET /app/host-analysis/risk-info/{hostIp}` - 获取主机风险信息

## 数据源整合

### 1. CMDB 数据源
- 通过 `securio-plugin` 模块的 WukongCMDBAPI 获取主机基本信息
- 包括主机名、操作系统、硬件配置、位置信息等

### 2. Agent 数据源
- 通过 `securio-agentmgr` 模块获取主机资产清单
- 包括用户、进程、端口、软件、启动项等统计信息

### 3. 日志数据源
- 通过 `securio-logmgr` 模块的 VictoriaLogsService 查询日志
- 支持审计日志、Nginx 访问日志、Zeek 网络日志的统计分析

## 技术特点

### 1. 模块化设计
- 遵循 SOLID 原则，高内聚低耦合
- 清晰的分层架构，便于维护和扩展

### 2. 数据整合
- 整合多个数据源，提供统一的分析视图
- 智能的数据补充和默认值处理

### 3. 错误处理
- 完善的异常处理机制
- 优雅的降级处理，确保服务可用性

### 4. 性能优化
- 使用 LogsQL 进行高效的日志查询
- 合理的查询限制和分页处理

## 使用示例

### 执行主机综合分析

```json
POST /app/host-analysis/analyze
{
  "hostIp": "*********",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-01-01 23:59:59",
  "timeRange": "24h",
  "analysisLevel": "comprehensive"
}
```

### 响应示例

```json
{
  "code": 200,
  "msg": "主机综合分析完成",
  "data": {
    "hostBasicInfo": {
      "hostIp": "*********",
      "hostname": "web-server-01",
      "osName": "CentOS Linux",
      "onlineStatus": "在线"
    },
    "hostAssetInfo": {
      "userCount": 15,
      "processCount": 120,
      "portCount": 8
    },
    "hostEventInfo": {
      "auditLogStat": {
        "totalCount": 1500
      }
    },
    "analysisSummary": {
      "overallAssessment": "主机运行状态正常...",
      "keyFindings": ["系统活动正常"],
      "recommendations": ["定期检查系统安全更新"]
    },
    "generateTime": "2024-01-01 12:00:00"
  }
}
```

## 开发规范

### 1. 代码规范
- 不使用 Lombok，手动生成 getter/setter
- 使用 NumberUtils 工具类进行数字转换
- 遵循 DRY、KISS、YAGNI 原则

### 2. 安全规范
- 遵循 OWASP 最佳实践
- 输入参数验证和 SQL 注入防护
- 敏感信息脱敏处理

### 3. 日志规范
- 使用 SLF4J 进行日志记录
- 关键操作记录详细日志
- 错误日志包含完整的异常信息

## 扩展指南

### 添加新的分析功能

1. 在 `HostAnalysisService` 接口中添加新方法
2. 在 `HostAnalysisServiceImpl` 中实现具体逻辑
3. 在 `HostAnalysisController` 中添加对应的 REST 接口
4. 更新 `HostAnalysisResponseDTO` 添加新的数据结构

### 集成新的数据源

1. 在服务实现类中添加新的数据获取方法
2. 处理数据源的异常情况和默认值
3. 在分析摘要生成中考虑新数据源的影响
4. 更新相关的 DTO 结构

## 部署说明

该模块作为 `securio-admin` 的依赖模块，会自动包含在主应用中。确保以下配置正确：

1. VictoriaLogs 连接配置
2. 其他模块的 API 地址配置
3. 数据库连接配置（如果需要）

## 版本历史

- v1.0.0 - 初始版本，包含主机综合分析功能 