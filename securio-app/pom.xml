<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>securio</artifactId>
        <groupId>com.wiwj</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>securio-app</artifactId>

    <description>
        综合应用模块 - 用户交互应用
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.wiwj</groupId>
            <artifactId>securio-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wiwj</groupId>
            <artifactId>securio-framework</artifactId>
        </dependency>

        <!-- 日志管理模块 -->
        <dependency>
            <groupId>com.wiwj</groupId>
            <artifactId>securio-logmgr</artifactId>
        </dependency>

        <!-- Agent管理模块 -->
        <dependency>
            <groupId>com.wiwj</groupId>
            <artifactId>securio-agentmgr</artifactId>
        </dependency>

        <!-- 插件模块 -->
        <dependency>
            <groupId>com.wiwj</groupId>
            <artifactId>securio-plugin</artifactId>
        </dependency>

        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>
        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project> 