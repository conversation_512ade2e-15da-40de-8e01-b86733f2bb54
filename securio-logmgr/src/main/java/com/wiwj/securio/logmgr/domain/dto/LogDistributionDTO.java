package com.wiwj.securio.logmgr.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 日志分布数据传输对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "LogDistributionDTO", description = "日志分布数据")
public class LogDistributionDTO {
    
    /** 日志类型分布数据 */
    @ApiModelProperty(value = "日志类型分布数据")
    private List<TypeDistribution> typeDistribution;
    
    /** 日志处理量排名数据 */
    @ApiModelProperty(value = "日志处理量排名数据")
    private List<TypeRanking> typeRanking;
    
    /** 图表数据 */
    @ApiModelProperty(value = "图表数据")
    private Map<String, Object> chartData;

    public List<TypeDistribution> getTypeDistribution() {
        return typeDistribution;
    }

    public void setTypeDistribution(List<TypeDistribution> typeDistribution) {
        this.typeDistribution = typeDistribution;
    }

    public List<TypeRanking> getTypeRanking() {
        return typeRanking;
    }

    public void setTypeRanking(List<TypeRanking> typeRanking) {
        this.typeRanking = typeRanking;
    }

    public Map<String, Object> getChartData() {
        return chartData;
    }

    public void setChartData(Map<String, Object> chartData) {
        this.chartData = chartData;
    }

    /**
     * 日志类型分布
     */
    public static class TypeDistribution {
        /** 日志类型 */
        @ApiModelProperty(value = "日志类型", example = "SECURIO_ZEEK_CONN")
        private String logType;
        
        /** 数量 */
        @ApiModelProperty(value = "数量", example = "1000")
        private long count;
        
        /** 百分比 */
        @ApiModelProperty(value = "百分比", example = "10.5")
        private double percentage;

        public String getLogType() {
            return logType;
        }

        public void setLogType(String logType) {
            this.logType = logType;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }

        public double getPercentage() {
            return percentage;
        }

        public void setPercentage(double percentage) {
            this.percentage = percentage;
        }
    }

    /**
     * 日志类型排名
     */
    public static class TypeRanking {
        /** 日志类型 */
        @ApiModelProperty(value = "日志类型", example = "SECURIO_ZEEK_CONN")
        private String logType;
        
        /** 数量 */
        @ApiModelProperty(value = "数量", example = "1000")
        private long count;
        
        /** 排名 */
        @ApiModelProperty(value = "排名", example = "1")
        private int rank;

        public String getLogType() {
            return logType;
        }

        public void setLogType(String logType) {
            this.logType = logType;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }
    }
}
