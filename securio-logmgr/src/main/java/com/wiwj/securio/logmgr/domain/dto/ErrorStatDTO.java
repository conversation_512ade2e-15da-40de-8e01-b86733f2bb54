package com.wiwj.securio.logmgr.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 错误统计数据传输对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "ErrorStatDTO", description = "错误统计数据")
public class ErrorStatDTO {
    
    /** 总错误数 */
    @ApiModelProperty(value = "总错误数", example = "10")
    private long totalErrors;
    
    /** 总丢弃数 */
    @ApiModelProperty(value = "总丢弃数", example = "5")
    private long totalDropped;
    
    /** 错误率 */
    @ApiModelProperty(value = "错误率", example = "0.1")
    private double errorRate;
    
    /** 丢弃率 */
    @ApiModelProperty(value = "丢弃率", example = "0.05")
    private double droppedRate;
    
    /** 最近错误时间 */
    @ApiModelProperty(value = "最近错误时间")
    private Date lastErrorTime;
    
    /** 最近错误组件 */
    @ApiModelProperty(value = "最近错误组件", example = "*output.VictoriaLogsOutput")
    private String lastErrorComponent;
    
    /** 错误趋势数据 */
    @ApiModelProperty(value = "错误趋势数据")
    private Map<String, List<Object>> errorTrend;

    public long getTotalErrors() {
        return totalErrors;
    }

    public void setTotalErrors(long totalErrors) {
        this.totalErrors = totalErrors;
    }

    public long getTotalDropped() {
        return totalDropped;
    }

    public void setTotalDropped(long totalDropped) {
        this.totalDropped = totalDropped;
    }

    public double getErrorRate() {
        return errorRate;
    }

    public void setErrorRate(double errorRate) {
        this.errorRate = errorRate;
    }

    public double getDroppedRate() {
        return droppedRate;
    }

    public void setDroppedRate(double droppedRate) {
        this.droppedRate = droppedRate;
    }

    public Date getLastErrorTime() {
        return lastErrorTime;
    }

    public void setLastErrorTime(Date lastErrorTime) {
        this.lastErrorTime = lastErrorTime;
    }

    public String getLastErrorComponent() {
        return lastErrorComponent;
    }

    public void setLastErrorComponent(String lastErrorComponent) {
        this.lastErrorComponent = lastErrorComponent;
    }

    public Map<String, List<Object>> getErrorTrend() {
        return errorTrend;
    }

    public void setErrorTrend(Map<String, List<Object>> errorTrend) {
        this.errorTrend = errorTrend;
    }
}
