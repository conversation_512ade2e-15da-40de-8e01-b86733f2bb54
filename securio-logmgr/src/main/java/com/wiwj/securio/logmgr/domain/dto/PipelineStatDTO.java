package com.wiwj.securio.logmgr.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 管道统计数据传输对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "PipelineStatDTO", description = "管道统计数据")
public class PipelineStatDTO {
    
    /** 管道名称 */
    @ApiModelProperty(value = "管道名称", example = "zeek")
    private String pipelineName;
    
    /** 输入组件统计 */
    @ApiModelProperty(value = "输入组件统计")
    private StageStats input;
    
    /** 处理组件统计 */
    @ApiModelProperty(value = "处理组件统计")
    private StageStats processor;
    
    /** 输出组件统计 */
    @ApiModelProperty(value = "输出组件统计")
    private StageStats output;
    
    /** 性能趋势数据 */
    @ApiModelProperty(value = "性能趋势数据")
    private Map<String, List<Object>> performanceTrend;
    
    /** 可用的管道列表 */
    @ApiModelProperty(value = "可用的管道列表")
    private List<String> availablePipelines;

    public String getPipelineName() {
        return pipelineName;
    }

    public void setPipelineName(String pipelineName) {
        this.pipelineName = pipelineName;
    }

    public StageStats getInput() {
        return input;
    }

    public void setInput(StageStats input) {
        this.input = input;
    }

    public StageStats getProcessor() {
        return processor;
    }

    public void setProcessor(StageStats processor) {
        this.processor = processor;
    }

    public StageStats getOutput() {
        return output;
    }

    public void setOutput(StageStats output) {
        this.output = output;
    }

    public Map<String, List<Object>> getPerformanceTrend() {
        return performanceTrend;
    }

    public void setPerformanceTrend(Map<String, List<Object>> performanceTrend) {
        this.performanceTrend = performanceTrend;
    }

    public List<String> getAvailablePipelines() {
        return availablePipelines;
    }

    public void setAvailablePipelines(List<String> availablePipelines) {
        this.availablePipelines = availablePipelines;
    }

    /**
     * 阶段统计
     */
    public static class StageStats {
        /** 接收消息数 */
        @ApiModelProperty(value = "接收消息数", example = "1000")
        private long received;
        
        /** 处理消息数 */
        @ApiModelProperty(value = "处理消息数", example = "1000")
        private long processed;
        
        /** 错误数 */
        @ApiModelProperty(value = "错误数", example = "0")
        private long errors;
        
        /** 丢弃消息数 */
        @ApiModelProperty(value = "丢弃消息数", example = "0")
        private long dropped;
        
        /** 平均处理时间（纳秒） */
        @ApiModelProperty(value = "平均处理时间（纳秒）", example = "1000")
        private double avgProcessingTime;

        public long getReceived() {
            return received;
        }

        public void setReceived(long received) {
            this.received = received;
        }

        public long getProcessed() {
            return processed;
        }

        public void setProcessed(long processed) {
            this.processed = processed;
        }

        public long getErrors() {
            return errors;
        }

        public void setErrors(long errors) {
            this.errors = errors;
        }

        public long getDropped() {
            return dropped;
        }

        public void setDropped(long dropped) {
            this.dropped = dropped;
        }

        public double getAvgProcessingTime() {
            return avgProcessingTime;
        }

        public void setAvgProcessingTime(double avgProcessingTime) {
            this.avgProcessingTime = avgProcessingTime;
        }
    }
}
