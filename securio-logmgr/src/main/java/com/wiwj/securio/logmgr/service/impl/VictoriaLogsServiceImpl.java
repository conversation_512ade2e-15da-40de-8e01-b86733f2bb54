package com.wiwj.securio.logmgr.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.logmgr.domain.dto.FieldStatDTO;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * VictoriaLogs 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class VictoriaLogsServiceImpl implements VictoriaLogsService {

    private static final Logger logger = LoggerFactory.getLogger(VictoriaLogsServiceImpl.class);

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${victoria.logs.url}")
    private String victoriaLogsUrl;

    @Value("${victoria.logs.url2}")
    private String victoriaLogsUrl2;

    @Value("${victoria.logs.api.path:/select/logsql}")
    private String victoriaLogsApiPath;

    @Override
    public String queryLogs(String query, Integer limit, String timeout, String instance) {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行 queryLogs 方法，参数: query={},limit={}, timeout={}, instance={}",
                   query, limit, timeout, instance);
        try {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("query", query);

            if (limit != null) {
                params.add("limit", limit.toString());
            }

            if (StringUtils.hasText(timeout)) {
                params.add("timeout", timeout);
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            // 根据实例参数选择不同的 URL
            String baseUrl = getInstanceUrl(instance);
            String url = baseUrl + victoriaLogsApiPath + "/query";
            logger.info("查询 URL: {}", url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            String result = response.getBody();
            long endTime = System.currentTimeMillis();
            logger.info("queryLogs 方法执行完成，耗时: {}ms", (endTime - startTime));
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("queryLogs 方法执行异常，耗时: {}ms, 错误信息: {}", (endTime - startTime), e.getMessage(), e);
            throw new RuntimeException("Error querying logs", e);
        }
    }

    @Override
    public String queryStats(String query, String time, Map<String, Object> extraFilters,
                            Map<String, Object> extraStreamFilters, String instance) {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行 queryStats 方法，参数: query={}, time={}, instance={}", query, time, instance);
        try {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("query", query);

            if (StringUtils.hasText(time)) {
                params.add("time", time);
            }

            addExtraFilters(params, extraFilters, extraStreamFilters);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            // 根据实例参数选择不同的 URL
            String baseUrl = getInstanceUrl(instance);
            String url = baseUrl + victoriaLogsApiPath + "/stats";
            logger.info("查询 URL: {}", url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            String result = response.getBody();
            long endTime = System.currentTimeMillis();
            logger.info("queryStats 方法执行完成，耗时: {}ms", (endTime - startTime));
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("queryStats 方法执行异常，耗时: {}ms, 错误信息: {}", (endTime - startTime), e.getMessage(), e);
            throw new RuntimeException("Error querying stats", e);
        }
    }

    @Override
    public String queryStatsRange(String query, String start, String end, String step,
                                 Map<String, Object> extraFilters, Map<String, Object> extraStreamFilters, String instance) {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行 queryStatsRange 方法，参数: query={}, start={}, end={}, step={}, instance={}",
                   query, start, end, step, instance);
        try {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("query", query);

            if (StringUtils.hasText(start)) {
                params.add("start", start);
            }

            if (StringUtils.hasText(end)) {
                params.add("end", end);
            }

            if (StringUtils.hasText(step)) {
                params.add("step", step);
            }

            addExtraFilters(params, extraFilters, extraStreamFilters);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            // 根据实例参数选择不同的 URL
            String baseUrl = getInstanceUrl(instance);
            String url = baseUrl + victoriaLogsApiPath + "/stats_query_range";
            logger.info("查询 URL: {}", url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            String result = response.getBody();
            long endTime = System.currentTimeMillis();
            logger.info("queryStatsRange 方法执行完成，耗时: {}ms", (endTime - startTime));
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("queryStatsRange 方法执行异常，耗时: {}ms, 错误信息: {}", (endTime - startTime), e.getMessage(), e);
            throw new RuntimeException("Error querying stats range", e);
        }
    }


    @Override
    public String getLogFieldStats(String stream, String timeRange, Integer limit, String instance) {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行 getLogFieldStats 方法，参数: stream={}, timeRange={}, limit={}, instance={}",
                   stream, timeRange, limit, instance);
        try {
            // 构建查询语句
            StringBuilder queryBuilder = new StringBuilder();
            if (StringUtils.hasText(timeRange)) {
                queryBuilder.append("_time:").append(timeRange);
            } else {
                queryBuilder.append("_time:10d"); // 默认查询10天内的数据
            }

            if (StringUtils.hasText(stream)) {
                queryBuilder.append(" and stream:\"").append(stream).append("\"");
            }

            String query = queryBuilder.toString();

            // 设置请求参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("query", query);

            if (limit != null) {
                params.add("limit", limit.toString());
            } else {
                params.add("limit", "10"); // 默认每个字段返回10个值
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            // 根据实例参数选择不同的 URL
            String baseUrl = getInstanceUrl(instance);
            String url = baseUrl + victoriaLogsApiPath + "/facets";
            logger.info("查询 URL: {}", url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            String result = response.getBody();
            long endTime = System.currentTimeMillis();
            logger.info("getLogFieldStats 方法执行完成，耗时: {}ms", (endTime - startTime));
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("getLogFieldStats 方法执行异常，耗时: {}ms, 错误信息: {}", (endTime - startTime), e.getMessage(), e);
            throw new RuntimeException("Error getting log field stats", e);
        }
    }

    @Override
    public String facets(String query, Integer limit, String instance) {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行 facets 方法，参数: query={}, limit={}, instance={}", query, limit, instance);
        try {
            // 构建请求参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("query", query);

            if (limit != null) {
                params.add("limit", limit.toString());
            } else {
                params.add("limit", "10"); // 默认每个字段返回10个值
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            // 根据实例参数选择不同的 URL
            String baseUrl = getInstanceUrl(instance);
            String url = baseUrl + "/select/logsql/facets";
            logger.info("查询 URL: {}", url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            String result = response.getBody();
            long endTime = System.currentTimeMillis();
            logger.info("facets 方法执行完成，耗时: {}ms", (endTime - startTime));
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("facets 方法执行异常，耗时: {}ms, 错误信息: {}", (endTime - startTime), e.getMessage(), e);
            throw new RuntimeException("Error calling VictoriaLogs facets API: " + e.getMessage(), e);
        }
    }

    @Override
    public JSONObject getTimeSeriesStats(String query, String start, String end, String step,
                                       Map<String, Object> extraFilters, Map<String, Object> extraStreamFilters, String instance) {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行 getTimeSeriesStats 方法，参数: query={}, start={}, end={}, step={}, instance={}",
                   query, start, end, step, instance);
        try {
            // 调用VictoriaLogs服务查询数据
            String result = queryStatsRange(query, start, end, step, extraFilters, extraStreamFilters, instance);

            // 解析原始数据并进行预处理
            JSONObject jsonResult = JSON.parseObject(result);

            // 检查数据格式是否正确
            if (jsonResult == null || !"success".equals(jsonResult.getString("status")) ||
                jsonResult.getJSONObject("data") == null ||
                jsonResult.getJSONObject("data").getJSONArray("result") == null) {
                throw new RuntimeException("无效的时间序列数据格式");
            }

            // 获取结果数组
            JSONArray results = jsonResult.getJSONObject("data").getJSONArray("result");

            // 如果没有数据，返回原始结果
            if (results.isEmpty()) {
                long endTime = System.currentTimeMillis();
                logger.info("getTimeSeriesStats 方法执行完成（无数据），耗时: {}ms", (endTime - startTime));
                return jsonResult;
            }

            // 对每个结果进行预处理
            for (int i = 0; i < results.size(); i++) {
                JSONObject resultItem = results.getJSONObject(i);
                JSONArray values = resultItem.getJSONArray("values");

                if (values != null && !values.isEmpty()) {
                    // 按时间戳排序
                    values.sort((a, b) -> {
                        JSONArray aArr = (JSONArray) a;
                        JSONArray bArr = (JSONArray) b;
                        return Long.compare(aArr.getLongValue(0), bArr.getLongValue(0));
                    });

                    // 处理缺失的时间点
                    if (values.size() > 1) {
                        JSONArray newValues = new JSONArray();
                        JSONArray firstPoint = values.getJSONArray(0);
                        newValues.add(firstPoint);

                        // 根据 step 参数计算时间间隔（默认为 1 小时 = 3600 秒）
                        long timeStep = 3600;
                        if (step != null && !step.isEmpty()) {
                            if (step.endsWith("h")) {
                                timeStep = Long.parseLong(step.substring(0, step.length() - 1)) * 3600;
                            } else if (step.endsWith("m")) {
                                timeStep = Long.parseLong(step.substring(0, step.length() - 1)) * 60;
                            } else if (step.endsWith("s")) {
                                timeStep = Long.parseLong(step.substring(0, step.length() - 1));
                            } else if (step.endsWith("d")) {
                                timeStep = Long.parseLong(step.substring(0, step.length() - 1)) * 86400;
                            }
                        }

                        for (int j = 1; j < values.size(); j++) {
                            JSONArray currentPoint = values.getJSONArray(j);
                            JSONArray prevPoint = newValues.getJSONArray(newValues.size() - 1);

                            long prevTime = prevPoint.getLongValue(0);
                            long currentTime = currentPoint.getLongValue(0);

                            // 如果两个时间点之间有缺失的数据点
                            if (currentTime - prevTime > timeStep * 1.5) {
                                // 计算需要插入的点数
                                long missingPoints = (currentTime - prevTime) / timeStep - 1;

                                // 插入缺失的数据点（值为 0）
                                for (long k = 1; k <= missingPoints; k++) {
                                    JSONArray newPoint = new JSONArray();
                                    newPoint.add(prevTime + k * timeStep);
                                    newPoint.add("0");
                                    newValues.add(newPoint);
                                }
                            }

                            newValues.add(currentPoint);
                        }

                        // 替换原始的 values 数组
                        resultItem.put("values", newValues);
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            logger.info("getTimeSeriesStats 方法执行完成，处理了 {} 条结果，耗时: {}ms",
                       results.size(), (endTime - startTime));
            return jsonResult;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("getTimeSeriesStats 方法执行异常，耗时: {}ms, 错误信息: {}",
                        (endTime - startTime), e.getMessage(), e);
            throw new RuntimeException("Error getting time series stats: " + e.getMessage(), e);
        }
    }


    @Override
    public List<FieldStatDTO> statByField(String stream, String field, String timeRange, Integer limit, String instance) {
        try {
            // 构建查询语句
            StringBuilder queryBuilder = new StringBuilder();

            // 添加时间范围
            if (StringUtils.hasText(timeRange)) {
                queryBuilder.append("_time:").append(timeRange);
            } else {
                queryBuilder.append("_time:7d"); // 默认查询7天内的数据
            }

            // 添加流过滤条件
            if (StringUtils.hasText(stream)) {
                queryBuilder.append(" and stream:\"").append(stream).append("\"");
            }

            // 添加统计语句
            queryBuilder.append(" | stats by (").append(field).append(") count(*) cnt | sort by (cnt desc)");

            String query = queryBuilder.toString();

            // 调用 queryLogs 方法获取结果，传递实例参数
            String result = queryLogs(query, limit, null, instance);

            // 解析结果并转换为 DTO 列表
            List<FieldStatDTO> statsList = new ArrayList<>();
            if (StringUtils.hasText(result)) {
                // 按行分割结果
                String[] lines = result.split("\\n");
                for (String line : lines) {
                    if (StringUtils.hasText(line)) {
                        try {
                            // 使用 fastjson 解析 JSON 字符串
                            JSONObject jsonObj = JSON.parseObject(line);
                            if (jsonObj != null && jsonObj.containsKey("cnt") && jsonObj.containsKey(field)) {
                                // 获取 cnt 值
                                Long count = jsonObj.getLong("cnt");

                                // 获取字段值
                                String fieldValue = jsonObj.getString(field);

                                statsList.add(new FieldStatDTO(fieldValue, count));
                            }
                        } catch (Exception e) {
                            // 解析失败，忽略该行
                        }
                    }
                }
            }

            return statsList;
        } catch (Exception e) {
            logger.error("statByField 方法执行异常: {}", e.getMessage(), e);
            throw new RuntimeException("Error getting field stats: " + e.getMessage(), e);
        }
    }

    @Override
    public int countUniqueValues(String stream, String field, String timeRange, String instance) {
        try {
            // 构建查询语句
            StringBuilder queryBuilder = new StringBuilder();

            // 添加时间范围
            if (StringUtils.hasText(timeRange)) {
                queryBuilder.append("_time:").append(timeRange);
            } else {
                queryBuilder.append("_time:7d"); // 默认查询7天内的数据
            }

            // 添加流过滤条件
            if (StringUtils.hasText(stream)) {
                queryBuilder.append(" and _stream: {stream=\"").append(stream).append("\"}");
            }

            // 添加去重和计数语句
            queryBuilder.append(" | uniq by (").append(field).append(") | count()");

            String query = queryBuilder.toString();
            logger.info("countUniqueValues 查询语句: {}", query);

            // 调用 VictoriaLogs API 获取结果
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("query", query);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            // 根据实例参数选择不同的 URL
            String baseUrl = getInstanceUrl(instance);
            String url = baseUrl + "/select/logsql/query";
            logger.info("查询 URL: {}", url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            String result = response.getBody();
            logger.info("countUniqueValues 结果: {}", result);

            // 解析结果
            if (StringUtils.hasText(result)) {
                try {
                    // 使用 fastjson 解析 JSON 字符串
                    JSONObject jsonObj = JSON.parseObject(result);
                    if (jsonObj != null && jsonObj.containsKey("count(*)")) {
                        String countStr = jsonObj.getString("count(*)");
                        return Integer.parseInt(countStr);
                    }
                } catch (Exception e) {
                    logger.error("解析 countUniqueValues 结果失败: {}", e.getMessage(), e);
                }
            }

            // 默认返回0
            return 0;
        } catch (Exception e) {
            logger.error("countUniqueValues 方法执行异常: {}", e.getMessage(), e);
            throw new RuntimeException("Error counting unique values: " + e.getMessage(), e);
        }
    }

    /**
     * 根据实例名称获取对应的 URL
     *
     * @param instance 实例名称，为 null 时使用默认实例
     * @return 实例对应的 URL
     */
    private String getInstanceUrl(String instance) {
        if (instance == null) {
            // 默认使用 victoriaLogsUrl 审计日志实例
            return victoriaLogsUrl;
        }

        // 根据实例名称返回对应的 URL
        switch (instance) {
            case "vmlog1":
                return victoriaLogsUrl;
            case "vmlog2":
                return victoriaLogsUrl2;
            default:
                return victoriaLogsUrl;
        }
    }

    /**
     * 添加额外过滤条件
     *
     * @param params 请求参数
     * @param extraFilters 额外过滤条件
     * @param extraStreamFilters 额外流过滤条件
     */
    private void addExtraFilters(MultiValueMap<String, String> params,
                                Map<String, Object> extraFilters,
                                Map<String, Object> extraStreamFilters) {
        if (extraFilters != null && !extraFilters.isEmpty()) {
            params.add("extra_filters", JSON.toJSONString(extraFilters));
        }

        if (extraStreamFilters != null && !extraStreamFilters.isEmpty()) {
            params.add("extra_stream_filters", JSON.toJSONString(extraStreamFilters));
        }
    }
}
