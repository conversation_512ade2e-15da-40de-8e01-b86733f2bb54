package com.wiwj.securio.logmgr.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 吞吐量数据传输对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "ThroughputDTO", description = "吞吐量数据")
public class ThroughputDTO {
    
    /** 时间单位 */
    @ApiModelProperty(value = "时间单位", example = "minute")
    private String timeUnit;
    
    /** 总吞吐量 */
    @ApiModelProperty(value = "总吞吐量", example = "1000")
    private long totalThroughput;
    
    /** 平均吞吐量 */
    @ApiModelProperty(value = "平均吞吐量", example = "100")
    private double avgThroughput;
    
    /** 最大吞吐量 */
    @ApiModelProperty(value = "最大吞吐量", example = "200")
    private long maxThroughput;
    
    /** 最小吞吐量 */
    @ApiModelProperty(value = "最小吞吐量", example = "50")
    private long minThroughput;
    
    /** 吞吐量趋势数据 */
    @ApiModelProperty(value = "吞吐量趋势数据")
    private Map<String, Object> throughputTrend;
    
    /** 日志类型吞吐量数据 */
    @ApiModelProperty(value = "日志类型吞吐量数据")
    private List<LogTypeThroughput> logTypeThroughput;

    public String getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
    }

    public long getTotalThroughput() {
        return totalThroughput;
    }

    public void setTotalThroughput(long totalThroughput) {
        this.totalThroughput = totalThroughput;
    }

    public double getAvgThroughput() {
        return avgThroughput;
    }

    public void setAvgThroughput(double avgThroughput) {
        this.avgThroughput = avgThroughput;
    }

    public long getMaxThroughput() {
        return maxThroughput;
    }

    public void setMaxThroughput(long maxThroughput) {
        this.maxThroughput = maxThroughput;
    }

    public long getMinThroughput() {
        return minThroughput;
    }

    public void setMinThroughput(long minThroughput) {
        this.minThroughput = minThroughput;
    }

    public Map<String, Object> getThroughputTrend() {
        return throughputTrend;
    }

    public void setThroughputTrend(Map<String, Object> throughputTrend) {
        this.throughputTrend = throughputTrend;
    }

    public List<LogTypeThroughput> getLogTypeThroughput() {
        return logTypeThroughput;
    }

    public void setLogTypeThroughput(List<LogTypeThroughput> logTypeThroughput) {
        this.logTypeThroughput = logTypeThroughput;
    }

    /**
     * 日志类型吞吐量
     */
    public static class LogTypeThroughput {
        /** 日志类型 */
        @ApiModelProperty(value = "日志类型", example = "SECURIO_ZEEK_CONN")
        private String logType;
        
        /** 吞吐量 */
        @ApiModelProperty(value = "吞吐量", example = "100")
        private long throughput;

        public String getLogType() {
            return logType;
        }

        public void setLogType(String logType) {
            this.logType = logType;
        }

        public long getThroughput() {
            return throughput;
        }

        public void setThroughput(long throughput) {
            this.throughput = throughput;
        }
    }
}
