package com.wiwj.securio.logmgr.service;

import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.logmgr.domain.dto.FieldStatDTO;

import java.util.List;
import java.util.Map;

/**
 * VictoriaLogs 服务接口
 *
 * <AUTHOR>
 */
public interface VictoriaLogsService {

    /**
     * 查询日志
     *
     * @param query 查询语句
     * @param limit 限制返回的日志条数
     * @param timeout 查询超时时间
     * @param instance VictoriaLogs 实例名称，为 null 时使用默认实例
     * @return 查询结果
     */
    String queryLogs(String query, Integer limit, String timeout, String instance);

    /**
     * 查询日志（使用默认实例）
     *
     * @param query 查询语句
     * @param limit 限制返回的日志条数
     * @param timeout 查询超时时间
     * @return 查询结果
     */
    default String queryLogs(String query,Integer limit, String timeout) {
        return queryLogs(query, limit, timeout, null);
    }

    /**
     * 查询日志统计
     *
     * @param query 查询语句
     * @param time 查询时间点
     * @param extraFilters 额外过滤条件
     * @param extraStreamFilters 额外流过滤条件
     * @param instance VictoriaLogs 实例名称，为 null 时使用默认实例
     * @return 日志统计结果
     */
    String queryStats(String query, String time, Map<String, Object> extraFilters,
                     Map<String, Object> extraStreamFilters, String instance);

    /**
     * 查询日志统计（使用默认实例）
     *
     * @param query 查询语句
     * @param time 查询时间点
     * @param extraFilters 额外过滤条件
     * @param extraStreamFilters 额外流过滤条件
     * @return 日志统计结果
     */
    default String queryStats(String query, String time, Map<String, Object> extraFilters,
                     Map<String, Object> extraStreamFilters) {
        return queryStats(query, time, extraFilters, extraStreamFilters, null);
    }

    /**
     * 查询日志范围统计
     *
     * @param query 查询语句
     * @param start 开始时间
     * @param end 结束时间
     * @param step 时间步长
     * @param extraFilters 额外过滤条件
     * @param extraStreamFilters 额外流过滤条件
     * @param instance VictoriaLogs 实例名称，为 null 时使用默认实例
     * @return 日志范围统计结果
     */
    String queryStatsRange(String query, String start, String end, String step,
                          Map<String, Object> extraFilters, Map<String, Object> extraStreamFilters, String instance);

    /**
     * 查询日志范围统计（使用默认实例）
     *
     * @param query 查询语句
     * @param start 开始时间
     * @param end 结束时间
     * @param step 时间步长
     * @param extraFilters 额外过滤条件
     * @param extraStreamFilters 额外流过滤条件
     * @return 日志范围统计结果
     */
    default String queryStatsRange(String query, String start, String end, String step,
                          Map<String, Object> extraFilters, Map<String, Object> extraStreamFilters) {
        return queryStatsRange(query, start, end, step, extraFilters, extraStreamFilters, null);
    }

    /**
     * 获取时间序列统计数据并进行预处理
     *
     * @param query 查询语句
     * @param start 开始时间
     * @param end 结束时间
     * @param step 时间步长
     * @param extraFilters 额外过滤条件
     * @param extraStreamFilters 额外流过滤条件
     * @param instance VictoriaLogs 实例名称，为 null 时使用默认实例
     * @return 处理后的时间序列统计结果
     */
    JSONObject getTimeSeriesStats(String query, String start, String end, String step,
                                 Map<String, Object> extraFilters, Map<String, Object> extraStreamFilters, String instance);

    /**
     * 获取时间序列统计数据并进行预处理（使用默认实例）
     *
     * @param query 查询语句
     * @param start 开始时间
     * @param end 结束时间
     * @param step 时间步长
     * @param extraFilters 额外过滤条件
     * @param extraStreamFilters 额外流过滤条件
     * @return 处理后的时间序列统计结果
     */
    default JSONObject getTimeSeriesStats(String query, String start, String end, String step,
                                 Map<String, Object> extraFilters, Map<String, Object> extraStreamFilters) {
        return getTimeSeriesStats(query, start, end, step, extraFilters, extraStreamFilters, null);
    }

    /**
     * 获取日志字段统计信息
     *
     * @param stream 日志流名称
     * @param timeRange 时间范围
     * @param limit 每个字段返回的最大值数量
     * @param instance VictoriaLogs 实例名称，为 null 时使用默认实例
     * @return 字段统计信息
     */
    String getLogFieldStats(String stream, String timeRange, Integer limit, String instance);

    /**
     * 获取日志字段统计信息（使用默认实例）
     *
     * @param stream 日志流名称
     * @param timeRange 时间范围
     * @param limit 每个字段返回的最大值数量
     * @return 字段统计信息
     */
    default String getLogFieldStats(String stream, String timeRange, Integer limit) {
        return getLogFieldStats(stream, timeRange, limit, null);
    }

    /**
     * 获取字段统计信息（直接调用 VictoriaLogs facets API）
     *
     * @param query 查询语句
     * @param limit 每个字段返回的最大值数量
     * @param instance VictoriaLogs 实例名称，为 null 时使用默认实例
     * @return facets 结果
     */
    String facets(String query, Integer limit, String instance);

    /**
     * 获取字段统计信息（直接调用 VictoriaLogs facets API，使用默认实例）
     *
     * @param query 查询语句
     * @param limit 每个字段返回的最大值数量
     * @return facets 结果
     */
    default String facets(String query, Integer limit) {
        return facets(query, limit, null);
    }

    /**
     * 按字段统计并返回 DTO 列表
     *
     * @param stream 日志流名称
     * @param field 统计字段
     * @param timeRange 时间范围
     * @param limit 返回的最大结果数量
     * @param instance VictoriaLogs 实例名称，为 null 时使用默认实例
     * @return 字段统计 DTO 列表
     */
    List<FieldStatDTO> statByField(String stream, String field, String timeRange, Integer limit, String instance);

    /**
     * 按字段统计并返回 DTO 列表（使用默认实例）
     *
     * @param stream 日志流名称
     * @param field 统计字段
     * @param timeRange 时间范围
     * @param limit 返回的最大结果数量
     * @return 字段统计 DTO 列表
     */
    default List<FieldStatDTO> statByField(String stream, String field, String timeRange, Integer limit) {
        return statByField(stream, field, timeRange, limit, null);
    }

    /**
     * 获取指定 stream 指定字段去重后的统计数量
     *
     * @param stream 日志流名称
     * @param field 需要去重的字段
     * @param timeRange 时间范围
     * @param instance VictoriaLogs 实例名称，为 null 时使用默认实例
     * @return 去重后的统计数量
     */
    int countUniqueValues(String stream, String field, String timeRange, String instance);

    /**
     * 获取指定 stream 指定字段去重后的统计数量（使用默认实例）
     *
     * @param stream 日志流名称
     * @param field 需要去重的字段
     * @param timeRange 时间范围
     * @return 去重后的统计数量
     */
    default int countUniqueValues(String stream, String field, String timeRange) {
        return countUniqueValues(stream, field, timeRange, null);
    }
}
