package com.wiwj.securio.logmgr.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;

import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * MySQL 日志审计控制器
 *
 * <AUTHOR>
 */
@Api(tags = "MySQL 日志审计管理", description = "MySQL 日志审计相关接口")
@RestController
@RequestMapping("/opslog/mysql")
public class MysqlLogAuditController extends BaseController {

    @Autowired
    private VictoriaLogsService victoriaLogsService;



    /**
     * 获取MySQL登录失败统计
     */
    @ApiOperation(value = "获取MySQL登录失败统计", notes = "获取MySQL登录失败统计数据")
    @GetMapping("/failed-login-stats")
    public AjaxResult getFailedLoginStats(
            @ApiParam(value = "统计类型", required = true) @RequestParam String type,
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "10d") String timeRange,
            @ApiParam(value = "限制返回的记录数") @RequestParam(required = false, defaultValue = "10") Integer limit,
            @ApiParam(value = "额外查询条件") @RequestParam(required = false) String additionalQuery) {

        try {
            // 根据统计类型选择分组字段
            String fieldName;
            switch (type) {
                case "user":
                    fieldName = "message.user";
                    break;
                case "host":
                    fieldName = "message.host";
                    break;
                case "agent":
                    fieldName = "agent_hostname";
                    break;
                default:
                    return AjaxResult.error("不支持的统计类型: " + type);
            }

            // 构建查询语句
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("_stream: {stream=\"AUDITLOG_MYSQL_USER\"} AND (*)");
            
            // 添加额外的查询条件（如数据库IP和端口过滤）
            if (additionalQuery != null && !additionalQuery.trim().isEmpty()) {
                queryBuilder.append(" and ").append(additionalQuery);
            }
            
            queryBuilder.append(" and message.cmd:\"Failed Login\" | stats by (").append(fieldName).append(") count(*) cnt |sort by (cnt desc)");
            
            String query = queryBuilder.toString();

            // 调用VictoriaLogs服务查询数据
            // 使用 queryLogs 方法直接查询
            String result = victoriaLogsService.queryLogs(query, 100, null);

            // 解析结果
            List<Map<String, Object>> parsedResult = new ArrayList<>();
            if (result != null && !result.isEmpty()) {
                try {
                    // 尝试解析整个 JSON 对象
                    JSONObject jsonResult = JSON.parseObject(result);

                    // 如果是单个对象，尝试直接解析
                    if (jsonResult.containsKey(fieldName) && jsonResult.containsKey("cnt")) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("fieldValue", jsonResult.getString(fieldName));
                        item.put("count", jsonResult.getIntValue("cnt"));
                        parsedResult.add(item);
                    } else {
                        // 如果是多行结果，按行解析
                        String[] lines = result.split("\n");
                        for (String line : lines) {
                            if (line.trim().isEmpty()) continue;

                            JSONObject jsonObj = JSON.parseObject(line);
                            Map<String, Object> item = new HashMap<>();

                            String fieldValue = jsonObj.getString(fieldName);
                            String count = jsonObj.getString("cnt");

                            if (fieldValue != null && count != null) {
                                item.put("fieldValue", fieldValue);
                                item.put("count", Integer.parseInt(count));
                                parsedResult.add(item);
                            }
                        }
                    }
                } catch (Exception e) {
                    // 如果 JSON 解析失败，尝试直接按行解析
                    String[] lines = result.split("\n");
                    for (String line : lines) {
                        if (line.trim().isEmpty()) continue;

                        try {
                            JSONObject jsonObj = JSON.parseObject(line);
                            Map<String, Object> item = new HashMap<>();

                            String fieldValue = jsonObj.getString(fieldName);
                            String count = jsonObj.getString("cnt");

                            if (fieldValue != null && count != null) {
                                item.put("fieldValue", fieldValue);
                                item.put("count", Integer.parseInt(count));
                                parsedResult.add(item);
                            }
                        } catch (Exception ex) {
                            // 忽略无法解析的行
                            continue;
                        }
                    }
                }
            }

            // 限制返回的记录数
            if (limit != null && parsedResult.size() > limit) {
                parsedResult = parsedResult.subList(0, limit);
            }

            return AjaxResult.success(parsedResult);
        } catch (Exception e) {
            return AjaxResult.error("获取登录失败统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取MySQL日志时间序列统计数据
     */
    @ApiOperation(value = "获取MySQL日志时间序列统计数据", notes = "获取MySQL日志的时间序列统计数据，用于生成曲线图")
    @GetMapping("/time-series-stats")
    public AjaxResult getTimeSeriesStats(
            @ApiParam(value = "统计类型", required = false) @RequestParam(required = false, defaultValue = "all") String type,
            @ApiParam(value = "统计维度", required = true) @RequestParam String dimension,
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "14d") String timeRange,
            @ApiParam(value = "时间步长") @RequestParam(required = false, defaultValue = "1h") String step,
            @ApiParam(value = "额外查询条件") @RequestParam(required = false) String additionalQuery) {

        try {
            // 根据统计维度选择分组字段
            String fieldName;
            switch (dimension) {
                case "agent":
                    fieldName = "agent_hostname";
                    break;
                case "client":
                    fieldName = "message.host";
                    break;
                case "user":
                    fieldName = "message.user";
                    break;
                case "cmd":
                    fieldName = "message.cmd";
                    break;
                default:
                    return AjaxResult.error("不支持的统计维度: " + dimension);
            }

            // 构建基础查询条件
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("_time:").append(timeRange).append(" and stream:\"AUDITLOG_MYSQL_USER\"");
            
            // 添加额外的查询条件（如数据库IP和端口过滤）
            if (additionalQuery != null && !additionalQuery.trim().isEmpty()) {
                queryBuilder.append(" and ").append(additionalQuery);
            }
            
            // 根据统计类型添加过滤条件
            if ("failed_login".equals(type)) {
                queryBuilder.append(" and message.cmd:\"Failed Login\"");
                logger.info("构建异常登录统计查询");
            } else {
                logger.info("构建全部统计查询");
            }
            
            // 添加统计和排序
            queryBuilder.append(" | stats by (").append(fieldName).append(") count(*) cnt | sort by (cnt desc)");
            
            String query = queryBuilder.toString();
            logger.info("查询语句: " + query);

            // 调用VictoriaLogsService的getTimeSeriesStats方法获取并处理时间序列数据
            JSONObject jsonResult = victoriaLogsService.getTimeSeriesStats(query, null, null, step, null, null);

            return AjaxResult.success(jsonResult);
        } catch (Exception e) {
            return AjaxResult.error("获取时间序列统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取MySQL日志数据流向统计数据（桑基图数据）
     */
    @ApiOperation(value = "获取MySQL日志数据流向统计数据", notes = "获取客户端 IP 到数据库服务器 IP 再到数据库名称的数据流向统计，用于生成桑基图")
    @GetMapping("/data-flow-stats")
    public AjaxResult getDataFlowStats(
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "1d") String timeRange,
            @ApiParam(value = "限制返回的数据库数量") @RequestParam(required = false, defaultValue = "10") Integer dbLimit,
            @ApiParam(value = "限制返回的客户端数量") @RequestParam(required = false, defaultValue = "10") Integer clientLimit,
            @ApiParam(value = "额外查询条件") @RequestParam(required = false) String additionalQuery) {

        try {
            // 构建基础查询条件
            StringBuilder baseQueryBuilder = new StringBuilder();
            baseQueryBuilder.append("_time:").append(timeRange).append(" and stream:\"AUDITLOG_MYSQL_USER\"");
            
            // 添加额外的查询条件（如数据库IP和端口过滤）
            if (additionalQuery != null && !additionalQuery.trim().isEmpty()) {
                baseQueryBuilder.append(" and ").append(additionalQuery);
            }
            
            String baseQuery = baseQueryBuilder.toString();

            // 第一步：获取 top N 访问的数据库服务器
            String dbQuery = baseQuery + " | stats by (agent_hostname) count() cnt | sort by (cnt desc) | limit " + dbLimit;
            logger.info("查询热门数据库服务器: " + dbQuery);
            String dbResult = victoriaLogsService.queryLogs(dbQuery, dbLimit, null);

            // 解析数据库服务器结果
            List<Map<String, Object>> databases = new ArrayList<>();
            if (dbResult != null && !dbResult.isEmpty()) {
                String[] lines = dbResult.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;

                    JSONObject jsonObj = JSON.parseObject(line);
                    String dbIp = jsonObj.getString("agent_hostname");
                    int count = jsonObj.getIntValue("cnt");

                    if (dbIp != null && !dbIp.isEmpty()) {
                        Map<String, Object> db = new HashMap<>();
                        db.put("ip", dbIp);
                        db.put("count", count);
                        databases.add(db);
                    }
                }
            }

            // 第二步：对每个数据库服务器，获取客户端到该数据库的请求
            List<Map<String, Object>> clientToDbLinks = new ArrayList<>();
            for (Map<String, Object> db : databases) {
                String dbIp = (String) db.get("ip");

                String clientQuery = baseQuery + " and agent_hostname:\"" + dbIp + "\" | stats by (message.host) count() cnt | sort by (cnt desc) | limit " + clientLimit;
                logger.info("查询数据库服务器 " + dbIp + " 的客户端: " + clientQuery);
                String clientResult = victoriaLogsService.queryLogs(clientQuery, clientLimit, null);

                // 解析客户端结果
                if (clientResult != null && !clientResult.isEmpty()) {
                    String[] lines = clientResult.split("\n");
                    for (String line : lines) {
                        if (line.trim().isEmpty()) continue;

                        JSONObject jsonObj = JSON.parseObject(line);
                        String clientIp = jsonObj.getString("message.host");
                        int count = jsonObj.getIntValue("cnt");

                        if (clientIp != null && !clientIp.isEmpty()) {
                            Map<String, Object> link = new HashMap<>();
                            link.put("source", clientIp);
                            link.put("target", dbIp);
                            link.put("value", count);
                            clientToDbLinks.add(link);
                        }
                    }
                }
            }

            // 第三步：对每个数据库服务器，获取该服务器上的数据库名称访问统计
            List<Map<String, Object>> dbToDbNameLinks = new ArrayList<>();
            List<Map<String, Object>> dbNames = new ArrayList<>();

            for (Map<String, Object> db : databases) {
                String dbIp = (String) db.get("ip");

                // 查询该数据库服务器上的数据库名称访问统计
                String dbNameQuery = baseQuery + " and agent_hostname:\"" + dbIp + "\" | stats by (db_name) count() cnt | sort by (cnt desc) | limit 10";
                logger.info("查询数据库服务器 " + dbIp + " 的数据库名称: " + dbNameQuery);
                String dbNameResult = victoriaLogsService.queryLogs(dbNameQuery, 10, null);

                // 解析数据库名称结果
                if (dbNameResult != null && !dbNameResult.isEmpty()) {
                    String[] lines = dbNameResult.split("\n");
                    for (String line : lines) {
                        if (line.trim().isEmpty()) continue;

                        try {
                            JSONObject jsonObj = JSON.parseObject(line);
                            String dbName = jsonObj.getString("db_name");
                            int count = jsonObj.getIntValue("cnt");

                            // 跳过空数据库名称
                            if (dbName == null || dbName.isEmpty() || dbName.equals("null")) {
                                continue;
                            }

                            // 添加数据库名称节点
                            String dbNameKey = dbIp + ":" + dbName; // 使用组合键避免不同服务器上的同名数据库冲突

                            // 检查是否已经添加过该数据库名称
                            boolean exists = false;
                            for (Map<String, Object> existingDbName : dbNames) {
                                if (dbNameKey.equals(existingDbName.get("key"))) {
                                    exists = true;
                                    break;
                                }
                            }

                            if (!exists) {
                                Map<String, Object> dbNameNode = new HashMap<>();
                                dbNameNode.put("key", dbNameKey);
                                dbNameNode.put("name", dbName);
                                dbNameNode.put("count", count);
                                dbNameNode.put("serverIp", dbIp);
                                dbNames.add(dbNameNode);
                            }

                            // 添加从数据库服务器到数据库名称的连接
                            Map<String, Object> link = new HashMap<>();
                            link.put("source", dbIp);
                            link.put("target", dbNameKey);
                            link.put("value", count);
                            dbToDbNameLinks.add(link);
                        } catch (Exception e) {
                            logger.warn("解析数据库名称结果失败: " + e.getMessage());
                            continue;
                        }
                    }
                }
            }

            // 构建桑基图数据
            Map<String, Object> result = new HashMap<>();
            result.put("databases", databases);
            result.put("dbNames", dbNames);
            result.put("clientToDbLinks", clientToDbLinks);
            result.put("dbToDbNameLinks", dbToDbNameLinks);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取数据流向统计数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取数据流向统计数据失败: " + e.getMessage());
        }
    }
}