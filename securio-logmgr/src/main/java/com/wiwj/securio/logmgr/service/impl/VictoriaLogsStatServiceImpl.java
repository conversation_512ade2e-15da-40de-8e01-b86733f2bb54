package com.wiwj.securio.logmgr.service.impl;

import com.wiwj.securio.logmgr.service.VictoriaLogsStatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * VictoriaLogs 集群节点指标服务实现类
 *
 * <AUTHOR>
 */
@Service
public class VictoriaLogsStatServiceImpl implements VictoriaLogsStatService {

    private static final Logger logger = LoggerFactory.getLogger(VictoriaLogsStatServiceImpl.class);

    private final RestTemplate restTemplate = new RestTemplate();

    // 预定义的VictoriaLogs节点列表
    private static final List<String> VICTORIA_LOGS_NODES = Arrays.asList(
            "http://10.20.200.202:9428",
            "http://10.20.200.202:9429",
            "http://10.20.200.234:9428",
            "http://10.20.200.234:9429"
    );

    @Override
    public List<String> getVictoriaLogsNodes() {
        return VICTORIA_LOGS_NODES;
    }

    @Override
    public Map<String, Object> getNodeMetrics(String nodeUrl) {
        try {
            // 构建请求URL
            String metricsUrl = nodeUrl + "/metrics";
            logger.info("请求节点指标数据: {}", metricsUrl);

            // 发送请求获取指标数据
            ResponseEntity<String> response = restTemplate.getForEntity(metricsUrl, String.class);
            String metricsData = response.getBody();

            // 解析指标数据
            Map<String, Object> metrics = parseMetricsData(metricsData);
            
            // 添加节点URL信息
            metrics.put("nodeUrl", nodeUrl);
            
            return metrics;
        } catch (Exception e) {
            logger.error("获取节点指标数据失败: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 解析Prometheus格式的指标数据
     *
     * @param metricsData 原始指标数据
     * @return 解析后的指标数据
     */
    private Map<String, Object> parseMetricsData(String metricsData) {
        Map<String, Object> result = new HashMap<>();
        
        if (metricsData == null || metricsData.isEmpty()) {
            return result;
        }

        // 按行分割指标数据
        String[] lines = metricsData.split("\\n");
        
        // 正则表达式匹配指标名称和值
        Pattern pattern = Pattern.compile("^([a-zA-Z0-9_]+)(?:\\{([^}]*)\\})?\\s+(.+)$");
        
        for (String line : lines) {
            // 跳过注释行
            if (line.startsWith("#") || line.trim().isEmpty()) {
                continue;
            }
            
            Matcher matcher = pattern.matcher(line);
            if (matcher.find()) {
                String metricName = matcher.group(1);
                String labels = matcher.group(2);
                String value = matcher.group(3);
                
                // 处理带标签的指标
                if (labels != null && !labels.isEmpty()) {
                    Map<String, String> labelMap = parseLabelString(labels);
                    String fullMetricName = metricName + "{" + labels + "}";
                    result.put(fullMetricName, value);
                } else {
                    // 处理不带标签的指标
                    result.put(metricName, value);
                }
            }
        }
        
        return result;
    }

    /**
     * 解析标签字符串为Map
     *
     * @param labels 标签字符串，格式如 "name=\"value\",name2=\"value2\""
     * @return 标签Map
     */
    private Map<String, String> parseLabelString(String labels) {
        Map<String, String> result = new HashMap<>();
        
        if (labels == null || labels.isEmpty()) {
            return result;
        }
        
        // 分割标签
        String[] labelPairs = labels.split(",");
        
        for (String pair : labelPairs) {
            String[] nameValue = pair.split("=", 2);
            if (nameValue.length == 2) {
                String name = nameValue[0].trim();
                String value = nameValue[1].trim();
                
                // 去除引号
                if (value.startsWith("\"") && value.endsWith("\"")) {
                    value = value.substring(1, value.length() - 1);
                }
                
                result.put(name, value);
            }
        }
        
        return result;
    }
}
