package com.wiwj.securio.logmgr.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 日志处理性能数据传输对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "LogPerformanceDTO", description = "日志处理性能数据")
public class LogPerformanceDTO {
    
    /** 平均处理时间（纳秒） */
    @ApiModelProperty(value = "平均处理时间（纳秒）", example = "1000000")
    private double avgProcessingTime;
    
    /** 每分钟处理量 */
    @ApiModelProperty(value = "每分钟处理量", example = "1000")
    private long processingRateMinute;
    
    /** 每小时处理量 */
    @ApiModelProperty(value = "每小时处理量", example = "60000")
    private long processingRateHour;
    
    /** 每天处理量 */
    @ApiModelProperty(value = "每天处理量", example = "1440000")
    private long processingRateDay;
    
    /** 处理时间趋势数据 */
    @ApiModelProperty(value = "处理时间趋势数据")
    private Map<String, List<Object>> processingTimeTrend;
    
    /** 处理量趋势数据 */
    @ApiModelProperty(value = "处理量趋势数据")
    private Map<String, List<Object>> processingRateTrend;

    public double getAvgProcessingTime() {
        return avgProcessingTime;
    }

    public void setAvgProcessingTime(double avgProcessingTime) {
        this.avgProcessingTime = avgProcessingTime;
    }

    public long getProcessingRateMinute() {
        return processingRateMinute;
    }

    public void setProcessingRateMinute(long processingRateMinute) {
        this.processingRateMinute = processingRateMinute;
    }

    public long getProcessingRateHour() {
        return processingRateHour;
    }

    public void setProcessingRateHour(long processingRateHour) {
        this.processingRateHour = processingRateHour;
    }

    public long getProcessingRateDay() {
        return processingRateDay;
    }

    public void setProcessingRateDay(long processingRateDay) {
        this.processingRateDay = processingRateDay;
    }

    public Map<String, List<Object>> getProcessingTimeTrend() {
        return processingTimeTrend;
    }

    public void setProcessingTimeTrend(Map<String, List<Object>> processingTimeTrend) {
        this.processingTimeTrend = processingTimeTrend;
    }

    public Map<String, List<Object>> getProcessingRateTrend() {
        return processingRateTrend;
    }

    public void setProcessingRateTrend(Map<String, List<Object>> processingRateTrend) {
        this.processingRateTrend = processingRateTrend;
    }
}
