package com.wiwj.securio.logmgr.service.impl;

import com.wiwj.securio.logmgr.domain.dto.*;
import com.wiwj.securio.logmgr.service.LogStatsService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.Calendar;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 日志统计服务实现类
 *
 * <AUTHOR>
 */
@Service
public class LogStatsServiceImpl implements LogStatsService {

    private static final Logger logger = LoggerFactory.getLogger(LogStatsServiceImpl.class);

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${victoria.metrics.url:http://0.0.0.0:8428}")
    private String victoriaMetricsUrl;


    private static final Pattern VALUE_PATTERN = Pattern.compile("\\s+([\\d.]+)$");

    // 不再需要日志类型映射，直接使用 VictoriaMetrics 中的 log_type

    @Override
    public List<LogStatsDTO> getLogStats() {
        Map<String, LogStatsDTO> statsMap = new HashMap<>();

        // 不再预先初始化日志类型，而是在处理数据时动态创建

        try {
            // 获取总日志数
            String totalUrl = victoriaMetricsUrl + "/api/v1/query?query=sum(increase(securio_log_stats_total[100y])) by (log_type)";
            logger.info("Total URL: " + totalUrl);
            ResponseEntity<String> totalResponse = restTemplate.getForEntity(totalUrl, String.class);
            logger.info("Total Response: " + totalResponse.getBody());
            processMetricsData(totalResponse.getBody(), statsMap, MetricType.TOTAL);

            // 获取今日日志数（从今天 0 点到当前时间）
            // 使用 Calendar 获取今天 0 点的时间
            Calendar today = Calendar.getInstance();
            today.set(Calendar.HOUR_OF_DAY, 0);
            today.set(Calendar.MINUTE, 0);
            today.set(Calendar.SECOND, 0);
            today.set(Calendar.MILLISECOND, 0);
            long todayStartTimeMillis = today.getTimeInMillis();
            long currentTimeMillis = System.currentTimeMillis()+1000*60*60;

            // 计算从今天 0 点到现在的时间范围（秒）
            long todayDurationSeconds = (currentTimeMillis - todayStartTimeMillis) / 1000;
            String todayUrl = victoriaMetricsUrl + "/api/v1/query?query=sum(increase(securio_log_stats_total[" + todayDurationSeconds + "s])) by (log_type)";
            logger.info("Today URL: " + todayUrl);
            ResponseEntity<String> todayResponse = restTemplate.getForEntity(todayUrl, String.class);
            logger.info("Today Response: " + todayResponse.getBody());
            processMetricsData(todayResponse.getBody(), statsMap, MetricType.TODAY);

            // 获取昨日日志数（从昨天 0 点到今天 0 点）
            Calendar yesterday = Calendar.getInstance();
            yesterday.set(Calendar.HOUR_OF_DAY, 0);
            yesterday.set(Calendar.MINUTE, 0);
            yesterday.set(Calendar.SECOND, 0);
            yesterday.set(Calendar.MILLISECOND, 0);
            yesterday.add(Calendar.DAY_OF_MONTH, -1); // 昨天
            long yesterdayStartTimeSec = yesterday.getTimeInMillis() / 1000; // 转换为秒
            long todayStartTimeSec = todayStartTimeMillis / 1000; // 转换为秒

            // 详细记录时间戳信息，便于调试
            logger.info("昨天0点时间戳(毫秒): " + yesterday.getTimeInMillis());
            logger.info("昨天0点时间戳(秒): " + yesterdayStartTimeSec);
            logger.info("今天0点时间戳(毫秒): " + todayStartTimeMillis);
            logger.info("今天0点时间戳(秒): " + todayStartTimeSec);
            logger.info("昨天日期: " + new Date(yesterday.getTimeInMillis()));
            logger.info("今天日期: " + new Date(todayStartTimeMillis));

            // 尝试两种查询方式

            // 1. 使用绝对时间戳查询 (可能存在语法问题)
            String yesterdayUrl1 = victoriaMetricsUrl + "/api/v1/query?query=sum(increase(securio_log_stats_total[" + yesterdayStartTimeSec + ":" + todayStartTimeSec + "])) by (log_type)";
            logger.info("Yesterday URL 方式1 (绝对时间戳): " + yesterdayUrl1);

            // 2. 使用相对时间范围查询 (24小时)
            String yesterdayUrl2 = victoriaMetricsUrl + "/api/v1/query?query=sum(increase(securio_log_stats_total[24h])) by (log_type)";
            logger.info("Yesterday URL 方式2 (相对时间24h): " + yesterdayUrl2);

            // 3. 使用相对时间范围查询 (昨天一整天)
            long yesterdayDurationSeconds = todayStartTimeSec - yesterdayStartTimeSec;
            String yesterdayUrl3 = victoriaMetricsUrl + "/api/v1/query?query=sum(increase(securio_log_stats_total[" + yesterdayDurationSeconds + "s] offset " + yesterdayDurationSeconds + "s)) by (log_type)";
            logger.info("Yesterday URL 方式3 (相对时间+偏移): " + yesterdayUrl3);

            // 使用方式3进行查询
            String yesterdayUrl = yesterdayUrl3;
            logger.info("使用查询方式3: " + yesterdayUrl);
            ResponseEntity<String> yesterdayResponse = restTemplate.getForEntity(yesterdayUrl, String.class);
            logger.info("Yesterday Response: " + yesterdayResponse.getBody());
            processMetricsData(yesterdayResponse.getBody(), statsMap, MetricType.YESTERDAY);

            // 输出调试信息
            logger.info("Total stats: " + statsMap.values().stream()
                    .map(dto -> dto.getLogType() + ": " + dto.getTotalCount())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("None"));

            logger.info("Today stats: " + statsMap.values().stream()
                    .map(dto -> dto.getLogType() + ": " + dto.getTodayCount())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("None"));

            logger.info("Yesterday stats: " + statsMap.values().stream()
                    .map(dto -> dto.getLogType() + ": " + dto.getYesterdayCount())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("None"));

        } catch (Exception e) {
            logger.error("Error fetching log stats from VictoriaMetrics: " + e.getMessage(), e);
        }

        // 转换为列表并返回
        return new ArrayList<>(statsMap.values());
    }

    private enum MetricType {
        TOTAL, TODAY, YESTERDAY
    }

    private void processMetricsData(String data, Map<String, LogStatsDTO> statsMap, MetricType metricType) {
        if (data == null || data.isEmpty()) {
            logger.warn("Empty data received for " + metricType);
            return;
        }

        try {
            // 记录原始数据的前 100 个字符，便于调试
            String dataSample = data.length() > 100 ? data.substring(0, 100) + "..." : data;
            logger.debug("Processing data for " + metricType + ": " + dataSample);

            // 检查数据是否是 JSON 格式
            if (data.trim().startsWith("{")) {
                // 解析 JSON 格式的响应
                logger.debug("Processing JSON data for " + metricType);
                processJsonMetricsData(data, statsMap, metricType);
            } else {
                // 处理文本格式的响应（旧格式）
                logger.debug("Processing text data for " + metricType);
                processTextMetricsData(data, statsMap, metricType);
            }
        } catch (Exception e) {
            logger.error("Error processing metrics data for " + metricType + ": " + e.getMessage(), e);
        }
    }

    private void processJsonMetricsData(String jsonData, Map<String, LogStatsDTO> statsMap, MetricType metricType) {
        try {
            // 记录原始 JSON 数据便于调试
            logger.info("Processing JSON data for " + metricType + ": " + jsonData);

            // 检查是否有数据
            if (jsonData == null || jsonData.isEmpty()) {
                logger.warn("No data found for " + metricType);
                return;
            }

            // 使用 fastjson2 解析 JSON 数据
            JSONObject jsonObject = JSON.parseObject(jsonData);
            if (jsonObject == null) {
                logger.warn("Failed to parse JSON data for " + metricType);
                return;
            }

            // 检查响应状态
            String status = jsonObject.getString("status");
            logger.info("Response status for " + metricType + ": " + status);
            if (!"success".equals(status)) {
                logger.warn("Response status is not success for " + metricType + ": " + status);
                // 检查是否有错误信息
                if (jsonObject.containsKey("error")) {
                    logger.error("Error in response for " + metricType + ": " + jsonObject.getString("error"));
                }
                return;
            }

            // 获取结果数组
            JSONArray resultArray = null;

            // 尝试从 data 对象中获取 result 数组
            JSONObject dataObj = jsonObject.getJSONObject("data");
            if (dataObj != null) {
                logger.info("Data object found for " + metricType + ": " + dataObj);
                resultArray = dataObj.getJSONArray("result");
            } else {
                logger.warn("No data object found in response for " + metricType);
            }

            // 如果上面的方法失败，尝试直接从根对象获取 result 数组
            if (resultArray == null || resultArray.isEmpty()) {
                logger.info("Trying to get result array directly from root object for " + metricType);
                resultArray = jsonObject.getJSONArray("result");
            }

            // 如果仍然失败，记录警告并返回
            if (resultArray == null || resultArray.isEmpty()) {
                logger.warn("No result array found in JSON data for " + metricType);
                return;
            }

            // 记录结果数组的大小和内容
            logger.info("Found " + resultArray.size() + " results for " + metricType);
            logger.info("Result array for " + metricType + ": " + resultArray);

            // 遍历结果数组
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject result = resultArray.getJSONObject(i);
                if (result == null) {
                    continue;
                }

                // 获取 metric 对象
                JSONObject metric = result.getJSONObject("metric");
                if (metric == null) {
                    continue;
                }

                // 从 metric 对象中提取 log_type
                String logType = metric.getString("log_type");
                if (logType == null || logType.isEmpty()) {
                    logger.debug("No log_type found in metric: " + metric);
                    continue;
                }

                logger.info("Found log_type: " + logType);

                // 记录当前处理的结果对象
                logger.debug("Processing result: " + result);

                // 获取值
                // 首先尝试获取单个值
                JSONArray valueArray = result.getJSONArray("value");
                if (valueArray != null && !valueArray.isEmpty()) {
                    logger.debug("Found value array: " + valueArray);
                    // value 格式通常是 [timestamp, value]
                    if (valueArray.size() >= 2) {
                        Object valueObj = valueArray.get(1);
                        logger.debug("Value object: " + valueObj + ", class: " + (valueObj != null ? valueObj.getClass().getName() : "null"));
                        if (valueObj != null && !(valueObj instanceof String && "null".equals(valueObj))) {
                            try {
                                double value = Double.parseDouble(valueObj.toString());
                                logger.debug("Parsed value: " + value);
                                updateStatsMap(statsMap, logType, Math.round(value), metricType);
                            } catch (NumberFormatException e) {
                                logger.warn("Invalid number format for value: " + valueObj);
                            }
                        }
                    }
                    continue;
                }

                // 然后尝试获取值数组
                JSONArray valuesArray = result.getJSONArray("values");
                if (valuesArray != null && !valuesArray.isEmpty()) {
                    logger.debug("Found values array: " + valuesArray);
                    // 获取最后一个非 null 值
                    for (int j = valuesArray.size() - 1; j >= 0; j--) {
                        JSONArray entry = valuesArray.getJSONArray(j);
                        if (entry != null && entry.size() >= 2) {
                            Object valueObj = entry.get(1);
                            logger.debug("Value object at index " + j + ": " + valueObj + ", class: " + (valueObj != null ? valueObj.getClass().getName() : "null"));
                            if (valueObj != null && !(valueObj instanceof String && "null".equals(valueObj))) {
                                try {
                                    double value = Double.parseDouble(valueObj.toString());
                                    logger.debug("Parsed value: " + value);
                                    updateStatsMap(statsMap, logType, Math.round(value), metricType);
                                    break;
                                } catch (NumberFormatException e) {
                                    logger.warn("Invalid number format for value: " + valueObj);
                                }
                            }
                        }
                    }
                }

                // 如果上面的方法都失败了，尝试直接从结果对象中获取值
                if (valueArray == null && valuesArray == null) {
                    logger.debug("No value or values array found, trying to get value directly from result");
                    // 尝试获取 v 字段
                    Object vObj = result.get("v");
                    if (vObj != null) {
                        try {
                            double value = Double.parseDouble(vObj.toString());
                            logger.debug("Parsed value from v field: " + value);
                            updateStatsMap(statsMap, logType, Math.round(value), metricType);
                        } catch (NumberFormatException e) {
                            logger.warn("Invalid number format for v field: " + vObj);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error parsing JSON metrics data: " + e.getMessage(), e);
        }
    }

    private void updateStatsMap(Map<String, LogStatsDTO> statsMap, String logType, long value, MetricType metricType) {
        // 直接使用 logType 作为 key
        LogStatsDTO dto = statsMap.get(logType);

        logger.info("更新统计数据 - 类型: " + metricType + ", 日志类型: " + logType + ", 值: " + value);

        // 如果该日志类型还不存在，则创建一个新的 DTO
        if (dto == null) {
            logger.info("创建新的统计对象，日志类型: " + logType);
            dto = new LogStatsDTO();
            dto.setLogType(logType);
            dto.setTodayCount(0);
            dto.setYesterdayCount(0);
            dto.setTotalCount(0);
            statsMap.put(logType, dto);
        } else {
            logger.info("更新已有统计对象，日志类型: " + logType +
                      ", 当前值 - 今日: " + dto.getTodayCount() +
                      ", 昨日: " + dto.getYesterdayCount() +
                      ", 总计: " + dto.getTotalCount());
        }

        // 更新相应的统计值
        long oldValue = 0;
        switch (metricType) {
            case TOTAL:
                oldValue = dto.getTotalCount();
                dto.setTotalCount(value);
                break;
            case TODAY:
                oldValue = dto.getTodayCount();
                dto.setTodayCount(value);
                break;
            case YESTERDAY:
                oldValue = dto.getYesterdayCount();
                dto.setYesterdayCount(value);
                break;
        }
        logger.info("统计数据已更新 - 类型: " + metricType + ", 日志类型: " + logType +
                  ", 旧值: " + oldValue + ", 新值: " + value);
    }

    private void processTextMetricsData(String data, Map<String, LogStatsDTO> statsMap, MetricType metricType) {
        String[] lines = data.split("\\n");
        String currentLogType = null;

        for (String line : lines) {
            // 跳过注释行
            if (line.startsWith("#")) {
                continue;
            }

            // 提取日志类型
            int logTypeIndex = line.indexOf("log_type=\"");
            if (logTypeIndex > 0) {
                int startQuote = logTypeIndex + 10; // "log_type="" 的长度是 10
                int endQuote = line.indexOf("\"", startQuote);
                if (endQuote > 0) {
                    currentLogType = line.substring(startQuote, endQuote);
                    continue;
                }
            }

            // 提取值并更新统计
            if (currentLogType != null) {
                Matcher valueMatcher = VALUE_PATTERN.matcher(line);
                if (valueMatcher.find()) {
                    try {
                        long value = Math.round(Double.parseDouble(valueMatcher.group(1)));
                        updateStatsMap(statsMap, currentLogType, value, metricType);
                    } catch (NumberFormatException e) {
                        logger.warn("Invalid number format for value: " + valueMatcher.group(1));
                    }
                }
            }
        }
    }

    @Override
    public Object getLogTrend(String startTime, String endTime) {
        logger.info("获取日志趋势数据，开始时间: {}, 结束时间: {}", startTime, endTime);

        try {
            // 构建查询语句，获取每小时的日志处理量
            String query = "sum(increase(securio_log_stats_total[1h])) by (log_type)";
            String url = victoriaMetricsUrl + "/api/v1/query_range?query=" + query;

            // 添加时间范围参数
            if (StringUtils.hasText(startTime)) {
                url += "&start=" + startTime;
            }
            if (StringUtils.hasText(endTime)) {
                url += "&end=" + endTime;
            }

            // 添加步长参数（1小时）
            url += "&step=1h";

            logger.info("日志趋势查询URL: {}", url);
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            String responseBody = response.getBody();
            logger.info("日志趋势查询响应: {}", responseBody);

            // 解析响应数据
            JSONObject jsonObject = JSON.parseObject(responseBody);
            if (!"success".equals(jsonObject.getString("status"))) {
                logger.error("查询日志趋势失败: {}", jsonObject.getString("error"));
                return null;
            }

            // 获取结果数据
            JSONObject dataObj = jsonObject.getJSONObject("data");
            JSONArray resultArray = dataObj.getJSONArray("result");

            // 处理结果数据，构建前端需要的格式
            List<String> xAxisData = new ArrayList<>();
            List<String> legend = new ArrayList<>();
            List<Map<String, Object>> seriesData = new ArrayList<>();

            // 首先获取所有时间点
            Set<String> timePoints = new HashSet<>();
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject result = resultArray.getJSONObject(i);
                JSONArray values = result.getJSONArray("values");
                for (int j = 0; j < values.size(); j++) {
                    JSONArray point = values.getJSONArray(j);
                    // 时间戳转换为日期字符串
                    long timestamp = point.getLongValue(0);
                    String timeStr = new Date(timestamp * 1000).toString();
                    timePoints.add(timeStr);
                }
            }

            // 按时间排序
            xAxisData = new ArrayList<>(timePoints);
            Collections.sort(xAxisData);

            // 处理每个日志类型的数据
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject result = resultArray.getJSONObject(i);
                JSONObject metric = result.getJSONObject("metric");
                String logType = metric.getString("log_type");
                legend.add(logType);

                // 构建该日志类型的数据系列
                Map<String, Object> series = new HashMap<>();
                series.put("name", logType);
                series.put("type", "line");
                series.put("smooth", true);

                // 处理数据点
                JSONArray values = result.getJSONArray("values");
                List<Double> data = new ArrayList<>();
                Map<String, Double> timeValueMap = new HashMap<>();

                for (int j = 0; j < values.size(); j++) {
                    JSONArray point = values.getJSONArray(j);
                    long timestamp = point.getLongValue(0);
                    String timeStr = new Date(timestamp * 1000).toString();
                    double value = point.getDoubleValue(1);
                    timeValueMap.put(timeStr, value);
                }

                // 按照xAxisData的顺序填充数据
                for (String time : xAxisData) {
                    data.add(timeValueMap.getOrDefault(time, 0.0));
                }

                series.put("data", data);
                seriesData.add(series);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("xAxisData", xAxisData);
            result.put("legend", legend);
            result.put("seriesData", seriesData);

            return result;
        } catch (Exception e) {
            logger.error("获取日志趋势数据失败: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public LogPerformanceDTO getPerformanceStats() {
        logger.info("获取日志处理性能数据");

        LogPerformanceDTO dto = new LogPerformanceDTO();

        try {
            // 获取平均处理时间
            String avgTimeQuery = "avg(securio_component_avg_processing_time)";
            String avgTimeUrl = victoriaMetricsUrl + "/api/v1/query?query=" + avgTimeQuery;
            ResponseEntity<String> avgTimeResponse = restTemplate.getForEntity(avgTimeUrl, String.class);
            JSONObject avgTimeJson = JSON.parseObject(avgTimeResponse.getBody());

            if ("success".equals(avgTimeJson.getString("status"))) {
                JSONObject dataObj = avgTimeJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        double avgTime = value.getDoubleValue(1);
                        dto.setAvgProcessingTime(avgTime);
                    }
                }
            }

            // 获取当前小时的累计处理量，用于计算平均每分钟处理量
            String rateHourQuery = "sum(securio_log_stats_hour)";
            String rateHourUrl = victoriaMetricsUrl + "/api/v1/query?query=" + rateHourQuery;
            ResponseEntity<String> rateHourResponse = restTemplate.getForEntity(rateHourUrl, String.class);
            JSONObject rateHourJson = JSON.parseObject(rateHourResponse.getBody());

            long avgProcessingRateMinute = 0;
            long avgProcessingRateHour = 0;

            if ("success".equals(rateHourJson.getString("status"))) {
                JSONObject dataObj = rateHourJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        // 获取当前小时的累计处理量
                        long currentHourTotal = Math.round(value.getDoubleValue(1));
                        
                        // 计算当前时间在这个小时中的分钟数
                        Calendar now = Calendar.getInstance();
                        int currentMinuteInHour = now.get(Calendar.MINUTE) + 1; // 加1避免除零，表示已过去的分钟数
                        
                        logger.info("当前小时累计处理量: {}, 当前小时已过去分钟数: {}", currentHourTotal, currentMinuteInHour);
                        
                        // 计算平均每分钟处理量 = 当前小时累计处理量 / 当前小时已过去的分钟数
                        if (currentMinuteInHour > 0) {
                            avgProcessingRateMinute = Math.round((double) currentHourTotal / currentMinuteInHour);
                        }
                        
                        // 计算平均每小时处理量 = 平均每分钟处理量 * 60
                        avgProcessingRateHour = avgProcessingRateMinute * 60;
                        
                        logger.info("计算得出的平均每分钟处理量: {}, 平均每小时处理量: {}", 
                                  avgProcessingRateMinute, avgProcessingRateHour);
                    }
                }
            }

            // 设置计算后的处理量
            dto.setProcessingRateMinute(avgProcessingRateMinute);
            dto.setProcessingRateHour(avgProcessingRateHour);

            // 获取每天处理量
            String rateDayQuery = "sum(securio_log_stats_day)";
            String rateDayUrl = victoriaMetricsUrl + "/api/v1/query?query=" + rateDayQuery;
            ResponseEntity<String> rateDayResponse = restTemplate.getForEntity(rateDayUrl, String.class);
            JSONObject rateDayJson = JSON.parseObject(rateDayResponse.getBody());

            if ("success".equals(rateDayJson.getString("status"))) {
                JSONObject dataObj = rateDayJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        long rateDay = Math.round(value.getDoubleValue(1));
                        dto.setProcessingRateDay(rateDay);
                    }
                }
            }

            // 获取处理时间趋势数据
            Map<String, List<Object>> processingTimeTrend = new HashMap<>();
            // 这里可以添加处理时间趋势数据的查询和处理逻辑
            dto.setProcessingTimeTrend(processingTimeTrend);

            // 获取处理量趋势数据
            Map<String, List<Object>> processingRateTrend = new HashMap<>();
            // 这里可以添加处理量趋势数据的查询和处理逻辑
            dto.setProcessingRateTrend(processingRateTrend);

        } catch (Exception e) {
            logger.error("获取日志处理性能数据失败: " + e.getMessage(), e);
        }

        return dto;
    }

    @Override
    public List<ComponentStatDTO> getComponentStats() {
        logger.info("获取组件统计数据");

        List<ComponentStatDTO> componentStats = new ArrayList<>();

        try {
            // 获取组件接收消息数
            String receivedQuery = "securio_component_received";
            String receivedUrl = victoriaMetricsUrl + "/api/v1/query?query=" + receivedQuery;
            ResponseEntity<String> receivedResponse = restTemplate.getForEntity(receivedUrl, String.class);
            JSONObject receivedJson = JSON.parseObject(receivedResponse.getBody());

            Map<String, ComponentStatDTO> componentMap = new HashMap<>();

            if ("success".equals(receivedJson.getString("status"))) {
                JSONObject dataObj = receivedJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");

                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject result = resultArray.getJSONObject(i);
                    JSONObject metric = result.getJSONObject("metric");
                    JSONArray value = result.getJSONArray("value");

                    if (value != null && value.size() >= 2) {
                        String pipeline = metric.getString("pipeline");
                        String componentType = metric.getString("component_type");
                        String componentName = metric.getString("component_name");
                        long received = Math.round(value.getDoubleValue(1));

                        String key = pipeline + ":" + componentType + ":" + componentName;
                        ComponentStatDTO dto = componentMap.getOrDefault(key, new ComponentStatDTO());
                        dto.setPipeline(pipeline);
                        dto.setComponentType(componentType);
                        dto.setComponentName(componentName);
                        dto.setReceived(received);

                        componentMap.put(key, dto);
                    }
                }
            }

            // 获取组件处理消息数
            String processedQuery = "securio_component_processed";
            String processedUrl = victoriaMetricsUrl + "/api/v1/query?query=" + processedQuery;
            ResponseEntity<String> processedResponse = restTemplate.getForEntity(processedUrl, String.class);
            JSONObject processedJson = JSON.parseObject(processedResponse.getBody());

            if ("success".equals(processedJson.getString("status"))) {
                JSONObject dataObj = processedJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");

                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject result = resultArray.getJSONObject(i);
                    JSONObject metric = result.getJSONObject("metric");
                    JSONArray value = result.getJSONArray("value");

                    if (value != null && value.size() >= 2) {
                        String pipeline = metric.getString("pipeline");
                        String componentType = metric.getString("component_type");
                        String componentName = metric.getString("component_name");
                        long processed = Math.round(value.getDoubleValue(1));

                        String key = pipeline + ":" + componentType + ":" + componentName;
                        ComponentStatDTO dto = componentMap.getOrDefault(key, new ComponentStatDTO());
                        dto.setPipeline(pipeline);
                        dto.setComponentType(componentType);
                        dto.setComponentName(componentName);
                        dto.setProcessed(processed);

                        componentMap.put(key, dto);
                    }
                }
            }

            // 获取组件错误数
            String errorsQuery = "securio_component_errors";
            String errorsUrl = victoriaMetricsUrl + "/api/v1/query?query=" + errorsQuery;
            ResponseEntity<String> errorsResponse = restTemplate.getForEntity(errorsUrl, String.class);
            JSONObject errorsJson = JSON.parseObject(errorsResponse.getBody());

            if ("success".equals(errorsJson.getString("status"))) {
                JSONObject dataObj = errorsJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");

                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject result = resultArray.getJSONObject(i);
                    JSONObject metric = result.getJSONObject("metric");
                    JSONArray value = result.getJSONArray("value");

                    if (value != null && value.size() >= 2) {
                        String pipeline = metric.getString("pipeline");
                        String componentType = metric.getString("component_type");
                        String componentName = metric.getString("component_name");
                        long errors = Math.round(value.getDoubleValue(1));

                        String key = pipeline + ":" + componentType + ":" + componentName;
                        ComponentStatDTO dto = componentMap.getOrDefault(key, new ComponentStatDTO());
                        dto.setPipeline(pipeline);
                        dto.setComponentType(componentType);
                        dto.setComponentName(componentName);
                        dto.setErrors(errors);

                        componentMap.put(key, dto);
                    }
                }
            }

            // 获取组件丢弃消息数
            String droppedQuery = "securio_component_dropped";
            String droppedUrl = victoriaMetricsUrl + "/api/v1/query?query=" + droppedQuery;
            ResponseEntity<String> droppedResponse = restTemplate.getForEntity(droppedUrl, String.class);
            JSONObject droppedJson = JSON.parseObject(droppedResponse.getBody());

            if ("success".equals(droppedJson.getString("status"))) {
                JSONObject dataObj = droppedJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");

                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject result = resultArray.getJSONObject(i);
                    JSONObject metric = result.getJSONObject("metric");
                    JSONArray value = result.getJSONArray("value");

                    if (value != null && value.size() >= 2) {
                        String pipeline = metric.getString("pipeline");
                        String componentType = metric.getString("component_type");
                        String componentName = metric.getString("component_name");
                        long dropped = Math.round(value.getDoubleValue(1));

                        String key = pipeline + ":" + componentType + ":" + componentName;
                        ComponentStatDTO dto = componentMap.getOrDefault(key, new ComponentStatDTO());
                        dto.setPipeline(pipeline);
                        dto.setComponentType(componentType);
                        dto.setComponentName(componentName);
                        dto.setDropped(dropped);

                        componentMap.put(key, dto);
                    }
                }
            }

            // 获取组件平均处理时间
            String avgTimeQuery = "securio_component_avg_processing_time";
            String avgTimeUrl = victoriaMetricsUrl + "/api/v1/query?query=" + avgTimeQuery;
            ResponseEntity<String> avgTimeResponse = restTemplate.getForEntity(avgTimeUrl, String.class);
            JSONObject avgTimeJson = JSON.parseObject(avgTimeResponse.getBody());

            if ("success".equals(avgTimeJson.getString("status"))) {
                JSONObject dataObj = avgTimeJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");

                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject result = resultArray.getJSONObject(i);
                    JSONObject metric = result.getJSONObject("metric");
                    JSONArray value = result.getJSONArray("value");

                    if (value != null && value.size() >= 2) {
                        String pipeline = metric.getString("pipeline");
                        String componentType = metric.getString("component_type");
                        String componentName = metric.getString("component_name");
                        double avgTime = value.getDoubleValue(1);

                        String key = pipeline + ":" + componentType + ":" + componentName;
                        ComponentStatDTO dto = componentMap.getOrDefault(key, new ComponentStatDTO());
                        dto.setPipeline(pipeline);
                        dto.setComponentType(componentType);
                        dto.setComponentName(componentName);
                        dto.setAvgProcessingTime(avgTime);

                        componentMap.put(key, dto);
                    }
                }
            }

            // 设置健康状态
            for (ComponentStatDTO dto : componentMap.values()) {
                if (dto.getErrors() > 0 || dto.getDropped() > 0) {
                    dto.setHealthStatus("异常");
                } else if (dto.getReceived() > 0 && dto.getProcessed() == 0) {
                    dto.setHealthStatus("阻塞");
                } else {
                    dto.setHealthStatus("正常");
                }
            }

            // 转换为列表
            componentStats = new ArrayList<>(componentMap.values());

        } catch (Exception e) {
            logger.error("获取组件统计数据失败: " + e.getMessage(), e);
        }

        return componentStats;
    }

    @Override
    public LogDistributionDTO getLogDistribution() {
        logger.info("获取日志分布数据");

        LogDistributionDTO dto = new LogDistributionDTO();

        try {
            // 获取日志类型分布数据
            String query = "sum(securio_log_stats_total) by (log_type)";
            String url = victoriaMetricsUrl + "/api/v1/query?query=" + query;
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            JSONObject jsonObject = JSON.parseObject(response.getBody());

            if (!"success".equals(jsonObject.getString("status"))) {
                logger.error("查询日志分布数据失败: {}", jsonObject.getString("error"));
                return dto;
            }

            // 获取结果数据
            JSONObject dataObj = jsonObject.getJSONObject("data");
            JSONArray resultArray = dataObj.getJSONArray("result");

            // 处理结果数据
            List<LogDistributionDTO.TypeDistribution> typeDistributions = new ArrayList<>();
            List<LogDistributionDTO.TypeRanking> typeRankings = new ArrayList<>();

            // 计算总数
            long totalCount = 0;
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject result = resultArray.getJSONObject(i);
                JSONArray value = result.getJSONArray("value");
                if (value != null && value.size() >= 2) {
                    totalCount += Math.round(value.getDoubleValue(1));
                }
            }

            // 处理每个日志类型的数据
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject result = resultArray.getJSONObject(i);
                JSONObject metric = result.getJSONObject("metric");
                JSONArray value = result.getJSONArray("value");

                if (value != null && value.size() >= 2) {
                    String logType = metric.getString("log_type");
                    long count = Math.round(value.getDoubleValue(1));

                    // 计算百分比
                    double percentage = totalCount > 0 ? (count * 100.0 / totalCount) : 0;

                    // 添加到分布数据
                    LogDistributionDTO.TypeDistribution distribution = new LogDistributionDTO.TypeDistribution();
                    distribution.setLogType(logType);
                    distribution.setCount(count);
                    distribution.setPercentage(percentage);
                    typeDistributions.add(distribution);

                    // 添加到排名数据
                    LogDistributionDTO.TypeRanking ranking = new LogDistributionDTO.TypeRanking();
                    ranking.setLogType(logType);
                    ranking.setCount(count);
                    ranking.setRank(0); // 暂时设为0，后面会重新排序
                    typeRankings.add(ranking);
                }
            }

            // 按数量排序
            typeDistributions.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));
            typeRankings.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));

            // 设置排名
            for (int i = 0; i < typeRankings.size(); i++) {
                typeRankings.get(i).setRank(i + 1);
            }

            // 设置到DTO
            dto.setTypeDistribution(typeDistributions);
            dto.setTypeRanking(typeRankings);

            // 构建图表数据
            Map<String, Object> chartData = new HashMap<>();

            // 饼图数据
            List<Map<String, Object>> pieData = new ArrayList<>();
            for (LogDistributionDTO.TypeDistribution distribution : typeDistributions) {
                Map<String, Object> item = new HashMap<>();
                item.put("name", distribution.getLogType());
                item.put("value", distribution.getCount());
                pieData.add(item);
            }
            chartData.put("pieData", pieData);

            // 柱状图数据
            List<String> barCategories = new ArrayList<>();
            List<Long> barValues = new ArrayList<>();
            for (LogDistributionDTO.TypeRanking ranking : typeRankings) {
                barCategories.add(ranking.getLogType());
                barValues.add(ranking.getCount());
            }
            chartData.put("barCategories", barCategories);
            chartData.put("barValues", barValues);

            dto.setChartData(chartData);

        } catch (Exception e) {
            logger.error("获取日志分布数据失败: " + e.getMessage(), e);
        }

        return dto;
    }

    @Override
    public PipelineStatDTO getPipelineStats(String pipelineName) {
        logger.info("获取管道统计数据，管道名称: {}", pipelineName);

        PipelineStatDTO dto = new PipelineStatDTO();
        dto.setPipelineName(pipelineName);

        try {
            // 获取可用的管道列表
            String pipelinesQuery = "sum(securio_component_received) by (pipeline)";
            String pipelinesUrl = victoriaMetricsUrl + "/api/v1/query?query=" + pipelinesQuery;
            ResponseEntity<String> pipelinesResponse = restTemplate.getForEntity(pipelinesUrl, String.class);
            JSONObject pipelinesJson = JSON.parseObject(pipelinesResponse.getBody());

            List<String> availablePipelines = new ArrayList<>();
            if ("success".equals(pipelinesJson.getString("status"))) {
                JSONObject dataObj = pipelinesJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");

                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject result = resultArray.getJSONObject(i);
                    JSONObject metric = result.getJSONObject("metric");
                    String pipeline = metric.getString("pipeline");
                    availablePipelines.add(pipeline);
                }
            }
            dto.setAvailablePipelines(availablePipelines);

            // 如果没有指定管道名称，使用第一个可用的管道
            if (pipelineName == null || pipelineName.isEmpty()) {
                if (!availablePipelines.isEmpty()) {
                    pipelineName = availablePipelines.get(0);
                    dto.setPipelineName(pipelineName);
                } else {
                    return dto; // 没有可用的管道
                }
            }

            // 获取输入组件统计
            PipelineStatDTO.StageStats inputStats = new PipelineStatDTO.StageStats();
            String inputQuery = "sum(securio_component_received{pipeline=\"" + pipelineName + "\",component_type=\"input\"})";
            String inputUrl = victoriaMetricsUrl + "/api/v1/query?query=" + inputQuery;
            ResponseEntity<String> inputResponse = restTemplate.getForEntity(inputUrl, String.class);
            JSONObject inputJson = JSON.parseObject(inputResponse.getBody());

            if ("success".equals(inputJson.getString("status"))) {
                JSONObject dataObj = inputJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        inputStats.setReceived(Math.round(value.getDoubleValue(1)));
                    }
                }
            }

            // 获取输入组件处理数
            String inputProcessedQuery = "sum(securio_component_processed{pipeline=\"" + pipelineName + "\",component_type=\"input\"})";
            String inputProcessedUrl = victoriaMetricsUrl + "/api/v1/query?query=" + inputProcessedQuery;
            ResponseEntity<String> inputProcessedResponse = restTemplate.getForEntity(inputProcessedUrl, String.class);
            JSONObject inputProcessedJson = JSON.parseObject(inputProcessedResponse.getBody());

            if ("success".equals(inputProcessedJson.getString("status"))) {
                JSONObject dataObj = inputProcessedJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        inputStats.setProcessed(Math.round(value.getDoubleValue(1)));
                    }
                }
            }

            // 获取输入组件错误数
            String inputErrorsQuery = "sum(securio_component_errors{pipeline=\"" + pipelineName + "\",component_type=\"input\"})";
            String inputErrorsUrl = victoriaMetricsUrl + "/api/v1/query?query=" + inputErrorsQuery;
            ResponseEntity<String> inputErrorsResponse = restTemplate.getForEntity(inputErrorsUrl, String.class);
            JSONObject inputErrorsJson = JSON.parseObject(inputErrorsResponse.getBody());

            if ("success".equals(inputErrorsJson.getString("status"))) {
                JSONObject dataObj = inputErrorsJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        inputStats.setErrors(Math.round(value.getDoubleValue(1)));
                    }
                }
            }

            // 获取输入组件丢弃数
            String inputDroppedQuery = "sum(securio_component_dropped{pipeline=\"" + pipelineName + "\",component_type=\"input\"})";
            String inputDroppedUrl = victoriaMetricsUrl + "/api/v1/query?query=" + inputDroppedQuery;
            ResponseEntity<String> inputDroppedResponse = restTemplate.getForEntity(inputDroppedUrl, String.class);
            JSONObject inputDroppedJson = JSON.parseObject(inputDroppedResponse.getBody());

            if ("success".equals(inputDroppedJson.getString("status"))) {
                JSONObject dataObj = inputDroppedJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        inputStats.setDropped(Math.round(value.getDoubleValue(1)));
                    }
                }
            }

            // 获取输入组件平均处理时间
            String inputAvgTimeQuery = "avg(securio_component_avg_processing_time{pipeline=\"" + pipelineName + "\",component_type=\"input\"})";
            String inputAvgTimeUrl = victoriaMetricsUrl + "/api/v1/query?query=" + inputAvgTimeQuery;
            ResponseEntity<String> inputAvgTimeResponse = restTemplate.getForEntity(inputAvgTimeUrl, String.class);
            JSONObject inputAvgTimeJson = JSON.parseObject(inputAvgTimeResponse.getBody());

            if ("success".equals(inputAvgTimeJson.getString("status"))) {
                JSONObject dataObj = inputAvgTimeJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        inputStats.setAvgProcessingTime(value.getDoubleValue(1));
                    }
                }
            }

            dto.setInput(inputStats);

            // 获取处理组件统计（类似于输入组件的查询，只需替换component_type为"processor"）
            PipelineStatDTO.StageStats processorStats = new PipelineStatDTO.StageStats();
            // 这里重复上面的查询逻辑，只需替换component_type为"processor"
            // 为简化代码，这里省略具体实现

            // 获取输出组件统计（类似于输入组件的查询，只需替换component_type为"output"）
            PipelineStatDTO.StageStats outputStats = new PipelineStatDTO.StageStats();
            // 这里重复上面的查询逻辑，只需替换component_type为"output"
            // 为简化代码，这里省略具体实现

            // 设置处理和输出组件的统计数据
            dto.setProcessor(processorStats);
            dto.setOutput(outputStats);

            // 获取性能趋势数据
            Map<String, List<Object>> performanceTrend = new HashMap<>();
            // 这里可以添加性能趋势数据的查询和处理逻辑
            dto.setPerformanceTrend(performanceTrend);

        } catch (Exception e) {
            logger.error("获取管道统计数据失败: " + e.getMessage(), e);
        }

        return dto;
    }

    @Override
    public ErrorStatDTO getErrorStats(String startTime, String endTime) {
        logger.info("获取错误统计数据，开始时间: {}, 结束时间: {}", startTime, endTime);

        ErrorStatDTO dto = new ErrorStatDTO();

        try {
            // 获取总错误数
            String errorsQuery = "sum(securio_component_errors)";
            String errorsUrl = victoriaMetricsUrl + "/api/v1/query?query=" + errorsQuery;
            ResponseEntity<String> errorsResponse = restTemplate.getForEntity(errorsUrl, String.class);
            JSONObject errorsJson = JSON.parseObject(errorsResponse.getBody());

            if ("success".equals(errorsJson.getString("status"))) {
                JSONObject dataObj = errorsJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        dto.setTotalErrors(Math.round(value.getDoubleValue(1)));
                    }
                }
            }

            // 获取总丢弃数
            String droppedQuery = "sum(securio_component_dropped)";
            String droppedUrl = victoriaMetricsUrl + "/api/v1/query?query=" + droppedQuery;
            ResponseEntity<String> droppedResponse = restTemplate.getForEntity(droppedUrl, String.class);
            JSONObject droppedJson = JSON.parseObject(droppedResponse.getBody());

            if ("success".equals(droppedJson.getString("status"))) {
                JSONObject dataObj = droppedJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        dto.setTotalDropped(Math.round(value.getDoubleValue(1)));
                    }
                }
            }

            // 获取总接收数，用于计算错误率和丢弃率
            String receivedQuery = "sum(securio_component_received)";
            String receivedUrl = victoriaMetricsUrl + "/api/v1/query?query=" + receivedQuery;
            ResponseEntity<String> receivedResponse = restTemplate.getForEntity(receivedUrl, String.class);
            JSONObject receivedJson = JSON.parseObject(receivedResponse.getBody());

            if ("success".equals(receivedJson.getString("status"))) {
                JSONObject dataObj = receivedJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        long totalReceived = Math.round(value.getDoubleValue(1));
                        if (totalReceived > 0) {
                            dto.setErrorRate(dto.getTotalErrors() * 100.0 / totalReceived);
                            dto.setDroppedRate(dto.getTotalDropped() * 100.0 / totalReceived);
                        }
                    }
                }
            }

            // 获取错误趋势数据
            Map<String, List<Object>> errorTrend = new HashMap<>();
            // 这里可以添加错误趋势数据的查询和处理逻辑
            dto.setErrorTrend(errorTrend);

            // 设置最近错误时间和组件（这里需要更复杂的查询，暂时省略）
            dto.setLastErrorTime(new Date());
            dto.setLastErrorComponent("*output.VictoriaLogsOutput");

        } catch (Exception e) {
            logger.error("获取错误统计数据失败: " + e.getMessage(), e);
        }

        return dto;
    }

    @Override
    public ThroughputDTO getThroughputStats(String timeUnit, String startTime, String endTime) {
        logger.info("获取吞吐量数据，时间单位: {}, 开始时间: {}, 结束时间: {}", timeUnit, startTime, endTime);

        ThroughputDTO dto = new ThroughputDTO();
        dto.setTimeUnit(timeUnit);

        try {
            // 根据时间单位选择查询指标
            String metricName;
            switch (timeUnit) {
                case "minute":
                    metricName = "securio_log_stats_minute";
                    break;
                case "hour":
                    metricName = "securio_log_stats_hour";
                    break;
                case "day":
                    metricName = "securio_log_stats_day";
                    break;
                default:
                    metricName = "securio_log_stats_hour"; // 默认使用小时
            }

            // 获取总吞吐量
            String totalQuery = "sum(" + metricName + ")";
            String totalUrl = victoriaMetricsUrl + "/api/v1/query?query=" + totalQuery;
            ResponseEntity<String> totalResponse = restTemplate.getForEntity(totalUrl, String.class);
            JSONObject totalJson = JSON.parseObject(totalResponse.getBody());

            if ("success".equals(totalJson.getString("status"))) {
                JSONObject dataObj = totalJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");
                if (resultArray != null && !resultArray.isEmpty()) {
                    JSONObject result = resultArray.getJSONObject(0);
                    JSONArray value = result.getJSONArray("value");
                    if (value != null && value.size() >= 2) {
                        dto.setTotalThroughput(Math.round(value.getDoubleValue(1)));
                    }
                }
            }

            // 获取各日志类型的吞吐量
            String typeQuery = metricName;
            String typeUrl = victoriaMetricsUrl + "/api/v1/query?query=" + typeQuery;
            ResponseEntity<String> typeResponse = restTemplate.getForEntity(typeUrl, String.class);
            JSONObject typeJson = JSON.parseObject(typeResponse.getBody());

            List<ThroughputDTO.LogTypeThroughput> logTypeThroughputs = new ArrayList<>();
            if ("success".equals(typeJson.getString("status"))) {
                JSONObject dataObj = typeJson.getJSONObject("data");
                JSONArray resultArray = dataObj.getJSONArray("result");

                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject result = resultArray.getJSONObject(i);
                    JSONObject metric = result.getJSONObject("metric");
                    JSONArray value = result.getJSONArray("value");

                    if (value != null && value.size() >= 2) {
                        String logType = metric.getString("log_type");
                        long throughput = Math.round(value.getDoubleValue(1));

                        ThroughputDTO.LogTypeThroughput typeThroughput = new ThroughputDTO.LogTypeThroughput();
                        typeThroughput.setLogType(logType);
                        typeThroughput.setThroughput(throughput);
                        logTypeThroughputs.add(typeThroughput);
                    }
                }
            }

            // 按吞吐量排序
            logTypeThroughputs.sort((a, b) -> Long.compare(b.getThroughput(), a.getThroughput()));
            dto.setLogTypeThroughput(logTypeThroughputs);

            // 计算平均吞吐量、最大吞吐量和最小吞吐量
            if (!logTypeThroughputs.isEmpty()) {
                long sum = 0;
                long max = Long.MIN_VALUE;
                long min = Long.MAX_VALUE;

                for (ThroughputDTO.LogTypeThroughput typeThroughput : logTypeThroughputs) {
                    long throughput = typeThroughput.getThroughput();
                    sum += throughput;
                    max = Math.max(max, throughput);
                    min = Math.min(min, throughput);
                }

                dto.setAvgThroughput((double) sum / logTypeThroughputs.size());
                dto.setMaxThroughput(max);
                dto.setMinThroughput(min);
            }

            // 获取吞吐量趋势数据
            Map<String, Object> throughputTrend = new HashMap<>();
            // 这里可以添加吞吐量趋势数据的查询和处理逻辑
            dto.setThroughputTrend(throughputTrend);

        } catch (Exception e) {
            logger.error("获取吞吐量数据失败: " + e.getMessage(), e);
        }

        return dto;
    }
}
