package com.wiwj.securio.logmgr.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网宿WAF访问日志审计控制器
 *
 * <AUTHOR>
 */
@Api(tags = "网宿WAF访问日志审计管理", description = "网宿WAF访问日志审计相关接口")
@RestController
@RequestMapping("/opslog/wangsu")
public class WangsuLogAuditController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WangsuLogAuditController.class);

    @Autowired
    private VictoriaLogsService victoriaLogsService;

    /**
     * 获取网宿WAF访问日志统计指标
     */
    @ApiOperation(value = "获取网宿WAF访问日志统计指标", notes = "获取网宿WAF访问日志的关键统计指标")
    @GetMapping("/stats")
    public AjaxResult getWangsuStats(
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "1h") String timeRange) {

        try {
            Map<String, Object> stats = new HashMap<>();

            // 1. 获取总请求数
            String totalReqQuery = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ACCESS\" | stats count() totalReq", timeRange);
            String totalReqResult = victoriaLogsService.queryLogs(totalReqQuery, 1, null, "vmlog2");
            long totalRequests = parseStatValue(totalReqResult, "totalReq");
            stats.put("totalRequests", totalRequests);

            // 2. 获取成功请求数
            String successReqQuery = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ACCESS\" and code:200 | stats count() successReq", timeRange);
            String successReqResult = victoriaLogsService.queryLogs(successReqQuery, 1, null, "vmlog2");
            long successRequests = parseStatValue(successReqResult, "successReq");
            stats.put("successRequests", successRequests);

            // 3. 获取独立请求数（基于host字段）
            String uniqReqQuery = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ACCESS\" | stats count_uniq(host) uniqReq", timeRange);
            String uniqReqResult = victoriaLogsService.queryLogs(uniqReqQuery, 1, null, "vmlog2");
            long uniqueRequests = parseStatValue(uniqReqResult, "uniqReq");
            stats.put("uniqueRequests", uniqueRequests);

            // 4. 获取服务端到客户端流量（size字段）
            String totalSizeQuery = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ACCESS\" | stats sum(size) totalSize", timeRange);
            String totalSizeResult = victoriaLogsService.queryLogs(totalSizeQuery, 1, null, "vmlog2");
            long totalSize = parseStatValue(totalSizeResult, "totalSize");
            stats.put("totalSize", totalSize);

            // 5. 获取客户端请求大小（upsize字段）
            String totalUpSizeQuery = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ACCESS\" | stats sum(upsize) totalUpSize", timeRange);
            String totalUpSizeResult = victoriaLogsService.queryLogs(totalUpSizeQuery, 1, null, "vmlog2");
            long totalUpSize = parseStatValue(totalUpSizeResult, "totalUpSize");
            stats.put("totalUpSize", totalUpSize);

            // 计算成功率
            double successRate = totalRequests > 0 ? (double) successRequests / totalRequests * 100 : 0;
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);

            logger.info("网宿WAF访问日志统计查询完成，时间范围: {}, 总请求数: {}, 成功请求数: {}, 独立请求数: {}, 总流量: {}, 上行流量: {}",
                    timeRange, totalRequests, successRequests, uniqueRequests, totalSize, totalUpSize);

            return AjaxResult.success(stats);

        } catch (Exception e) {
            logger.error("获取网宿WAF访问日志统计失败: " + e.getMessage(), e);
            return AjaxResult.error("获取网宿WAF访问日志统计失败: " + e.getMessage());
        }
    }

    /**
     * 解析统计值
     * @param result 查询结果
     * @param fieldName 字段名
     * @return 统计值
     */
    private long parseStatValue(String result, String fieldName) {
        if (result == null || result.trim().isEmpty()) {
            return 0;
        }

        try {
            // 按行分割结果
            String[] lines = result.split("\n");
            for (String line : lines) {
                if (line.trim().isEmpty()) continue;

                JSONObject jsonObj = JSON.parseObject(line);
                if (jsonObj.containsKey(fieldName)) {
                    String value = jsonObj.getString(fieldName);
                    if (value != null && !value.isEmpty()) {
                        return Long.parseLong(value);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析统计值失败，字段: {}, 结果: {}", fieldName, result, e);
        }

        return 0;
    }

    /**
     * 获取网宿WAF访问日志图表数据
     */
    @ApiOperation(value = "获取网宿WAF访问日志图表数据", notes = "获取网宿WAF访问日志的facets统计数据用于图表展示")
    @GetMapping("/charts")
    public AjaxResult getWangsuCharts(
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "1h") String timeRange,
            @ApiParam(value = "限制返回的记录数") @RequestParam(required = false, defaultValue = "1000") Integer limit) {

        try {
            // 构建查询语句
            String query = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ACCESS\"", timeRange);
            
            // 调用VictoriaLogs的facets接口
            String facetsResult = victoriaLogsService.facets(query, limit, "vmlog2");
            
            if (facetsResult != null && !facetsResult.trim().isEmpty()) {
                // 解析facets结果
                JSONObject jsonResult = JSON.parseObject(facetsResult);
                
                logger.info("网宿WAF访问日志图表数据查询完成，时间范围: {}, 限制: {}", timeRange, limit);
                
                return AjaxResult.success(jsonResult);
            } else {
                return AjaxResult.error("获取图表数据失败：返回结果为空");
            }

        } catch (Exception e) {
            logger.error("获取网宿WAF访问日志图表数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取网宿WAF访问日志图表数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取网宿WAF访问日志时间序列统计数据
     */
    @ApiOperation(value = "获取网宿WAF访问日志时间序列统计数据", notes = "获取指定渠道的时间序列统计数据，支持多种指标")
    @GetMapping("/timeseries")
    public AjaxResult getWangsuTimeSeries(
            @ApiParam(value = "渠道名称") @RequestParam(required = true) String channel,
            @ApiParam(value = "开始时间") @RequestParam(required = true) String start,
            @ApiParam(value = "结束时间") @RequestParam(required = true) String end,
            @ApiParam(value = "时间步长") @RequestParam(required = false, defaultValue = "1h") String step,
            @ApiParam(value = "统计指标类型") @RequestParam(required = false, defaultValue = "count") String metric) {

        try {
            // 构建基础查询语句
            String baseQuery = String.format("_time:1d and stream:\"SECURIO_WANGSU_WAF_ACCESS\" and channel:\"%s\"", channel);
            
            // 根据指标类型构建不同的统计查询
            String statsQuery;
            switch (metric.toLowerCase()) {
                case "count":
                    statsQuery = baseQuery + " | stats by (channel) count(*)";
                    break;
                case "size":
                    statsQuery = baseQuery + " | stats by (channel) sum(size)";
                    break;
                case "upsize":
                    statsQuery = baseQuery + " | stats by (channel) sum(upsize)";
                    break;
                case "unique":
                    statsQuery = baseQuery + " | stats by (channel) count_uniq(host)";
                    break;
                default:
                    return AjaxResult.error("不支持的统计指标类型: " + metric);
            }
            
            logger.info("执行网宿WAF时间序列查询: channel={}, metric={}, start={}, end={}, step={}", 
                       channel, metric, start, end, step);
            
            // 调用VictoriaLogs的stats_query_range接口
            String result = victoriaLogsService.queryStatsRange(
                statsQuery, 
                start, 
                end, 
                step, 
                null, 
                null, 
                "vmlog2"
            );
            
            if (result != null && !result.trim().isEmpty()) {
                // 解析查询结果
                JSONObject jsonResult = JSON.parseObject(result);
                
                logger.info("网宿WAF时间序列查询完成，渠道: {}, 指标: {}", channel, metric);
                
                return AjaxResult.success(jsonResult);
            } else {
                return AjaxResult.error("获取时间序列数据失败：返回结果为空");
            }

        } catch (Exception e) {
            logger.error("获取网宿WAF时间序列数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取网宿WAF时间序列数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取网宿WAF访问日志渠道排名统计数据
     */
    @ApiOperation(value = "获取网宿WAF访问日志渠道排名统计数据", notes = "获取指定时间范围内渠道访问量排名")
    @GetMapping("/channel-ranking")
    public AjaxResult getWangsuChannelRanking(
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "1d") String timeRange,
            @ApiParam(value = "限制返回的记录数") @RequestParam(required = false, defaultValue = "50") Integer limit) {

        try {
            // 构建查询语句
            String query = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ACCESS\" | stats by (channel) count() cnt | sort by (cnt desc) | limit %d", 
                                        timeRange, limit);
            
            logger.info("执行网宿WAF渠道排名查询: timeRange={}, limit={}", timeRange, limit);
            
            // 调用VictoriaLogs查询接口
            String result = victoriaLogsService.queryLogs(query, limit, "60s", "vmlog2");
            
            if (result != null && !result.trim().isEmpty()) {
                // 解析查询结果
                List<Map<String, Object>> rankings = parseChannelRankingResult(result);
                
                logger.info("网宿WAF渠道排名查询完成，返回{}条记录", rankings.size());
                
                return AjaxResult.success(rankings);
            } else {
                return AjaxResult.error("获取渠道排名数据失败：返回结果为空");
            }

        } catch (Exception e) {
            logger.error("获取网宿WAF渠道排名数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取网宿WAF渠道排名数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析渠道排名查询结果
     */
    private List<Map<String, Object>> parseChannelRankingResult(String result) {
        List<Map<String, Object>> rankings = new ArrayList<>();
        
        try {
            // 按行分割结果
            String[] lines = result.split("\n");
            
            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();
                if (line.isEmpty()) continue;
                
                try {
                    // 解析JSON格式的行数据
                    JSONObject jsonLine = JSON.parseObject(line);
                    
                    Map<String, Object> ranking = new HashMap<>();
                    ranking.put("rank", i + 1); // 排名从1开始
                    ranking.put("channel", jsonLine.getString("channel"));
                    ranking.put("count", jsonLine.getLong("cnt"));
                    
                    rankings.add(ranking);
                } catch (Exception e) {
                    logger.warn("解析渠道排名数据行失败: {}", line, e);
                }
            }
            
        } catch (Exception e) {
            logger.error("解析渠道排名结果失败", e);
        }
        
        return rankings;
    }
} 