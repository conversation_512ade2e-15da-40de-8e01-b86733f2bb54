package com.wiwj.securio.logmgr.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;

import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * Nginx 日志审计控制器
 *
 * <AUTHOR>
 */
@Api(tags = "Nginx 日志审计管理", description = "Nginx 日志审计相关接口")
@RestController
@RequestMapping("/opslog/nginx")
public class NginxLogAuditController extends BaseController {

    @Autowired
    private VictoriaLogsService victoriaLogsService;

    /**
     * 获取Nginx访问统计
     */
    @ApiOperation(value = "获取Nginx访问统计", notes = "获取Nginx访问统计数据")
    @GetMapping("/access-stats")
    public AjaxResult getAccessStats(
            @ApiParam(value = "统计类型", required = true) @RequestParam String type,
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "10d") String timeRange,
            @ApiParam(value = "限制返回的记录数") @RequestParam(required = false, defaultValue = "10") Integer limit) {

        try {
            // 根据统计类型选择分组字段
            String fieldName;
            switch (type) {
                case "ip":
                    fieldName = "message.remote_addr";
                    break;
                case "url":
                    fieldName = "message.request_uri";
                    break;
                case "status":
                    fieldName = "message.status";
                    break;
                default:
                    return AjaxResult.error("不支持的统计类型: " + type);
            }

            // 构建查询语句
            String query = String.format("_stream: {stream=\"NGINX_ACCESS\"} AND _time:%s | stats by (%s) count(*) cnt |sort by (cnt desc)",
                    timeRange, fieldName);

            // 调用VictoriaLogs服务查询数据，指定使用 vmlog2 实例
            String result = victoriaLogsService.queryLogs(query, limit, null, "vmlog2");

            // 解析结果
            List<Map<String, Object>> parsedResult = new ArrayList<>();
            if (result != null && !result.isEmpty()) {
                try {
                    // 按行分割结果
                    String[] lines = result.split("\n");
                    for (String line : lines) {
                        if (line.trim().isEmpty()) continue;

                        try {
                            JSONObject jsonObj = JSON.parseObject(line);
                            Map<String, Object> item = new HashMap<>();

                            // 处理只有 cnt 字段的情况（第一行）
                            if (jsonObj.containsKey("cnt") && !jsonObj.containsKey(fieldName)) {
                                // 跳过只有 cnt 的行
                                continue;
                            }

                            String fieldValue = jsonObj.getString(fieldName);
                            String count = jsonObj.getString("cnt");

                            if (fieldValue != null && count != null) {
                                item.put("fieldValue", fieldValue);
                                item.put("hits", Integer.parseInt(count));
                                parsedResult.add(item);
                            }
                        } catch (Exception ex) {
                            logger.error("解析行失败: " + line, ex);
                            // 忽略无法解析的行
                            continue;
                        }
                    }
                } catch (Exception e) {
                    logger.error("解析结果失败: " + result, e);
                    return AjaxResult.error("解析结果失败: " + e.getMessage());
                }
            }

            // 限制返回的记录数
            if (limit != null && parsedResult.size() > limit) {
                parsedResult = parsedResult.subList(0, limit);
            }

            return AjaxResult.success(parsedResult);
        } catch (Exception e) {
            return AjaxResult.error("获取访问统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取Nginx错误统计
     */
    @ApiOperation(value = "获取Nginx错误统计", notes = "获取Nginx错误状态码统计数据")
    @GetMapping("/error-stats")
    public AjaxResult getErrorStats(
            @ApiParam(value = "统计类型", required = true) @RequestParam String type,
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "10d") String timeRange,
            @ApiParam(value = "限制返回的记录数") @RequestParam(required = false, defaultValue = "10") Integer limit) {

        try {
            // 构建查询语句
            String query;
            if ("4xx".equals(type)) {
                query = String.format("_stream: {stream=\"NGINX_ACCESS\"} AND _time:%s AND message.status:~\"^4\" | stats by (message.status) count(*) cnt |sort by (cnt desc)",
                        timeRange);
            } else if ("5xx".equals(type)) {
                query = String.format("_stream: {stream=\"NGINX_ACCESS\"} AND _time:%s AND message.status:~\"^5\" | stats by (message.status) count(*) cnt |sort by (cnt desc)",
                        timeRange);
            } else {
                return AjaxResult.error("不支持的统计类型: " + type);
            }

            // 调用VictoriaLogs服务查询数据，指定使用 vmlog2 实例
            String result = victoriaLogsService.queryLogs(query,  limit,  null, "vmlog2");

            // 解析结果
            List<Map<String, Object>> parsedResult = new ArrayList<>();
            if (result != null && !result.isEmpty()) {
                String[] lines = result.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;

                    try {
                        JSONObject jsonObj = JSON.parseObject(line);
                        Map<String, Object> item = new HashMap<>();

                        String status = jsonObj.getString("message.status");
                        String count = jsonObj.getString("cnt");

                        if (status != null && count != null) {
                            item.put("fieldValue", status);
                            item.put("count", Integer.parseInt(count));
                            parsedResult.add(item);
                        }
                    } catch (Exception e) {
                        // 忽略无法解析的行
                        continue;
                    }
                }
            }

            // 限制返回的记录数
            if (limit != null && parsedResult.size() > limit) {
                parsedResult = parsedResult.subList(0, limit);
            }

            return AjaxResult.success(parsedResult);
        } catch (Exception e) {
            return AjaxResult.error("获取错误统计失败: " + e.getMessage());
        }
    }


}
