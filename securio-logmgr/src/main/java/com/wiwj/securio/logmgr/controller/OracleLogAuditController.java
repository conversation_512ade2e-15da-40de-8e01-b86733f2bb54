package com.wiwj.securio.logmgr.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * Oracle 日志审计控制器
 */
@Api(tags = "Oracle 日志审计")
@RestController
@RequestMapping("/opslog/oracle")
public class OracleLogAuditController {

    private static final Logger logger = LoggerFactory.getLogger(OracleLogAuditController.class);

    @Autowired
    private VictoriaLogsService victoriaLogsService;


    /**
     * 获取Oracle日志时间序列统计数据
     */
    @ApiOperation(value = "获取Oracle日志时间序列统计数据", notes = "获取Oracle日志的时间序列统计数据，用于生成曲线图")
    @GetMapping("/time-series-stats")
    public AjaxResult getTimeSeriesStats(
            @ApiParam(value = "统计类型", required = false) @RequestParam(required = false, defaultValue = "all") String type,
            @ApiParam(value = "统计维度", required = true) @RequestParam String dimension,
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "14d") String timeRange,
            @ApiParam(value = "时间步长") @RequestParam(required = false, defaultValue = "1h") String step) {

        try {
            // 根据统计维度选择分组字段
            String fieldName;
            switch (dimension) {
                case "server_ip":
                    fieldName = "after.DBIP";
                    break;
                case "db_name":
                    fieldName = "after.SERVERNAME";
                    break;
                case "client_host":
                    fieldName = "after.USERHOST";
                    break;
                case "client_user":
                    fieldName = "after.USERNAME";
                    break;
                default:
                    return AjaxResult.error("不支持的统计维度: " + dimension);
            }

            // 构建查询语句 - 始终使用全部日志类型
            String query = String.format("_time:%s and stream:\"AUDITLOG_ORACLE_USER\" | stats by (%s) count(*) cnt | sort by (cnt desc)",
                                      timeRange, fieldName);
            logger.info("构建查询语句: " + query);

            // 调用VictoriaLogsService的getTimeSeriesStats方法获取并处理时间序列数据
            JSONObject jsonResult = victoriaLogsService.getTimeSeriesStats(query, null, null, step, null, null);

            return AjaxResult.success(jsonResult);
        } catch (Exception e) {
            logger.error("获取Oracle日志时间序列统计数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取Oracle日志时间序列统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析时间序列数据
     * @deprecated 使用 VictoriaLogsService.getTimeSeriesStats 方法替代
     */
    @Deprecated
    private List<List<Object>> parseTimeSeriesData(String data) {
        List<List<Object>> result = new ArrayList<>();
        if (data == null || data.isEmpty()) {
            return result;
        }

        try {
            String[] lines = data.split("\n");
            for (String line : lines) {
                if (line.trim().isEmpty()) continue;

                JSONObject jsonObj = JSON.parseObject(line);
                long timestamp = jsonObj.getLongValue("_time");
                double value = jsonObj.getDoubleValue("value");

                List<Object> point = new ArrayList<>();
                point.add(timestamp * 1000); // 转换为毫秒
                point.add(value);
                result.add(point);
            }

            // 按时间戳排序
            result.sort(Comparator.comparing(point -> (Long) point.get(0)));

            return result;
        } catch (Exception e) {
            logger.error("解析时间序列数据失败: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取Oracle日志数据流向统计数据（桑基图数据）
     */
    @ApiOperation(value = "获取Oracle日志数据流向统计数据", notes = "获取客户端主机名到数据库服务器 IP 再到数据库服务名的数据流向统计，用于生成桑基图")
    @GetMapping("/data-flow-stats")
    public AjaxResult getDataFlowStats(
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "1d") String timeRange,
            @ApiParam(value = "限制返回的数据库数量") @RequestParam(required = false, defaultValue = "10") Integer dbLimit,
            @ApiParam(value = "限制返回的客户端数量") @RequestParam(required = false, defaultValue = "10") Integer clientLimit) {

        try {
            // 第一步：获取 top N 访问的数据库服务器 IP
            String dbQuery = String.format("_time:%s and stream:\"AUDITLOG_ORACLE_USER\" | stats by (after.DBIP) count() cnt | sort by (cnt desc) | limit %d",
                                         timeRange, dbLimit);
            logger.info("查询热门数据库服务器: " + dbQuery);
            String dbResult = victoriaLogsService.queryLogs(dbQuery, dbLimit, null);

            // 解析数据库服务器结果
            List<Map<String, Object>> dbServers = new ArrayList<>();
            if (dbResult != null && !dbResult.isEmpty()) {
                String[] lines = dbResult.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;

                    JSONObject jsonObj = JSON.parseObject(line);
                    String dbIp = jsonObj.getString("after.DBIP");
                    int count = jsonObj.getIntValue("cnt");

                    if (dbIp != null && !dbIp.isEmpty()) {
                        Map<String, Object> db = new HashMap<>();
                        db.put("ip", dbIp);
                        db.put("count", count);
                        dbServers.add(db);
                    }
                }
            }

            // 第二步：对每个数据库服务器，获取客户端到这个数据库服务器的请求
            List<Map<String, Object>> clientLinks = new ArrayList<>();
            for (Map<String, Object> db : dbServers) {
                String dbIp = (String) db.get("ip");

                String clientQuery = String.format("_time:%s and stream:\"AUDITLOG_ORACLE_USER\" and after.DBIP:\"%s\" | stats by (after.USERHOST) count() cnt | sort by (cnt desc) | limit %d",
                                                timeRange, dbIp, clientLimit);
                logger.info("查询数据库服务器 " + dbIp + " 的客户端: " + clientQuery);
                String clientResult = victoriaLogsService.queryLogs(clientQuery, clientLimit, null);

                // 解析客户端结果
                if (clientResult != null && !clientResult.isEmpty()) {
                    String[] lines = clientResult.split("\n");
                    for (String line : lines) {
                        if (line.trim().isEmpty()) continue;

                        JSONObject jsonObj = JSON.parseObject(line);
                        String clientHost = jsonObj.getString("after.USERHOST");
                        int count = jsonObj.getIntValue("cnt");

                        if (clientHost != null && !clientHost.isEmpty()) {
                            Map<String, Object> link = new HashMap<>();
                            link.put("source", clientHost);
                            link.put("target", dbIp);
                            link.put("value", count);
                            clientLinks.add(link);
                        }
                    }
                }
            }

            // 第三步：对每个数据库服务器，获取数据库服务器到数据库服务名的请求
            List<Map<String, Object>> dbServiceLinks = new ArrayList<>();
            List<Map<String, Object>> dbServices = new ArrayList<>();
            for (Map<String, Object> db : dbServers) {
                String dbIp = (String) db.get("ip");

                String serviceQuery = String.format("_time:%s and stream:\"AUDITLOG_ORACLE_USER\" and after.DBIP:\"%s\" | stats by (after.SERVERNAME) count() cnt | sort by (cnt desc) | limit %d",
                                                timeRange, dbIp, dbLimit);
                logger.info("查询数据库服务器 " + dbIp + " 的数据库服务: " + serviceQuery);
                String serviceResult = victoriaLogsService.queryLogs(serviceQuery, dbLimit, null);

                // 解析数据库服务结果
                if (serviceResult != null && !serviceResult.isEmpty()) {
                    String[] lines = serviceResult.split("\n");
                    for (String line : lines) {
                        if (line.trim().isEmpty()) continue;

                        JSONObject jsonObj = JSON.parseObject(line);
                        String serviceName = jsonObj.getString("after.SERVERNAME");
                        int count = jsonObj.getIntValue("cnt");

                        if (serviceName != null && !serviceName.isEmpty()) {
                            // 添加数据库服务
                            Map<String, Object> service = new HashMap<>();
                            service.put("name", serviceName);
                            service.put("count", count);
                            dbServices.add(service);

                            // 添加数据库服务器到数据库服务的连接
                            Map<String, Object> link = new HashMap<>();
                            link.put("source", dbIp);
                            link.put("target", serviceName);
                            link.put("value", count);
                            dbServiceLinks.add(link);
                        }
                    }
                }
            }

            // 构建桑基图数据
            Map<String, Object> result = new HashMap<>();
            result.put("dbServers", dbServers);
            result.put("dbServices", dbServices);
            result.put("clientLinks", clientLinks);
            result.put("dbServiceLinks", dbServiceLinks);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取数据流向统计数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取数据流向统计数据失败: " + e.getMessage());
        }
    }
}
