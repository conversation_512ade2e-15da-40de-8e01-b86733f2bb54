package com.wiwj.securio.logmgr.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.logmgr.service.VictoriaLogsStatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * VictoriaLogs 集群节点指标控制器
 *
 * <AUTHOR>
 */
@Api(tags = "VictoriaLogs 集群节点指标", description = "VictoriaLogs 集群节点指标相关接口")
@RestController
@RequestMapping("/opslog/victoria/metrics")
public class VictoriaLogsStatController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(VictoriaLogsStatController.class);

    @Autowired
    private VictoriaLogsStatService victoriaLogsStatService;

    /**
     * 获取VictoriaLogs集群节点列表
     */
    @ApiOperation(value = "获取VictoriaLogs集群节点列表", notes = "获取所有可用的VictoriaLogs集群节点")
    @GetMapping("/nodes")
    public AjaxResult getNodes() {
        try {
            logger.info("获取VictoriaLogs集群节点列表");
            return AjaxResult.success(victoriaLogsStatService.getVictoriaLogsNodes());
        } catch (Exception e) {
            logger.error("获取VictoriaLogs集群节点列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取VictoriaLogs集群节点列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定节点的指标数据
     */
    @ApiOperation(value = "获取指定节点的指标数据", notes = "获取指定VictoriaLogs节点的详细指标数据")
    @GetMapping("/node")
    public AjaxResult getNodeMetrics(
            @ApiParam(value = "节点URL", required = true) @RequestParam String nodeUrl) {
        try {
            logger.info("获取节点指标数据: nodeUrl={}", nodeUrl);
            return AjaxResult.success(victoriaLogsStatService.getNodeMetrics(nodeUrl));
        } catch (Exception e) {
            logger.error("获取节点指标数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取节点指标数据失败: " + e.getMessage());
        }
    }
}
