package com.wiwj.securio.logmgr.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网宿WAF攻击日志审计控制器
 */
@Api(tags = "网宿WAF攻击日志审计")
@RestController
@RequestMapping("/opslog/wangsu-attack")
public class WangsuAttackLogController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WangsuAttackLogController.class);

    @Autowired
    private VictoriaLogsService victoriaLogsService;

    /**
     * 获取网宿WAF攻击日志统计数据
     */
    @ApiOperation(value = "获取网宿WAF攻击日志统计数据", notes = "获取指定时间范围内的攻击统计指标")
    @GetMapping("/stats")
    public AjaxResult getWangsuAttackStats(
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "1h") String timeRange) {

        try {
            logger.info("执行网宿WAF攻击日志统计查询: timeRange={}", timeRange);

            // 构建统计查询语句
            String baseQuery = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ATTACK\"", timeRange);
            
            // 并行执行多个统计查询
            Map<String, Object> stats = new HashMap<>();
            
            // 1. 总攻击次数
            String totalQuery = baseQuery + " | stats count() as total";
            String totalResult = victoriaLogsService.queryLogs(totalQuery, 1, "60s", "vmlog2");
            stats.put("totalAttacks", parseCountResult(totalResult));
            
            // 2. 独立攻击IP数
            String ipQuery = baseQuery + " | stats count_uniq(ip) as unique_ips";
            String ipResult = victoriaLogsService.queryLogs(ipQuery, 1, "60s", "vmlog2");
            stats.put("uniqueIps", parseCountResult(ipResult));
            
            // 3. 攻击类型数量
            String typeQuery = baseQuery + " | stats count_uniq(attack_type) as attack_types";
            String typeResult = victoriaLogsService.queryLogs(typeQuery, 1, "60s", "vmlog2");
            stats.put("attackTypes", parseCountResult(typeResult));
            
            // 4. 被攻击域名数
            String hostQuery = baseQuery + " | stats count_uniq(host) as hosts";
            String hostResult = victoriaLogsService.queryLogs(hostQuery, 1, "60s", "vmlog2");
            stats.put("attackedHosts", parseCountResult(hostResult));
            
            // 5. 高危攻击数量（假设act=1为高危）
            String highRiskQuery = baseQuery + " and act:\"1\" | stats count() as high_risk";
            String highRiskResult = victoriaLogsService.queryLogs(highRiskQuery, 1, "60s", "vmlog2");
            stats.put("highRiskAttacks", parseCountResult(highRiskResult));

            logger.info("网宿WAF攻击日志统计查询完成");
            return AjaxResult.success(stats);

        } catch (Exception e) {
            logger.error("获取网宿WAF攻击日志统计数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取网宿WAF攻击日志统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取网宿WAF攻击日志图表数据
     */
    @ApiOperation(value = "获取网宿WAF攻击日志图表数据", notes = "获取各维度的分布统计数据")
    @GetMapping("/charts")
    public AjaxResult getWangsuAttackCharts(
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "1h") String timeRange,
            @ApiParam(value = "限制返回的记录数") @RequestParam(required = false, defaultValue = "1000") Integer limit) {

        try {
            logger.info("执行网宿WAF攻击日志图表查询: timeRange={}, limit={}", timeRange, limit);

            // 构建基础查询语句
            String baseQuery = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ATTACK\"", timeRange);
            
            // 调用VictoriaLogs facets接口获取多维度数据
            String facetsResult = victoriaLogsService.facets(baseQuery, limit, "vmlog2");
            
            if (facetsResult != null && !facetsResult.trim().isEmpty()) {

                 // 解析facets结果
                 JSONObject jsonResult = JSON.parseObject(facetsResult);
                
                 logger.info("网宿WAF攻击日志图表查询完成: {}, 限制: {}", timeRange, limit);
                 
                 return AjaxResult.success(jsonResult);

            } else {
                return AjaxResult.error("获取攻击日志图表数据失败：返回结果为空");
            }

        } catch (Exception e) {
            logger.error("获取网宿WAF攻击日志图表数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取网宿WAF攻击日志图表数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取攻击趋势时间序列数据
     */
    @ApiOperation(value = "获取攻击趋势时间序列数据", notes = "获取指定时间范围内的攻击趋势数据")
    @GetMapping("/timeseries")
    public AjaxResult getAttackTimeSeries(
            @ApiParam(value = "开始时间") @RequestParam String start,
            @ApiParam(value = "结束时间") @RequestParam String end,
            @ApiParam(value = "时间步长") @RequestParam(required = false, defaultValue = "1h") String step,
            @ApiParam(value = "统计指标类型") @RequestParam(required = false, defaultValue = "count") String metric,
            @ApiParam(value = "攻击类型过滤") @RequestParam(required = false) String attackType) {

        try {
            logger.info("执行攻击趋势时间序列查询: start={}, end={}, step={}, metric={}, attackType={}", 
                       start, end, step, metric, attackType);

            // 构建查询语句
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("stream:\"SECURIO_WANGSU_WAF_ATTACK\"");
            
            if (attackType != null && !attackType.trim().isEmpty()) {
                queryBuilder.append(" and attack_type:\"").append(attackType).append("\"");
            }
            
            // 根据统计指标类型构建不同的查询
            switch (metric) {
                case "count":
                    queryBuilder.append(" | stats count()");
                    break;
                case "unique_ips":
                    queryBuilder.append(" | stats count_uniq(ip)");
                    break;
                case "unique_hosts":
                    queryBuilder.append(" | stats count_uniq(host)");
                    break;
                default:
                    queryBuilder.append(" | stats count()");
            }

            String query = queryBuilder.toString();
            
            // 调用VictoriaLogs时间序列查询接口
            String result = victoriaLogsService.queryStatsRange(query, start, end, step, null, null, "vmlog2");
            
            if (result != null && !result.trim().isEmpty()) {
                logger.info("攻击趋势时间序列查询完成");
                return AjaxResult.success(JSON.parse(result));
            } else {
                return AjaxResult.error("获取攻击趋势数据失败：返回结果为空");
            }

        } catch (Exception e) {
            logger.error("获取攻击趋势时间序列数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取攻击趋势时间序列数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取Top攻击IP排名
     */
    @ApiOperation(value = "获取Top攻击IP排名", notes = "获取指定时间范围内攻击次数最多的IP地址")
    @GetMapping("/top-ips")
    public AjaxResult getTopAttackIps(
            @ApiParam(value = "时间范围") @RequestParam(required = false, defaultValue = "1h") String timeRange,
            @ApiParam(value = "限制返回的记录数") @RequestParam(required = false, defaultValue = "20") Integer limit) {

        try {
            // 构建查询语句
            String query = String.format("_time:%s and stream:\"SECURIO_WANGSU_WAF_ATTACK\" | stats by (ip, ip_city_cn, ip_province_cn) count() cnt | sort by (cnt desc) | limit %d", 
                                        timeRange, limit);
            
            logger.info("执行Top攻击IP查询: timeRange={}, limit={}", timeRange, limit);
            
            // 调用VictoriaLogs查询接口
            String result = victoriaLogsService.queryLogs(query, limit, "60s", "vmlog2");
            
            if (result != null && !result.trim().isEmpty()) {
                // 解析查询结果
                List<Map<String, Object>> rankings = parseIpRankingResult(result);
                
                logger.info("Top攻击IP查询完成，返回{}条记录", rankings.size());
                
                return AjaxResult.success(rankings);
            } else {
                return AjaxResult.error("获取Top攻击IP数据失败：返回结果为空");
            }

        } catch (Exception e) {
            logger.error("获取Top攻击IP数据失败: " + e.getMessage(), e);
            return AjaxResult.error("获取Top攻击IP数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析计数查询结果
     */
    private long parseCountResult(String result) {
        try {
            if (result == null || result.trim().isEmpty()) {
                return 0L;
            }
            
            String[] lines = result.split("\n");
            for (String line : lines) {
                if (line.trim().isEmpty()) continue;
                
                JSONObject jsonLine = JSON.parseObject(line);
                if (jsonLine.containsKey("total")) {
                    return jsonLine.getLong("total");
                } else if (jsonLine.containsKey("unique_ips")) {
                    return jsonLine.getLong("unique_ips");
                } else if (jsonLine.containsKey("attack_types")) {
                    return jsonLine.getLong("attack_types");
                } else if (jsonLine.containsKey("hosts")) {
                    return jsonLine.getLong("hosts");
                } else if (jsonLine.containsKey("high_risk")) {
                    return jsonLine.getLong("high_risk");
                }
            }
        } catch (Exception e) {
            logger.warn("解析计数结果失败: {}", result, e);
        }
        return 0L;
    }

    /**
     * 解析IP排名查询结果
     */
    private List<Map<String, Object>> parseIpRankingResult(String result) {
        List<Map<String, Object>> rankings = new ArrayList<>();
        
        try {
            // 按行分割结果
            String[] lines = result.split("\n");
            
            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();
                if (line.isEmpty()) continue;
                
                try {
                    // 解析JSON格式的行数据
                    JSONObject jsonLine = JSON.parseObject(line);
                    
                    Map<String, Object> ranking = new HashMap<>();
                    ranking.put("rank", i + 1); // 排名从1开始
                    ranking.put("ip", jsonLine.getString("ip"));
                    ranking.put("city", jsonLine.getString("ip_city_cn"));
                    ranking.put("province", jsonLine.getString("ip_province_cn"));
                    ranking.put("count", jsonLine.getLong("cnt"));
                    
                    rankings.add(ranking);
                } catch (Exception e) {
                    logger.warn("解析IP排名数据行失败: {}", line, e);
                }
            }
            
        } catch (Exception e) {
            logger.error("解析IP排名结果失败", e);
        }
        
        return rankings;
    }
} 