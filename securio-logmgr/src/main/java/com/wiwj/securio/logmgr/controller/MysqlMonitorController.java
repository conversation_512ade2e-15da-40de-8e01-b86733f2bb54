package com.wiwj.securio.logmgr.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import com.wiwj.securio.logmgr.util.TimeFormatUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * MySQL 监控控制器
 * 基于 SECURIO_FALCO_EVENTS 数据源进行 MySQL 连接行为监控
 *
 * <AUTHOR>
 */
@Api(tags = "MySQL 监控管理", description = "MySQL 连接行为监控相关接口")
@RestController
@RequestMapping("/opslog/mysql-monitor")
public class MysqlMonitorController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MysqlMonitorController.class);

    @Autowired
    private VictoriaLogsService victoriaLogsService;

    /**
     * 获取MySQL连接桑基图数据
     */
    @ApiOperation(value = "获取MySQL连接桑基图数据", notes = "获取MySQL连接流向统计数据，用于生成桑基图")
    @GetMapping("/sankey-data")
    public AjaxResult getSankeyData(
            @ApiParam(value = "连接规则", required = false, defaultValue = "mysql_connection_established") 
            @RequestParam(required = false, defaultValue = "mysql_connection_established") String rule,
            @ApiParam(value = "数据库IP", required = false) 
            @RequestParam(required = false) String databaseIp,
            @ApiParam(value = "时间范围", required = false, defaultValue = "1d") 
            @RequestParam(required = false, defaultValue = "1d") String timeRange,
            @ApiParam(value = "开始时间", required = false) 
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间", required = false) 
            @RequestParam(required = false) String endTime,
            @ApiParam(value = "限制返回的记录数", required = false, defaultValue = "10") 
            @RequestParam(required = false, defaultValue = "10") Integer limit) {

        try {
            String timeQuery = buildTimeQuery(startTime, endTime, timeRange);
            String baseQueryPart = " and stream:\"SECURIO_FALCO_EVENTS\" and rule:\"" + rule + "\"";

            String inboundResult = "";
            if (databaseIp != null && !databaseIp.trim().isEmpty()) {
                // 入站查询: 其他IP -> 指定IP
                String inboundQuery = timeQuery + baseQueryPart +
                        " and agent_hostip:\"" + databaseIp + "\"" +
                        " and not src_ip:\"" + databaseIp + "\"" + // 排除自己到自己的连接
                        " | stats by (src_ip) count() cnt | sort by (cnt desc) | limit " + limit;
                logger.info("桑基图入站查询语句: {}", inboundQuery);
                inboundResult = victoriaLogsService.queryLogs(inboundQuery, limit, null);
            }

            String outboundResult = "";
            if (databaseIp != null && !databaseIp.trim().isEmpty()) {
                // 出站查询: 指定IP -> 其他IP
                // 注意：我们使用 agent_hostip作为目标IP，因为它代表了Falco agent所在的机器
                String outboundQuery = timeQuery + baseQueryPart +
                        " and src_ip:\"" + databaseIp + "\"" +
                        " | stats by (dst_ip) count() cnt | sort by (cnt desc) | limit " + limit;
                logger.info("桑基图出站查询语句: {}", outboundQuery);
                outboundResult = victoriaLogsService.queryLogs(outboundQuery, limit, null);
            }
            
            // 解析并合并入站和出站数据
            Map<String, Object> sankeyData = parseSankeyData(inboundResult, outboundResult, databaseIp);

            return AjaxResult.success(sankeyData);
        } catch (Exception e) {
            logger.error("获取MySQL连接桑基图数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取桑基图数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取MySQL连接时间序列数据
     */
    @ApiOperation(value = "获取MySQL连接时间序列数据", notes = "获取MySQL连接的时间序列统计数据，用于生成时间趋势图")
    @GetMapping("/time-series-data")
    public AjaxResult getTimeSeriesData(
            @ApiParam(value = "连接规则", required = false, defaultValue = "mysql_connection_established") 
            @RequestParam(required = false, defaultValue = "mysql_connection_established") String rule,
            @ApiParam(value = "数据库IP", required = false) 
            @RequestParam(required = false) String databaseIp,
            @ApiParam(value = "分组字段", required = false, defaultValue = "src_ip") 
            @RequestParam(required = false, defaultValue = "src_ip") String groupBy,
            @ApiParam(value = "时间范围", required = false, defaultValue = "10d") 
            @RequestParam(required = false, defaultValue = "10d") String timeRange,
            @ApiParam(value = "时间步长", required = false, defaultValue = "1h") 
            @RequestParam(required = false, defaultValue = "1h") String step,
            @ApiParam(value = "开始时间", required = false) 
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间", required = false) 
            @RequestParam(required = false) String endTime) {

        try {
            // 构建查询条件
            StringBuilder queryBuilder = new StringBuilder();
            
            // 构建时间查询条件
            String timeQuery = buildTimeQuery(startTime, endTime, timeRange);
            queryBuilder.append(timeQuery);
            queryBuilder.append(" and stream:\"SECURIO_FALCO_EVENTS\" and rule:\"").append(rule).append("\"");
            
            // 如果指定了数据库IP，添加到查询条件
            if (databaseIp != null && !databaseIp.trim().isEmpty()) {
                queryBuilder.append(" and agent_hostip:\"").append(databaseIp).append("\"");
                queryBuilder.append(" and not src_ip:\"").append(databaseIp).append("\"");
            }
            
            queryBuilder.append(" | stats by (").append(groupBy).append(") count(*)");
            
            String query = queryBuilder.toString();
            logger.info("时间序列查询语句: {}", query);

            // 调用VictoriaLogsService的时间序列查询方法
            JSONObject jsonResult = victoriaLogsService.getTimeSeriesStats(query, startTime, endTime, step, null, null);

            return AjaxResult.success(jsonResult);
        } catch (Exception e) {
            logger.error("获取MySQL连接时间序列数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取时间序列数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取MySQL连接统计概览
     */
    @ApiOperation(value = "获取MySQL连接统计概览", notes = "获取MySQL连接的基本统计信息")
    @GetMapping("/overview-stats")
    public AjaxResult getOverviewStats(
            @ApiParam(value = "连接规则", required = false, defaultValue = "mysql_connection_established") 
            @RequestParam(required = false, defaultValue = "mysql_connection_established") String rule,
            @ApiParam(value = "数据库IP", required = false) 
            @RequestParam(required = false) String databaseIp,
            @ApiParam(value = "时间范围", required = false, defaultValue = "1d") 
            @RequestParam(required = false, defaultValue = "1d") String timeRange,
            @ApiParam(value = "开始时间", required = false) 
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间", required = false) 
            @RequestParam(required = false) String endTime) {

        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 1. 获取总连接数
            String totalQuery = buildBaseQuery(rule, databaseIp, startTime, endTime, timeRange) + " | stats count() total";
            String totalResult = victoriaLogsService.queryLogs(totalQuery, 1, null);
            Long totalConnections = parseTotalCount(totalResult);
            stats.put("totalConnections", totalConnections);

            // 2. 获取唯一来源IP数
            String uniqueSrcQuery = buildBaseQuery(rule, databaseIp, startTime, endTime, timeRange) + " | stats by (src_ip) count() cnt | stats count() unique_src";
            String uniqueSrcResult = victoriaLogsService.queryLogs(uniqueSrcQuery, 1, null);
            Long uniqueSourceIps = parseTotalCount(uniqueSrcResult);
            stats.put("uniqueSourceIps", uniqueSourceIps);

            // 3. 获取唯一数据库IP数
            String uniqueDbQuery = buildBaseQuery(rule, databaseIp, startTime, endTime, timeRange) + " | stats by (agent_hostip) count() cnt | stats count() unique_db";
            String uniqueDbResult = victoriaLogsService.queryLogs(uniqueDbQuery, 1, null);
            Long uniqueDatabaseIps = parseTotalCount(uniqueDbResult);
            stats.put("uniqueDatabaseIps", uniqueDatabaseIps);

            // 4. 获取TOP来源IP
            String topSrcQuery = buildBaseQuery(rule, databaseIp, startTime, endTime, timeRange) + " | stats by (src_ip) count() cnt | sort by (cnt desc) | limit 5";
            String topSrcResult = victoriaLogsService.queryLogs(topSrcQuery, 5, null);
            List<Map<String, Object>> topSourceIps = parseTopResults(topSrcResult, "src_ip");
            stats.put("topSourceIps", topSourceIps);

            // 5. 获取TOP数据库IP（如果没有指定特定数据库IP）
            if (databaseIp == null || databaseIp.trim().isEmpty()) {
                String topDbQuery = buildBaseQuery(rule, null, startTime, endTime, timeRange) + " | stats by (agent_hostip) count() cnt | sort by (cnt desc) | limit 5";
                String topDbResult = victoriaLogsService.queryLogs(topDbQuery, 5, null);
                List<Map<String, Object>> topDatabaseIps = parseTopResults(topDbResult, "agent_hostip");
                stats.put("topDatabaseIps", topDatabaseIps);
            }

            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取MySQL连接统计概览失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取统计概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取MySQL流量时间序列数据（入站和出站）
     */
    @ApiOperation(value = "获取MySQL流量时间序列数据", notes = "获取MySQL客户端请求的入站和出站流量统计数据")
    @GetMapping("/traffic-data")
    public AjaxResult getTrafficData(
            @ApiParam(value = "数据库IP", required = true) 
            @RequestParam String databaseIp,
            @ApiParam(value = "时间范围", required = false, defaultValue = "1d") 
            @RequestParam(required = false, defaultValue = "1d") String timeRange,
            @ApiParam(value = "时间步长", required = false, defaultValue = "1h") 
            @RequestParam(required = false, defaultValue = "1h") String step,
            @ApiParam(value = "开始时间", required = false) 
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间", required = false) 
            @RequestParam(required = false) String endTime) {

        try {
            if (databaseIp == null || databaseIp.trim().isEmpty()) {
                return AjaxResult.error("数据库IP不能为空");
            }

            String timeQuery = buildTimeQuery(startTime, endTime, timeRange);
            String baseQueryPart = " and stream:\"SECURIO_FALCO_EVENTS\" and rule:\"mysql_client_request\"";

            // 入站流量查询
            String inboundQuery = timeQuery + baseQueryPart +
                    " and src_ip:\"" + databaseIp + "\"" +
                    " | stats sum(message.output_fields.evt.arg.res) ressize";
            logger.info("入站流量查询语句: {}", inboundQuery);

            // 出站流量查询
            String outboundQuery = timeQuery + baseQueryPart +
                    " and dst_ip:\"" + databaseIp + "\"" +
                    " | stats sum(message.output_fields.evt.arg.res) ressize";
            logger.info("出站流量查询语句: {}", outboundQuery);

            // 调用VictoriaLogsService的时间序列查询方法
            JSONObject inboundResult = victoriaLogsService.getTimeSeriesStats(inboundQuery, startTime, endTime, step, null, null);
            JSONObject outboundResult = victoriaLogsService.getTimeSeriesStats(outboundQuery, startTime, endTime, step, null, null);

            // 构建返回数据
            Map<String, Object> trafficData = new HashMap<>();
            trafficData.put("inbound", inboundResult);
            trafficData.put("outbound", outboundResult);

            return AjaxResult.success(trafficData);
        } catch (Exception e) {
            logger.error("获取MySQL流量数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取流量数据失败: " + e.getMessage());
        }
    }

    /**
     * 构建时间查询条件
     * 使用 TimeFormatUtil 工具类
     */
    private String buildTimeQuery(String startTime, String endTime, String timeRange) {
        return TimeFormatUtil.buildTimeQuery(startTime, endTime, timeRange);
    }

    /**
     * 构建基础查询条件
     */
    private String buildBaseQuery(String rule, String databaseIp, String startTime, String endTime, String timeRange) {
        StringBuilder queryBuilder = new StringBuilder();
        
        // 构建时间查询条件
        String timeQuery = buildTimeQuery(startTime, endTime, timeRange);
        queryBuilder.append(timeQuery);
        queryBuilder.append(" and stream:\"SECURIO_FALCO_EVENTS\" and rule:\"").append(rule).append("\"");
        
        if (databaseIp != null && !databaseIp.trim().isEmpty()) {
            queryBuilder.append(" and agent_hostip:\"").append(databaseIp).append("\"");
        }
        
        return queryBuilder.toString();
    }

    /**
     * 解析桑基图数据（包含入站和出站）
     */
    private Map<String, Object> parseSankeyData(String inboundData, String outboundData, String centralIp) {
        Map<String, Object> sankeyData = new HashMap<>();
        List<Map<String, Object>> nodes = new ArrayList<>();
        List<Map<String, Object>> links = new ArrayList<>();
        java.util.Set<String> nodeNames = new java.util.HashSet<>();

        try {
            if (centralIp == null || centralIp.trim().isEmpty()) {
                sankeyData.put("nodes", nodes);
                sankeyData.put("links", links);
                return sankeyData;
            }

            // 添加中心节点
            Map<String, Object> centralNode = new HashMap<>();
            centralNode.put("name", centralIp);
            centralNode.put("type", "target"); // 在图中，此节点既是源也是目标
            nodes.add(centralNode);
            nodeNames.add(centralIp);

            // 处理入站数据 (source -> centralIp)
            if (inboundData != null && !inboundData.trim().isEmpty()) {
                String[] lines = inboundData.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;
                    try {
                        JSONObject json = JSON.parseObject(line.trim());
                        String srcIp = json.getString("src_ip");
                        Long count = json.getLong("cnt");
                        if (srcIp != null && count != null) {
                            if (nodeNames.add(srcIp)) { // 如果是新节点，则添加
                                Map<String, Object> sourceNode = new HashMap<>();
                                sourceNode.put("name", srcIp);
                                sourceNode.put("type", "source");
                                nodes.add(sourceNode);
                            }
                            Map<String, Object> link = new HashMap<>();
                            link.put("source", srcIp);
                            link.put("target", centralIp);
                            link.put("value", count);
                            links.add(link);
                        }
                    } catch (Exception e) {
                        logger.warn("解析桑基图入站数据行失败: {}, 错误: {}", line, e.getMessage());
                    }
                }
            }

            // 处理出站数据 (centralIp -> target)
            if (outboundData != null && !outboundData.trim().isEmpty()) {
                String[] lines = outboundData.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;
                    try {
                        JSONObject json = JSON.parseObject(line.trim());
                        String destIp = json.getString("dst_ip");
                        Long count = json.getLong("cnt");
                        if (destIp != null && count != null) {
                            if (nodeNames.add(destIp)) { // 如果是新节点，则添加
                                Map<String, Object> destNode = new HashMap<>();
                                destNode.put("name", destIp);
                                destNode.put("type", "target");
                                nodes.add(destNode);
                            }
                            Map<String, Object> link = new HashMap<>();
                            link.put("source", centralIp);
                            link.put("target", destIp);
                            link.put("value", count);
                            links.add(link);
                        }
                    } catch (Exception e) {
                        logger.warn("解析桑基图出站数据行失败: {}, 错误: {}", line, e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析桑基图数据失败: {}", e.getMessage(), e);
        }

        sankeyData.put("nodes", nodes);
        sankeyData.put("links", links);
        return sankeyData;
    }

    /**
     * 解析总数统计结果
     */
    private Long parseTotalCount(String rawData) {
        try {
            if (rawData != null && !rawData.trim().isEmpty()) {
                String[] lines = rawData.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;
                    
                    JSONObject json = JSON.parseObject(line.trim());
                    // 尝试不同的字段名
                    if (json.containsKey("total")) {
                        return json.getLong("total");
                    } else if (json.containsKey("unique_src")) {
                        return json.getLong("unique_src");
                    } else if (json.containsKey("unique_db")) {
                        return json.getLong("unique_db");
                    } else if (json.containsKey("cnt")) {
                        return json.getLong("cnt");
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("解析总数统计失败: {}", e.getMessage());
        }
        return 0L;
    }

    /**
     * 解析TOP结果
     */
    private List<Map<String, Object>> parseTopResults(String rawData, String fieldName) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try {
            if (rawData != null && !rawData.trim().isEmpty()) {
                String[] lines = rawData.split("\n");
                for (String line : lines) {
                    if (line.trim().isEmpty()) continue;
                    
                    try {
                        JSONObject json = JSON.parseObject(line.trim());
                        String fieldValue = json.getString(fieldName);
                        String countStr = json.getString("cnt");
                        
                        if (fieldValue != null && countStr != null) {
                            Map<String, Object> item = new HashMap<>();
                            item.put("name", fieldValue);
                            item.put("value", Long.parseLong(countStr));
                            results.add(item);
                        }
                    } catch (Exception e) {
                        logger.warn("解析TOP结果行失败: {}, 错误: {}", line, e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析TOP结果失败: {}", e.getMessage(), e);
        }
        
        return results;
    }
} 