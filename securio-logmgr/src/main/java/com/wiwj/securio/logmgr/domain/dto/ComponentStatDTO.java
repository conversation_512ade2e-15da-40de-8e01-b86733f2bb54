package com.wiwj.securio.logmgr.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 组件统计数据传输对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "ComponentStatDTO", description = "组件统计数据")
public class ComponentStatDTO {
    
    /** 组件名称 */
    @ApiModelProperty(value = "组件名称", example = "*processor.JSONProcessor")
    private String componentName;
    
    /** 组件类型 */
    @ApiModelProperty(value = "组件类型", example = "processor")
    private String componentType;
    
    /** 管道名称 */
    @ApiModelProperty(value = "管道名称", example = "zeek")
    private String pipeline;
    
    /** 接收消息数 */
    @ApiModelProperty(value = "接收消息数", example = "1000")
    private long received;
    
    /** 处理消息数 */
    @ApiModelProperty(value = "处理消息数", example = "1000")
    private long processed;
    
    /** 错误数 */
    @ApiModelProperty(value = "错误数", example = "0")
    private long errors;
    
    /** 丢弃消息数 */
    @ApiModelProperty(value = "丢弃消息数", example = "0")
    private long dropped;
    
    /** 处理时间（纳秒） */
    @ApiModelProperty(value = "处理时间（纳秒）", example = "1000000")
    private long processingTime;
    
    /** 平均处理时间（纳秒） */
    @ApiModelProperty(value = "平均处理时间（纳秒）", example = "1000")
    private double avgProcessingTime;
    
    /** 健康状态 */
    @ApiModelProperty(value = "健康状态", example = "正常")
    private String healthStatus;

    public String getComponentName() {
        return componentName;
    }

    public void setComponentName(String componentName) {
        this.componentName = componentName;
    }

    public String getComponentType() {
        return componentType;
    }

    public void setComponentType(String componentType) {
        this.componentType = componentType;
    }

    public String getPipeline() {
        return pipeline;
    }

    public void setPipeline(String pipeline) {
        this.pipeline = pipeline;
    }

    public long getReceived() {
        return received;
    }

    public void setReceived(long received) {
        this.received = received;
    }

    public long getProcessed() {
        return processed;
    }

    public void setProcessed(long processed) {
        this.processed = processed;
    }

    public long getErrors() {
        return errors;
    }

    public void setErrors(long errors) {
        this.errors = errors;
    }

    public long getDropped() {
        return dropped;
    }

    public void setDropped(long dropped) {
        this.dropped = dropped;
    }

    public long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(long processingTime) {
        this.processingTime = processingTime;
    }

    public double getAvgProcessingTime() {
        return avgProcessingTime;
    }

    public void setAvgProcessingTime(double avgProcessingTime) {
        this.avgProcessingTime = avgProcessingTime;
    }

    public String getHealthStatus() {
        return healthStatus;
    }

    public void setHealthStatus(String healthStatus) {
        this.healthStatus = healthStatus;
    }
}
