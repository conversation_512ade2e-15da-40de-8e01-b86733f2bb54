package com.wiwj.securio.logmgr.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 时间格式转换工具类
 * 用于处理VictoriaLogs的时间格式转换和时区转换
 *
 * <AUTHOR>
 */
public class TimeFormatUtil {

    private static final Logger logger = LoggerFactory.getLogger(TimeFormatUtil.class);

    // 常用的时间格式
    private static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
    private static final DateTimeFormatter ISO_WITH_MILLIS_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    private static final DateTimeFormatter SIMPLE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter VICTORIA_LOGS_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

    // 时区定义
    private static final ZoneId CHINA_ZONE = ZoneId.of("Asia/Shanghai"); // 东八区
    private static final ZoneId UTC_ZONE = ZoneId.of("UTC"); // UTC时区

    /**
     * 将前端传入的时间字符串转换为VictoriaLogs可识别的UTC格式
     *
     * @param timeStr 时间字符串（假设为东八区时间）
     * @return 转换后的UTC时间字符串
     */
    public static String formatForVictoriaLogs(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }

        try {
            LocalDateTime localDateTime;
            
            // 尝试解析不同的时间格式
            if (timeStr.contains("T") && timeStr.endsWith("Z")) {
                // ISO 8601格式：2024-01-01T00:00:00Z
                // 这种情况下，移除Z并按本地时间解析（前端通常传递的是本地时间）
                localDateTime = LocalDateTime.parse(timeStr.replace("Z", ""), DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } else if (timeStr.contains("T") && timeStr.contains(".") && timeStr.endsWith("Z")) {
                // ISO 8601格式带毫秒：2024-01-01T00:00:00.000Z
                String timeWithoutZ = timeStr.replace("Z", "");
                if (timeWithoutZ.contains(".")) {
                    // 移除毫秒部分以简化处理
                    timeWithoutZ = timeWithoutZ.substring(0, timeWithoutZ.indexOf('.'));
                }
                localDateTime = LocalDateTime.parse(timeWithoutZ, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } else if (timeStr.contains("T")) {
                // ISO 8601格式不带Z：2024-01-01T00:00:00
                localDateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } else if (timeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                // 简单格式：2024-01-01 00:00:00
                localDateTime = LocalDateTime.parse(timeStr, SIMPLE_FORMATTER);
            } else {
                logger.warn("无法识别的时间格式: {}", timeStr);
                return timeStr; // 如果无法解析，返回原始字符串
            }

            // 将东八区时间转换为UTC时间
            ZonedDateTime chinaTime = localDateTime.atZone(CHINA_ZONE);
            ZonedDateTime utcTime = chinaTime.withZoneSameInstant(UTC_ZONE);
            
            // 格式化为VictoriaLogs期望的格式
            String result = utcTime.format(VICTORIA_LOGS_FORMATTER);
            
            logger.debug("时间转换: {} (东八区) -> {} (UTC)", timeStr, result);
            return result;

        } catch (DateTimeParseException e) {
            logger.error("时间格式转换失败: {}, 错误: {}", timeStr, e.getMessage());
            return timeStr; // 如果转换失败，返回原始字符串
        }
    }

    /**
     * 构建VictoriaLogs的时间范围查询条件
     *
     * @param startTime 开始时间（东八区）
     * @param endTime   结束时间（东八区）
     * @return 时间范围查询条件（UTC时间）
     */
    public static String buildTimeRangeQuery(String startTime, String endTime) {
        try {
            String formattedStartTime = formatForVictoriaLogs(startTime);
            String formattedEndTime = formatForVictoriaLogs(endTime);

            if (formattedStartTime != null && formattedEndTime != null) {
                // VictoriaLogs的时间范围查询需要保留Z
                String query = String.format("_time:[%s,%s]", formattedStartTime, formattedEndTime);
                logger.info("构建时间范围查询: {}", query);
                return query;
            } else {
                logger.warn("时间范围格式化失败，使用默认1天范围. startTime: {}, endTime: {}", startTime, endTime);
                return "_time:1d";
            }
        } catch (Exception e) {
            logger.error("构建时间范围查询失败: {}", e.getMessage(), e);
            return "_time:1d";
        }
    }

    /**
     * 验证时间字符串格式是否有效
     *
     * @param timeStr 时间字符串
     * @return 是否有效
     */
    public static boolean isValidTimeFormat(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return false;
        }

        try {
            formatForVictoriaLogs(timeStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前时间的VictoriaLogs格式字符串（UTC时间）
     *
     * @return 当前时间字符串
     */
    public static String getCurrentTimeUTC() {
        return ZonedDateTime.now(UTC_ZONE).format(VICTORIA_LOGS_FORMATTER);
    }

    /**
     * 获取当前时间的东八区格式字符串
     *
     * @return 当前时间字符串
     */
    public static String getCurrentTimeChina() {
        return ZonedDateTime.now(CHINA_ZONE).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
    }

    /**
     * 获取指定分钟前的时间字符串（UTC时间）
     *
     * @param minutesAgo 多少分钟前
     * @return 时间字符串
     */
    public static String getTimeMinutesAgoUTC(int minutesAgo) {
        return ZonedDateTime.now(UTC_ZONE).minusMinutes(minutesAgo)
                .format(VICTORIA_LOGS_FORMATTER);
    }

    /**
     * 获取指定分钟前的时间字符串（东八区时间）
     *
     * @param minutesAgo 多少分钟前
     * @return 时间字符串
     */
    public static String getTimeMinutesAgoChina(int minutesAgo) {
        return ZonedDateTime.now(CHINA_ZONE).minusMinutes(minutesAgo)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
    }

    /**
     * 将UTC时间转换为东八区时间
     *
     * @param utcTimeStr UTC时间字符串
     * @return 东八区时间字符串
     */
    public static String convertUtcToChina(String utcTimeStr) {
        if (utcTimeStr == null || utcTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 解析UTC时间
            ZonedDateTime utcTime;
            if (utcTimeStr.endsWith("Z")) {
                utcTime = ZonedDateTime.parse(utcTimeStr, DateTimeFormatter.ISO_ZONED_DATE_TIME);
            } else {
                LocalDateTime localDateTime = LocalDateTime.parse(utcTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                utcTime = localDateTime.atZone(UTC_ZONE);
            }

            // 转换为东八区时间
            ZonedDateTime chinaTime = utcTime.withZoneSameInstant(CHINA_ZONE);
            return chinaTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));

        } catch (Exception e) {
            logger.error("UTC时间转换为东八区时间失败: {}, 错误: {}", utcTimeStr, e.getMessage());
            return utcTimeStr;
        }
    }

    /**
     * 测试时区转换的方法（用于调试）
     *
     * @param timeStr 输入时间字符串
     */
    public static void testTimeConversion(String timeStr) {
        logger.info("=== 时区转换测试 ===");
        logger.info("输入时间: {}", timeStr);
        
        String utcTime = formatForVictoriaLogs(timeStr);
        logger.info("转换为UTC: {}", utcTime);
        
        String chinaTime = convertUtcToChina(utcTime);
        logger.info("转换回东八区: {}", chinaTime);
        
        String query = buildTimeRangeQuery(timeStr, timeStr);
        logger.info("VictoriaLogs查询条件: {}", query);
        logger.info("=== 测试结束 ===");
    }

    /**
     * 构建VictoriaLogs的时间查询条件
     * 根据timeRange参数判断是快速时间选择还是精确时间选择
     *
     * @param startTime 开始时间（东八区）
     * @param endTime   结束时间（东八区）
     * @param timeRange 时间范围快捷选择值（如：1h, 1d等）
     * @return 时间查询条件
     */
    public static String buildTimeQuery(String startTime, String endTime, String timeRange) {
        try {
            // 如果有快速时间选择值且不是"precise"，优先使用快速时间选择
            if (timeRange != null && !timeRange.trim().isEmpty() && !"precise".equals(timeRange)) {
                // 验证快速时间格式（如1h、30m、1d等）
                if (isValidQuickTimeRange(timeRange)) {
                    String query = "_time:" + timeRange;
                    logger.info("使用快速时间选择: {}", query);
                    return query;
                } else {
                    logger.warn("无效的快速时间格式: {}, 回退到精确时间范围", timeRange);
                }
            }
            
            // 使用精确时间范围
            return buildTimeRangeQuery(startTime, endTime);
            
        } catch (Exception e) {
            logger.error("构建时间查询条件失败: {}", e.getMessage(), e);
            return "_time:1d"; // 默认返回1天
        }
    }

    /**
     * 验证快速时间范围格式是否有效
     *
     * @param timeRange 时间范围字符串（如：1h, 30m, 1d等）
     * @return 是否有效
     */
    public static boolean isValidQuickTimeRange(String timeRange) {
        if (timeRange == null || timeRange.trim().isEmpty()) {
            return false;
        }
        
        // 匹配格式：数字+单位（m=分钟, h=小时, d=天, w=周）
        return timeRange.matches("^\\d+[mhdw]$");
    }

    /**
     * 判断是否为快速时间选择
     *
     * @param timeRange 时间范围参数
     * @return 是否为快速时间选择
     */
    public static boolean isQuickTimeSelection(String timeRange) {
        return timeRange != null && !timeRange.trim().isEmpty() && 
               !"precise".equals(timeRange) && isValidQuickTimeRange(timeRange);
    }
} 