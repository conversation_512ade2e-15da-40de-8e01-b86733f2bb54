package com.wiwj.framework.web.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import com.wiwj.common.core.domain.entity.SysUser;
import com.wiwj.common.core.domain.model.LoginUser;
import com.wiwj.common.enums.UserStatus;
import com.wiwj.common.exception.ServiceException;
import com.wiwj.common.utils.MessageUtils;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.system.service.ISysUserService;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService
{
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysPermissionService permissionService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException
    {
        SysUser user = userService.selectUserByUserName(username);
        if (StringUtils.isNull(user))
        {
            log.error("登录用户：{} 不存在", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        }
        else if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            log.error("登录用户：{} 已被删除", username);
            throw new ServiceException(MessageUtils.message("user.password.delete"));
        }
        else if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            log.error("登录用户：{} 已被停用", username);
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }
        
        // 只有admin用户需要进行密码验证（本地认证）
        // LDAP用户已经通过LDAP认证，不需要再次验证密码
        if ("admin".equals(username)) {
            passwordService.validate(user);
            log.debug("admin用户密码验证通过");
        } else {
            log.debug("LDAP用户跳过密码验证：{}", username);
        }
        
        log.info("用户验证通过，创建登录用户信息：{}", username);
        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user)
    {
        return new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
    }
}
