package com.wiwj.framework.web.service;

import com.wiwj.common.utils.SecurityUtils;
import com.wiwj.framework.web.domain.LdapPerson;
import com.wiwj.common.core.domain.entity.SysUser;
import com.wiwj.framework.web.domain.server.Sys;
import com.wiwj.system.service.ISysUserService;
import com.wiwj.system.service.ISysRoleService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.control.PagedResultsDirContextProcessor;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.query.LdapQuery;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.wiwj.common.utils.StringUtils;

import javax.naming.directory.SearchControls;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import static org.springframework.ldap.query.LdapQueryBuilder.query;

@Service
public class LdapUserService {
    private static final Logger log = LoggerFactory.getLogger(LdapUserService.class);
    
    @Autowired
    private LdapTemplate ldapTemplate ;

    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private ISysRoleService roleService;
    
    /**
     * 根据用户名查询单个LDAP用户
     * @param username 用户名
     * @return LDAP用户信息
     */
    public LdapPerson queryUserByUsername(String username) {
        try {
            SearchControls searchControls = new SearchControls();
            searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
            
            // 映射对象
            AttributesMapper<LdapPerson> CN_ATTRIBUTES_MAPPER = attributes -> {
                LdapPerson ldapUser = new LdapPerson();
                ldapUser.setUid(String.valueOf(attributes.get("uid") != null ? attributes.get("uid").get() : null));
                ldapUser.setGivenName(String.valueOf(attributes.get("givenName") != null ? attributes.get("givenName").get() : null));
                ldapUser.setUidNumber(String.valueOf(attributes.get("uidNumber") != null ? attributes.get("uidNumber").get() : null));
                ldapUser.setMail(String.valueOf(attributes.get("mail") != null ? attributes.get("mail").get() : null));
                ldapUser.setGidNumber(String.valueOf(attributes.get("gidNumber") != null ? attributes.get("gidNumber").get() : null));
                ldapUser.setUserPassword(String.valueOf(attributes.get("userPassword") != null ? attributes.get("userPassword").get() : null));
                return ldapUser;
            };

            // 查询条件：根据uid查询
            LdapQuery queryPerson = query().where("objectClass").is("person").and("uid").is(username);
            
            List<LdapPerson> users = ldapTemplate.search(queryPerson.base(),
                    queryPerson.filter().encode(),
                    searchControls,
                    CN_ATTRIBUTES_MAPPER);
            
            if (users != null && !users.isEmpty()) {
                return users.get(0);
            }
            return null;
        } catch (Exception e) {
            log.error("查询LDAP用户失败，用户名: {}, 错误: {}", username, e.getMessage());
            return null;
        }
    }
    
    /**
     * 同步单个LDAP用户到系统
     * @param username 用户名
     * @return 同步后的系统用户，失败返回null
     */
    public SysUser syncSingleUser(String username) {
        try {
            log.info("开始同步LDAP用户: {}", username);
            
            // 跳过admin用户
            if ("admin".equals(username)) {
                log.warn("跳过admin用户的LDAP同步");
                return null;
            }
            
            // 从LDAP查询用户信息
            LdapPerson ldapUser = queryUserByUsername(username);
            if (ldapUser == null) {
                log.warn("在LDAP中未找到用户: {}", username);
                return null;
            }
            
            // 检查必要字段
            if (StringUtils.isEmpty(ldapUser.getUid()) || StringUtils.isEmpty(ldapUser.getUidNumber())) {
                log.warn("LDAP用户信息不完整，uid: {}, uidNumber: {}", ldapUser.getUid(), ldapUser.getUidNumber());
                return null;
            }
            
            // 转换为系统用户
            SysUser user = ldapUserToSystemUser(ldapUser);
            
            // 检查用户是否已存在
            SysUser existingUserById = null;
            SysUser existingUserByName = sysUserService.selectUserByUserName(username);
            
            try {
                if (StringUtils.isNotEmpty(ldapUser.getUidNumber()) && !"null".equals(ldapUser.getUidNumber())) {
                    existingUserById = sysUserService.selectUserById(Long.valueOf(ldapUser.getUidNumber()));
                }
            } catch (NumberFormatException e) {
                log.warn("LDAP用户uidNumber格式错误: {}", ldapUser.getUidNumber());
            }
            
            if (existingUserById != null || existingUserByName != null) {
                // 用户已存在，更新信息
                SysUser existingUser = existingUserByName != null ? existingUserByName : existingUserById;
                user.setUserId(existingUser.getUserId());
                user.setPassword(existingUser.getPassword()); // 保持原密码
                user.setUpdateBy("ldap-sync");
                
                int result = sysUserService.updateUserProfile(user);
                if (result > 0) {
                    log.info("LDAP用户更新成功: {}", username);
                    return sysUserService.selectUserByUserName(username);
                } else {
                    log.error("LDAP用户更新失败: {}", username);
                    return null;
                }
            } else {
                // 用户不存在，新增用户
                user.setCreateBy("ldap-sync");
                user.setUpdateBy("ldap-sync");
                // 设置默认密码（LDAP用户不使用本地密码）
                user.setPassword(SecurityUtils.encryptPassword("ldap-user-no-local-password"));
                
                int result = sysUserService.insertUser(user);
                if (result > 0) {
                    log.info("LDAP用户创建成功: {}", username);
                    
                    // 为新创建的LDAP用户分配默认角色（普通用户角色，ID为2）
                    SysUser newUser = sysUserService.selectUserByUserName(username);
                    if (newUser != null) {
                        try {
                            Long[] userIds = new Long[]{newUser.getUserId()};
                            int roleResult = roleService.insertAuthUsers(2L, userIds);
                            if (roleResult > 0) {
                                log.info("为LDAP用户分配默认角色成功: {}", username);
                            } else {
                                log.warn("为LDAP用户分配默认角色失败: {}", username);
                            }
                        } catch (Exception e) {
                            log.error("为LDAP用户分配默认角色时发生异常: {}, 错误: {}", username, e.getMessage());
                        }
                    }
                    
                    return newUser;
                } else {
                    log.error("LDAP用户创建失败: {}", username);
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("同步LDAP用户失败，用户名: {}, 错误: {}", username, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 分页查询LDAP中的所有的用户息
     *https://blog.51cto.com/u_14210396/8831295
     * @return 结果
     */
    public List<LdapPerson> queryUsersPage() {
        List<LdapPerson> list = new ArrayList<>();
        SearchControls searchControls = new SearchControls();
        /*
         * 0:OBJECT_SCOPE,搜索指定的命名对象。
         * 1:ONE LEVEL_SCOPE,只搜索指定命名对象的一个级别，这是缺省值。
         * 2:SUBTREE_SCOPE,搜索以指定命名对象为根结点的整棵树
         */
        searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
        // 每次查询条数：默认1000条
        PagedResultsDirContextProcessor processor = new PagedResultsDirContextProcessor(1000);
        //返回的参数
        // 映射对象
        AttributesMapper<LdapPerson> CN_ATTRIBUTES_MAPPER = attributes -> {
            LdapPerson ldapUser = new LdapPerson();
            ldapUser.setUid(String.valueOf(attributes.get("uid") != null ? attributes.get("uid").get() : null));
            ldapUser.setGivenName(String.valueOf(attributes.get("givenName") != null ? attributes.get("givenName").get() : null));
            ldapUser.setUidNumber(String.valueOf(attributes.get("uidNumber") != null ? attributes.get("uidNumber").get() : null));
            ldapUser.setMail(String.valueOf(attributes.get("mail") != null ? attributes.get("mail").get() : null));
            ldapUser.setGidNumber(String.valueOf(attributes.get("gidNumber") != null ? attributes.get("gidNumber").get() : null));
            ldapUser.setUserPassword(String.valueOf(attributes.get("userPassword") != null ? attributes.get("userPassword").get() : null));
            return ldapUser;
        };

        //查询条件
        LdapQuery queryPerson = query().where("objectClass").is("person");

        do {
            List<LdapPerson> search = ldapTemplate.search(queryPerson.base(),
                    queryPerson.filter().encode(),
                    searchControls,
                    CN_ATTRIBUTES_MAPPER,
                    processor);
            list.addAll(search);
        } while (processor.hasMore());

        return list;
    }


    /**
     将映射的LDAP用户对象 转换为 当前系统的用户对象
     */
    public SysUser ldapUserToSystemUser(LdapPerson ldapUser) {
        SysUser user = new SysUser();
        
        // 处理用户ID
        if (StringUtils.isNotEmpty(ldapUser.getUidNumber()) && !"null".equals(ldapUser.getUidNumber())) {
            try {
                user.setUserId(Long.valueOf(ldapUser.getUidNumber()));// 员工编号-id
            } catch (NumberFormatException e) {
                log.warn("LDAP用户uidNumber格式错误: {}, 将使用自动生成ID", ldapUser.getUidNumber());
                // 不设置userId，让数据库自动生成
            }
        }
        
        user.setNickName(StringUtils.isNotEmpty(ldapUser.getGivenName()) && !"null".equals(ldapUser.getGivenName()) 
                ? ldapUser.getGivenName() : ldapUser.getUid());//中文姓名，如果没有则使用用户名
        user.setEmail(StringUtils.isNotEmpty(ldapUser.getMail()) && !"null".equals(ldapUser.getMail()) 
                ? ldapUser.getMail() : null);//邮箱
        user.setUserName(ldapUser.getUid()); //登录名-姓名拼音
        user.setStatus("0"); //默认状态 启用
        user.setDelFlag("0"); //未删除
        user.setSex("2"); //未知性别
        
        // 设置默认部门ID（可根据实际需求调整）
        user.setDeptId(100L); // 假设100是默认部门ID
        
        return user;
    }

    /**
        同步ldap用户到系统
     */
    public void userSyncTask() {
        List<LdapPerson> allLdapUsers = this.queryUsersPage();
        // 从ldap 更新用户到系统
        for (LdapPerson ldapUser : allLdapUsers) {
            // 将 LdapUser 转换为 User
            String userid = String.valueOf(ldapUser.getUidNumber());//根据用户ID查询
            String username = String.valueOf(ldapUser.getUid());// 根据用户名查(登录名)
            if (username.equals("admin")){ // admin用户跳过
                continue;
            }
            SysUser user = this.ldapUserToSystemUser(ldapUser);
            SysUser user_exit_id = sysUserService.selectUserById(Long.valueOf(userid));
            SysUser user_exit_name = sysUserService.selectUserByUserName(username);
            if (user_exit_id != null || user_exit_name != null) // 用户id和用户名任一存在，则更新数据
            {
                int up_result = sysUserService.updateUserProfile(user);
                System.out.println(up_result);
            }
            else{ // 不存在则新增数据
                int insert_result =sysUserService.insertUser(user);
                System.out.println(insert_result);
            }
        }

        // 删除系统中的无用账户（除admin） TODO
        // sysUserService.selectUserList()
    }
}
