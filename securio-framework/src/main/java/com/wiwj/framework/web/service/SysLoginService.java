package com.wiwj.framework.web.service;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;
import com.wiwj.common.constant.CacheConstants;
import com.wiwj.common.constant.Constants;
import com.wiwj.common.constant.UserConstants;
import com.wiwj.common.core.domain.entity.SysUser;
import com.wiwj.common.core.domain.model.LoginUser;
import com.wiwj.common.core.redis.RedisCache;
import com.wiwj.common.exception.ServiceException;
import com.wiwj.common.exception.user.BlackListException;
import com.wiwj.common.exception.user.CaptchaException;
import com.wiwj.common.exception.user.CaptchaExpireException;
import com.wiwj.common.exception.user.UserNotExistsException;
import com.wiwj.common.exception.user.UserPasswordNotMatchException;
import com.wiwj.common.utils.DateUtils;
import com.wiwj.common.utils.MessageUtils;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.common.utils.ip.IpUtils;
import com.wiwj.framework.manager.AsyncManager;
import com.wiwj.framework.manager.factory.AsyncFactory;
import com.wiwj.framework.security.context.AuthenticationContextHolder;
import com.wiwj.system.service.ISysConfigService;
import com.wiwj.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Hashtable;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    private static final Logger log = LoggerFactory.getLogger(SysLoginService.class);
    
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;
    
    @Autowired
    private LdapUserService ldapUserService;

    @Value("${spring.ldap.urls}")
    private  String ladpUrls;

    @Value("${spring.ldap.baseDnSuffix}")
    private  String ladpBaseDnSuffix;

    @Value("${spring.ldap.baseDnMiddle}")
    private  String ladpBaseDnMiddle;

    @Autowired
    private UserDetailsService userDetailsService;
    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid)
    {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        // 登录用户
        LoginUser loginUser = null;
        UsernamePasswordAuthenticationToken authenticationToken = null;
        try
        {
            boolean ldapCheck = ldapValidate(username, password);
            if (ldapCheck) {
                log.info("LDAP认证成功，用户: {}", username);
                
                // LDAP验证通过，检查用户是否存在于系统中
                SysUser existingUser = userService.selectUserByUserName(username);
                if (existingUser == null) {
                    log.info("用户 {} 在系统中不存在，开始从LDAP同步", username);
                    // 用户不存在，从LDAP同步用户信息
                    SysUser syncedUser = ldapUserService.syncSingleUser(username);
                    if (syncedUser == null) {
                        log.error("LDAP用户同步失败，用户: {}", username);
                        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, "LDAP用户同步失败"));
                        throw new ServiceException("LDAP用户同步失败，请联系管理员");
                    }
                    log.info("LDAP用户同步成功，用户: {}", username);
                } else {
                    log.info("用户 {} 已存在于系统中，跳过同步", username);
                }
                
                // 生成登录用户信息
                loginUser = (LoginUser) userDetailsService.loadUserByUsername(username);
            }
            else {
                log.info("LDAP认证失败，尝试本地认证，用户: {}", username);
                // LDAP验证失败,使用内置验证(admin用户总会走这里)
                authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
                AuthenticationContextHolder.setContext(authenticationToken);
                // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
                authentication = authenticationManager.authenticate(authenticationToken);
                loginUser = (LoginUser) authentication.getPrincipal();
                log.info("本地认证成功，用户: {}", username);
            }

        }
        catch (Exception e)
        {
            log.error("用户登录失败，用户: {}, 错误: {}", username, e.getMessage());
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        recordLoginInfo(loginUser.getUserId());
        log.info("用户登录成功，用户: {}, ID: {}", username, loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            redisCache.deleteObject(verifyKey);
            if (captcha == null)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            if (!code.equalsIgnoreCase(captcha))
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    /**
     * LDAP认证验证
     * @param username 用户名
     * @param password 密码
     * @return 认证结果
     */
    private boolean ldapValidate(String username, String password) {
        try {
            // 检查LDAP配置是否完整
            if (StringUtils.isEmpty(ladpUrls) || StringUtils.isEmpty(ladpBaseDnSuffix) || StringUtils.isEmpty(ladpBaseDnMiddle)) {
                log.warn("LDAP配置不完整，跳过LDAP认证");
                return false;
            }
            
            String bindUserDN = "uid=" + username +","+ladpBaseDnMiddle+","+ladpBaseDnSuffix;  //用户 DN
            log.debug("尝试LDAP认证，用户DN: {}", bindUserDN);
            
            Hashtable<String, String> env = getStringStringHashtable(password, bindUserDN);
            javax.naming.directory.DirContext ctx = new javax.naming.directory.InitialDirContext(env);
            ctx.close(); // 关闭连接
            
            log.info("LDAP认证成功，用户: {}", username);
            return true;
        } catch (Exception e) {
            log.debug("LDAP认证失败，用户: {}, 错误: {}", username, e.getMessage());
            return false;
        }
    }

    private  Hashtable<String, String> getStringStringHashtable(String password, String bindUserDN) {
        Hashtable<String, String> env = new Hashtable<String, String>();
        env.put(javax.naming.Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(javax.naming.Context.PROVIDER_URL, ladpUrls);
        env.put(javax.naming.Context.SECURITY_AUTHENTICATION, "simple");
        env.put(javax.naming.Context.SECURITY_PRINCIPAL, bindUserDN);
        env.put(javax.naming.Context.SECURITY_CREDENTIALS, password);
        return env;
    }
}
