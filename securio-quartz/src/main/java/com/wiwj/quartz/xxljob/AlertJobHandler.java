package com.wiwj.quartz.xxljob;

import com.wiwj.securio.alert.service.IAlertAsyncService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 告警相关定时任务处理器
 * 
 * <AUTHOR>
 */
@Component
public class AlertJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(AlertJobHandler.class);

    @Autowired
    private IAlertAsyncService alertAsyncService;

    /**
     * 批量同步FlashDuty故障信息定时任务
     * 查询最近三天内 fdIncidentId 为空或 assignedUserIds 为空的告警
     * 批量调用 FlashDuty API 同步故障信息和分派人员
     */
    @XxlJob("batchSyncFlashDutyJobHandler")
    public void batchSyncFlashDuty() {
        logger.info("批量同步FlashDuty故障信息定时任务开始");
        XxlJobHelper.log("批量同步FlashDuty故障信息定时任务开始");
        
        try {
            // 调用批量同步方法
            int syncCount = alertAsyncService.batchSyncFlashDutyIncidentsForRecentAlerts();
            
            String resultMessage = String.format("批量同步FlashDuty故障信息完成，成功同步 %d 条告警", syncCount);
            logger.info(resultMessage);
            XxlJobHelper.log(resultMessage);
            XxlJobHelper.handleSuccess(resultMessage);
            
        } catch (Exception e) {
            String errorMessage = "批量同步FlashDuty故障信息发生异常: " + e.getMessage();
            logger.error(errorMessage, e);
            XxlJobHelper.log(errorMessage, e);
            XxlJobHelper.handleFail(errorMessage);
        }
    }

    /**
     * 同步FlashDuty故障信息任务（保留原有方法，可用于单个同步测试）
     */
    @XxlJob("syncFlashDutyJobHandler")
    public void syncFlashDuty() {
        logger.info("同步FlashDuty故障信息任务开始");
        XxlJobHelper.log("同步FlashDuty故障信息任务开始");
        
        try {
            // 这里可以根据需要实现单个同步逻辑
            // 目前保留为占位符，实际使用时可以添加具体逻辑
            
            logger.info("同步FlashDuty故障信息任务完成");
            XxlJobHelper.log("同步FlashDuty故障信息任务完成");
            XxlJobHelper.handleSuccess("同步FlashDuty故障信息任务执行成功");
            
        } catch (Exception e) {
            String errorMessage = "同步FlashDuty故障信息任务发生异常: " + e.getMessage();
            logger.error(errorMessage, e);
            XxlJobHelper.log(errorMessage, e);
            XxlJobHelper.handleFail(errorMessage);
        }
    }
}
