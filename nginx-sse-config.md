# Nginx SSE 配置指南

## 问题描述
在生产环境中，SSE (Server-Sent Events) 连接可能会遇到 `ERR_INCOMPLETE_CHUNKED_ENCODING` 错误，这通常是由于Nginx代理服务器的缓冲机制导致的。

## 解决方案

### 1. Nginx 配置修改

在您的Nginx配置文件中添加以下配置：

```nginx
# 在 http 块中添加
http {
    # 其他配置...
    
    # 针对SSE的全局配置
    proxy_buffering off;
    proxy_cache off;
    proxy_set_header Connection '';
    proxy_http_version 1.1;
    
    server {
        listen 80;
        server_name securio.5i5j.com;
        
        # 其他location配置...
        
        # 专门针对SSE端点的配置
        location /prod-api/app/host-analysis/analyze/progress/ {
            proxy_pass http://backend_servers;
            
            # 禁用缓冲，确保实时传输
            proxy_buffering off;
            proxy_cache off;
            
            # 设置SSE相关的头部
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 关键配置：禁用Nginx对响应的缓冲
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            proxy_set_header Cache-Control no-cache;
            proxy_set_header X-Accel-Buffering no;
            
            # 设置超时时间
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 3600s; # 1小时，SSE连接可能较长
            
            # 允许大的响应体
            proxy_max_temp_file_size 0;
            
            # CORS支持
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            
            # 处理OPTIONS请求
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
                add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type 'text/plain; charset=utf-8';
                add_header Content-Length 0;
                return 204;
            }
        }
        
        # 其他API请求的通用配置
        location /prod-api/ {
            proxy_pass http://backend_servers;
            
            # 标准的代理配置
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 设置合理的超时时间
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
    }
}
```

### 2. 后端服务器组配置

```nginx
upstream backend_servers {
    server 127.0.0.1:8080; # 替换为您的后端服务器地址
    # 如果有多个后端服务器，可以添加更多
    # server 127.0.0.1:8081;
    
    # 确保同一个SSE连接始终路由到同一个后端服务器
    ip_hash;
}
```

### 3. 完整的配置示例

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log  /var/log/nginx/access.log  main;
    error_log   /var/log/nginx/error.log   warn;
    
    # 基本设置
    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;
    types_hash_max_size 2048;
    
    # Gzip压缩（但对SSE要小心）
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # 后端服务器
    upstream backend_servers {
        server 127.0.0.1:8080;
        ip_hash; # 确保SSE连接的粘性
    }
    
    server {
        listen       80;
        server_name  securio.5i5j.com;
        
        # 静态文件服务
        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        
        # SSE专用配置
        location /prod-api/app/host-analysis/analyze/progress/ {
            proxy_pass http://backend_servers;
            
            # 禁用所有缓冲和压缩
            proxy_buffering off;
            proxy_cache off;
            gzip off;
            
            # HTTP/1.1 和连接设置
            proxy_http_version 1.1;
            proxy_set_header Connection '';
            
            # 标准代理头
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # SSE特定头部
            proxy_set_header Cache-Control 'no-cache, no-store, must-revalidate';
            proxy_set_header X-Accel-Buffering no;
            
            # 长连接超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 3600s;
            
            # CORS
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'GET, OPTIONS';
                add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type 'text/plain; charset=utf-8';
                add_header Content-Length 0;
                return 204;
            }
        }
        
        # 其他API请求
        location /prod-api/ {
            proxy_pass http://backend_servers;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # 错误页面
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}
```

## 配置说明

### 关键配置项解释：

1. **`proxy_buffering off`**: 禁用代理缓冲，确保数据实时传输
2. **`proxy_cache off`**: 禁用代理缓存
3. **`gzip off`**: 在SSE路径上禁用gzip压缩
4. **`proxy_http_version 1.1`**: 使用HTTP/1.1支持长连接
5. **`proxy_set_header Connection ''`**: 清空Connection头，让Nginx管理连接
6. **`proxy_set_header X-Accel-Buffering no`**: 明确告诉Nginx不要缓冲这个响应
7. **`proxy_read_timeout 3600s`**: 设置长连接超时时间为1小时
8. **`ip_hash`**: 确保同一客户端的请求始终路由到同一后端服务器

### 应用配置：

1. 备份现有Nginx配置
2. 根据您的环境调整上述配置
3. 重新加载Nginx配置：`nginx -s reload`
4. 测试SSE连接

## 验证方法

可以使用以下命令验证SSE连接：

```bash
curl -N -H "Accept: text/event-stream" \
  "http://securio.5i5j.com/prod-api/app/host-analysis/analyze/progress/test-task-id"
```

如果配置正确，您应该看到SSE事件流输出，而不是连接错误。

## 故障排除

如果仍然遇到问题，请检查：

1. **Nginx错误日志**: `/var/log/nginx/error.log`
2. **后端服务日志**: 检查Java应用的SSE实现日志
3. **浏览器开发者工具**: Network标签查看请求详情
4. **防火墙设置**: 确保没有中间设备干扰长连接

## 监控建议

建议监控以下指标：
- SSE连接成功率
- 连接持续时间
- 错误重连次数
- 后端服务器资源使用情况 