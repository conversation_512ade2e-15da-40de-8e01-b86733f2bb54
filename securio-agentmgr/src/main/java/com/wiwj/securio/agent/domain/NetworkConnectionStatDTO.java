package com.wiwj.securio.agent.domain;

/**
 * 网络连接统计数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public class NetworkConnectionStatDTO {
    

    private Long pid;

    /** 进程名称 */
    private String processName;
    
    /** 远程IP地址 */
    private String remoteIp;
    
    
    /** 连接数量 */
    private Integer connectionCount;
    
    /** 主机名 */
    private String hostname;

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public Integer getConnectionCount() {
        return connectionCount;
    }

    public void setConnectionCount(Integer connectionCount) {
        this.connectionCount = connectionCount;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }
    
    @Override
    public String toString() {
        return "NetworkConnectionStatDTO [processName=" + processName + ", remoteIp=" + remoteIp + ", connectionCount=" + connectionCount + ", hostname=" + hostname + "]";
    }
}
