package com.wiwj.securio.agent.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.agent.domain.AgentCommand;
import com.wiwj.securio.agent.service.IAgentCommandService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 探针命令记录Controller
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@RestController
@RequestMapping("/agent/command")
public class AgentCommandController extends BaseController {
    @Autowired
    private IAgentCommandService agentCommandService;

    /**
     * 查询探针命令记录列表
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping("/list")
    public TableDataInfo list(AgentCommand agentCommand) {
        startPage();
        List<AgentCommand> list = agentCommandService.selectAgentCommandList(agentCommand);
        return getDataTable(list);
    }

    /**
     * 导出探针命令记录列表
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "探针命令记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgentCommand agentCommand) {
        List<AgentCommand> list = agentCommandService.selectAgentCommandList(agentCommand);
        ExcelUtil<AgentCommand> util = new ExcelUtil<AgentCommand>(AgentCommand.class);
        util.exportExcel(response, list, "探针命令记录数据");
    }

    /**
     * 获取探针命令记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(agentCommandService.selectAgentCommandById(id));
    }

    /**
     * 新增探针命令记录
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "探针命令记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgentCommand agentCommand) {
        return toAjax(agentCommandService.insertAgentCommand(agentCommand));
    }

    /**
     * 修改探针命令记录
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "探针命令记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgentCommand agentCommand) {
        return toAjax(agentCommandService.updateAgentCommand(agentCommand));
    }

    /**
     * 删除探针命令记录
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "探针命令记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(agentCommandService.deleteAgentCommandByIds(ids));
    }
}
