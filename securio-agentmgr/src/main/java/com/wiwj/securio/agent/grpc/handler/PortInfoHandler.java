package com.wiwj.securio.agent.grpc.handler;

import com.wiwj.securio.agent.domain.AgentInfo;
import com.wiwj.securio.agent.domain.InventoryPortInfo;
import com.wiwj.securio.agent.grpc.proto.PortInfoData;
import com.wiwj.securio.agent.grpc.proto.PortInfoRequest;
import com.wiwj.securio.agent.grpc.proto.PortInfoResponse;
import com.wiwj.securio.agent.service.IAgentInfoService;
import com.wiwj.securio.agent.service.IInventoryPortInfoService;
import io.grpc.stub.StreamObserver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 端口信息上报处理器
 */
@Component
public class PortInfoHandler extends AbstractGrpcRequestHandler<PortInfoRequest, PortInfoResponse> {

    @Autowired
    private IAgentInfoService agentInfoService;

    @Autowired
    private IInventoryPortInfoService portInfoService;

    @Override
    public void handle(PortInfoRequest request, StreamObserver<PortInfoResponse> responseObserver) {
        logger.info("Received port info from agent: {}, isInitial: {}", request.getUuid(), request.getIsInitial());

        try {
            // 验证token
            if (!validateToken(request.getToken())) {
                PortInfoResponse response = PortInfoResponse.newBuilder()
                        .setSuccess(false)
                        .setError("Invalid token")
                        .build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }

            AgentInfo agentInfo = agentInfoService.selectAgentInfoByAgentId(request.getUuid());
            if (agentInfo == null) {
                PortInfoResponse response = PortInfoResponse.newBuilder()
                        .setSuccess(false)
                        .setError("Agent not found")
                        .build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }

            if(request.getPage() == 1){
                int count = portInfoService.deleteInventoryPortInfoByAgentId(request.getUuid());
                logger.info("Deleted {} port info records for agent: {}", count, request.getUuid());
            }

            // 保存端口信息到数据库
            List<InventoryPortInfo> ports = new ArrayList<>();

            for (PortInfoData portData : request.getPortsList()) {
                InventoryPortInfo portInfo = new InventoryPortInfo();
                portInfo.setAgentId(request.getUuid());
                portInfo.setPid(Long.valueOf(portData.getPid()));
                portInfo.setProcess(portData.getProcess());
                portInfo.setProtocol(portData.getProtocol());
                portInfo.setLocalIp(portData.getLocalIp());
                portInfo.setLocalPort(Long.valueOf(portData.getLocalPort()));
                portInfo.setRemoteIp(portData.getRemoteIp());
                portInfo.setRemotePort(Long.valueOf(portData.getRemotePort()));
                portInfo.setState(portData.getState());
                portInfo.setCreateAt(System.currentTimeMillis());
                portInfo.setUpdateAt(System.currentTimeMillis());
                portInfo.setCreateBy("system");
                portInfo.setUpdateBy("system");
                portInfo.setHostIp(agentInfo.getHostIp());
                portInfo.setIsDel(0);
                ports.add(portInfo);
            }

            // 批量插入端口信息
            if (!ports.isEmpty()) {
                if(request.getPage() == 0){
                    if(request.getPage() == 0){
                        int count = portInfoService.deleteInventoryPortInfoByAgentId(request.getUuid());
                        logger.info("Deleted {} port info records for agent: {}", count, request.getUuid());
                    }
                }
                portInfoService.batchInsertInventoryPortInfo(ports);
            }

            // 记录一些重要端口的信息
            ports.stream()
                .filter(p -> p.getLocalPort() < 1024 || "LISTEN".equals(p.getState()))
                .forEach(p -> logger.debug("Important port - {}:{} ({}) PID: {} Process: {}",
                    p.getLocalIp(), p.getLocalPort(), p.getState(), p.getPid(), p.getProcess()));

            PortInfoResponse response = PortInfoResponse.newBuilder()
                    .setSuccess(true)
                    .setError("")
                    .build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            logger.error("Error processing port info for agent: " + request.getUuid(), e);
            PortInfoResponse response = PortInfoResponse.newBuilder()
                    .setSuccess(false)
                    .setError("Internal error: " + e.getMessage())
                    .build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }
}
