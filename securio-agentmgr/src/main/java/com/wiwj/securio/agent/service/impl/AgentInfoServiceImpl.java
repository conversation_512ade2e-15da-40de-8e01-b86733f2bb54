package com.wiwj.securio.agent.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.agent.mapper.AgentInfoMapper;
import com.wiwj.securio.agent.domain.AgentInfo;
import com.wiwj.securio.agent.service.IAgentInfoService;

/**
 * 探针信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
@Service
public class AgentInfoServiceImpl implements IAgentInfoService {

    private static final Logger logger = LoggerFactory.getLogger(AgentInfoServiceImpl.class);

    @Autowired
    private AgentInfoMapper agentInfoMapper;

    /**
     * 查询探针信息
     *
     * @param id 探针信息主键
     * @return 探针信息
     */
    @Override
    public AgentInfo selectAgentInfoById(Long id) {
        return agentInfoMapper.selectAgentInfoById(id);
    }

    /**
     * 查询探针信息列表
     *
     * @param agentInfo 探针信息
     * @return 探针信息
     */
    @Override
    public List<AgentInfo> selectAgentInfoList(AgentInfo agentInfo) {
        return agentInfoMapper.selectAgentInfoList(agentInfo);
    }

    /**
     * 新增探针信息
     *
     * @param agentInfo 探针信息
     * @return 结果
     */
    @Override
    public int insertAgentInfo(AgentInfo agentInfo) {

        // 插入 agent 默认配置信息
        // AgentConfigs agentConfigs = new AgentConfigs();
        // agentConfigs.setAgentId(agentInfo.getAgentId());

        // // 从资源文件中读取默认配置信息
        // try {
        //     ClassPathResource resource = new ClassPathResource(DEFAULT_CONFIG_FILE);
        //     InputStream inputStream = resource.getInputStream();
        //     String defaultConfig = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        //     inputStream.close();

        //     agentConfigs.setConfig(defaultConfig);
        //     agentConfigsService.insertAgentConfigs(agentConfigs);

        // } catch (IOException e) {
        //     logger.error("读取默认配置文件失败: {}", e.getMessage(), e);
        //     // 即使读取配置文件失败，仍然继续执行探针信息的插入操作
        // }

        agentInfo.setIsDel(0);
        agentInfo.setCreateAt(System.currentTimeMillis());
        agentInfo.setUpdateAt(System.currentTimeMillis());
        return agentInfoMapper.insertAgentInfo(agentInfo);
    }

    /**
     * 修改探针信息
     *
     * @param agentInfo 探针信息
     * @return 结果
     */
    @Override
    public int updateAgentInfo(AgentInfo agentInfo) {
        return agentInfoMapper.updateAgentInfo(agentInfo);
    }

    /**
     * 批量删除探针信息
     *
     * @param ids 需要删除的探针信息主键
     * @return 结果
     */
    @Override
    public int deleteAgentInfoByIds(Long[] ids) {
        return agentInfoMapper.deleteAgentInfoByIds(ids);
    }

    /**
     * 删除探针信息信息
     *
     * @param id 探针信息主键
     * @return 结果
     */
    @Override
    public int deleteAgentInfoById(Long id) {
        return agentInfoMapper.deleteAgentInfoById(id);
    }

    @Override
    public AgentInfo selectAgentInfoByAgentId(String agentId) {
        return agentInfoMapper.selectAgentInfoByAgentId(agentId);
    }

    /**
     * 根据主机IP查询探针信息
     *
     * @param hostIp 主机IP
     * @return 探针信息
     */
    @Override
    public AgentInfo selectAgentInfoByHostIp(String hostIp) {
        return agentInfoMapper.selectAgentInfoByHostIp(hostIp);
    }

    /**
     * 获取探针统计数据
     *
     * @return 包含探针总数、在线数、异常数、停用数、离线数的Map
     */
    @Override
    public Map<String, Object> getAgentStats() {
        try {
            Map<String, Object> stats = agentInfoMapper.selectAgentStats();

            if (stats == null || stats.isEmpty()) {
                logger.warn("No agent stats found, returning empty stats");
                stats = new HashMap<>();
                stats.put("total", 0);
                stats.put("online", 0);
                stats.put("abnormal", 0);
                stats.put("disabled", 0);
                stats.put("offline", 0);
            }

            // 确保所有统计项都有值，避免前端出现 null
            if (!stats.containsKey("total")) stats.put("total", 0);
            if (!stats.containsKey("online")) stats.put("online", 0);
            if (!stats.containsKey("abnormal")) stats.put("abnormal", 0);
            if (!stats.containsKey("disabled")) stats.put("disabled", 0);
            if (!stats.containsKey("offline")) stats.put("offline", 0);

            return stats;
        } catch (Exception e) {
            logger.error("Failed to get agent stats", e);
            // 出错时返回空统计数据
            Map<String, Object> emptyStats = new HashMap<>();
            emptyStats.put("total", 0);
            emptyStats.put("online", 0);
            emptyStats.put("abnormal", 0);
            emptyStats.put("disabled", 0);
            emptyStats.put("offline", 0);
            return emptyStats;
        }
    }
}
