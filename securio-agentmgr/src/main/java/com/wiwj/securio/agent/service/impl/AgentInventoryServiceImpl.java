package com.wiwj.securio.agent.service.impl;

import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.agent.service.IAgentInventoryService;
import com.wiwj.securio.agent.mapper.AgentInventoryMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * Agent资产清单统计信息Service实现
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Service
public class AgentInventoryServiceImpl implements IAgentInventoryService {
    private static final Logger log = LoggerFactory.getLogger(AgentInventoryServiceImpl.class);

    @Autowired
    private AgentInventoryMapper agentInventoryMapper;

    /**
     * 获取Agent资产清单统计信息
     * 
     * @param agentId Agent ID
     * @return 资产清单统计信息
     */
    @Override
    public Map<String, Object> getAgentInventoryStats(String agentId) {
        log.info("Getting inventory stats for agentId: {}", agentId);
        try {
            // 调用mapper一次查询获取所有统计数据（效率更高）
            Map<String, Object> stats = agentInventoryMapper.selectAgentInventoryStats(agentId);
            
            if (stats == null || stats.isEmpty()) {
                log.info("No inventory stats found for agentId: {}, using individual queries", agentId);
                // 如果批量查询失败，尝试单独查询各项统计数据
                stats = new HashMap<>();
                stats.put("processes", agentInventoryMapper.countProcesses(agentId));
                stats.put("ports", agentInventoryMapper.countPorts(agentId));
                stats.put("software", agentInventoryMapper.countSoftware(agentId));
                stats.put("users", agentInventoryMapper.countUsers(agentId));
                stats.put("risks", 0); // 风险数暂无对应表
                stats.put("startups", agentInventoryMapper.countStartups(agentId));
                stats.put("kernelModules", agentInventoryMapper.countKernelModules(agentId));
                stats.put("scheduledTasks", agentInventoryMapper.countScheduledTasks(agentId));
                stats.put("dockerImages", 0); // Docker镜像数暂无对应表
                stats.put("dockerContainers", 0); // Docker容器数暂无对应表
                stats.put("dockerNetworks", 0); // Docker网络数暂无对应表
            }
            
            // 转换数值类型：确保所有统计项为整数
            convertToInteger(stats);
            
            return stats;
        } catch (Exception e) {
            log.error("Failed to get inventory stats for agentId: {}", agentId, e);
            // 出错时返回空统计数据
            Map<String, Object> emptyStats = new HashMap<>();
            emptyStats.put("processes", 0);
            emptyStats.put("ports", 0);
            emptyStats.put("software", 0);
            emptyStats.put("users", 0);
            emptyStats.put("risks", 0);
            emptyStats.put("startups", 0);
            emptyStats.put("kernelModules", 0);
            emptyStats.put("scheduledTasks", 0);
            emptyStats.put("dockerImages", 0);
            emptyStats.put("dockerContainers", 0);
            emptyStats.put("dockerNetworks", 0);
            return emptyStats;
        }
    }
    
    /**
     * 获取全局资产清单统计信息
     * 
     * @return 全局资产清单统计信息
     */
    @Override
    public Map<String, Object> getGlobalInventoryStats() {
        log.info("Getting global inventory stats");
        try {
            // 调用mapper一次查询获取所有统计数据（效率更高）
            Map<String, Object> stats = agentInventoryMapper.selectGlobalInventoryStats();
            
            if (stats == null || stats.isEmpty()) {
                log.info("No global inventory stats found, returning empty stats");
                // 如果查询失败，返回空统计数据
                stats = new HashMap<>();
                stats.put("processes", 0);
                stats.put("ports", 0);
                stats.put("software", 0);
                stats.put("users", 0);
                stats.put("websites", 0);
                stats.put("startups", 0);
                stats.put("kernelModules", 0);
                stats.put("scheduledTasks", 0);
                stats.put("dockerImages", 0);
                stats.put("dockerContainers", 0);
                stats.put("dockerNetworks", 0);
            }
            
            // 转换数值类型：确保所有统计项为整数
            convertToInteger(stats);
            
            return stats;
        } catch (Exception e) {
            log.error("Failed to get global inventory stats", e);
            // 出错时返回空统计数据
            Map<String, Object> emptyStats = new HashMap<>();
            emptyStats.put("processes", 0);
            emptyStats.put("ports", 0);
            emptyStats.put("software", 0);
            emptyStats.put("users", 0);
            emptyStats.put("websites", 0);
            emptyStats.put("startups", 0);
            emptyStats.put("kernelModules", 0);
            emptyStats.put("scheduledTasks", 0);
            emptyStats.put("dockerImages", 0);
            emptyStats.put("dockerContainers", 0);
            emptyStats.put("dockerNetworks", 0);
            return emptyStats;
        }
    }

    /**
     * 将统计数据转换为整数类型
     * 
     * @param stats 统计数据Map
     */
    private void convertToInteger(Map<String, Object> stats) {
        if (stats != null) {
            for (Map.Entry<String, Object> entry : stats.entrySet()) {
                if (entry.getValue() != null) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    
                    if (value instanceof Number) {
                        // 如果是数字类型，转为整数
                        stats.put(key, NumberUtils.toInt(String.valueOf(value), 0));
                    } else if (value instanceof String) {
                        // 如果是字符串类型，尝试转为整数
                        try {
                            stats.put(key, NumberUtils.toInt((String) value, 0));
                        } catch (Exception e) {
                            log.warn("Failed to convert value for key {} to integer: {}", key, value);
                            stats.put(key, 0);
                        }
                    } else {
                        // 其他类型，设为0
                        stats.put(key, 0);
                    }
                } else {
                    // 空值，设为0
                    stats.put(entry.getKey(), 0);
                }
            }
        }
    }
} 