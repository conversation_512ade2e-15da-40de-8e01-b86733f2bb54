package com.wiwj.securio.agent.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.agent.domain.BaseConfig;
import com.wiwj.securio.agent.service.IBaseConfigService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * agent 基础配置Controller
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@RestController
@RequestMapping("/agent/baseConfig")
public class BaseConfigController extends BaseController {
    @Autowired
    private IBaseConfigService baseConfigService;

    /**
     * 查询agent 基础配置列表
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping("/list")
    public TableDataInfo list(BaseConfig baseConfig) {
        startPage();
        List<BaseConfig> list = baseConfigService.selectBaseConfigList(baseConfig);
        return getDataTable(list);
    }

    /**
     * 导出agent 基础配置列表
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "agent 基础配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseConfig baseConfig) {
        List<BaseConfig> list = baseConfigService.selectBaseConfigList(baseConfig);
        ExcelUtil<BaseConfig> util = new ExcelUtil<BaseConfig>(BaseConfig.class);
        util.exportExcel(response, list, "agent 基础配置数据");
    }

    /**
     * 获取agent 基础配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(baseConfigService.selectBaseConfigById(id));
    }

    /**
     * 获取最新版本的基础配置
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping("/getCurrentBaseConfig")
    public AjaxResult getCurrentBaseConfig() {
        return success(baseConfigService.selectBaseConfigWithMaxVersion());
    }

    /**
     * 新增agent 基础配置
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "agent 基础配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseConfig baseConfig) {
        return toAjax(baseConfigService.insertBaseConfig(baseConfig));
    }

    /**
     * 修改agent 基础配置
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "agent 基础配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseConfig baseConfig) {
        // 获取当前最大版本号的配置
        BaseConfig maxVersionConfig = baseConfigService.selectBaseConfigWithMaxVersion();
        if (maxVersionConfig != null) {
            // 设置版本号加1
            baseConfig.setVersion(maxVersionConfig.getVersion() + 1);
        } else {
            // 如果没有配置，设置版本号为1
            baseConfig.setVersion(1L);
        }
        return toAjax(baseConfigService.updateBaseConfig(baseConfig));
    }

    /**
     * 删除agent 基础配置
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "agent 基础配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(baseConfigService.deleteBaseConfigByIds(ids));
    }
}
