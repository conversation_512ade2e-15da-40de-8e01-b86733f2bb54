package com.wiwj.securio.agent.service;

import java.util.List;
import java.util.Map;

import com.wiwj.securio.agent.domain.InventoryPortInfo;
import com.wiwj.securio.agent.domain.NetworkConnectionStatDTO;

/**
 * 存储Agent主机的端口信息Service接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface IInventoryPortInfoService {
    /**
     * 查询存储Agent主机的端口信息
     *
     * @param id 存储Agent主机的端口信息主键
     * @return 存储Agent主机的端口信息
     */
    public InventoryPortInfo selectInventoryPortInfoById(Long id);

    /**
     * 查询存储Agent主机的端口信息列表
     *
     * @param inventoryPortInfo 存储Agent主机的端口信息
     * @return 存储Agent主机的端口信息集合
     */
    public List<InventoryPortInfo> selectInventoryPortInfoList(InventoryPortInfo inventoryPortInfo);
    
    /**
     * 根据主机IP获取端口统计信息
     *
     * @param hostIp 主机IP
     * @return 端口统计信息，包含按状态分组的数量
     */
    public Map<String, Long> selectPortStatisticsByHostIp(String hostIp);
    
    /**
     * 根据主机IP获取外部连接信息
     *
     * @param hostIp 主机IP
     * @return 外部连接信息列表
     */
    public List<Map<String, Object>> selectExternalConnections(String hostIp);
    
    /**
     * 根据主机IP获取网络连接统计信息
     * 
     * @param hostIp 主机IP
     * @return 网络连接统计信息列表
     */
    public List<NetworkConnectionStatDTO> selectNetworkConnectionStats(String hostIp);

    /**
     * 根据主机IP获取本地监听端口列表
     * 
     * @param hostIp 主机IP
     * @return 本地监听端口列表
     */
    public List<Map<String, Object>> selectListeningPorts(String hostIp);

    /**
     * 新增存储Agent主机的端口信息
     *
     * @param inventoryPortInfo 存储Agent主机的端口信息
     * @return 结果
     */
    public int insertInventoryPortInfo(InventoryPortInfo inventoryPortInfo);

    /**
     * 修改存储Agent主机的端口信息
     *
     * @param inventoryPortInfo 存储Agent主机的端口信息
     * @return 结果
     */
    public int updateInventoryPortInfo(InventoryPortInfo inventoryPortInfo);

    /**
     * 批量删除存储Agent主机的端口信息
     *
     * @param ids 需要删除的存储Agent主机的端口信息主键集合
     * @return 结果
     */
    public int deleteInventoryPortInfoByIds(Long[] ids);

    /**
     * 删除存储Agent主机的端口信息
     *
     * @param id 存储Agent主机的端口信息主键
     * @return 结果
     */
    public int deleteInventoryPortInfoById(Long id);

    /**
     * 删除存储Agent主机的端口信息
     *
     * @param agentId Agent ID
     * @return 结果
     */
    public int deleteInventoryPortInfoByAgentId(String agentId);

    /**
     * 批量新增存储Agent主机的端口信息
     *
     * @param inventoryPortInfoList 存储Agent主机的端口信息列表
     * @return 结果
     */
    public int batchInsertInventoryPortInfo(List<InventoryPortInfo> inventoryPortInfoList);
}
