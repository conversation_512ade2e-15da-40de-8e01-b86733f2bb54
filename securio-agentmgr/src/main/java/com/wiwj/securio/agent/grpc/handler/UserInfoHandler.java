package com.wiwj.securio.agent.grpc.handler;

import com.wiwj.securio.agent.domain.AgentInfo;
import com.wiwj.securio.agent.domain.InventoryUserInfo;
import com.wiwj.securio.agent.grpc.proto.UserInfoData;
import com.wiwj.securio.agent.grpc.proto.UserInfoRequest;
import com.wiwj.securio.agent.grpc.proto.UserInfoResponse;
import com.wiwj.securio.agent.service.IAgentInfoService;
import com.wiwj.securio.agent.service.IInventoryUserInfoService;
import io.grpc.stub.StreamObserver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户信息上报处理器
 */
@Component
public class UserInfoHandler extends AbstractGrpcRequestHandler<UserInfoRequest, UserInfoResponse> {

    @Autowired
    private IAgentInfoService agentInfoService;

    @Autowired
    private IInventoryUserInfoService inventoryUserInfoService;

    @Override
    public void handle(UserInfoRequest request, StreamObserver<UserInfoResponse> responseObserver) {
        logger.info("Received user info from agent: {}, page: {}", request.getUuid(),request.getPage());

        try {
            // 验证token
            if (!validateToken(request.getToken())) {
                UserInfoResponse response = UserInfoResponse.newBuilder()
                        .setSuccess(false)
                        .setError("Invalid token")
                        .build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }

            AgentInfo agentInfo = agentInfoService.selectAgentInfoByAgentId(request.getUuid());
            if (agentInfo == null) {
                UserInfoResponse response = UserInfoResponse.newBuilder()
                        .setSuccess(false)
                        .setError("Agent not found")
                        .build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }

            if(request.getPage() == 1){
                int deleteCount = inventoryUserInfoService.deleteInventoryUserInfoByAgentId(request.getUuid());
                logger.info("Deleted {} user info records for agent: {}", deleteCount, agentInfo.getHostIp());
            }

            // 保存用户信息到数据库
            List<InventoryUserInfo> users = new ArrayList<>();

            for (UserInfoData userData : request.getUsersList()) {
                InventoryUserInfo userInfo = new InventoryUserInfo();
                userInfo.setAgentId(request.getUuid());
                userInfo.setUsername(userData.getUsername());
                userInfo.setUid(userData.getUid());
                userInfo.setGid(userData.getGid());
                userInfo.setHomeDir(userData.getHomeDir());
                userInfo.setShell(userData.getShell());
                userInfo.setDescription(userData.getDescription());
                userInfo.setPrivileged(userData.getPrivileged() ? 1 : 0);
                userInfo.setCanLogin(userData.getCanLogin() ? 1 : 0);
                userInfo.setEnabled(userData.getEnabled() ? 1 : 0);
                userInfo.setCreateAt(System.currentTimeMillis());
                userInfo.setUpdateAt(System.currentTimeMillis());
                userInfo.setCreateBy("system");
                userInfo.setUpdateBy("system");
                userInfo.setIsDel(0);
                userInfo.setHostIp(agentInfo.getHostIp());
                users.add(userInfo);
            }

            // 批量插入用户信息
            if (!users.isEmpty()) {
                if(request.getPage() == 0){
                    int deleteCount = inventoryUserInfoService.deleteInventoryUserInfoByAgentId(request.getUuid());
                    logger.info("Deleted {} user info records for agent: {}", deleteCount, request.getUuid());
                }
                inventoryUserInfoService.batchInsertInventoryUserInfo(users);
            }

            UserInfoResponse response = UserInfoResponse.newBuilder()
                    .setSuccess(true)
                    .build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            logger.error("Error processing user info for agent: " + request.getUuid(), e);
            UserInfoResponse response = UserInfoResponse.newBuilder()
                    .setSuccess(false)
                    .setError("Internal error: " + e.getMessage())
                    .build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }
}
