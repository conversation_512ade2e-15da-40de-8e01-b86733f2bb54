package com.wiwj.securio.agent.service;

import java.util.Map;

/**
 * Agent资产清单统计信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface IAgentInventoryService {
    /**
     * 获取Agent资产清单统计信息
     * 
     * @param agentId Agent ID
     * @return 资产清单统计信息，包括：
     *         - processes: 进程数
     *         - ports: 端口数
     *         - software: 软件数
     *         - users: 用户数
     *         - risks: 风险数
     *         - startups: 启动项数
     *         - kernelModules: 内核模块数
     *         - scheduledTasks: 计划任务数
     *         - dockerImages: Docker镜像数
     *         - dockerContainers: Docker容器数
     *         - dockerNetworks: Docker网络数
     */
    Map<String, Object> getAgentInventoryStats(String agentId);
    
    /**
     * 获取全局资产清单统计信息
     * 
     * @return 全局资产清单统计信息，包括：
     *         - processes: 进程数
     *         - ports: 端口数
     *         - software: 软件数
     *         - users: 用户数
     *         - websites: 网站数
     *         - startups: 启动项数
     *         - kernelModules: 内核模块数
     *         - scheduledTasks: 计划任务数
     *         - dockerImages: Docker镜像数
     *         - dockerContainers: Docker容器数
     *         - dockerNetworks: Docker网络数
     */
    Map<String, Object> getGlobalInventoryStats();
} 