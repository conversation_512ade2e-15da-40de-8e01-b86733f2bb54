package com.wiwj.securio.agent.mapper;

import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Agent资产清单统计数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Mapper
public interface AgentInventoryMapper {
    
    /**
     * 获取Agent所有资产清单的统计数据
     * 
     * @param agentId Agent ID
     * @return 统计数据Map
     */
    @Select({
        "<script>",
        "SELECT",
        "  (SELECT COUNT(1) FROM inventory_process_info WHERE agent_id = #{agentId}) AS processes,",
        "  (SELECT COUNT(1) FROM inventory_port_info WHERE agent_id = #{agentId}) AS ports,",
        "  (SELECT COUNT(1) FROM inventory_package_info WHERE agent_id = #{agentId}) AS software,",
        "  (SELECT COUNT(1) FROM inventory_user_info WHERE agent_id = #{agentId}) AS users,",
        "  0 AS risks,", // 风险数，暂时没有相关表，默认为0
        "  (SELECT COUNT(1) FROM inventory_startup_info WHERE agent_id = #{agentId}) AS startups,",
        "  (SELECT COUNT(1) FROM inventory_kernel_module_info WHERE agent_id = #{agentId}) AS kernelModules,",
        "  (SELECT COUNT(1) FROM inventory_scheduled_task_info WHERE agent_id = #{agentId}) AS scheduledTasks,",
        "  0 AS dockerImages,", // Docker镜像数，暂时没有相关表，默认为0
        "  0 AS dockerContainers,", // Docker容器数，暂时没有相关表，默认为0
        "  0 AS dockerNetworks", // Docker网络数，暂时没有相关表，默认为0
        "</script>"
    })
    Map<String, Object> selectAgentInventoryStats(@Param("agentId") String agentId);
    
    /**
     * 获取全局资产清单统计数据（所有Agent的资产清单统计汇总）
     * 
     * @return 全局统计数据Map
     */
    @Select({
        "<script>",
        "SELECT",
        "  (SELECT COUNT(1) FROM inventory_process_info) AS processes,",
        "  (SELECT COUNT(1) FROM inventory_port_info) AS ports,",
        "  (SELECT COUNT(1) FROM inventory_package_info) AS software,",
        "  (SELECT COUNT(1) FROM inventory_user_info) AS users,",
        "  0 AS websites,", // 网站表不存在，暂时使用固定值0
        "  (SELECT COUNT(1) FROM inventory_startup_info) AS startups,",
        "  (SELECT COUNT(1) FROM inventory_kernel_module_info) AS kernelModules,",
        "  (SELECT COUNT(1) FROM inventory_scheduled_task_info) AS scheduledTasks,",
        "  0 AS dockerImages,", // Docker镜像表不存在，使用固定值0
        "  0 AS dockerContainers,", // Docker容器表不存在，使用固定值0
        "  0 AS dockerNetworks", // Docker网络表不存在，使用固定值0
        "</script>"
    })
    Map<String, Object> selectGlobalInventoryStats();
    
    /**
     * 获取进程数量
     */
    @Select("SELECT COUNT(1) FROM inventory_process_info WHERE agent_id = #{agentId}")
    int countProcesses(@Param("agentId") String agentId);
    
    /**
     * 获取端口数量
     */
    @Select("SELECT COUNT(1) FROM inventory_port_info WHERE agent_id = #{agentId}")
    int countPorts(@Param("agentId") String agentId);
    
    /**
     * 获取软件包数量
     */
    @Select("SELECT COUNT(1) FROM inventory_package_info WHERE agent_id = #{agentId}")
    int countSoftware(@Param("agentId") String agentId);
    
    /**
     * 获取用户数量
     */
    @Select("SELECT COUNT(1) FROM inventory_user_info WHERE agent_id = #{agentId}")
    int countUsers(@Param("agentId") String agentId);
    
    /**
     * 获取启动项数量
     */
    @Select("SELECT COUNT(1) FROM inventory_startup_info WHERE agent_id = #{agentId}")
    int countStartups(@Param("agentId") String agentId);
    
    /**
     * 获取内核模块数量
     */
    @Select("SELECT COUNT(1) FROM inventory_kernel_module_info WHERE agent_id = #{agentId}")
    int countKernelModules(@Param("agentId") String agentId);
    
    /**
     * 获取计划任务数量
     */
    @Select("SELECT COUNT(1) FROM inventory_scheduled_task_info WHERE agent_id = #{agentId}")
    int countScheduledTasks(@Param("agentId") String agentId);
} 