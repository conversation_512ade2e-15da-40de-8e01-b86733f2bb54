package com.wiwj.securio.agent.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.agent.mapper.InventoryPortInfoMapper;
import com.wiwj.securio.agent.domain.InventoryPortInfo;
import com.wiwj.securio.agent.domain.NetworkConnectionStatDTO;
import com.wiwj.securio.agent.service.IInventoryPortInfoService;

/**
 * 存储Agent主机的端口信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Service
public class InventoryPortInfoServiceImpl implements IInventoryPortInfoService {
    
    private static final Logger logger = LoggerFactory.getLogger(InventoryPortInfoServiceImpl.class);
    
    @Autowired
    private InventoryPortInfoMapper inventoryPortInfoMapper;

    /**
     * 查询存储Agent主机的端口信息
     *
     * @param id 存储Agent主机的端口信息主键
     * @return 存储Agent主机的端口信息
     */
    @Override
    public InventoryPortInfo selectInventoryPortInfoById(Long id) {
        return inventoryPortInfoMapper.selectInventoryPortInfoById(id);
    }

    /**
     * 查询存储Agent主机的端口信息列表
     *
     * @param inventoryPortInfo 存储Agent主机的端口信息
     * @return 存储Agent主机的端口信息
     */
    @Override
    public List<InventoryPortInfo> selectInventoryPortInfoList(InventoryPortInfo inventoryPortInfo) {
        return inventoryPortInfoMapper.selectInventoryPortInfoList(inventoryPortInfo);
    }

    /**
     * 新增存储Agent主机的端口信息
     *
     * @param inventoryPortInfo 存储Agent主机的端口信息
     * @return 结果
     */
    @Override
    public int insertInventoryPortInfo(InventoryPortInfo inventoryPortInfo) {
        inventoryPortInfo.setIsDel(0);
        inventoryPortInfo.setCreateAt(System.currentTimeMillis());
        inventoryPortInfo.setUpdateAt(System.currentTimeMillis());
        return inventoryPortInfoMapper.insertInventoryPortInfo(inventoryPortInfo);
    }

    /**
     * 修改存储Agent主机的端口信息
     *
     * @param inventoryPortInfo 存储Agent主机的端口信息
     * @return 结果
     */
    @Override
    public int updateInventoryPortInfo(InventoryPortInfo inventoryPortInfo) {
        return inventoryPortInfoMapper.updateInventoryPortInfo(inventoryPortInfo);
    }

    /**
     * 批量删除存储Agent主机的端口信息
     *
     * @param ids 需要删除的存储Agent主机的端口信息主键
     * @return 结果
     */
    @Override
    public int deleteInventoryPortInfoByIds(Long[] ids) {
        return inventoryPortInfoMapper.deleteInventoryPortInfoByIds(ids);
    }

    /**
     * 删除存储Agent主机的端口信息信息
     *
     * @param id 存储Agent主机的端口信息主键
     * @return 结果
     */
    @Override
    public int deleteInventoryPortInfoById(Long id) {
        return inventoryPortInfoMapper.deleteInventoryPortInfoById(id);
    }

    /**
     * 批量新增存储Agent主机的端口信息
     *
     * @param inventoryPortInfoList 存储Agent主机的端口信息列表
     * @return 结果
     */
    @Override
    public int batchInsertInventoryPortInfo(List<InventoryPortInfo> inventoryPortInfoList) {
        return inventoryPortInfoMapper.batchInsertInventoryPortInfo(inventoryPortInfoList);
    }

    @Override
    public int deleteInventoryPortInfoByAgentId(String agentId) {
        return inventoryPortInfoMapper.deleteInventoryPortInfoByAgentId(agentId);
    }
    
    /**
     * 根据主机IP获取端口统计信息
     *
     * @param hostIp 主机IP
     * @return 端口统计信息，包含按状态分组的数量
     */
    @Override
    public Map<String, Long> selectPortStatisticsByHostIp(String hostIp) {
        logger.info("获取主机[{}]的端口统计信息", hostIp);
        Map<String, Long> result = new HashMap<>();
        
        try {
            // 构建查询条件
            InventoryPortInfo query = new InventoryPortInfo();
            query.setHostIp(hostIp);
            
            // 查询该主机下的所有端口信息
            List<InventoryPortInfo> portInfoList = inventoryPortInfoMapper.selectInventoryPortInfoList(query);
            
            if (portInfoList != null && !portInfoList.isEmpty()) {
                // 按照端口状态进行分组统计
                result = portInfoList.stream()
                        .filter(port -> port.getState() != null && !port.getState().isEmpty())
                        .collect(Collectors.groupingBy(InventoryPortInfo::getState, Collectors.counting()));
                
                // 确保至少包含一些常见状态，即使数量为0
                ensureCommonStates(result);
            } else {
                // 如果没有数据，返回一些默认的状态分类
                ensureCommonStates(result);
            }
        } catch (Exception e) {
            logger.error("获取主机[{}]的端口统计信息失败", hostIp, e);
            // 发生异常时返回一些默认值
            ensureCommonStates(result);
        }
        
        return result;
    }
    
    /**
     * 根据主机IP获取外部连接信息
     *
     * @param hostIp 主机IP
     * @return 外部连接信息列表
     */
    @Override
    public List<Map<String, Object>> selectExternalConnections(String hostIp) {
        logger.info("获取主机[{}]的外部连接信息", hostIp);
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            // 构建查询条件
            InventoryPortInfo query = new InventoryPortInfo();
            query.setHostIp(hostIp);
            
            // 查询该主机下的所有端口信息
            List<InventoryPortInfo> portInfoList = inventoryPortInfoMapper.selectInventoryPortInfoList(query);
            
            if (portInfoList != null && !portInfoList.isEmpty()) {
                // 过滤出与外部有连接的端口信息，并转换为所需的结构
                for (InventoryPortInfo port : portInfoList) {
                    // 只处理有远程地址的连接（非LISTEN状态的通常有远程地址）
                    if (port.getRemoteIp() != null && !port.getRemoteIp().isEmpty() 
                            && !"0.0.0.0".equals(port.getRemoteIp()) 
                            && !"*".equals(port.getRemoteIp())) {
                        
                        Map<String, Object> connection = new HashMap<>();
                        connection.put("localIp", port.getHostIp());
                        connection.put("localPort", port.getLocalPort());
                        connection.put("remoteIp", port.getRemoteIp());
                        connection.put("remotePort", port.getRemotePort());
                        connection.put("status", port.getState());
                        connection.put("protocol", port.getProtocol());
                        connection.put("processName", port.getProcess());
                        connection.put("pid", port.getPid());
                        
                        result.add(connection);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取主机[{}]的外部连接信息失败", hostIp, e);
        }
        
        return result;
    }
    
    /**
     * 根据主机IP获取网络连接统计信息
     * 
     * @param hostIp 主机IP
     * @return 网络连接统计信息列表
     */
    @Override
    public List<NetworkConnectionStatDTO> selectNetworkConnectionStats(String hostIp) {
        logger.info("获取主机[{}]的网络连接统计信息", hostIp);
        
        try {
            // 获取连接统计原始数据
            List<Map<String, Object>> rawStats = inventoryPortInfoMapper.selectNetworkConnectionStats(hostIp);
            List<NetworkConnectionStatDTO> result = new ArrayList<>();
            
            if (rawStats != null && !rawStats.isEmpty()) {
                // 收集所有远程 IP
                Set<String> remoteIps = new HashSet<>();
                for (Map<String, Object> item : rawStats) {
                    String remoteIp = (String) item.get("remoteIp");
                    if (remoteIp != null && !remoteIp.isEmpty() && !"0.0.0.0".equals(remoteIp) && !"*".equals(remoteIp)) {
                        remoteIps.add(remoteIp);
                    }
                }
                
                // 根据远程 IP 批量查询主机名
                Map<String, String> ipHostnameMap = new HashMap<>();
                if (!remoteIps.isEmpty()) {
                    try {
                        // 在这里做批量查询，而不是调用现有的方法
                        List<Map<String, Object>> hostnameList = inventoryPortInfoMapper.selectHostnameByIpList(remoteIps);
                        for (Map<String, Object> hostItem : hostnameList) {
                            String ip = (String) hostItem.get("hostIp");
                            String hostname = (String) hostItem.get("hostname");
                            if (ip != null && hostname != null) {
                                ipHostnameMap.put(ip, hostname);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("获取主机名信息失败", e);
                    }
                }
                
                // 将原始数据转换为 DTO 对象
                for (Map<String, Object> item : rawStats) {
                    NetworkConnectionStatDTO dto = new NetworkConnectionStatDTO();
                    dto.setPid((Long) item.get("pid"));
                    dto.setProcessName((String) item.get("processName"));
                    dto.setRemoteIp((String) item.get("remoteIp"));
                    
                    Object countObj = item.get("connectionCount");
                    if (countObj instanceof Integer) {
                        dto.setConnectionCount((Integer) countObj);
                    } else if (countObj instanceof Long) {
                        dto.setConnectionCount(((Long) countObj).intValue());
                    } else if (countObj != null) {
                        dto.setConnectionCount(Integer.valueOf(countObj.toString()));
                    }
                    
                    // 设置主机名
                    String remoteIp = dto.getRemoteIp();
                    if (remoteIp != null && ipHostnameMap.containsKey(remoteIp)) {
                        dto.setHostname(ipHostnameMap.get(remoteIp));
                    } else {
                        dto.setHostname("");
                    }
                    
                    result.add(dto);
                }
            }
            
            return result;
        } catch (Exception e) {
            logger.error("获取主机[{}]的网络连接统计信息失败", hostIp, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 确保结果中包含常见的端口状态，如果不存在则添加为0
     * 
     * @param result 端口状态统计结果
     */
    private void ensureCommonStates(Map<String, Long> result) {
        String[] commonStates = {"LISTEN", "ESTABLISHED", "CLOSE_WAIT", "TIME_WAIT", "CLOSED"};
        for (String state : commonStates) {
            if (!result.containsKey(state)) {
                result.put(state, 0L);
            }
        }
    }
    
    /**
     * 根据主机IP获取本地监听端口列表
     * 
     * @param hostIp 主机IP
     * @return 本地监听端口列表
     */
    @Override
    public List<Map<String, Object>> selectListeningPorts(String hostIp) {
        logger.info("获取主机[{}]的本地监听端口列表", hostIp);
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            // 构建查询条件：只查询状态为LISTEN的端口
            InventoryPortInfo query = new InventoryPortInfo();
            query.setHostIp(hostIp);
            query.setState("LISTEN");
            
            // 查询该主机下的所有监听端口
            List<InventoryPortInfo> listeningPorts = inventoryPortInfoMapper.selectInventoryPortInfoList(query);
            
            if (listeningPorts != null && !listeningPorts.isEmpty()) {
                for (InventoryPortInfo port : listeningPorts) {
                    Map<String, Object> portInfo = new HashMap<>();
                    portInfo.put("localIp", port.getLocalIp());
                    portInfo.put("localPort", port.getLocalPort());
                    portInfo.put("protocol", port.getProtocol());
                    portInfo.put("process", port.getProcess());
                    portInfo.put("pid", port.getPid());
                    portInfo.put("state", port.getState());
                    result.add(portInfo);
                }
                
                // 按端口号排序
                result.sort((a, b) -> {
                    Object portAObj = a.get("localPort");
                    Object portBObj = b.get("localPort");
                    
                    Integer portA = portAObj instanceof Long ? ((Long) portAObj).intValue() : (Integer) portAObj;
                    Integer portB = portBObj instanceof Long ? ((Long) portBObj).intValue() : (Integer) portBObj;
                    
                    return portA.compareTo(portB);
                });
            }
            
            logger.info("成功获取主机[{}]的本地监听端口列表，共{}个端口", hostIp, result.size());
            
        } catch (Exception e) {
            logger.error("获取主机[{}]的本地监听端口列表失败", hostIp, e);
        }
        
        return result;
    }
}
