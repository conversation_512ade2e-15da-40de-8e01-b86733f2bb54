package com.wiwj.securio.agent.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.agent.domain.AgentInfo;
import com.wiwj.securio.agent.service.IAgentInfoService;
import com.wiwj.securio.agent.service.IAgentInventoryService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 探针信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
@RestController
@RequestMapping("/agent/agentInfo")
public class AgentInfoController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(AgentInfoController.class);

    @Autowired
    private IAgentInfoService agentInfoService;

    @Autowired
    private IAgentInventoryService agentInventoryService;

    /**
     * 查询探针信息列表
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping("/list")
    public TableDataInfo list(AgentInfo agentInfo) {
        startPage();
        List<AgentInfo> list = agentInfoService.selectAgentInfoList(agentInfo);
        return getDataTable(list);
    }

    /**
     * 导出探针信息列表
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "探针信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgentInfo agentInfo) {
        List<AgentInfo> list = agentInfoService.selectAgentInfoList(agentInfo);
        ExcelUtil<AgentInfo> util = new ExcelUtil<AgentInfo>(AgentInfo.class);
        util.exportExcel(response, list, "探针信息数据");
    }

    /**
     * 获取探针信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(agentInfoService.selectAgentInfoById(id));
    }

    /**
     * 根据主机IP获取探针信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping(value = "/hostIp/{hostIp}")
    public AjaxResult getInfoByHostIp(@PathVariable("hostIp") String hostIp) {
        return success(agentInfoService.selectAgentInfoByHostIp(hostIp));
    }

    /**
     * 新增探针信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "探针信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgentInfo agentInfo) {
        return toAjax(agentInfoService.insertAgentInfo(agentInfo));
    }

    /**
     * 修改探针信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "探针信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgentInfo agentInfo) {
        return toAjax(agentInfoService.updateAgentInfo(agentInfo));
    }

    /**
     * 删除探针信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "探针信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(agentInfoService.deleteAgentInfoByIds(ids));
    }

    /**
     * 获取探针资产清单统计信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping("/inventory/stats")
    public AjaxResult getInventoryStats(@RequestParam(required = false) String agentId,
                                        @RequestParam(required = false) String hostIp) {
        log.info("Getting inventory stats for agentId: {}, hostIp: {}", agentId, hostIp);

        if (agentId == null && hostIp == null) {
            return error("参数错误：agentId和hostIp不能同时为空");
        }

        // 如果只提供了hostIp，则通过hostIp获取agentId
        if (agentId == null) {
            AgentInfo agentInfo = agentInfoService.selectAgentInfoByHostIp(hostIp);
            if (agentInfo == null) {
                return error("未找到指定IP的探针信息");
            }
            agentId = agentInfo.getAgentId();
        }

        try {
            // 获取资产清单统计信息
            Map<String, Object> stats = agentInventoryService.getAgentInventoryStats(agentId);
            return success(stats);
        } catch (Exception e) {
            log.error("Failed to get inventory stats", e);
            return error("获取资产清单统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取全量资产清单统计信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping("/inventory/global/stats")
    public AjaxResult getGlobalInventoryStats() {
        log.info("Getting global inventory stats");
        try {
            // 获取全局资产清单统计信息
            Map<String, Object> stats = agentInventoryService.getGlobalInventoryStats();
            return success(stats);
        } catch (Exception e) {
            log.error("Failed to get global inventory stats", e);
            return error("获取全局资产清单统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取探针统计数据
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping("/stats")
    public AjaxResult getAgentStats() {
        log.info("Getting agent stats");
        try {
            // 获取探针统计数据
            Map<String, Object> stats = agentInfoService.getAgentStats();
            return success(stats);
        } catch (Exception e) {
            log.error("Failed to get agent stats", e);
            return error("获取探针统计数据失败：" + e.getMessage());
        }
    }
}
