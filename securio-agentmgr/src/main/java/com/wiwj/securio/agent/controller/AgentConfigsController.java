package com.wiwj.securio.agent.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.agent.domain.AgentConfigs;
import com.wiwj.securio.agent.service.IAgentConfigsService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * Agent 自定义配置Controller
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@RestController
@RequestMapping("/agent/agentConfigs")
public class AgentConfigsController extends BaseController {
    @Autowired
    private IAgentConfigsService agentConfigsService;

    /**
     * 查询Agent 自定义配置列表
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping("/list")
    public TableDataInfo list(AgentConfigs agentConfigs) {
        startPage();
        List<AgentConfigs> list = agentConfigsService.selectAgentConfigsList(agentConfigs);
        return getDataTable(list);
    }

    /**
     * 导出Agent 自定义配置列表
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "Agent 自定义配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgentConfigs agentConfigs) {
        List<AgentConfigs> list = agentConfigsService.selectAgentConfigsList(agentConfigs);
        ExcelUtil<AgentConfigs> util = new ExcelUtil<AgentConfigs>(AgentConfigs.class);
        util.exportExcel(response, list, "Agent 自定义配置数据");
    }

    /**
     * 获取Agent 自定义配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(agentConfigsService.selectAgentConfigsById(id));
    }

    /**
     * 获取Agent 自定义配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @GetMapping(value = "/agentId/{agentId}")
    public AjaxResult getByAgentId(@PathVariable("agentId") String agentId) {
        return success(agentConfigsService.getAgentConfigs(agentId));
    }

    /**
     * 新增Agent 自定义配置
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "Agent 自定义配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgentConfigs agentConfigs) {
        return toAjax(agentConfigsService.insertAgentConfigs(agentConfigs));
    }

    /**
     * 修改Agent 自定义配置
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "Agent 自定义配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgentConfigs agentConfigs) {
        return toAjax(agentConfigsService.updateAgentConfigs(agentConfigs));
    }

    /**
     * 删除Agent 自定义配置
     */
    @PreAuthorize("@ss.hasPermi('agent:agentInfo')")
    @Log(title = "Agent 自定义配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(agentConfigsService.deleteAgentConfigsByIds(ids));
    }
}
