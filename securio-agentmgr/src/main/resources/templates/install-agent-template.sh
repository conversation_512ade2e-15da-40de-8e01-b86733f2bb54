#!/bin/bash

# Securio Agent 安装脚本
# 下载 URL 和安装目录
VERSION="${VERSION}"
TAR_FILE="/tmp/securio-agent-linux-amd64-${VERSION}.tar.gz"
DOWNLOAD_URL="${DOWNLOAD_URL}"
TEMP_DIR="/tmp/securio-agent-install"
INSTALL_DIR="/usr/local/securio-agent"
BINARY_NAME="securio-agent"
CONFIG_FILE="agent.yaml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${YELLOW}[INFO] $1${NC}"
}

print_detail() {
    echo -e "${YELLOW}       → $1${NC}"
}

print_success() {
    echo -e "${GREEN}[成功] $1${NC}"
}

print_success_detail() {
    echo -e "${GREEN}       ✓ $1${NC}"
}

print_error() {
    echo -e "${RED}[失败] $1${NC}"
    exit 1
}

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then
  print_error "请以 root 权限运行此脚本"
  exit 1
fi

# 检查服务是否已安装并运行
print_info "检查 Securio Agent 服务状态..."
SERVICE_INSTALLED=false
SERVICE_RUNNING=false

# 检查 systemd 服务
if command -v systemctl &> /dev/null; then
  if systemctl list-unit-files | grep -q securio-agent.service; then
    SERVICE_INSTALLED=true
    print_detail "检测到 systemd 服务已安装"

    if systemctl is-active --quiet securio-agent; then
      SERVICE_RUNNING=true
      print_detail "服务当前状态: 运行中"

      # 停止服务
      print_detail "正在停止 securio-agent 服务..."
      systemctl stop securio-agent
      sleep 2

      if systemctl is-active --quiet securio-agent; then
        print_detail "服务停止失败，尝试强制终止..."
        pkill -f "${BINARY_NAME}"
        sleep 2
      else
        print_success_detail "服务已成功停止"
      fi
    else
      print_detail "服务当前状态: 已停止"
    fi
  fi
# 检查 init.d 服务
elif [ -f "/etc/init.d/securio-agent" ]; then
  SERVICE_INSTALLED=true
  print_detail "检测到 init.d 服务已安装"

  if [ -f "/var/run/securio-agent.pid" ]; then
    PID=$(cat "/var/run/securio-agent.pid")
    if [ -n "$PID" ] && kill -0 $PID 2>/dev/null; then
      SERVICE_RUNNING=true
      print_detail "服务当前状态: 运行中"

      # 停止服务
      print_detail "正在停止 securio-agent 服务..."
      /etc/init.d/securio-agent stop
      sleep 2

      if [ -f "/var/run/securio-agent.pid" ]; then
        PID=$(cat "/var/run/securio-agent.pid")
        if [ -n "$PID" ] && kill -0 $PID 2>/dev/null; then
          print_detail "服务停止失败，尝试强制终止..."
          pkill -f "${BINARY_NAME}"
          sleep 2
        fi
      else
        print_success_detail "服务已成功停止"
      fi
    else
      print_detail "服务当前状态: 已停止"
    fi
  else
    print_detail "服务当前状态: 已停止"
  fi
fi

# 如果服务已安装，备份配置文件
if [ "$SERVICE_INSTALLED" = true ]; then
  print_info "准备进行全新安装..."

  # 创建临时备份目录
  BACKUP_DIR="/tmp/securio-agent-backup-$(date +%Y%m%d%H%M%S)"
  print_detail "创建备份目录: $BACKUP_DIR"
  mkdir -p "$BACKUP_DIR"

  # 备份配置文件（如果存在）
  if [ -d "${INSTALL_DIR}/configs" ]; then
    print_detail "备份配置文件..."
    cp -r "${INSTALL_DIR}/configs" "$BACKUP_DIR/" || print_detail "备份配置文件失败，将使用默认配置"
  fi

  print_success_detail "原有配置已备份到: $BACKUP_DIR"
  print_detail "继续进行全新安装..."
fi

# 下载安装包
print_info "正在下载 Securio Agent 安装包..."
print_detail "下载源: $DOWNLOAD_URL"
print_detail "保存到: $TAR_FILE"
print_detail "版本号: $VERSION"
curl -L "$DOWNLOAD_URL" -o "$TAR_FILE" || {
  print_error "下载失败，请检查网络连接或URL是否正确"
}
print_success "下载完成"
print_success_detail "文件大小: $(du -h "$TAR_FILE" | cut -f1) ($(stat -c%s "$TAR_FILE") 字节)"

# 创建临时目录并解压
print_info "解压 Securio Agent 安装包..."
print_detail "源文件: $TAR_FILE"
print_detail "解压到: $TEMP_DIR"
mkdir -p "$TEMP_DIR" || {
  print_error "创建临时目录 $TEMP_DIR 失败"
}
print_success_detail "创建临时目录 $TEMP_DIR 成功"

tar -xzf "$TAR_FILE" -C "$TEMP_DIR" || {
  print_error "解压失败，请检查tar.gz文件是否有效"
}
print_success "解压完成"
print_success_detail "文件已解压到 $TEMP_DIR 目录"

# 切换到解压目录
print_info "切换到解压目录..."
print_detail "目标目录: $TEMP_DIR/package"
cd "$TEMP_DIR/package" || {
  print_error "无法进入临时目录 $TEMP_DIR/package"
}
print_success_detail "当前工作目录: $(pwd)"

# 创建安装目录结构
print_info "创建 Securio Agent 安装目录结构..."
print_detail "主安装目录: $INSTALL_DIR"
print_detail "创建子目录: configs, data, logs"

# 删除现有目录（如果存在）以确保清洁安装
if [ -d "$INSTALL_DIR" ]; then
  print_detail "删除现有安装目录: $INSTALL_DIR"
  rm -rf "$INSTALL_DIR"
fi

# 创建新的目录结构
mkdir -p ${INSTALL_DIR}/{configs,data,logs} || {
  print_error "创建安装目录 $INSTALL_DIR 失败"
}

# 设置目录权限
print_detail "设置目录权限..."
chmod 755 "$INSTALL_DIR"              # 主目录可读可执行
chmod -R 755 "${INSTALL_DIR}/configs" # 配置目录可读可执行
chmod -R 750 "${INSTALL_DIR}/data"    # 数据目录只允许用户和组访问
chmod -R 750 "${INSTALL_DIR}/logs"    # 日志目录只允许用户和组访问

# 创建 securio-agent 用户和组
print_detail "创建 securio-agent 用户和组..."
if ! getent group securio-agent > /dev/null; then
  groupadd -r securio-agent
  print_success_detail "创建组 securio-agent 成功"
fi

if ! getent passwd securio-agent > /dev/null; then
  useradd -r -g securio-agent -s /sbin/nologin -d ${INSTALL_DIR} -c "Securio Agent Service Account" securio-agent
  print_success_detail "创建用户 securio-agent 成功"
fi

# 使用 ACL 机制授权 securio-agent 用户读取 /var/log 目录
print_detail "使用 ACL 机制授权 securio-agent 用户读取 /var/log 目录..."
if command -v setfacl &> /dev/null; then
  setfacl -R -m u:securio-agent:rX /var/log 2>/dev/null || print_detail "设置 /var/log 目录 ACL 权限失败，可能需要手动设置"
  setfacl -R -m d:u:securio-agent:rX /var/log 2>/dev/null || print_detail "设置 /var/log 目录默认 ACL 权限失败，可能需要手动设置"
  setfacl -m u:securio-agent:x /var/log/audit 2>/dev/null || print_detail "设置 /var/log 目录默认 ACL 权限失败，可能需要手动设置"
  setfacl -m u:securio-agent:r /var/log/cron /var/log/secure /var/log/messages /var/log/audit/audit.log 2>/dev/null|| print_detail "设置 /var/log 目录默认 ACL 权限失败，可能需要手动设置"
  print_success_detail "已授权 securio-agent 用户读取 /var/log 目录"
else
  print_detail "未找到 setfacl 命令，尝试安装 ACL 工具..."
  if command -v apt-get &> /dev/null; then
    apt-get update && apt-get install -y acl
  elif command -v yum &> /dev/null; then
    yum install -y acl
  elif command -v dnf &> /dev/null; then
    dnf install -y acl
  else
    print_detail "无法安装 ACL 工具，请手动安装并设置 /var/log 目录权限"
  fi

  # 再次尝试设置 ACL
  if command -v setfacl &> /dev/null; then
    setfacl -R -m u:securio-agent:rX /var/log 2>/dev/null || print_detail "设置 /var/log 目录 ACL 权限失败，可能需要手动设置"
    setfacl -R -m d:u:securio-agent:rX /var/log 2>/dev/null || print_detail "设置 /var/log 目录默认 ACL 权限失败，可能需要手动设置"
    setfacl -m u:securio-agent:x /var/log/audit 2>/dev/null || print_detail "设置 /var/log 目录默认 ACL 权限失败，可能需要手动设置"
    setfacl -m u:securio-agent:r /var/log/cron /var/log/secure /var/log/messages /var/log/audit/audit.log 2>/dev/null|| print_detail "设置 /var/log 目录默认 ACL 权限失败，可能需要手动设置"
    print_success_detail "已授权 securio-agent 用户读取 /var/log 目录"
  else
    print_detail "无法设置 ACL 权限，请手动授权 securio-agent 用户读取 /var/log 目录"
  fi
fi

# 设置目录所有权为 securio-agent 用户
chown -R securio-agent:securio-agent "$INSTALL_DIR"

print_success "创建安装目录完成"
print_success_detail "目录结构: $INSTALL_DIR/"
print_success_detail "          ├── configs/ (权限: $(stat -c %a ${INSTALL_DIR}/configs))"
print_success_detail "          ├── data/   (权限: $(stat -c %a ${INSTALL_DIR}/data))"
print_success_detail "          └── logs/   (权限: $(stat -c %a ${INSTALL_DIR}/logs))"

# 复制二进制文件
print_info "安装 Securio Agent 二进制文件..."
print_detail "源文件: $(pwd)/${BINARY_NAME}"
print_detail "目标位置: ${INSTALL_DIR}/${BINARY_NAME}"
if [ ! -f "${BINARY_NAME}" ]; then
  print_error "二进制文件 ${BINARY_NAME} 不存在于当前目录"
fi
cp ${BINARY_NAME} ${INSTALL_DIR}/ || {
  print_error "复制二进制文件失败，请检查文件权限"
}

# 设置正确的文件权限和所有权
chmod 750 ${INSTALL_DIR}/${BINARY_NAME}
chown securio-agent:securio-agent ${INSTALL_DIR}/${BINARY_NAME}

# 检查系统版本，决定是否使用 Linux capabilities
print_detail "检查系统版本..."
USE_CAPABILITIES=true

# 检查内核版本，确定是否支持 capabilities
KERNEL_VERSION=$(uname -r | cut -d. -f1,2)
if (( $(echo "$KERNEL_VERSION < 2.6.33" | bc -l 2>/dev/null) )); then
  print_detail "检测到低版本内核 $KERNEL_VERSION，将不使用 Linux capabilities"
  USE_CAPABILITIES=false
else
  print_detail "检测到内核版本 $KERNEL_VERSION，将使用 Linux capabilities"
fi

if [ "$USE_CAPABILITIES" = true ]; then
  # 检查并安装 libcap 工具
  print_detail "检查 libcap 工具..."
  if ! command -v setcap &> /dev/null; then
    print_detail "安装 libcap 工具..."
    if command -v apt-get &> /dev/null; then
      apt-get update && apt-get install -y libcap2-bin
    elif command -v yum &> /dev/null; then
      yum install -y libcap
    elif command -v dnf &> /dev/null; then
      dnf install -y libcap
    else
      print_detail "无法安装 libcap 工具，将使用 root 权限替代"
      USE_CAPABILITIES=false
    fi
  fi

  # 再次检查 setcap 命令是否可用
  if [ "$USE_CAPABILITIES" = true ] && ! command -v setcap &> /dev/null; then
    print_detail "未找到 setcap 命令，将使用 root 权限替代"
    USE_CAPABILITIES=false
  fi

  # 设置 Linux capabilities
  if [ "$USE_CAPABILITIES" = true ]; then
    print_detail "设置 Linux capabilities..."
    if ! setcap "cap_chown,cap_dac_override,cap_dac_read_search,cap_fowner,cap_kill,cap_net_bind_service,cap_net_admin,cap_net_raw,cap_ipc_lock,cap_ipc_owner,cap_sys_module,cap_sys_rawio,cap_sys_ptrace,cap_sys_admin,cap_sys_resource,cap_audit_control,cap_syslog+ei" ${INSTALL_DIR}/${BINARY_NAME} 2>/dev/null; then
      print_detail "设置 Linux capabilities 失败，将使用 root 权限替代"
      USE_CAPABILITIES=false
    else
      print_success_detail "Linux capabilities 设置成功: $(getcap ${INSTALL_DIR}/${BINARY_NAME} 2>/dev/null || echo '无法获取 capabilities 信息')"
    fi
  fi
fi

# 如果不使用 capabilities，则使用 root 权限
if [ "$USE_CAPABILITIES" = false ]; then
  print_detail "配置 agent 以 root 权限运行..."
  # 设置二进制文件为 root 所有并添加 setuid 位
  chown root:root ${INSTALL_DIR}/${BINARY_NAME}
  chmod 4750 ${INSTALL_DIR}/${BINARY_NAME}
  print_success_detail "已设置二进制文件为 root 所有并添加 setuid 位 (权限: $(stat -c '%a' ${INSTALL_DIR}/${BINARY_NAME}))"
fi

print_success "二进制文件安装完成"
print_success_detail "文件大小: $(du -h "${INSTALL_DIR}/${BINARY_NAME}" | cut -f1)"
print_success_detail "文件权限: $(stat -c '%a' ${INSTALL_DIR}/${BINARY_NAME})"
print_success_detail "文件所有者: $(stat -c '%U:%G' ${INSTALL_DIR}/${BINARY_NAME})"

# 复制配置文件
print_info "安装 Securio Agent 配置文件..."
print_detail "源目录: $(pwd)/configs/"
print_detail "目标目录: ${INSTALL_DIR}/configs/"
if [ ! -d "configs" ]; then
  print_error "配置目录 configs 不存在于当前目录"
fi

# 如果是重新安装，记录备份信息但不恢复配置
if [ "$SERVICE_INSTALLED" = true ] && [ -d "$BACKUP_DIR/configs" ]; then
  print_detail "检测到之前的配置文件已备份，将进行全新安装"
fi

# 复制新的配置文件
cp -r configs/* ${INSTALL_DIR}/configs/ || {
  print_error "复制配置文件失败，请检查文件权限"
}

# 设置配置文件的权限和所有权
print_detail "设置配置文件权限..."
find ${INSTALL_DIR}/configs/ -type f -exec chmod 640 {} \;
find ${INSTALL_DIR}/configs/ -type f -exec chown securio-agent:securio-agent {} \;

print_success "配置文件安装完成"
print_success_detail "已安装的配置文件:"
ls -la ${INSTALL_DIR}/configs/ | grep -v "^total" | grep -v "^d" | while read perm links owner group size date time file; do
  if [[ "$file" != "." && "$file" != ".." ]]; then
    print_success_detail "          └── $file (权限: $perm, 所有者: $owner:$group)"
  fi
done

# 检测系统类型和服务管理方式
print_info "检测系统类型和服务管理方式..."
USE_SYSTEMD=false
USE_INITD=false

# 检查是否有 systemctl 命令
if command -v systemctl &> /dev/null; then
  print_detail "检测到 systemd 服务管理系统"
  USE_SYSTEMD=true
  SYSTEMD_VERSION=$(systemctl --version | head -1 | awk '{print $2}' 2>/dev/null || echo "0")
  print_detail "systemd 版本: $SYSTEMD_VERSION"
# 检查是否有 service 命令和 /etc/init.d 目录
elif [ -d "/etc/init.d" ] && (command -v service &> /dev/null || command -v chkconfig &> /dev/null); then
  print_detail "检测到传统 init.d 服务管理系统"
  USE_INITD=true
  # 检查是否有 chkconfig 命令（主要用于 RHEL/CentOS）
  if command -v chkconfig &> /dev/null; then
    print_detail "检测到 chkconfig 命令 (RHEL/CentOS)"
    USE_CHKCONFIG=true
  else
    USE_CHKCONFIG=false
  fi
else
  print_error "无法检测到支持的服务管理系统 (systemd 或 init.d)"
fi

# 卸载旧服务（如果存在）
if [ "$SERVICE_INSTALLED" = true ]; then
  print_info "卸载旧服务..."

  # 卸载 systemd 服务
  if [ -f "/etc/systemd/system/securio-agent.service" ]; then
    print_detail "卸载 systemd 服务..."
    systemctl disable securio-agent 2>/dev/null
    rm -f /etc/systemd/system/securio-agent.service
    systemctl daemon-reload 2>/dev/null
    print_success_detail "systemd 服务已卸载"
  fi

  # 卸载 init.d 服务
  if [ -f "/etc/init.d/securio-agent" ]; then
    print_detail "卸载 init.d 服务..."
    if command -v chkconfig &> /dev/null; then
      chkconfig --del securio-agent 2>/dev/null
    elif command -v update-rc.d &> /dev/null; then
      update-rc.d -f securio-agent remove 2>/dev/null
    else
      # 手动删除启动链接
      rm -f /etc/rc*.d/*securio-agent 2>/dev/null
    fi
    rm -f /etc/init.d/securio-agent
    print_success_detail "init.d 服务已卸载"
  fi
fi

# 根据系统类型安装服务
if [ "$USE_SYSTEMD" = true ]; then
  # 安装 systemd 服务
  print_info "安装 Securio Agent systemd 服务..."
  print_detail "服务文件: /etc/systemd/system/securio-agent.service"
  print_detail "启动命令: ${INSTALL_DIR}/${BINARY_NAME} --config ${INSTALL_DIR}/configs/${CONFIG_FILE}"
  print_detail "工作目录: ${INSTALL_DIR}"

  # 创建 systemd 服务文件
  if [ "$SYSTEMD_VERSION" -ge 230 ]; then
    print_detail "使用新版 systemd 服务配置 (版本 >= 230)"

    # 根据是否使用 capabilities 选择用户
    if [ "$USE_CAPABILITIES" = true ]; then
      SERVICE_USER="securio-agent"
      SERVICE_GROUP="securio-agent"
      print_detail "使用 securio-agent 用户运行服务（使用 capabilities 模式）"
    else
      SERVICE_USER="root"
      SERVICE_GROUP="root"
      print_detail "使用 root 用户运行服务（使用 setuid 模式）"
    fi

    cat > /etc/systemd/system/securio-agent.service << EOF
[Unit]
Description=Securio Agent - Host Security Monitoring
After=network.target

[Service]
Type=simple
StandardOutput=journal
StandardError=journal
StartLimitInterval=3600
StartLimitBurst=10
ExecStart=${INSTALL_DIR}/${BINARY_NAME} --config ${INSTALL_DIR}/configs/${CONFIG_FILE}
Restart=on-failure
RestartSec=300
WorkingDirectory=${INSTALL_DIR}
ProtectHome=true
PrivateTmp=true
ReadWritePaths=${INSTALL_DIR}/data
ReadWritePaths=${INSTALL_DIR}/logs

[Install]
WantedBy=multi-user.target
EOF
  else
    print_detail "使用兼容版 systemd 服务配置 (版本 < 230)"

    # 根据是否使用 capabilities 选择用户
    if [ "$USE_CAPABILITIES" = true ]; then
      SERVICE_USER="securio-agent"
      SERVICE_GROUP="securio-agent"
      print_detail "使用 securio-agent 用户运行服务（使用 capabilities 模式）"
    else
      SERVICE_USER="root"
      SERVICE_GROUP="root"
      print_detail "使用 root 用户运行服务（使用 setuid 模式）"
    fi

    cat > /etc/systemd/system/securio-agent.service << EOF
[Unit]
Description=Securio Agent - Host Security Monitoring
After=network.target

[Service]
Type=simple
StandardOutput=journal
StandardError=journal
StartLimitInterval=3600
StartLimitBurst=10
ExecStart=${INSTALL_DIR}/${BINARY_NAME} --config ${INSTALL_DIR}/configs/${CONFIG_FILE}
Restart=on-failure
RestartSec=300
WorkingDirectory=${INSTALL_DIR}
# 低版本 systemd 兼容配置
# 不使用高级安全限制选项，以确保兼容性

[Install]
WantedBy=multi-user.target
EOF
  fi

  # 设置服务文件权限
  chmod 644 /etc/systemd/system/securio-agent.service
  chown root:root /etc/systemd/system/securio-agent.service

  print_success "systemd 服务安装完成"

  # 重新加载 systemd
  print_info "重新加载 systemd 配置..."
  print_detail "执行: systemctl daemon-reload"
  systemctl daemon-reload || {
    print_error "重新加载 systemd 配置失败"
  }
  print_success "systemd 配置重新加载完成"
  print_success_detail "systemd 已识别新服务配置"

  # 启用并启动服务
  print_info "启用并启动 Securio Agent 服务..."
  print_detail "执行: systemctl enable securio-agent"
  systemctl enable securio-agent || {
    print_error "启用服务失败"
  }
  print_success_detail "服务已设置为开机自启动"

  print_detail "执行: systemctl start securio-agent"
  systemctl start securio-agent || {
    print_error "启动服务失败"
  }
  print_success "Securio Agent 服务启动完成"
  print_success_detail "服务状态: $(systemctl is-active securio-agent)"
  print_success_detail "服务启用状态: $(systemctl is-enabled securio-agent)"

elif [ "$USE_INITD" = true ]; then
  # 安装 init.d 服务
  print_info "安装 Securio Agent init.d 服务..."
  print_detail "服务文件: /etc/init.d/securio-agent"
  print_detail "启动命令: ${INSTALL_DIR}/${BINARY_NAME} --config ${INSTALL_DIR}/configs/${CONFIG_FILE}"
  print_detail "工作目录: ${INSTALL_DIR}"

  # 创建 init.d 服务脚本
  cat > /etc/init.d/securio-agent << 'EOF'
#!/bin/bash
#
# securio-agent      Start/Stop the Securio Agent daemon
#
# chkconfig: 2345 90 10
# description: Securio Agent - Host Security Monitoring
# processname: securio-agent
# pidfile: /var/run/securio-agent.pid

### BEGIN INIT INFO
# Provides: securio-agent
# Required-Start: $network $local_fs
# Required-Stop: $network $local_fs
# Default-Start: 2 3 4 5
# Default-Stop: 0 1 6
# Short-Description: Securio Agent - Host Security Monitoring
# Description: Securio Agent - Host Security Monitoring
### END INIT INFO

# Source function library.
if [ -f /etc/init.d/functions ]; then
  . /etc/init.d/functions
elif [ -f /etc/rc.d/init.d/functions ]; then
  . /etc/rc.d/init.d/functions
fi

# 服务配置
INSTALL_DIR="INSTALL_DIR_PLACEHOLDER"
BINARY_NAME="BINARY_NAME_PLACEHOLDER"
CONFIG_FILE="CONFIG_FILE_PLACEHOLDER"
USER="root"
PIDFILE="/var/run/securio-agent.pid"
LOGFILE="${INSTALL_DIR}/logs/securio-agent.log"
EXEC="${INSTALL_DIR}/${BINARY_NAME}"
EXEC_ARGS="--config ${INSTALL_DIR}/configs/${CONFIG_FILE}"

# 确保二进制文件存在且可执行
if [ ! -x "$EXEC" ]; then
  echo "Error: $EXEC not found or not executable"
  exit 5
fi

start() {
  echo -n "Starting securio-agent: "

  # 检查服务是否已经在运行
  if [ -f "$PIDFILE" ]; then
    PID=$(cat "$PIDFILE")
    if [ -n "$PID" ] && kill -0 $PID 2>/dev/null; then
      echo "已在运行 (PID: $PID)"
      return 0
    fi
  fi

  # 以配置的用户身份启动服务（低版本系统使用 root）
  su -s /bin/bash -c "cd ${INSTALL_DIR} && ${EXEC} ${EXEC_ARGS} > ${LOGFILE} 2>&1 & echo \$! > ${PIDFILE}" ${USER}

  # 检查启动是否成功
  sleep 1
  if [ -f "$PIDFILE" ]; then
    PID=$(cat "$PIDFILE")
    if [ -n "$PID" ] && kill -0 $PID 2>/dev/null; then
      echo "成功 (PID: $PID)"
      return 0
    fi
  fi

  echo "失败"
  return 1
}

stop() {
  echo -n "Stopping securio-agent: "

  # 检查 PID 文件是否存在
  if [ ! -f "$PIDFILE" ]; then
    echo "未运行"
    return 0
  fi

  # 获取 PID 并尝试终止进程
  PID=$(cat "$PIDFILE")
  if [ -n "$PID" ]; then
    kill $PID

    # 等待进程终止
    TIMEOUT=10
    while [ $TIMEOUT -gt 0 ]; do
      kill -0 $PID 2>/dev/null || break
      sleep 1
      TIMEOUT=$((TIMEOUT - 1))
    done

    # 如果进程仍在运行，强制终止
    if kill -0 $PID 2>/dev/null; then
      kill -9 $PID
      sleep 1
    fi

    # 检查进程是否已终止
    if kill -0 $PID 2>/dev/null; then
      echo "失败"
      return 1
    else
      rm -f "$PIDFILE"
      echo "成功"
      return 0
    fi
  else
    echo "PID 文件为空"
    rm -f "$PIDFILE"
    return 0
  fi
}

status() {
  # 检查 PID 文件是否存在
  if [ ! -f "$PIDFILE" ]; then
    echo "securio-agent 未运行"
    return 3
  fi

  # 获取 PID 并检查进程是否在运行
  PID=$(cat "$PIDFILE")
  if [ -n "$PID" ] && kill -0 $PID 2>/dev/null; then
    echo "securio-agent 正在运行 (PID: $PID)"
    return 0
  else
    echo "securio-agent 已停止，但 PID 文件存在"
    return 1
  fi
}

# 根据命令行参数执行相应的操作
case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  restart|reload)
    stop
    start
    ;;
  status)
    status
    ;;
  *)
    echo "Usage: $0 {start|stop|restart|status}"
    exit 1
esac

exit $?
EOF

  # 根据是否使用 capabilities 选择用户
  if [ "$USE_CAPABILITIES" = true ]; then
    SERVICE_USER="securio-agent"
    print_detail "使用 securio-agent 用户运行服务（使用 capabilities 模式）"
  else
    SERVICE_USER="root"
    print_detail "使用 root 用户运行服务（使用 setuid 模式）"
  fi

  # 替换占位符
  sed -i "s|INSTALL_DIR_PLACEHOLDER|${INSTALL_DIR}|g" /etc/init.d/securio-agent
  sed -i "s|BINARY_NAME_PLACEHOLDER|${BINARY_NAME}|g" /etc/init.d/securio-agent
  sed -i "s|CONFIG_FILE_PLACEHOLDER|${CONFIG_FILE}|g" /etc/init.d/securio-agent
  sed -i "s|USER=\"root\"|USER=\"${SERVICE_USER}\"|g" /etc/init.d/securio-agent

  # 设置服务文件权限
  chmod 755 /etc/init.d/securio-agent
  chown root:root /etc/init.d/securio-agent

  print_success "init.d 服务安装完成"

  # 配置服务自启动
  print_info "配置服务自启动..."
  if [ "$USE_CHKCONFIG" = true ]; then
    print_detail "使用 chkconfig 配置自启动"
    chkconfig --add securio-agent
    chkconfig securio-agent on
    print_success_detail "服务已通过 chkconfig 设置为开机自启动"
  else
    print_detail "使用 update-rc.d 配置自启动"
    if command -v update-rc.d &> /dev/null; then
      update-rc.d securio-agent defaults
      print_success_detail "服务已通过 update-rc.d 设置为开机自启动"
    else
      print_detail "无法找到 update-rc.d 命令，尝试手动创建启动链接"
      ln -sf /etc/init.d/securio-agent /etc/rc2.d/S90securio-agent
      ln -sf /etc/init.d/securio-agent /etc/rc3.d/S90securio-agent
      ln -sf /etc/init.d/securio-agent /etc/rc4.d/S90securio-agent
      ln -sf /etc/init.d/securio-agent /etc/rc5.d/S90securio-agent
      ln -sf /etc/init.d/securio-agent /etc/rc0.d/K10securio-agent
      ln -sf /etc/init.d/securio-agent /etc/rc1.d/K10securio-agent
      ln -sf /etc/init.d/securio-agent /etc/rc6.d/K10securio-agent
      print_success_detail "服务已通过手动创建启动链接设置为开机自启动"
    fi
  fi

  # 启动服务
  print_info "启动 Securio Agent 服务..."
  print_detail "执行: /etc/init.d/securio-agent start"
  /etc/init.d/securio-agent start || {
    print_error "启动服务失败"
  }
  print_success "Securio Agent 服务启动完成"

  # 检查服务状态
  SERVICE_STATUS=$(/etc/init.d/securio-agent status)
  print_success_detail "服务状态: $SERVICE_STATUS"
else
  print_error "无法安装服务，未检测到支持的服务管理系统"
fi

# 清理临时文件
print_info "清理临时文件和目录..."
print_detail "清理文件: $TAR_FILE"
print_detail "清理目录: $TEMP_DIR"
rm -f "$TAR_FILE" && print_success_detail "已删除文件: $TAR_FILE"
rm -rf "$TEMP_DIR" && print_success_detail "已删除目录: $TEMP_DIR"
print_success "清理完成"

# 最终权限检查
print_info "执行最终权限检查..."

# 确保 data 和 logs 目录有写入权限
print_detail "检查目录权限..."

# 根据服务管理系统设置不同的权限
if [ "$USE_SYSTEMD" = true ]; then
  # 对于低版本 systemd，需要确保目录权限更宽松
  if [ "$SYSTEMD_VERSION" -lt 230 ]; then
    print_detail "低版本 systemd (版本 < 230)，设置更宽松的目录权限"
    # 确保目录存在并有正确的权限
    chmod -R 770 "${INSTALL_DIR}/data"
    chmod -R 770 "${INSTALL_DIR}/logs"
    chown -R securio-agent:securio-agent "${INSTALL_DIR}/data"
    chown -R securio-agent:securio-agent "${INSTALL_DIR}/logs"
    print_detail "已设置 data 和 logs 目录权限为 770"
  else
    # 正常权限检查
    if [ ! -w "${INSTALL_DIR}/data" ]; then
      print_detail "设置 data 目录写入权限"
      chmod -R 750 "${INSTALL_DIR}/data"
      chown -R securio-agent:securio-agent "${INSTALL_DIR}/data"
    fi

    if [ ! -w "${INSTALL_DIR}/logs" ]; then
      print_detail "设置 logs 目录写入权限"
      chmod -R 750 "${INSTALL_DIR}/logs"
      chown -R securio-agent:securio-agent "${INSTALL_DIR}/logs"
    fi
  fi
elif [ "$USE_INITD" = true ]; then
  # 对于 init.d 系统，设置更宽松的权限
  print_detail "init.d 系统，设置更宽松的目录权限"
  chmod -R 770 "${INSTALL_DIR}/data"
  chmod -R 770 "${INSTALL_DIR}/logs"
  chown -R securio-agent:securio-agent "${INSTALL_DIR}/data"
  chown -R securio-agent:securio-agent "${INSTALL_DIR}/logs"
  print_detail "已设置 data 和 logs 目录权限为 770"
fi

# 根据是否使用 capabilities 进行不同的权限检查
if [ "$USE_CAPABILITIES" = true ]; then
  # 再次检查 capabilities 设置
  print_detail "检查 Linux capabilities 设置..."

  # 根据服务管理系统设置不同的 capabilities
  if [ "$USE_SYSTEMD" = true ] && [ "$SYSTEMD_VERSION" -lt 230 ]; then
    print_detail "低版本 systemd (版本 < 230)，确保完整的 capabilities 设置"
    setcap "cap_chown,cap_dac_override,cap_dac_read_search,cap_fowner,cap_kill,cap_net_bind_service,cap_net_admin,cap_net_raw,cap_ipc_lock,cap_ipc_owner,cap_sys_module,cap_sys_rawio,cap_sys_ptrace,cap_sys_admin,cap_sys_resource,cap_audit_control,cap_syslog+ei" ${INSTALL_DIR}/${BINARY_NAME} 2>/dev/null || print_detail "设置 capabilities 失败，但将继续安装"
  elif [ "$USE_INITD" = true ]; then
    print_detail "init.d 系统，确保完整的 capabilities 设置"
    setcap "cap_chown,cap_dac_override,cap_dac_read_search,cap_fowner,cap_kill,cap_net_bind_service,cap_net_admin,cap_net_raw,cap_ipc_lock,cap_ipc_owner,cap_sys_module,cap_sys_rawio,cap_sys_ptrace,cap_sys_admin,cap_sys_resource,cap_audit_control,cap_syslog+ei" ${INSTALL_DIR}/${BINARY_NAME} 2>/dev/null || print_detail "设置 capabilities 失败，但将继续安装"
  else
    # 正常 capabilities 检查
    if ! getcap ${INSTALL_DIR}/${BINARY_NAME} 2>/dev/null | grep -q "cap_sys_admin"; then
      print_detail "重新设置 Linux capabilities..."
      setcap "cap_chown,cap_dac_override,cap_dac_read_search,cap_fowner,cap_kill,cap_net_bind_service,cap_net_admin,cap_net_raw,cap_ipc_lock,cap_ipc_owner,cap_sys_module,cap_sys_rawio,cap_sys_ptrace,cap_sys_admin,cap_sys_resource,cap_audit_control,cap_syslog+ei" ${INSTALL_DIR}/${BINARY_NAME} 2>/dev/null || print_detail "设置 capabilities 失败，但将继续安装"
    fi
  fi

  # 显示当前 capabilities
  if command -v getcap &> /dev/null; then
    CAPS=$(getcap ${INSTALL_DIR}/${BINARY_NAME} 2>/dev/null)
    if [ -n "$CAPS" ]; then
      print_success_detail "Linux capabilities: $CAPS"
    else
      print_detail "未检测到 capabilities，可能设置失败，切换到 setuid 模式"
      # 如果 capabilities 设置失败，切换到 setuid 模式
      USE_CAPABILITIES=false
    fi
  else
    print_success_detail "Linux capabilities: 无法检测（getcap 命令不可用）"
  fi
fi

# 如果不使用 capabilities，则检查 setuid 权限
if [ "$USE_CAPABILITIES" = false ]; then
  print_detail "检查 setuid 权限设置..."

  # 确保二进制文件有正确的 setuid 权限
  if [ "$(stat -c '%u' ${INSTALL_DIR}/${BINARY_NAME})" != "0" ] || [ "$(stat -c '%a' ${INSTALL_DIR}/${BINARY_NAME} | cut -c1)" != "4" ]; then
    print_detail "重新设置 setuid 权限..."
    chown root:root ${INSTALL_DIR}/${BINARY_NAME}
    chmod 4750 ${INSTALL_DIR}/${BINARY_NAME}
  fi

  print_success_detail "二进制文件权限: $(stat -c '%a' ${INSTALL_DIR}/${BINARY_NAME}) (root:$(stat -c '%G' ${INSTALL_DIR}/${BINARY_NAME}))"
fi

# 打印最终权限状态
print_success_detail "data 目录权限: $(stat -c '%a' ${INSTALL_DIR}/data)"
print_success_detail "logs 目录权限: $(stat -c '%a' ${INSTALL_DIR}/logs)"

# 测试写入权限
print_detail "测试写入权限..."
touch "${INSTALL_DIR}/data/test_write_permission" && rm "${INSTALL_DIR}/data/test_write_permission" && \
print_success_detail "数据目录写入测试成功"

touch "${INSTALL_DIR}/logs/test_write_permission" && rm "${INSTALL_DIR}/logs/test_write_permission" && \
print_success_detail "日志目录写入测试成功"

print_info "Securio Agent 安装完成！"
print_success_detail "安装目录: $INSTALL_DIR"
if [ "$USE_CAPABILITIES" = true ]; then
  print_success_detail "运行用户: securio-agent (非 root 用户)"
else
  print_success_detail "运行用户: root (低版本系统使用 root 用户)"
fi

# 如果是重新安装，显示备份信息
if [ "$SERVICE_INSTALLED" = true ]; then
  print_success_detail "原有配置备份位置: $BACKUP_DIR"
  print_success_detail "已进行全新安装，使用新的配置文件"
fi
print_success_detail "二进制文件: $INSTALL_DIR/$BINARY_NAME"
print_success_detail "  - 权限: $(stat -c '%a' ${INSTALL_DIR}/${BINARY_NAME})"
print_success_detail "  - 所有者: $(stat -c '%U:%G' ${INSTALL_DIR}/${BINARY_NAME})"
if [ "$USE_CAPABILITIES" = true ]; then
  print_success_detail "  - 权限模式: Linux capabilities"
  if command -v getcap &> /dev/null; then
    CAPS=$(getcap ${INSTALL_DIR}/${BINARY_NAME} 2>/dev/null | cut -d' ' -f2-)
    if [ -n "$CAPS" ]; then
      print_success_detail "  - Linux capabilities: $CAPS"
    fi
  fi
else
  print_success_detail "  - 权限模式: setuid root"
fi
print_success_detail "配置文件: $INSTALL_DIR/configs/$CONFIG_FILE"
print_success_detail "  - 权限: $(stat -c '%a' ${INSTALL_DIR}/configs/$CONFIG_FILE)"
print_success_detail "  - 所有者: $(stat -c '%U:%G' ${INSTALL_DIR}/configs/$CONFIG_FILE)"
print_success_detail "数据目录: $INSTALL_DIR/data"
print_success_detail "  - 权限: $(stat -c '%a' ${INSTALL_DIR}/data)"
print_success_detail "  - 所有者: $(stat -c '%U:%G' ${INSTALL_DIR}/data)"
print_success_detail "日志目录: $INSTALL_DIR/logs"
print_success_detail "  - 权限: $(stat -c '%a' ${INSTALL_DIR}/logs)"
print_success_detail "  - 所有者: $(stat -c '%U:%G' ${INSTALL_DIR}/logs)"
print_success_detail "服务名称: securio-agent"

# 根据服务管理系统显示不同的状态信息
if [ "$USE_SYSTEMD" = true ]; then
  print_success_detail "当前状态: $(systemctl is-active securio-agent)"
  print_success_detail "服务管理: systemd"
  echo -e "${GREEN}查看服务状态: ${YELLOW}systemctl status securio-agent${NC}"
  echo -e "${GREEN}查看日志: ${YELLOW}journalctl -u securio-agent -f${NC}"
elif [ "$USE_INITD" = true ]; then
  print_success_detail "当前状态: 已启动"
  print_success_detail "服务管理: init.d"
  echo -e "${GREEN}查看服务状态: ${YELLOW}/etc/init.d/securio-agent status${NC}"
  echo -e "${GREEN}查看日志: ${YELLOW}tail -f ${INSTALL_DIR}/logs/securio-agent.log${NC}"
fi

print_success_detail "版本号: $VERSION"

# 通用的服务管理命令提示
echo -e "${GREEN}启动服务: ${YELLOW}$([ "$USE_SYSTEMD" = true ] && echo "systemctl start securio-agent" || echo "/etc/init.d/securio-agent start")${NC}"
echo -e "${GREEN}停止服务: ${YELLOW}$([ "$USE_SYSTEMD" = true ] && echo "systemctl stop securio-agent" || echo "/etc/init.d/securio-agent stop")${NC}"
echo -e "${GREEN}重启服务: ${YELLOW}$([ "$USE_SYSTEMD" = true ] && echo "systemctl restart securio-agent" || echo "/etc/init.d/securio-agent restart")${NC}"
