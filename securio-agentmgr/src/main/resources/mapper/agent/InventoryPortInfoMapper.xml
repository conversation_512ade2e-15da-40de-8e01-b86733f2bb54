<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.agent.mapper.InventoryPortInfoMapper">
    
    <resultMap type="InventoryPortInfo" id="InventoryPortInfoResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="protocol"    column="protocol"    />
        <result property="localIp"    column="local_ip"    />
        <result property="localPort"    column="local_port"    />
        <result property="remoteIp"    column="remote_ip"    />
        <result property="remotePort"    column="remote_port"    />
        <result property="state"    column="state"    />
        <result property="pid"    column="pid"    />
        <result property="process"    column="process"    />
        <result property="createAt"    column="create_at"    />
        <result property="isDel"    column="is_del"    />
        <result property="updateAt"    column="update_at"    />
        <result property="hostIp"    column="host_ip"    />
    </resultMap>

    <sql id="selectInventoryPortInfoVo">
        select id, agent_id, protocol, local_ip, local_port, remote_ip, remote_port, state, pid, process, create_at, is_del, update_at, host_ip from inventory_port_info
    </sql>

    <select id="selectInventoryPortInfoList" parameterType="InventoryPortInfo" resultMap="InventoryPortInfoResult">
        <include refid="selectInventoryPortInfoVo"/>
        <where>  
            <if test="agentId != null  and agentId != ''"> and agent_id = #{agentId}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="localIp != null  and localIp != ''"> and local_ip = #{localIp}</if>
            <if test="localPort != null "> and local_port = #{localPort}</if>
            <if test="remoteIp != null  and remoteIp != ''"> and remote_ip = #{remoteIp}</if>
            <if test="remotePort != null "> and remote_port = #{remotePort}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="process != null  and process != ''"> and process = #{process}</if>
            <if test="createAt != null "> and create_at = #{createAt}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="updateAt != null "> and update_at = #{updateAt}</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
        </where>
        order by create_at desc
    </select>
    
    <select id="selectInventoryPortInfoById" parameterType="Long" resultMap="InventoryPortInfoResult">
        <include refid="selectInventoryPortInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertInventoryPortInfo" parameterType="InventoryPortInfo" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_port_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null and agentId != ''">agent_id,</if>
            <if test="protocol != null">protocol,</if>
            <if test="localIp != null">local_ip,</if>
            <if test="localPort != null">local_port,</if>
            <if test="remoteIp != null">remote_ip,</if>
            <if test="remotePort != null">remote_port,</if>
            <if test="state != null">state,</if>
            <if test="pid != null">pid,</if>
            <if test="process != null">process,</if>
            <if test="createAt != null">create_at,</if>
            <if test="isDel != null">is_del,</if>
            <if test="updateAt != null">update_at,</if>
            <if test="hostIp != null">host_ip,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null and agentId != ''">#{agentId},</if>
            <if test="protocol != null">#{protocol},</if>
            <if test="localIp != null">#{localIp},</if>
            <if test="localPort != null">#{localPort},</if>
            <if test="remoteIp != null">#{remoteIp},</if>
            <if test="remotePort != null">#{remotePort},</if>
            <if test="state != null">#{state},</if>
            <if test="pid != null">#{pid},</if>
            <if test="process != null">#{process},</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="updateAt != null">#{updateAt},</if>
            <if test="hostIp != null">#{hostIp},</if>
         </trim>
    </insert>

    <update id="updateInventoryPortInfo" parameterType="InventoryPortInfo">
        update inventory_port_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null and agentId != ''">agent_id = #{agentId},</if>
            <if test="protocol != null">protocol = #{protocol},</if>
            <if test="localIp != null">local_ip = #{localIp},</if>
            <if test="localPort != null">local_port = #{localPort},</if>
            <if test="remoteIp != null">remote_ip = #{remoteIp},</if>
            <if test="remotePort != null">remote_port = #{remotePort},</if>
            <if test="state != null">state = #{state},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="process != null">process = #{process},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
            <if test="hostIp != null">host_ip = #{hostIp},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryPortInfoById" parameterType="Long">
        delete from inventory_port_info where id = #{id}
    </delete>

    <delete id="deleteInventoryPortInfoByIds" parameterType="String">
        delete from inventory_port_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteInventoryPortInfoByAgentId" parameterType="String">
        delete from inventory_port_info where agent_id = #{agentId}
    </delete>

    <insert id="batchInsertInventoryPortInfo" parameterType="java.util.List">
        INSERT INTO inventory_port_info
        (
        agent_id, protocol, local_ip, local_port, remote_ip, remote_port, state, pid, process, create_at, is_del, update_at, host_ip
        )
        VALUES
        <foreach collection="list" item="model"  separator=",">
            (
            #{ model.agentId },#{ model.protocol },#{ model.localIp },#{ model.localPort },#{ model.remoteIp },#{ model.remotePort },#{ model.state },#{ model.pid },#{ model.process },#{ model.createAt },#{ model.isDel },#{ model.updateAt },#{ model.hostIp }
            )
        </foreach>
    </insert>
    
    <!-- 根据主机IP获取网络连接统计信息 -->
    <select id="selectNetworkConnectionStats" parameterType="String" resultType="java.util.Map">
        SELECT 
            pid,
            process as "processName",
            remote_ip as "remoteIp",
            count(1) as "connectionCount"
        FROM 
            inventory_port_info 
        WHERE 
            host_ip = #{hostIp}
            AND remote_ip IS NOT NULL
            AND remote_ip != ''
            AND remote_ip != '0.0.0.0'
            AND remote_ip != '*'
        GROUP BY 
            pid,process, remote_ip
        ORDER BY 
            count(1) DESC
    </select>
    
    <!-- 根据IP列表获取主机名信息 -->
    <select id="selectHostnameByIpList" resultType="java.util.Map">
        SELECT 
            host_ip as "hostIp",
            hostname as "hostname"
        FROM 
            agent_info 
        WHERE 
            host_ip IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>