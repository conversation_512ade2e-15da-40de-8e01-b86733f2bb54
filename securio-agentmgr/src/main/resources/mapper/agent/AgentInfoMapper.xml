<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.agent.mapper.AgentInfoMapper">
    
    <resultMap type="AgentInfo" id="AgentInfoResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="agentName"    column="agent_name"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="hostname"    column="hostname"    />
        <result property="startTime"    column="start_time"    />
        <result property="heartbeatTime"    column="heartbeat_time"    />
        <result property="version"    column="version"    />
        <result property="status"    column="status"    />
        <result property="token"    column="token"    />
        <result property="tokenExpTime"    column="token_exp_time"    />
        <result property="osName"    column="os_name"    />
        <result property="osVersion"    column="os_version"    />
        <result property="boardSerial"    column="board_serial"    />
        <result property="cpuName"    column="cpu_name"    />
        <result property="cpuCores"    column="cpu_cores"    />
        <result property="cpuMhz"    column="cpu_mhz"    />
        <result property="ramTotal"    column="ram_total"    />
        <result property="ramFree"    column="ram_free"    />
        <result property="agentCpuUsage"    column="agent_cpu_usage"    />
        <result property="cpuUsage"    column="cpu_usage"    />
        <result property="ramUsage"    column="ram_usage"    />
        <result property="agentRamUsage"    column="agent_ram_usage"    />
        <result property="architecture"    column="architecture"    />
        <result property="osCodename"    column="os_codename"    />
        <result property="osBuild"    column="os_build"    />
        <result property="osPlatform"    column="os_platform"    />
        <result property="isDel"    column="is_del"    />
        <result property="createAt"    column="create_at"    />
        <result property="updateAt"    column="update_at"    />
    </resultMap>

    <sql id="selectAgentInfoVo">
        select id, agent_id, agent_name, host_ip, hostname, start_time, heartbeat_time, version, status, token, token_exp_time, os_name, os_version, board_serial, cpu_name, cpu_cores, cpu_mhz, ram_total, ram_free, agent_cpu_usage, cpu_usage, ram_usage, agent_ram_usage, architecture, os_codename, os_build, os_platform, is_del, create_at, update_at from agent_info
    </sql>

    <select id="selectAgentInfoList" parameterType="AgentInfo" resultMap="AgentInfoResult">
        <include refid="selectAgentInfoVo"/>
        <where>  
            <if test="agentId != null  and agentId != ''"> and agent_id = #{agentId}</if>
            <if test="agentName != null  and agentName != ''"> and agent_name like concat('%', #{agentName}, '%')</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip like concat('%', #{hostIp}, '%')</if>
            <if test="hostname != null  and hostname != ''"> and hostname like concat('%', #{hostname}, '%')</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="heartbeatTime != null "> and heartbeat_time = #{heartbeatTime}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="token != null  and token != ''"> and token = #{token}</if>
            <if test="tokenExpTime != null "> and token_exp_time = #{tokenExpTime}</if>
            <if test="osName != null  and osName != ''"> and os_name like concat('%', #{osName}, '%')</if>
            <if test="osVersion != null  and osVersion != ''"> and os_version = #{osVersion}</if>
            <if test="boardSerial != null  and boardSerial != ''"> and board_serial = #{boardSerial}</if>
            <if test="cpuName != null  and cpuName != ''"> and cpu_name like concat('%', #{cpuName}, '%')</if>
            <if test="cpuCores != null "> and cpu_cores = #{cpuCores}</if>
            <if test="cpuMhz != null "> and cpu_mhz = #{cpuMhz}</if>
            <if test="ramTotal != null "> and ram_total = #{ramTotal}</if>
            <if test="ramFree != null "> and ram_free = #{ramFree}</if>
            <if test="agentCpuUsage != null "> and agent_cpu_usage = #{agentCpuUsage}</if>
            <if test="cpuUsage != null "> and cpu_usage = #{cpuUsage}</if>
            <if test="ramUsage != null "> and ram_usage = #{ramUsage}</if>
            <if test="agentRamUsage != null "> and agent_ram_usage = #{agentRamUsage}</if>
            <if test="architecture != null  and architecture != ''"> and architecture = #{architecture}</if>
            <if test="osCodename != null  and osCodename != ''"> and os_codename like concat('%', #{osCodename}, '%')</if>
            <if test="osBuild != null  and osBuild != ''"> and os_build = #{osBuild}</if>
            <if test="osPlatform != null  and osPlatform != ''"> and os_platform = #{osPlatform}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="createAt != null "> and create_at = #{createAt}</if>
            <if test="updateAt != null "> and update_at = #{updateAt}</if>
            <if test="hostIp ==null || hostIp == ''">and host_ip is not null and host_ip != ''</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectAgentInfoById" parameterType="Long" resultMap="AgentInfoResult">
        <include refid="selectAgentInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectAgentInfoByAgentId" parameterType="String" resultMap="AgentInfoResult">
        <include refid="selectAgentInfoVo"/>
        where agent_id = #{agentId}
    </select>

    <select id="selectAgentInfoByHostIp" parameterType="String" resultMap="AgentInfoResult">
        <include refid="selectAgentInfoVo"/>
        where host_ip = #{hostIp} and is_del = 0
        limit 1
    </select>
        
    <insert id="insertAgentInfo" parameterType="AgentInfo" useGeneratedKeys="true" keyProperty="id">
        insert into agent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">agent_id,</if>
            <if test="agentName != null and agentName != ''">agent_name,</if>
            <if test="hostIp != null">host_ip,</if>
            <if test="hostname != null">hostname,</if>
            <if test="startTime != null">start_time,</if>
            <if test="heartbeatTime != null">heartbeat_time,</if>
            <if test="version != null">version,</if>
            <if test="status != null">status,</if>
            <if test="token != null">token,</if>
            <if test="tokenExpTime != null">token_exp_time,</if>
            <if test="osName != null">os_name,</if>
            <if test="osVersion != null">os_version,</if>
            <if test="boardSerial != null">board_serial,</if>
            <if test="cpuName != null">cpu_name,</if>
            <if test="cpuCores != null">cpu_cores,</if>
            <if test="cpuMhz != null">cpu_mhz,</if>
            <if test="ramTotal != null">ram_total,</if>
            <if test="ramFree != null">ram_free,</if>
            <if test="agentCpuUsage != null">agent_cpu_usage,</if>
            <if test="cpuUsage != null">cpu_usage,</if>
            <if test="ramUsage != null">ram_usage,</if>
            <if test="agentRamUsage != null">agent_ram_usage,</if>
            <if test="architecture != null">architecture,</if>
            <if test="osCodename != null">os_codename,</if>
            <if test="osBuild != null">os_build,</if>
            <if test="osPlatform != null">os_platform,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createAt != null">create_at,</if>
            <if test="updateAt != null">update_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">#{agentId},</if>
            <if test="agentName != null and agentName != ''">#{agentName},</if>
            <if test="hostIp != null">#{hostIp},</if>
            <if test="hostname != null">#{hostname},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="heartbeatTime != null">#{heartbeatTime},</if>
            <if test="version != null">#{version},</if>
            <if test="status != null">#{status},</if>
            <if test="token != null">#{token},</if>
            <if test="tokenExpTime != null">#{tokenExpTime},</if>
            <if test="osName != null">#{osName},</if>
            <if test="osVersion != null">#{osVersion},</if>
            <if test="boardSerial != null">#{boardSerial},</if>
            <if test="cpuName != null">#{cpuName},</if>
            <if test="cpuCores != null">#{cpuCores},</if>
            <if test="cpuMhz != null">#{cpuMhz},</if>
            <if test="ramTotal != null">#{ramTotal},</if>
            <if test="ramFree != null">#{ramFree},</if>
            <if test="agentCpuUsage != null">#{agentCpuUsage},</if>
            <if test="cpuUsage != null">#{cpuUsage},</if>
            <if test="ramUsage != null">#{ramUsage},</if>
            <if test="agentRamUsage != null">#{agentRamUsage},</if>
            <if test="architecture != null">#{architecture},</if>
            <if test="osCodename != null">#{osCodename},</if>
            <if test="osBuild != null">#{osBuild},</if>
            <if test="osPlatform != null">#{osPlatform},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="updateAt != null">#{updateAt},</if>
         </trim>
    </insert>

    <update id="updateAgentInfo" parameterType="AgentInfo">
        update agent_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="agentName != null and agentName != ''">agent_name = #{agentName},</if>
            <if test="hostIp != null">host_ip = #{hostIp},</if>
            <if test="hostname != null">hostname = #{hostname},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="heartbeatTime != null">heartbeat_time = #{heartbeatTime},</if>
            <if test="version != null">version = #{version},</if>
            <if test="status != null">status = #{status},</if>
            <if test="token != null">token = #{token},</if>
            <if test="tokenExpTime != null">token_exp_time = #{tokenExpTime},</if>
            <if test="osName != null">os_name = #{osName},</if>
            <if test="osVersion != null">os_version = #{osVersion},</if>
            <if test="boardSerial != null">board_serial = #{boardSerial},</if>
            <if test="cpuName != null">cpu_name = #{cpuName},</if>
            <if test="cpuCores != null">cpu_cores = #{cpuCores},</if>
            <if test="cpuMhz != null">cpu_mhz = #{cpuMhz},</if>
            <if test="ramTotal != null">ram_total = #{ramTotal},</if>
            <if test="ramFree != null">ram_free = #{ramFree},</if>
            <if test="agentCpuUsage != null">agent_cpu_usage = #{agentCpuUsage},</if>
            <if test="cpuUsage != null">cpu_usage = #{cpuUsage},</if>
            <if test="ramUsage != null">ram_usage = #{ramUsage},</if>
            <if test="agentRamUsage != null">agent_ram_usage = #{agentRamUsage},</if>
            <if test="architecture != null">architecture = #{architecture},</if>
            <if test="osCodename != null">os_codename = #{osCodename},</if>
            <if test="osBuild != null">os_build = #{osBuild},</if>
            <if test="osPlatform != null">os_platform = #{osPlatform},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgentInfoById" parameterType="Long">
        delete from agent_info where id = #{id}
    </delete>

    <delete id="deleteAgentInfoByIds" parameterType="String">
        delete from agent_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>