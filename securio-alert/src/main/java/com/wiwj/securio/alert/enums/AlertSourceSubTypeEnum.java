package com.wiwj.securio.alert.enums;

/**
 * 告警来源子类型枚举
 * 用于定义不同告警源的具体子类型
 * 
 * <AUTHOR>
 */
public enum AlertSourceSubTypeEnum {
    
    // ==================== Zeek 相关子类型 ====================
    /** Zeek Notice 告警 */
    ZEEK_NOTICE("zeek-notice", "Zeek Notice告警", "zeek"),
    
    /** Zeek EVE 告警 */
    ZEEK_EVE("zeek-eve", "Zeek EVE告警", "zeek"),
    
    /** Zeek Weird 告警 */
    ZEEK_WEIRD("zeek-weird", "Zeek Weird告警", "zeek"),
    
    /** Zeek 连接日志 */
    ZEEK_CONN("zeek-conn", "Zeek连接日志", "zeek"),
    
    /** Zeek DNS 日志 */
    ZEEK_DNS("zeek-dns", "Zeek DNS日志", "zeek"),
    
    /** Zeek HTTP 日志 */
    ZEEK_HTTP("zeek-http", "Zeek HTTP日志", "zeek"),
    
    /** Zeek SSL 日志 */
    ZEEK_SSL("zeek-ssl", "Zeek SSL日志", "zeek"),
    
    /** Zeek 文件日志 */
    ZEEK_FILES("zeek-files", "Zeek文件日志", "zeek"),
    
    // ==================== Suricata 相关子类型 ====================
    /** Suricata 告警 */
    SURICATA_ALERT("suricata-alert", "Suricata告警", "suricata"),
    
    /** Suricata EVE 事件 */
    SURICATA_EVE("suricata-eve", "Suricata EVE事件", "suricata"),
    
    /** Suricata Fast 日志 */
    SURICATA_FAST("suricata-fast", "Suricata Fast日志", "suricata"),
    
    // ==================== Grafana 相关子类型 ====================
    /** Grafana 告警 */
    GRAFANA_ALERT("grafana-alert", "Grafana告警", "grafana"),
    
    /** Grafana Webhook */
    GRAFANA_WEBHOOK("grafana-webhook", "Grafana Webhook", "grafana"),
    
    // ==================== Prometheus 相关子类型 ====================
    /** Prometheus 告警 */
    PROMETHEUS_ALERT("prometheus-alert", "Prometheus告警", "prometheus"),
    
    /** Prometheus Webhook */
    PROMETHEUS_WEBHOOK("prometheus-webhook", "Prometheus Webhook", "prometheus"),
    
    // ==================== N9E 相关子类型 ====================
    /** N9E 告警 */
    N9E_ALERT("n9e-alert", "N9E告警", "n9e"),
    
    /** N9E Webhook */
    N9E_WEBHOOK("n9e-webhook", "N9E Webhook", "n9e"),
    
    // ==================== 牧云 相关子类型 ====================
    /** 牧云 WAF */
    MUYUN_WAF("muyun-waf", "牧云WAF", "muyun"),
    
    /** 牧云 DDoS */
    MUYUN_DDOS("muyun-ddos", "牧云DDoS", "muyun"),
    
    // ==================== 网宿 相关子类型 ====================
    /** 网宿 WAF */
    WANGSU_WAF("wangsu-waf", "网宿WAF", "wangsu"),
    
    /** 网宿 CDN */
    WANGSU_CDN("wangsu-cdn", "网宿CDN", "wangsu"),
    
    // ==================== Zabbix 相关子类型 ====================
    /** Zabbix 触发器 */
    ZABBIX_TRIGGER("zabbix-trigger", "Zabbix触发器", "zabbix"),
    
    /** Zabbix 监控项 */
    ZABBIX_ITEM("zabbix-item", "Zabbix监控项", "zabbix"),
    
    // ==================== 其他类型 ====================
    /** 默认类型 */
    DEFAULT("default", "默认类型", "unknown");
    
    /** 子类型代码 */
    private final String code;
    
    /** 子类型描述 */
    private final String description;
    
    /** 父类型 */
    private final String parentType;
    
    AlertSourceSubTypeEnum(String code, String description, String parentType) {
        this.code = code;
        this.description = description;
        this.parentType = parentType;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getParentType() {
        return parentType;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static AlertSourceSubTypeEnum fromCode(String code) {
        for (AlertSourceSubTypeEnum subType : values()) {
            if (subType.getCode().equals(code)) {
                return subType;
            }
        }
        return DEFAULT;
    }
    
    /**
     * 根据父类型获取所有子类型
     */
    public static AlertSourceSubTypeEnum[] getByParentType(String parentType) {
        return java.util.Arrays.stream(values())
            .filter(subType -> subType.getParentType().equals(parentType))
            .toArray(AlertSourceSubTypeEnum[]::new);
    }
    
    /**
     * 检查子类型是否属于指定的父类型
     */
    public boolean belongsTo(String parentType) {
        return this.parentType.equals(parentType);
    }
}
