package com.wiwj.securio.alert.enums;

/**
 * 告警标签枚举
 * 定义不同告警类型中具有共性的标签
 *
 * <AUTHOR>
 */
public enum AlertTagEnum {

    // ==================== 环境和部署相关 ====================

    /** 环境标识 */
    ENVIRONMENT("environment", "环境标识", "production/staging/development/test"),

    /** 数据中心 */
    DATACENTER("datacenter", "数据中心", "dc1/dc2/aws-us-east-1"),

    /** 可用区 */
    AVAILABILITY_ZONE("availability_zone", "可用区", "us-east-1a/us-east-1b"),

    /** 地域 */
    REGION("region", "地域", "us-east-1/ap-southeast-1"),

    /** 集群 */
    CLUSTER("cluster", "集群", "k8s-prod/hadoop-cluster"),

    // ==================== 服务和应用相关 ====================

    /** 服务名称 */
    SERVICE("service", "服务名称", "user-service/order-service/payment-api"),

    /** 应用名称 */
    APPLICATION("application", "应用名称", "web-app/mobile-app/admin-portal"),

    /** 组件名称 */
    COMPONENT("component", "组件名称", "database/cache/message-queue"),

    /** 模块名称 */
    MODULE("module", "模块名称", "auth/payment/notification"),

    /** 版本 */
    VERSION("version", "版本", "v1.2.3/2023.12.01"),

    /** 命名空间 */
    NAMESPACE("namespace", "命名空间", "default/kube-system/monitoring"),

    // ==================== 基础设施相关 ====================

    /** 主机名 */
    HOSTNAME("hostname", "主机名", "web-server-01/db-master-02"),

    /** 实例ID */
    INSTANCE_ID("instance_id", "实例ID", "i-1234567890abcdef0"),

    /** 节点名称 */
    NODE("node", "节点名称", "k8s-node-01/worker-node-02"),

    /** 容器名称 */
    CONTAINER("container", "容器名称", "nginx/mysql/redis"),

    /** Pod名称 */
    POD("pod", "Pod名称", "web-deployment-abc123"),

    // ==================== 网络相关 ====================

    /** 源IP */
    SOURCE_IP("source_ip", "源IP地址", "*************"),

    /** 目标IP */
    TARGET_IP("target_ip", "目标IP地址", "*********"),

    /** 端口 */
    PORT("port", "端口", "80/443/3306"),

    /** 协议 */
    PROTOCOL("protocol", "协议", "HTTP/HTTPS/TCP/UDP"),

    /** 域名 */
    DOMAIN("domain", "域名", "api.example.com"),

    /** URL路径 */
    URL_PATH("url_path", "URL路径", "/api/v1/users"),

    // ==================== 业务相关 ====================

    /** 业务线 */
    BUSINESS_LINE("business_line", "业务线", "电商/金融/游戏"),

    /** 产品 */
    PRODUCT("product", "产品", "商城/支付/广告"),

    /** 团队 */
    TEAM("team", "团队", "backend/frontend/devops/security"),

    /** 负责人 */
    OWNER("owner", "负责人", "张三/李四"),

    /** 项目 */
    PROJECT("project", "项目", "project-alpha/project-beta"),

    // ==================== 监控和指标相关 ====================

    /** 指标名称 */
    METRIC_NAME("metric_name", "指标名称", "cpu_usage/memory_usage/response_time"),

    /** 阈值 */
    THRESHOLD("threshold", "阈值", "80%/5s/1000"),

    /** 当前值 */
    CURRENT_VALUE("current_value", "当前值", "85%/8s/1500"),

    /** 监控系统 */
    MONITOR_SYSTEM("monitor_system", "监控系统", "prometheus/zabbix/nagios"),

    /** 严重程度 */
    SEVERITY("severity", "严重程度", "critical/high/medium/low/info"),

    // ==================== 安全相关 ====================

    /** 攻击类型 */
    ATTACK_TYPE("attack_type", "攻击类型", "sql_injection/xss/brute_force"),

    /** 威胁级别 */
    THREAT_LEVEL("threat_level", "威胁级别", "low/medium/high/critical"),

    /** 攻击者IP */
    ATTACKER_IP("attacker_ip", "攻击者IP", "************"),

    /** 用户代理 */
    USER_AGENT("user_agent", "用户代理", "Mozilla/5.0..."),

    /** 安全规则 */
    SECURITY_RULE("security_rule", "安全规则", "WAF-001/IDS-002"),

    // ==================== 错误和异常相关 ====================

    /** 错误代码 */
    ERROR_CODE("error_code", "错误代码", "500/404/timeout"),

    /** 异常类型 */
    EXCEPTION_TYPE("exception_type", "异常类型", "NullPointerException/SQLException"),

    /** 错误消息 */
    ERROR_MESSAGE("error_message", "错误消息", "Connection timeout"),

    // ==================== 时间相关 ====================

    /** 时间窗口 */
    TIME_WINDOW("time_window", "时间窗口", "5m/1h/1d"),

    /** 频率 */
    FREQUENCY("frequency", "频率", "10/min/100/hour"),

    // ==================== 特殊标签 ====================

    /** 测试标识 */
    TEST("test", "测试标识", "true/false"),

    /** 紧急程度 */
    URGENCY("urgency", "紧急程度", "low/medium/high/critical"),

    /** 影响范围 */
    IMPACT("impact", "影响范围", "single_user/multiple_users/service_down"),

    /** 自动化处理 */
    AUTOMATED("automated", "自动化处理", "true/false"),

    /** 静默状态 */
    SILENCED("silenced", "静默状态", "true/false"),

    /** 确认状态 */
    ACKNOWLEDGED("acknowledged", "确认状态", "true/false");

    private final String key;
    private final String description;
    private final String examples;

    AlertTagEnum(String key, String description, String examples) {
        this.key = key;
        this.description = description;
        this.examples = examples;
    }

    public String getKey() {
        return key;
    }

    public String getDescription() {
        return description;
    }

    public String getExamples() {
        return examples;
    }

    /**
     * 根据key获取枚举
     */
    public static AlertTagEnum getByKey(String key) {
        for (AlertTagEnum tag : values()) {
            if (tag.getKey().equals(key)) {
                return tag;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的标签key
     */
    public static boolean isValidTag(String key) {
        return getByKey(key) != null;
    }

    /**
     * 获取所有标签key
     */
    public static String[] getAllKeys() {
        AlertTagEnum[] values = values();
        String[] keys = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            keys[i] = values[i].getKey();
        }
        return keys;
    }
}
