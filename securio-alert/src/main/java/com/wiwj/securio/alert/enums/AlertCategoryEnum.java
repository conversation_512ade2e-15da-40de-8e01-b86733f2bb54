package com.wiwj.securio.alert.enums;

import lombok.Getter;

/**
 * 告警分类枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AlertCategoryEnum {
    OPS("ops", "运维告警"),
    SECURITY("security", "安全告警");

    /** 分类编码 */
    private final String code;

    /** 分类名称 */
    private final String name;

    AlertCategoryEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     */
    public static AlertCategoryEnum getByCode(String code) {
        for (AlertCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据告警源类型判断分类
     * SAFELINE_WAF,WANGSU_WAF,HONEYPOT,MUYUN,TDP,ZEEK,SURICATA 属于安全告警
     * 其他属于运维告警
     */
    public static AlertCategoryEnum getBySourceType(String sourceType) {
        if (sourceType == null) {
            return OPS;
        }
        
        switch (sourceType) {
            case "safeline_waf":
            case "wangsu_waf":
            case "honeypot":
            case "muyun":
            case "tdp":
            case "zeek":
            case "suricata":
                return SECURITY;
            default:
                return OPS;
        }
    }

    /**
     * 获取字典数据
     */
    public static AlertCategoryEnum[] getDictData() {
        return values();
    }
} 