package com.wiwj.securio.alert.domain;

import com.wiwj.securio.alert.enums.PerformanceLevel;
import lombok.Data;

/**
 * 人员统计实体
 *
 * <AUTHOR>
 */
@Data
public class UserStatistics {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 团队ID
     */
    private String teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 故障总量
     */
    private Integer totalAlerts;

    /**
     * 严重告警数量（critical）
     */
    private Integer criticalAlerts;

    /**
     * 待处理数量
     */
    private Integer pendingAlerts;

    /**
     * 已解决数量
     */
    private Integer resolvedAlerts;

    /**
     * 平均认领时间（小时）
     */
    private Double mtta;

    /**
     * 平均恢复时间（小时）
     */
    private Double mttr;

    /**
     * 性能等级
     */
    private PerformanceLevel performanceLevel;

    /**
     * 带标识的显示名称
     */
    private String displayName;

    /**
     * 计算显示名称
     */
    public String getDisplayName() {
        if (performanceLevel == null) {
            return userName;
        }
        return performanceLevel.getIcon() + userName;
    }

    /**
     * 设置性能等级
     */
    public void setPerformanceLevel() {
        this.performanceLevel = PerformanceLevel.getLevel(mtta, mttr);
    }
} 