package com.wiwj.securio.alert.integration.flashduty.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.enums.AlertStatusEnum;
import com.wiwj.securio.alert.integration.flashduty.constants.FlashDutyConstants;
import com.wiwj.securio.alert.integration.flashduty.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.HashMap;

/**
 * FlashDuty服务
 *
 * <AUTHOR>
 */
@Service
public class FlashDutyService {

    private static final Logger logger = LoggerFactory.getLogger(FlashDutyService.class);

    @Autowired
    private RestTemplate restTemplate;

    @Value("${flashduty.api.base_url:https://api.flashcat.cloud/api/v1}")
    private String flashDutyApiBaseUrl;

    @Value("${flashduty.api.integration_key:}")
    private String defaultIntegrationKey;

    @Value("${flashduty.api.app_key}")
    private String appKey;


    /**
     * 分页查询告警列表
     * @param request
     * @return
     */
    public IncidentListResponse queryIncidentList(IncidentListRequest request) {
        logger.info("queryIncidentList received request: {}", JSON.toJSONString(request));
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<IncidentListRequest> requestEntity = new HttpEntity<>(request, headers);

        String apiUrl = "https://api.flashcat.cloud/incident/list";

        try {
            // 发送请求
            ResponseEntity<IncidentListResponse> response = restTemplate.exchange(
                    apiUrl + "?app_key=" + appKey,
                    HttpMethod.POST,
                    requestEntity,
                    IncidentListResponse.class
            );

            logger.info("queryIncidentList: {}", JSON.toJSONString(response.getBody()));

            // 返回响应体
            return response.getBody();
        } catch (Exception e) {
            logger.error("查询FlashDuty告警列表失败", e);
        }
        return new IncidentListResponse();
    }


    /**
     * 发送告警事件到FlashDuty
     *
     * @param alert 告警信息
     * @return 是否发送成功
     */
    public boolean sendAlertToFlashDuty(Alert alert) {
        return sendStandardAlertToFlashDuty(alert, defaultIntegrationKey);
    }

    /**
     * 发送告警事件到FlashDuty
     *
     * @param alert          告警信息
     * @param integrationKey 集成密钥，如果为null则使用默认集成密钥
     * @return 是否发送成功
     */
    public boolean sendAlertToFlashDuty(Alert alert, String integrationKey) {
        return sendStandardAlertToFlashDuty(alert, integrationKey);
    }

    /**
     * 发送标准告警事件到FlashDuty
     *
     * @param alert          告警信息
     * @param integrationKey 集成密钥，如果为null则使用默认集成密钥
     * @return 是否发送成功
     */
    public boolean sendStandardAlertToFlashDuty(Alert alert, String integrationKey) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            FlashDutyStandardAlertRequest requestBody = buildStandardAlertRequest(alert);

            // 构建URL
            String url = buildUrl(FlashDutyConstants.API_PATH_ALERT, integrationKey);

            // 发送请求
            HttpEntity<FlashDutyStandardAlertRequest> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<CommonMapResponse> responseEntity = restTemplate.postForEntity(url, requestEntity, CommonMapResponse.class);

            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                CommonMapResponse response = responseEntity.getBody();
                if (response != null && response.getData() != null && response.getData().containsKey("alert_key")) {
                    logger.info("Successfully sent alert to FlashDuty: {}, alert_key: {}", alert.getId(), response.getData().get("alert_key"));
                    return true;
                } else {
                    logger.error("Failed to send alert to FlashDuty. Error: {}",
                            response != null && response.getError() != null ? response.getError() : "Unknown error");
                    return false;
                }
            } else {
                logger.error("Failed to send alert to FlashDuty. Status code: {}", responseEntity.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            logger.error("Error sending alert to FlashDuty", e);
            return false;
        }
    }

    /**
     * 发送变更事件到FlashDuty
     *
     * @param changeKey   变更单号
     * @param title       变更标题
     * @param description 变更描述
     * @param status      变更状态
     * @param eventTime   变更事件时间
     * @param link        变更单地址
     * @param labels      变更标签
     * @return 是否发送成功
     */
    public boolean sendChangeToFlashDuty(String changeKey, String title, String description, String status,
                                         Long eventTime, String link, Map<String, String> labels) {
        return sendChangeToFlashDuty(changeKey, title, description, status, eventTime, link, labels, defaultIntegrationKey);
    }

    /**
     * 发送变更事件到FlashDuty
     *
     * @param changeKey      变更单号
     * @param title          变更标题
     * @param description    变更描述
     * @param status         变更状态
     * @param eventTime      变更事件时间
     * @param link           变更单地址
     * @param labels         变更标签
     * @param integrationKey 集成密钥
     * @return 是否发送成功
     */
    public boolean sendChangeToFlashDuty(String changeKey, String title, String description, String status,
                                         Long eventTime, String link, Map<String, String> labels, String integrationKey) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            FlashDutyStandardChangeRequest requestBody = new FlashDutyStandardChangeRequest();
            requestBody.setChange_key(changeKey);
            requestBody.setTitle(title);
            requestBody.setChange_status(status);

            // 设置可选字段
            if (description != null) {
                requestBody.setDescription(description);
            }

            if (eventTime != null) {
                requestBody.setEvent_time(eventTime);
            } else {
                requestBody.setEvent_time(System.currentTimeMillis() / 1000);
            }

            if (link != null) {
                requestBody.setLink(link);
            }

            // 设置标签
            if (labels != null) {
                requestBody.getLabels().putAll(labels);
            }
            requestBody.addLabel("source", "securio");
            requestBody.addLabel("change_key", changeKey);

            // 构建URL
            String url = buildUrl(FlashDutyConstants.API_PATH_CHANGE, integrationKey);

            // 发送请求
            HttpEntity<FlashDutyStandardChangeRequest> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<FlashDutyResponse> responseEntity = restTemplate.postForEntity(url, requestEntity, FlashDutyResponse.class);

            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                FlashDutyResponse response = responseEntity.getBody();
                if (response != null && response.getData() != null) {
                    logger.info("Successfully sent change to FlashDuty, request_id: {}", response.getRequest_id());
                    return true;
                } else {
                    logger.error("Failed to send change to FlashDuty. Error: {}",
                            response != null && response.getError() != null ? response.getError() : "Unknown error");
                    return false;
                }
            } else {
                logger.error("Failed to send change to FlashDuty. Status code: {}", responseEntity.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            logger.error("Error sending change to FlashDuty", e);
            return false;
        }
    }

    /**
     * 构建标准告警请求
     *
     * @param alert 告警信息
     * @return FlashDuty标准告警请求
     */
    private FlashDutyStandardAlertRequest buildStandardAlertRequest(Alert alert) {
        FlashDutyStandardAlertRequest request = new FlashDutyStandardAlertRequest();

        // 设置告警标题
        request.setTitle_rule(alert.getTitle());

        // 设置告警状态
        request.setEvent_status(mapSeverityToEventStatus(alert.getSeverity()));

        if (alert.getResolvedAt() != null && Objects.equals(alert.getStatus(), AlertStatusEnum.RESOLVED.getCode())) {
            //设置为恢复
            request.setEvent_status(FlashDutyConstants.EVENT_STATUS_OK);
        }

        // 设置告警标识
        if (alert.getId() != null) {
            request.setAlert_key("securio-alert-" + alert.getId());
        }

        // 设置告警描述
        if (alert.getDescription() != null) {
            request.setDescription(alert.getDescription());
        }

        //填充标签
        fillLabels(alert, request);

        return request;

    }

    private void fillLabels(Alert alert, FlashDutyStandardAlertRequest request) {
        // 添加标签
        request.addLabel("source", "securio-alert");
        request.addLabel("alert_id", String.valueOf(alert.getId()));

        if (alert.getSeverity() != null) {
            request.addLabel("severity", alert.getSeverity());
        }
        if (alert.getSeverity() != null) {
            request.addLabel("sourceType", alert.getSourceType());
        }

        if (alert.getSourceSubType() != null) {
            request.addLabel("source_sub_type", alert.getSourceSubType());
        }

        if (alert.getAlertType() != null) {
            request.addLabel("alert_type", alert.getAlertType());
        }

        if (alert.getStatus() != null) {
            request.addLabel("status", alert.getStatus());
        }

        if (alert.getSourceName() != null) {
            request.addLabel("source_name", alert.getSourceName());
        }

        if (alert.getSourceIdent() != null) {
            request.addLabel("source_ident", alert.getSourceIdent());
        }

        if (alert.getDetectedAt() != null) {
            request.addLabel("detected_at", formatDate(alert.getDetectedAt()));
        }

        if (alert.getOccurredAt() != null) {
            request.addLabel("occurred_at", formatDate(alert.getOccurredAt()));
        }

        if (alert.getResolvedAt() != null) {
            request.addLabel("resolved_at", formatDate(alert.getResolvedAt()));
        }

        if (alert.getResolvedBy() != null) {
            request.addLabel("resolved_by", alert.getResolvedBy());
        }

        if (alert.getResolvedByName() != null) {
            request.addLabel("resolved_by_name", alert.getResolvedByName());
        }

        if (alert.getResolutionNote() != null) {
            request.addLabel("resolution_note", alert.getResolutionNote());
        }

        if (alert.getSrcIp() != null) {
            request.addLabel("src_ip", alert.getSrcIp());
        }

        if (alert.getSrcPort() != null) {
            request.addLabel("src_port", alert.getSrcPort());
        }

        if (alert.getDstIp() != null) {
            request.addLabel("dst_ip", alert.getDstIp());
        }

        if (alert.getDstPort()!= null) {
            request.addLabel("dst_port", alert.getDstPort());
        }

        if (alert.getGroupName() != null) {
            request.addLabel("group_name", alert.getGroupName());
        }

        if (alert.getRuleName() != null) {
            request.addLabel("rule_name", alert.getRuleName());
        }

        if (alert.getUrl() != null) {
            request.addLabel("url", alert.getUrl());
        }

        if (alert.getEventId() != null) {
            request.addLabel("event_id", alert.getEventId());
            request.addLabel("eventId", alert.getEventId());
        }

        String tags = alert.getTags();
        if (StringUtils.isNotEmpty(tags)) {
            JSONObject jsonObject = JSON.parseObject(tags);
            for (String key : jsonObject.keySet()) {
                request.addLabel(key, jsonObject.getString(key));
            }
        }

        if (StringUtils.isNotEmpty(alert.getAttributes())) {
            request.addLabel("attributes", alert.getAttributes());
        }
    }

    /**
     * 将Securio告警级别映射到FlashDuty告警状态
     *
     * @param severity Securio告警级别
     * @return FlashDuty告警状态
     */
    private String mapSeverityToEventStatus(String severity) {
        if (severity == null) {
            return FlashDutyConstants.EVENT_STATUS_INFO;
        }

        switch (severity.toLowerCase()) {
            case "critical":
                return FlashDutyConstants.EVENT_STATUS_CRITICAL;
            case "high":
                return FlashDutyConstants.EVENT_STATUS_WARNING;
            case "medium":
                return FlashDutyConstants.EVENT_STATUS_WARNING;
            case "low":
                return FlashDutyConstants.EVENT_STATUS_INFO;
            case "info":
                return FlashDutyConstants.EVENT_STATUS_INFO;
            default:
                return FlashDutyConstants.EVENT_STATUS_INFO;
        }
    }

    /**
     * 构建API URL
     *
     * @param path           API路径
     * @param integrationKey 集成密钥
     * @return 完整URL
     */
    private String buildUrl(String path, String integrationKey) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(flashDutyApiBaseUrl + path);

        // 添加集成密钥
        if (integrationKey != null && !integrationKey.isEmpty()) {
            builder.queryParam(FlashDutyConstants.PARAM_INTEGRATION_KEY, integrationKey);
        }

        return builder.build().toUriString();
    }

    /**
     * 格式化日期
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期字符串
     */
    private String formatDate(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(timestamp));
    }

    /**
     * 使用完整的 Webhook URL 发送 FlashDuty 标准事件
     *
     * @param alert      告警信息
     * @param webhookUrl 完整的 Webhook URL
     * @return 是否发送成功
     */
    public boolean sendFlashDutyStandardAlert(Alert alert, String webhookUrl) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            FlashDutyStandardAlertRequest requestBody = buildStandardAlertRequest(alert);

            // 直接使用完整的 Webhook URL
            // 发送请求
            HttpEntity<FlashDutyStandardAlertRequest> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<CommonMapResponse> responseEntity = restTemplate.postForEntity(webhookUrl, requestEntity, CommonMapResponse.class);

            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                CommonMapResponse response = responseEntity.getBody();
                if (response != null && response.getData() != null && response.getData().containsKey("alert_key")) {
                    logger.info("Successfully sent alert to FlashDuty: {}, alert_key: {}", alert.getId(), response.getData().get("alert_key"));
                    return true;
                } else {
                    logger.error("Failed to send alert to FlashDuty. Error: {}",
                            response != null && response.getError() != null ? response.getError() : "Unknown error");
                    return false;
                }
            } else {
                logger.error("Failed to send alert to FlashDuty. Status code: {}", responseEntity.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            logger.error("Error sending alert to FlashDuty", e);
            return false;
        }
    }

    public boolean sendFlashDutyStandardAlert2(String json, String webhookUrl) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            JSONObject jsonObject = JSON.parseObject(json);

            // 直接使用完整的 Webhook URL
            // 发送请求
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);
            ResponseEntity<CommonMapResponse> responseEntity = restTemplate.postForEntity(webhookUrl, requestEntity, CommonMapResponse.class);

            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                CommonMapResponse response = responseEntity.getBody();
                if (response != null && response.getData() != null && response.getData().containsKey("alert_key")) {
                    logger.info("Successfully sent alert to FlashDuty: {}, alert_key: {}", response.getData().get("alert_key"));
                    return true;
                } else {
                    logger.error("Failed to send alert to FlashDuty. Error: {}",
                            response != null && response.getError() != null ? response.getError() : "Unknown error");
                    return false;
                }
            } else {
                logger.error("Failed to send alert to FlashDuty. Status code: {}", responseEntity.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            logger.error("Error sending alert to FlashDuty", e);
            return false;
        }
    }

    /**
     * 直接转发原始数据到 FlashDuty
     *
     * @param rawData    原始数据
     * @param webhookUrl 完整的 Webhook URL
     * @param alertId    告警ID（用于日志记录）
     * @return 是否发送成功
     */
    public boolean forwardRawDataToFlashDuty(String rawData, String webhookUrl, Long alertId) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 直接使用原始数据作为请求体，不做任何处理
            HttpEntity<String> requestEntity = new HttpEntity<>(rawData, headers);

            // 直接发送到配置的 Webhook URL
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(webhookUrl, requestEntity, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully forwarded raw data to FlashDuty, alert ID: {}", alertId);
                return true;
            } else {
                logger.error("Failed to forward raw data to FlashDuty. Status code: {}, alert ID: {}", responseEntity.getStatusCode(), alertId);
                return false;
            }
        } catch (Exception e) {
            logger.error("Error forwarding raw data to FlashDuty, alert ID: {}", alertId, e);
            return false;
        }
    }

    /**
     * 解决故障
     *
     * @param incidentIds 故障ID列表
     * @param rootCause   根本原因
     * @param resolution  解决方案
     * @return 错误信息，成功时返回null
     */
    public FlashDutyResponse.FlashDutyError resolveIncidents(List<String> incidentIds, String rootCause, String resolution) {
        return resolveIncidents(incidentIds, rootCause, resolution, appKey);
    }

    /**
     * 解决故障
     *
     * @param incidentIds 故障ID列表
     * @param rootCause   根本原因
     * @param resolution  解决方案
     * @param appKey      应用密钥
     * @return 错误信息，成功时返回null
     */
    public FlashDutyResponse.FlashDutyError resolveIncidents(List<String> incidentIds, String rootCause, String resolution, String appKey) {
        try {
            // 验证参数
            if (incidentIds == null || incidentIds.isEmpty()) {
                return createError("INVALID_PARAMS", "故障ID列表不能为空");
            }
            if (incidentIds.size() > 100) {
                return createError("INVALID_PARAMS", "故障ID列表不能超过100项");
            }
            if (appKey == null || appKey.isEmpty()) {
                return createError("INVALID_PARAMS", "应用密钥不能为空");
            }

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            FlashDutyIncidentResolveRequest requestBody = new FlashDutyIncidentResolveRequest();
            requestBody.setIncident_ids(incidentIds);
            requestBody.setRoot_cause(rootCause);
            requestBody.setResolution(resolution);

            // 构建URL，使用新的buildUrlWithAppKey方法
            String url = buildUrlWithAppKey(FlashDutyConstants.API_PATH_INCIDENT_RESOLVE, appKey);

            // 发送请求
            HttpEntity<FlashDutyIncidentResolveRequest> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<FlashDutyResponse> responseEntity = restTemplate.postForEntity(url, requestEntity, FlashDutyResponse.class);

            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                FlashDutyResponse response = responseEntity.getBody();
                logger.info("Successfully resolved incidents in FlashDuty: {}, request_id: {}",
                        incidentIds, response != null ? response.getRequest_id() : "unknown");
                return null; // 成功时返回null表示无错误
            } else {
                logger.error("Failed to resolve incidents in FlashDuty. Status code: {}, incidents: {}",
                        responseEntity.getStatusCode(), incidentIds);
                return createError("HTTP_ERROR", "HTTP状态码: " + responseEntity.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Error resolving incidents in FlashDuty: {}", incidentIds, e);
            return createError("SYSTEM_ERROR", "系统错误: " + e.getMessage());
        }
    }

    /**
     * 认领故障
     *
     * @param incidentIds 故障ID列表
     * @param appKey      应用密钥
     * @return 错误信息，成功时返回null
     */
    public FlashDutyResponse.FlashDutyError acknowledgeIncidents(List<String> incidentIds, String appKey) {
        try {
            // 验证参数
            if (incidentIds == null || incidentIds.isEmpty()) {
                return createError("INVALID_PARAMS", "故障ID列表不能为空");
            }
            if (appKey == null || appKey.isEmpty()) {
                return createError("INVALID_PARAMS", "应用密钥不能为空");
            }

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            FlashDutyIncidentAckRequest requestBody = new FlashDutyIncidentAckRequest();
            requestBody.setIncident_ids(incidentIds);

            // 构建URL
            String url = buildUrlWithAppKey(FlashDutyConstants.API_PATH_INCIDENT_ACK, appKey);

            // 发送请求
            HttpEntity<FlashDutyIncidentAckRequest> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<FlashDutyResponse> responseEntity = restTemplate.postForEntity(url, requestEntity, FlashDutyResponse.class);

            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                FlashDutyResponse response = responseEntity.getBody();
                logger.info("Successfully acknowledged incidents in FlashDuty: {}, request_id: {}",
                        incidentIds, response != null ? response.getRequest_id() : "unknown");
                return null; // 成功时返回null表示无错误
            } else {
                logger.error("Failed to acknowledge incidents in FlashDuty. Status code: {}, incidents: {}",
                        responseEntity.getStatusCode(), incidentIds);
                return createError("HTTP_ERROR", "HTTP状态码: " + responseEntity.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Error acknowledging incidents in FlashDuty: {}", incidentIds, e);
            return createError("SYSTEM_ERROR", "系统错误: " + e.getMessage());
        }
    }

    /**
     * 构建带有app_key参数的API URL
     *
     * @param path   API路径
     * @param appKey 应用密钥
     * @return 完整URL
     */
    private String buildUrlWithAppKey(String path, String appKey) {
        // 直接使用完整的API URL，因为故障解决接口不在/api/v1路径下
        String baseUrl = "https://api.flashcat.cloud";
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl + path);

        // 添加应用密钥
        if (appKey != null && !appKey.isEmpty()) {
            builder.queryParam(FlashDutyConstants.PARAM_APP_KEY, appKey);
        }

        return builder.build().toUriString();
    }

    /**
     * 创建错误对象
     *
     * @param code    错误码
     * @param message 错误信息
     * @return FlashDutyError对象
     */
    private FlashDutyResponse.FlashDutyError createError(String code, String message) {
        FlashDutyResponse.FlashDutyError error = new FlashDutyResponse.FlashDutyError();
        error.setCode(code);
        error.setMessage(message);
        return error;
    }

    /**
     * 获取团队列表
     * @param request 查询请求参数
     * @return 团队列表响应
     */
    public TeamListResponse queryTeamList(TeamListRequest request) {
        logger.info("queryTeamList received request: {}", JSON.toJSONString(request));
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<TeamListRequest> requestEntity = new HttpEntity<>(request, headers);

        String apiUrl = "https://api.flashcat.cloud/team/list";

        try {
            // 发送请求
            ResponseEntity<TeamListResponse> response = restTemplate.exchange(
                    apiUrl + "?app_key=" + appKey,
                    HttpMethod.POST,
                    requestEntity,
                    TeamListResponse.class
            );

            logger.info("queryTeamList response: {}", JSON.toJSONString(response.getBody()));

            // 返回响应体
            return response.getBody();
        } catch (Exception e) {
            logger.error("查询FlashDuty团队列表失败", e);
        }
        return new TeamListResponse();
    }

    /**
     * 获取成员列表
     * @param request 查询请求参数
     * @return 成员列表响应
     */
    public MemberListResponse queryMemberList(MemberListRequest request) {
        logger.info("queryMemberList received request: {}", JSON.toJSONString(request));
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<MemberListRequest> requestEntity = new HttpEntity<>(request, headers);

        String apiUrl = "https://api.flashcat.cloud/member/list";

        try {
            // 发送请求
            ResponseEntity<MemberListResponse> response = restTemplate.exchange(
                    apiUrl + "?app_key=" + appKey,
                    HttpMethod.POST,
                    requestEntity,
                    MemberListResponse.class
            );

            logger.info("queryMemberList response: {}", JSON.toJSONString(response.getBody()));

            // 返回响应体
            return response.getBody();
        } catch (Exception e) {
            logger.error("查询FlashDuty成员列表失败", e);
        }
        return new MemberListResponse();
    }

    /**
     * 查询故障时间线
     * @param request 查询请求
     * @return 时间线响应
     */
    public IncidentFeedResponse queryIncidentFeed(IncidentFeedRequest request) {
        try {
            logger.info("查询故障时间线开始，incident_id: {}", request.getIncident_id());
            
            // 构建请求URL
            String url = "https://api.flashcat.cloud/incident/feed?app_key=" + appKey;
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("incident_id", request.getIncident_id());
            
            if (request.getTypes() != null && !request.getTypes().isEmpty()) {
                requestBody.put("types", request.getTypes());
            }
            if (request.getP() != null) {
                requestBody.put("p", request.getP());
            }
            if (request.getLimit() != null) {
                requestBody.put("limit", request.getLimit());
            }
            if (request.getAsc() != null) {
                requestBody.put("asc", request.getAsc());
            }
            
            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            logger.debug("发送FlashDuty时间线查询请求，URL: {}, 请求体: {}", url, requestBody);
            
            ResponseEntity<IncidentFeedResponse> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, IncidentFeedResponse.class
            );
            
            logger.debug("FlashDuty时间线查询响应，状态码: {}, 响应体: {}", 
                response.getStatusCode(), response.getBody());
            IncidentFeedResponse incidentFeedResponse = response.getBody();


            logger.info("查询故障时间线完成，获取到 {} 条记录",
                    incidentFeedResponse!=null&&incidentFeedResponse.getData() != null && incidentFeedResponse.getData().getItems() != null
                ? incidentFeedResponse.getData().getItems().size() : 0);
            
            return incidentFeedResponse;
            
        } catch (Exception e) {
            logger.error("查询故障时间线失败，incident_id: {}", request.getIncident_id(), e);
            throw new RuntimeException("查询故障时间线失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除故障
     * @param request 删除请求
     * @return 删除响应
     */
    public IncidentRemoveResponse removeIncidents(IncidentRemoveRequest request) {
        try {
            logger.info("删除故障开始，incident_ids: {}", request.getIncident_ids());
            
            // 参数校验
            if (request.getIncident_ids() == null || request.getIncident_ids().isEmpty()) {
                IncidentRemoveResponse errorResponse = new IncidentRemoveResponse();
                FlashDutyResponse.FlashDutyError error = new FlashDutyResponse.FlashDutyError();
                error.setCode("InvalidParameter");
                error.setMessage("故障ID列表不能为空");
                errorResponse.setError(error);
                return errorResponse;
            }
            
            // 构建请求URL
            String url = "https://api.flashcat.cloud/incident/remove?app_key=" + appKey;
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("incident_ids", request.getIncident_ids());
            
            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            logger.debug("发送FlashDuty删除故障请求，URL: {}, 请求体: {}", url, requestBody);
            
            ResponseEntity<IncidentRemoveResponse> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, IncidentRemoveResponse.class
            );
            
            logger.debug("FlashDuty删除故障响应，状态码: {}, 响应体: {}", 
                response.getStatusCode(), response.getBody());
            
            IncidentRemoveResponse removeResponse = response.getBody();
            
            // 检查删除结果
            if (removeResponse != null && removeResponse.getError() == null) {
                logger.info("删除故障成功，删除的故障ID: {}", request.getIncident_ids());
            } else {
                logger.warn("删除故障可能失败，错误信息: {}", 
                    removeResponse != null ? removeResponse.getError() : "响应为空");
            }
            
            return removeResponse;
            
        } catch (Exception e) {
            logger.error("删除故障失败，incident_ids: {}", request.getIncident_ids(), e);
            
            // 创建错误响应
            IncidentRemoveResponse errorResponse = new IncidentRemoveResponse();
            FlashDutyResponse.FlashDutyError error = new FlashDutyResponse.FlashDutyError();
            error.setCode("InternalError");
            error.setMessage("删除故障失败: " + e.getMessage());
            errorResponse.setError(error);
            return errorResponse;
        }
    }

    /**
     * 分页查询告警列表
     * @param request 请求参数
     * @return 告警列表响应
     */
    public AlertListResponse queryAlertList(AlertListRequest request) {
        logger.info("queryAlertList received request: {}", JSON.toJSONString(request));
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<AlertListRequest> requestEntity = new HttpEntity<>(request, headers);

        String apiUrl = "https://api.flashcat.cloud/alert/list";

        try {
            // 发送请求
            ResponseEntity<AlertListResponse> response = restTemplate.exchange(
                    apiUrl + "?app_key=" + appKey,
                    HttpMethod.POST,
                    requestEntity,
                    AlertListResponse.class
            );

            logger.info("queryAlertList response status: {}", response.getStatusCode());
            
            if (response.getBody() != null && response.getBody().getData() != null) {
                logger.info("queryAlertList found {} alerts, has_next_page: {}", 
                        response.getBody().getData().getItems() != null ? response.getBody().getData().getItems().size() : 0,
                        response.getBody().getData().getHas_next_page());
            }

            // 返回响应体
            return response.getBody();
        } catch (Exception e) {
            logger.error("查询FlashDuty告警列表失败", e);
        }
        return new AlertListResponse();
    }


}
