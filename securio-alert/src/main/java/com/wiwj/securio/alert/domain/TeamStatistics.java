package com.wiwj.securio.alert.domain;

import lombok.Data;
import java.util.List;

/**
 * 团队统计实体
 *
 * <AUTHOR>
 */
@Data
public class TeamStatistics {

    /**
     * 团队ID
     */
    private String teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 故障总量
     */
    private Integer totalAlerts;

    /**
     * 严重告警数量（critical）
     */
    private Integer criticalAlerts;

    /**
     * 待处理数量
     */
    private Integer pendingAlerts;

    /**
     * 已解决数量
     */
    private Integer resolvedAlerts;

    /**
     * 平均认领时间（小时）
     */
    private Double mtta;

    /**
     * 平均恢复时间（小时）
     */
    private Double mttr;

    /**
     * 人员列表
     */
    private List<UserStatistics> users;
} 