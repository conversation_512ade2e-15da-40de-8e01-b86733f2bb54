# FlashDuty集成指南

本文档介绍如何使用FlashDutyService将告警事件和变更事件发送到FlashDuty平台。

## 配置

在`application.yml`中配置FlashDuty API的基础URL和集成密钥：

```yaml
# FlashDuty配置
flashduty:
  api:
    # API基础地址
    base_url: https://api.flashcat.cloud/api/v1
    # 集成密钥
    integration_key: your_integration_key_here
```

## 使用方法

### 1. 注入FlashDutyService

```java
@Autowired
private FlashDutyService flashDutyService;
```

### 2. 发送告警事件

```java
// 创建告警对象
Alert alert = new Alert();
alert.setId(1L);
alert.setTitle("系统异常");
alert.setDescription("服务器CPU使用率超过90%");
alert.setSeverity("high");
alert.setStatus("new");
alert.setSourceName("系统监控");
alert.setSourceInstance("server-001");
alert.setOccurredAt(new Date());

// 发送告警事件
boolean success = flashDutyService.sendAlertToFlashDuty(alert);
if (success) {
    logger.info("告警事件发送成功");
} else {
    logger.error("告警事件发送失败");
}
```

### 3. 使用自定义集成密钥发送告警事件

```java
// 使用自定义集成密钥发送告警事件
String customIntegrationKey = "custom_integration_key";
boolean success = flashDutyService.sendAlertToFlashDuty(alert, customIntegrationKey);
```

### 4. 发送解决事件

```java
// 更新告警状态为已解决
alert.setStatus("resolved");
alert.setResolvedAt(new Date());
alert.setResolvedBy("admin");
alert.setResolvedByName("系统管理员");
alert.setResolutionNote("已重启服务器");

// 发送解决事件
boolean success = flashDutyService.sendAlertToFlashDuty(alert);
```

### 5. 发送变更事件

```java
// 准备变更事件参数
String title = "系统升级";
String body = "升级系统版本从1.0到2.0";
String changeType = FlashDutyConstants.CHANGE_TYPE_NORMAL;
String status = FlashDutyConstants.CHANGE_STATUS_PLANNED;
String sourceName = "系统管理";
String sourceInstance = "server-001";
Date startTime = new Date(); // 变更开始时间
Date endTime = new Date(System.currentTimeMillis() + 3600000); // 变更结束时间，1小时后

// 准备标签
Map<String, String> tags = new HashMap<>();
tags.put("team", "infrastructure");
tags.put("environment", "production");

// 准备字段
Map<String, Object> fields = new HashMap<>();
fields.put("version", "2.0");
fields.put("impact", "低影响");
fields.put("approver", "技术总监");

// 发送变更事件
boolean success = flashDutyService.sendChangeToFlashDuty(
    title, body, changeType, status, sourceName, sourceInstance, 
    startTime, endTime, tags, fields);
if (success) {
    logger.info("变更事件发送成功");
} else {
    logger.error("变更事件发送失败");
}
```

### 6. 使用自定义集成密钥发送变更事件

```java
// 使用自定义集成密钥发送变更事件
String customIntegrationKey = "custom_integration_key";
boolean success = flashDutyService.sendChangeToFlashDuty(
    title, body, changeType, status, sourceName, sourceInstance, 
    startTime, endTime, tags, fields, customIntegrationKey);
```

## API参数说明

### 告警事件API

FlashDuty告警事件API接受以下参数：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| title | String | 是 | 告警标题 |
| body | String | 否 | 告警内容 |
| priority | String | 否 | 告警优先级(critical/high/medium/low/info) |
| event_type | String | 否 | 事件类型(alert/resolve) |
| source | Object | 否 | 告警来源 |
| fingerprint | String | 否 | 告警标识，用于关联告警 |
| start_time | Long | 否 | 告警开始时间（Unix时间戳，秒） |
| end_time | Long | 否 | 告警结束时间（Unix时间戳，秒） |
| tags | Object | 否 | 告警标签 |
| fields | Object | 否 | 告警字段 |
| links | Object | 否 | 告警链接 |

### 变更事件API

FlashDuty变更事件API接受以下参数：

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| title | String | 是 | 变更标题 |
| body | String | 否 | 变更内容 |
| change_type | String | 否 | 变更类型(normal/emergency) |
| status | String | 否 | 变更状态(planned/in_progress/completed/canceled/failed) |
| source | Object | 否 | 变更来源 |
| fingerprint | String | 否 | 变更标识，用于关联变更 |
| start_time | Long | 否 | 变更开始时间（Unix时间戳，秒） |
| end_time | Long | 否 | 变更结束时间（Unix时间戳，秒） |
| tags | Object | 否 | 变更标签 |
| fields | Object | 否 | 变更字段 |
| links | Object | 否 | 变更链接 |

## 常量定义

FlashDutyConstants类定义了以下常量：

### 事件类型

- `EVENT_TYPE_ALERT`: 告警事件
- `EVENT_TYPE_RESOLVE`: 解决事件

### 优先级

- `PRIORITY_CRITICAL`: 严重
- `PRIORITY_HIGH`: 高
- `PRIORITY_MEDIUM`: 中
- `PRIORITY_LOW`: 低
- `PRIORITY_INFO`: 信息

### 变更类型

- `CHANGE_TYPE_NORMAL`: 常规变更
- `CHANGE_TYPE_EMERGENCY`: 紧急变更

### 变更状态

- `CHANGE_STATUS_PLANNED`: 计划中
- `CHANGE_STATUS_IN_PROGRESS`: 进行中
- `CHANGE_STATUS_COMPLETED`: 已完成
- `CHANGE_STATUS_CANCELED`: 已取消
- `CHANGE_STATUS_FAILED`: 已失败

## 错误处理

FlashDutyService会处理以下错误情况：

1. HTTP请求错误
2. FlashDuty API返回的错误
3. 网络异常

所有错误都会被记录到日志中，并返回false表示发送失败。
