package com.wiwj.securio.alert.util;

import java.time.*;
import java.time.temporal.ChronoUnit;

public class TimeUtils {

    /**
     * 根据时间戳（秒）获取当天的开始和结束时间（秒）
     * @param timestamp 输入的时间戳（单位：秒）
     * @param zoneId 时区ID（如 ZoneId.of("UTC") 或 ZoneId.systemDefault()）
     * @return 包含开始时间和结束时间的数组，arr[0]=开始时间，arr[1]=结束时间
     */
    public static long[] getDayStartAndEndInSeconds(long timestamp, ZoneId zoneId) {
        // 1. 将时间戳转换为Instant
        Instant instant = Instant.ofEpochSecond(timestamp);
        
        // 2. 转换为指定时区的ZonedDateTime
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);
        
        // 3. 获取当天的开始时间（00:00:00）
        ZonedDateTime startOfDay = zonedDateTime.truncatedTo(ChronoUnit.DAYS);
        long startOfDayInSeconds = startOfDay.toEpochSecond();
        
        // 4. 获取当天的结束时间（23:59:59）
        ZonedDateTime endOfDay = startOfDay.plusDays(1).minusSeconds(1);
        long endOfDayInSeconds = endOfDay.toEpochSecond();
        
        return new long[]{startOfDayInSeconds, endOfDayInSeconds};
    }

    public static long[] getDayStartAndEndInSeconds(long timestamp) {
        // 1. 将时间戳转换为Instant
        Instant instant = Instant.ofEpochSecond(timestamp);

        // 2. 转换为指定时区的ZonedDateTime
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());

        // 3. 获取当天的开始时间（00:00:00）
        ZonedDateTime startOfDay = zonedDateTime.truncatedTo(ChronoUnit.DAYS);
        long startOfDayInSeconds = startOfDay.toEpochSecond();

        // 4. 获取当天的结束时间（23:59:59）
        ZonedDateTime endOfDay = startOfDay.plusDays(1).minusSeconds(1);
        long endOfDayInSeconds = endOfDay.toEpochSecond();

        return new long[]{startOfDayInSeconds, endOfDayInSeconds};
    }

    // 示例用法
    public static void main(String[] args) {
        long currentTimestamp = Instant.now().getEpochSecond(); // 当前时间戳（秒）
        ZoneId zone = ZoneId.systemDefault(); // 使用系统默认时区
        
        long[] dayRange = getDayStartAndEndInSeconds(currentTimestamp, zone);
        System.out.println("当天开始时间（秒）: " + dayRange[0]);
        System.out.println("当天结束时间（秒）: " + dayRange[1]);
        
        // 格式化输出（可选）
        System.out.println("开始时间: " + Instant.ofEpochSecond(dayRange[0]).atZone(zone));
        System.out.println("结束时间: " + Instant.ofEpochSecond(dayRange[1]).atZone(zone));
    }
}