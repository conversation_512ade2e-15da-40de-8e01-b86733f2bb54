package com.wiwj.securio.alert.service;

import com.wiwj.securio.alert.integration.flashduty.model.PageIncidentResponseData;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 告警导出服务接口
 * 
 * <AUTHOR>
 */
public interface IAlertExportService {

    /**
     * 分页查询指定时间区间内的所有告警数据
     * 
     * @param startTime 开始时间(秒)
     * @param endTime 结束时间(秒)
     * @return 告警列表
     */
    List<PageIncidentResponseData.IncidentItem> queryAllIncidentsInTimeRange(Long startTime, Long endTime);

    /**
     * 导出告警列表到Excel
     * 
     * @param startTime 开始时间(秒)
     * @param endTime 结束时间(秒)
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportAlertsToExcel(Long startTime, Long endTime, HttpServletResponse response) throws IOException;

    /**
     * 导出Top20告警统计数据到Excel（包含两个sheet）
     * 
     * @param startTime 开始时间(秒)
     * @param endTime 结束时间(秒)
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportTop20AlertsToExcel(Long startTime, Long endTime, HttpServletResponse response) throws IOException;
} 