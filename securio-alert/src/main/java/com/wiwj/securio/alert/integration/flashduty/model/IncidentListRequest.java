package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 告警列表查询请求参数
 */
@Data
public class IncidentListRequest {
    /**
     * 游标分页，不设置为第一页。与p参数二选一
     */
    private String search_after_ctx;
    
    /**
     * 传统分页，不设置从第一页开始，默认值为1，与search_after_ctx参数二选一
     */
    private Integer p = 1;
    
    /**
     * 分页条数，默认20
     */
    private Integer limit = 20;
    
    /**
     * 是否升序，默认true
     */
    private Boolean asc = true;
    
    /**
     * 处理状态：Triggered,Processing,Closed
     */
    private String progress;
    
    /**
     * 故障标题
     */
    private String title;
    
    /**
     * 协作空间ID列表，0代表全局故障
     */
    private List<Long> channel_ids;
    
    /**
     * 处理人ID列表
     */
    private List<Integer> responder_ids;
    
    /**
     * 认领人ID列表
     */
    private List<Integer> acker_ids;
    
    /**
     * 创建人ID列表，0代表系统聚合产生的故障
     */
    private List<Integer> creator_ids;
    
    /**
     * 故障ID列表
     */
    private List<String> incident_ids;
    
    /**
     * 检索区间起点(秒)
     */
    private Long start_time;
    
    /**
     * 检索区间终点(秒)
     */
    private Long end_time;
    
    /**
     * 严重程度：Critical,Warning,Info
     */
    private String incident_severity;
    
    /**
     * 是否与我相关（登录人为发起人、被指派、关闭人或处理人之一）
     */
    private Boolean is_related;
    
    /**
     * 是否暂缓中
     */
    private Boolean is_snoozed;
    
    /**
     * 是否仅统计我所在团队下属协作空间内的故障
     */
    private Boolean is_my_team;
    
    /**
     * 是否仅统计我的空间（我所在团队下属协作空间以及我个人创建的协作空间）下的故障
     */
    private Boolean is_my_channel;
    
    /**
     * 标签筛选，支持精确匹配、正则匹配和通配匹配
     */
    private Map<String, String> labels;
    
    /**
     * 自定义字段筛选，仅支持精确匹配
     */
    private Map<String, Object> fields;
}