package com.wiwj.securio.alert.enums;

import lombok.Getter;

/**
 * 告警解决类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AlertResolutionTypeEnum {

    AUTO_RESOLVED("auto_resolved", "系统自动恢复", "系统检测到问题已自动恢复"),
    MANUAL_RESOLVED("manual_resolved", "人工解决", "运维人员手动解决告警"),
    SERVICE_RESTART("service_restart", "服务重启", "通过重启服务解决问题"),
    CONFIG_CHANGE("config_change", "配置调整", "通过调整配置参数解决问题"),
    NETWORK_FIXED("network_fixed", "网络修复", "网络连接问题已修复"),
    RESOURCE_ADJUSTED("resource_adjusted", "资源调整", "调整系统资源解决问题"),
    FALSE_POSITIVE("false_positive", "误报", "确认为误报告警");

    /** 解决类型编码 */
    private final String code;

    /** 解决类型名称 */
    private final String name;

    /** 解决类型描述 */
    private final String description;

    AlertResolutionTypeEnum(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据编码获取枚举
     */
    public static AlertResolutionTypeEnum getByCode(String code) {
        for (AlertResolutionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为自动解决
     */
    public boolean isAutoResolved() {
        return this == AUTO_RESOLVED;
    }
}