package com.wiwj.securio.alert.enums;

import lombok.Getter;

/**
 * 告警严重程度枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum AlertSeverityEnum {
    
    /** 严重 */
    CRITICAL("critical", "严重", 1, "#f56c6c"),
    
    /** 高危 */
    HIGH("high", "高危", 2, "#e6a23c"),
    
    /** 中危 */
    MEDIUM("medium", "中危", 3, "#409eff"),
    
    /** 低危 */
    LOW("low", "低危", 4, "#67c23a"),
    
    /** 信息 */
    INFO("info", "信息", 5, "#909399");
    
    /** 严重程度编码 */
    private final String code;
    
    /** 严重程度名称 */
    private final String name;
    
    /** 严重程度级别（数字越小越严重） */
    private final int level;
    
    /** 显示颜色 */
    private final String color;
    
    AlertSeverityEnum(String code, String name, int level, String color) {
        this.code = code;
        this.name = name;
        this.level = level;
        this.color = color;
    }
    
    /**
     * 根据编码获取枚举
     */
    public static AlertSeverityEnum getByCode(String code) {
        for (AlertSeverityEnum severity : values()) {
            if (severity.getCode().equals(code)) {
                return severity;
            }
        }
        return INFO; // 默认返回信息级别
    }
    
    /**
     * 根据级别获取枚举
     */
    public static AlertSeverityEnum getByLevel(int level) {
        for (AlertSeverityEnum severity : values()) {
            if (severity.getLevel() == level) {
                return severity;
            }
        }
        return INFO; // 默认返回信息级别
    }
    
    /**
     * 获取 ElementUI 的标签类型
     */
    public String getElementUIType() {
        switch (this) {
            case CRITICAL:
                return "danger";
            case HIGH:
                return "warning";
            case MEDIUM:
                return "";  // 默认蓝色
            case LOW:
                return "success";
            case INFO:
            default:
                return "info";
        }
    }
}
