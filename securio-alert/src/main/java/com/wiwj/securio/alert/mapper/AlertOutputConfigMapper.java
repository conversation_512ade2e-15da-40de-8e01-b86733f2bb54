package com.wiwj.securio.alert.mapper;

import java.util.List;
import com.wiwj.securio.alert.domain.AlertOutputConfig;

/**
 * 告警输出配置Mapper接口
 *
 * <AUTHOR>
 */
public interface AlertOutputConfigMapper {
    /**
     * 查询告警输出配置
     *
     * @param id 告警输出配置ID
     * @return 告警输出配置
     */
    public AlertOutputConfig selectOutputConfigById(Long id);

    /**
     * 查询告警输出配置列表
     *
     * @param outputConfig 告警输出配置
     * @return 告警输出配置集合
     */
    public List<AlertOutputConfig> selectOutputConfigList(AlertOutputConfig outputConfig);

    /**
     * 根据告警源ID查询告警输出配置
     *
     * @param sourceId 告警源ID
     * @return 告警输出配置集合
     */
    public List<AlertOutputConfig> selectOutputConfigsBySourceId(Long sourceId);

    /**
     * 查询匹配的告警输出配置
     *
     * @param outputConfig 查询条件
     * @return 匹配的告警输出配置列表
     */
    public List<AlertOutputConfig> selectMatchingOutputConfigs(AlertOutputConfig outputConfig);

    /**
     * 新增告警输出配置
     *
     * @param outputConfig 告警输出配置
     * @return 结果
     */
    public int insertOutputConfig(AlertOutputConfig outputConfig);

    /**
     * 修改告警输出配置
     *
     * @param outputConfig 告警输出配置
     * @return 结果
     */
    public int updateOutputConfig(AlertOutputConfig outputConfig);

    /**
     * 删除告警输出配置
     *
     * @param id 告警输出配置ID
     * @return 结果
     */
    public int deleteOutputConfigById(Long id);

    /**
     * 批量删除告警输出配置
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteOutputConfigByIds(Long[] ids);
}
