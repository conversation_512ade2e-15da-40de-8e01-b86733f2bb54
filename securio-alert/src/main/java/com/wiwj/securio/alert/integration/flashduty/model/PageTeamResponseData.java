package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;
import java.util.List;

/**
 * 团队分页响应数据
 */
@Data
public class PageTeamResponseData {

    /**
     * 团队列表
     */
    private List<TeamItem> items;

    /**
     * 页码
     */
    private Integer p;

    /**
     * 分页条数
     */
    private Integer limit;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 团队信息
     */
    @Data
    public static class TeamItem {
        /**
         * 团队ID
         */
        private Long team_id;

        /**
         * 团队名称
         */
        private String team_name;

        /**
         * 描述
         */
        private String description;

        /**
         * 创建时间戳
         */
        private Long created_at;

        /**
         * 更新时间戳
         */
        private Long updated_at;

        /**
         * 上次修改人
         */
        private Long updated_by;

        /**
         * 上次修改人名称
         */
        private String updated_by_name;

        /**
         * 创建人
         */
        private Long creator_id;

        /**
         * 关联ID
         */
        private String ref_id;

        /**
         * 人员列表
         */
        private List<Long> person_ids;
    }
} 