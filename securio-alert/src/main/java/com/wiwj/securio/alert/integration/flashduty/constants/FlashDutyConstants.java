package com.wiwj.securio.alert.integration.flashduty.constants;

/**
 * FlashDuty常量
 *
 * <AUTHOR>
 */
public class FlashDutyConstants {

    /** API路径：告警事件 */
    public static final String API_PATH_ALERT = "/event/push/alert/standard";

    /** API路径：变更事件 */
    public static final String API_PATH_CHANGE = "/event/push/change/standard";

    /** API路径：故障解决 */
    public static final String API_PATH_INCIDENT_RESOLVE = "/incident/resolve";

    /** API路径：故障认领 */
    public static final String API_PATH_INCIDENT_ACK = "/incident/ack";

    /** 参数：集成密钥 */
    public static final String PARAM_INTEGRATION_KEY = "integration_key";

    /** 参数：应用密钥 */
    public static final String PARAM_APP_KEY = "app_key";

    /** 告警状态：严重 */
    public static final String EVENT_STATUS_CRITICAL = "Critical";

    /** 告警状态：警告 */
    public static final String EVENT_STATUS_WARNING = "Warning";

    /** 告警状态：提醒 */
    public static final String EVENT_STATUS_INFO = "Info";

    /** 告警状态：恢复 */
    public static final String EVENT_STATUS_OK = "Ok";

    /** 优先级：严重 */
    public static final String PRIORITY_CRITICAL = "critical";

    /** 优先级：高 */
    public static final String PRIORITY_HIGH = "high";

    /** 优先级：中 */
    public static final String PRIORITY_MEDIUM = "medium";

    /** 优先级：低 */
    public static final String PRIORITY_LOW = "low";

    /** 优先级：信息 */
    public static final String PRIORITY_INFO = "info";

    /** 变更状态：已提单 */
    public static final String CHANGE_STATUS_PLANNED = "Planned";

    /** 变更状态：即将开始 */
    public static final String CHANGE_STATUS_READY = "Ready";

    /** 变更状态：进行中 */
    public static final String CHANGE_STATUS_PROCESSING = "Processing";

    /** 变更状态：已取消 */
    public static final String CHANGE_STATUS_CANCELED = "Canceled";

    /** 变更状态：已完成 */
    public static final String CHANGE_STATUS_DONE = "Done";

    /** 标签：来源 */
    public static final String TAG_SOURCE = "source";

    /** 标签：告警ID */
    public static final String TAG_ALERT_ID = "alert_id";

    /** 标签：变更ID */
    public static final String TAG_CHANGE_ID = "change_id";

    /** 标签：严重程度 */
    public static final String TAG_SEVERITY = "severity";

    /** 标签：状态 */
    public static final String TAG_STATUS = "status";

    /** 字段：告警ID */
    public static final String FIELD_ALERT_ID = "alert_id";

    /** 字段：变更ID */
    public static final String FIELD_CHANGE_ID = "change_id";

    /** 字段：告警源 */
    public static final String FIELD_SOURCE = "source";

    /** 字段：告警源实例 */
    public static final String FIELD_SOURCE_INSTANCE = "source_instance";

    /** 字段：告警时间 */
    public static final String FIELD_OCCURRED_AT = "occurred_at";

    /** 字段：解决时间 */
    public static final String FIELD_RESOLVED_AT = "resolved_at";

    /** 字段：解决人 */
    public static final String FIELD_RESOLVED_BY = "resolved_by";

    /** 字段：解决说明 */
    public static final String FIELD_RESOLUTION_NOTE = "resolution_note";

    /** 字段：变更类型 */
    public static final String FIELD_CHANGE_TYPE = "change_type";

    /** 字段：变更状态 */
    public static final String FIELD_CHANGE_STATUS = "change_status";

    /** 字段：变更开始时间 */
    public static final String FIELD_CHANGE_START_TIME = "change_start_time";

    /** 字段：变更结束时间 */
    public static final String FIELD_CHANGE_END_TIME = "change_end_time";
}
