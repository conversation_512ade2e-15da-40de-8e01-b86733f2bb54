package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;

/**
 * 团队列表查询请求参数
 */
@Data
public class TeamListRequest {
    /**
     * 页码，从1开始
     */
    private Integer p;
    
    /**
     * 分页条数
     */
    private Integer limit;
    
    /**
     * 是否升序
     */
    private Boolean asc;
    
    /**
     * 排序字段
     */
    private String orderby;
    
    /**
     * 查询语句，查询范围：name和description
     */
    private String query;
    
    /**
     * 人员id，查询包含此人员的团队，人员可以为account或member
     */
    private Integer person_id;
} 