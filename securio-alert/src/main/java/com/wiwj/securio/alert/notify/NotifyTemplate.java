package com.wiwj.securio.alert.notify;

import java.util.Map;

/**
 * 通知模板
 * 
 * <AUTHOR>
 */
public class NotifyTemplate {
    
    /** 模板ID */
    private String id;
    
    /** 模板名称 */
    private String name;
    
    /** 模板类型 */
    private String type;
    
    /** 标题模板 */
    private String titleTemplate;
    
    /** 内容模板 */
    private String contentTemplate;
    
    /** 模板参数 */
    private Map<String, Object> params;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitleTemplate() {
        return titleTemplate;
    }

    public void setTitleTemplate(String titleTemplate) {
        this.titleTemplate = titleTemplate;
    }

    public String getContentTemplate() {
        return contentTemplate;
    }

    public void setContentTemplate(String contentTemplate) {
        this.contentTemplate = contentTemplate;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
