package com.wiwj.securio.alert.integration.flashduty.model;

import java.util.Map;

/**
 * 故障操作记录
 */
public class IncidentAction {
    
    /**
     * 操作时间（毫秒时间戳）
     */
    private Long created_at;
    
    /**
     * 操作人ID（0代表系统）
     */
    private Long creator_id;
    
    /**
     * 操作类型
     */
    private String type;
    
    /**
     * 操作详情（根据type类型解析不同的内容）
     */
    private Map<String, Object> detail;

    public Long getCreated_at() {
        return created_at;
    }

    public void setCreated_at(Long created_at) {
        this.created_at = created_at;
    }

    public Long getCreator_id() {
        return creator_id;
    }

    public void setCreator_id(Long creator_id) {
        this.creator_id = creator_id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, Object> getDetail() {
        return detail;
    }

    public void setDetail(Map<String, Object> detail) {
        this.detail = detail;
    }
} 