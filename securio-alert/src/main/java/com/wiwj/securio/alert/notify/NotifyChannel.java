package com.wiwj.securio.alert.notify;

import com.wiwj.securio.alert.domain.AlertData;

/**
 * 通知渠道接口
 * 
 * <AUTHOR>
 */
public interface NotifyChannel {
    
    /**
     * 获取渠道类型
     * 
     * @return 渠道类型
     */
    String getType();
    
    /**
     * 初始化渠道
     * 
     * @param config 渠道配置
     * @throws Exception 初始化异常
     */
    void initialize(NotifyChannelConfig config) throws Exception;
    
    /**
     * 发送通知
     * 
     * @param message 通知消息
     * @return 发送结果
     */
    NotifyResult send(NotifyMessage message);
    
    /**
     * 测试渠道连接
     * 
     * @return 测试结果
     */
    NotifyResult testConnection();
    
    /**
     * 渲染通知内容
     * 
     * @param template 通知模板
     * @param alertData 告警数据
     * @return 渲染后的通知内容
     */
    NotifyMessage renderMessage(NotifyTemplate template, AlertData alertData);
}
