package com.wiwj.securio.alert.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.alert.service.IAlertTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 告警标签控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/alert")
public class AlertTagController extends BaseController {

    @Autowired
    private IAlertTagService alertTagService;

    /**
     * 获取标签统计信息
     * 返回各个标签的使用频率
     */
    @GetMapping("/tag-statistics")
    public AjaxResult getTagStatistics() {
        try {
            Map<String, Object> statistics = alertTagService.getTagStatistics();
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            logger.error("获取标签统计失败", e);
            return AjaxResult.error("获取标签统计失败");
        }
    }

    /**
     * 搜索标签建议
     * 根据输入的关键词返回匹配的标签
     */
    @GetMapping("/tag-suggestions")
    public AjaxResult getTagSuggestions(String keyword) {
        try {
            Map<String, Object> suggestions = alertTagService.getTagSuggestions(keyword);
            return AjaxResult.success(suggestions);
        } catch (Exception e) {
            logger.error("获取标签建议失败", e);
            return AjaxResult.error("获取标签建议失败");
        }
    }

    /**
     * 根据来源类型获取可用的子类型
     */
    @GetMapping("/source-sub-types")
    public AjaxResult getSourceSubTypes(String sourceType) {
        try {
            Map<String, Object> subTypes = alertTagService.getSourceSubTypes(sourceType);
            return AjaxResult.success(subTypes);
        } catch (Exception e) {
            logger.error("获取来源子类型失败", e);
            return AjaxResult.error("获取来源子类型失败");
        }
    }
}
