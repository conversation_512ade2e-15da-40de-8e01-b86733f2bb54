package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * FlashDuty告警列表响应数据
 *
 * <AUTHOR>
 */
@Data
public class AlertListResponseData {

    /**
     * 告警项列表
     */
    private List<AlertItem> items;

    /**
     * 游标分页上下文
     */
    private String search_after_ctx;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 是否有下一页
     */
    private Boolean has_next_page;

    /**
     * 告警项
     */
    @Data
    public static class AlertItem {
        /**
         * 告警ID
         */
        private String alert_id;
        
        /**
         * 协作空间ID
         */
        private Long channel_id;
        
        /**
         * 协作空间名称
         */
        private String channel_name;
        
        /**
         * 数据源ID
         */
        private Long data_source_id;
        
        /**
         * 数据源名称
         */
        private String data_source_name;
        
        /**
         * 数据源类型
         */
        private String data_source_type;
        
        /**
         * 数据源引用ID
         */
        private String data_source_ref_id;
        
        /**
         * 标题
         */
        private String title;
        
        /**
         * 标题规则
         */
        private String title_rule;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 告警键
         */
        private String alert_key;
        
        /**
         * 告警严重程度
         */
        private String alert_severity;
        
        /**
         * 告警状态
         */
        private String alert_status;
        
        /**
         * 处理进度
         */
        private String progress;
        
        /**
         * 开始时间（秒）
         */
        private Long start_time;
        
        /**
         * 最后时间（秒）
         */
        private Long last_time;
        
        /**
         * 结束时间（秒）
         */
        private Long end_time;
        
        /**
         * 关闭时间（秒）
         */
        private Long close_time;
        
        /**
         * 创建时间（秒）
         */
        private Long created_at;
        
        /**
         * 更新时间（秒）
         */
        private Long updated_at;
        
        /**
         * 标签
         */
        private Map<String, String> labels;
        
        /**
         * 事件列表
         */
        private List<EventItem> events;
        
        /**
         * 响应人ID
         */
        private Integer responder_id;
        
        /**
         * 故障信息
         */
        private IncidentInfo incident;
        
        /**
         * 事件数量
         */
        private Integer event_cnt;
        
        /**
         * 是否被静默过
         */
        private Boolean ever_muted;
    }
    
    /**
     * 事件项
     */
    @Data
    public static class EventItem {
        /**
         * 事件ID
         */
        private String event_id;
        
        /**
         * 数据源ID
         */
        private Long data_source_id;
        
        /**
         * 协作空间ID
         */
        private Long channel_id;
        
        /**
         * 标题
         */
        private String title;
        
        /**
         * 标题规则
         */
        private String title_rule;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 告警ID
         */
        private String alert_id;
        
        /**
         * 事件状态
         */
        private String event_status;
        
        /**
         * 事件严重程度
         */
        private String event_severity;
        
        /**
         * 事件时间（秒）
         */
        private Long event_time;
        
        /**
         * 标签
         */
        private Map<String, String> labels;
    }
    
    /**
     * 故障信息
     */
    @Data
    public static class IncidentInfo {
        /**
         * 故障ID
         */
        private String incident_id;
        
        /**
         * 故障标题
         */
        private String title;
    }
} 