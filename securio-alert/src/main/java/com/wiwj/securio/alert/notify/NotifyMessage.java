package com.wiwj.securio.alert.notify;

import java.util.List;
import java.util.Map;

/**
 * 通知消息
 * 
 * <AUTHOR>
 */
public class NotifyMessage {
    
    /** 消息标题 */
    private String title;
    
    /** 消息内容 */
    private String content;
    
    /** 消息类型 */
    private String type;
    
    /** 接收人列表 */
    private List<String> receivers;
    
    /** 附加参数 */
    private Map<String, Object> params;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getReceivers() {
        return receivers;
    }

    public void setReceivers(List<String> receivers) {
        this.receivers = receivers;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
