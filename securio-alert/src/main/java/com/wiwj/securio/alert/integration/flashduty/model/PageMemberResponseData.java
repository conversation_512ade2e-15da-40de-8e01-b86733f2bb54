package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;
import java.util.List;

/**
 * 成员分页响应数据
 */
@Data
public class PageMemberResponseData {

    /**
     * 页码
     */
    private Integer p;

    /**
     * 分页条数
     */
    private Integer limit;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 成员列表
     */
    private List<MemberItem> items;

    /**
     * 成员信息
     */
    @Data
    public static class MemberItem {
        /**
         * 成员ID
         */
        private Long member_id;

        /**
         * 成员名称
         */
        private String member_name;

        /**
         * 电话号码
         */
        private String phone;

        /**
         * 电话验证状态
         */
        private String phone_verified;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 邮箱验证状态
         */
        private String email_verified;

        /**
         * 账户角色ID列表
         */
        private List<Long> account_role_ids;

        /**
         * 时区
         */
        private String time_zone;

        /**
         * 语言环境
         */
        private String locale;

        /**
         * 状态
         */
        private String status;

        /**
         * 创建时间戳
         */
        private Long created_at;

        /**
         * 更新时间戳
         */
        private Long updated_at;

        /**
         * 关联ID
         */
        private String ref_id;
    }
} 