package com.wiwj.securio.alert.notify.impl;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.wiwj.securio.alert.domain.AlertData;
import com.wiwj.securio.alert.notify.NotifyChannel;
import com.wiwj.securio.alert.notify.NotifyChannelConfig;
import com.wiwj.securio.alert.notify.NotifyMessage;
import com.wiwj.securio.alert.notify.NotifyResult;
import com.wiwj.securio.alert.notify.NotifyTemplate;

/**
 * 邮件通知渠道实现
 * 
 * <AUTHOR>
 */
@Component
public class EmailNotifyChannel implements NotifyChannel {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailNotifyChannel.class);
    
    /** 渠道配置 */
    private NotifyChannelConfig config;
    
    @Override
    public String getType() {
        return "email";
    }
    
    @Override
    public void initialize(NotifyChannelConfig config) throws Exception {
        if (config == null) {
            throw new IllegalArgumentException("Email notify channel config cannot be null");
        }
        
        // 验证必要参数
        String smtpHost = config.getParam("smtp.host");
        String smtpPort = config.getParam("smtp.port");
        String username = config.getParam("username");
        String password = config.getParam("password");
        
        if (smtpHost == null || smtpPort == null || username == null || password == null) {
            throw new IllegalArgumentException("Missing required email configuration parameters");
        }
        
        this.config = config;
    }
    
    @Override
    public NotifyResult send(NotifyMessage message) {
        try {
            // 这里实现实际的邮件发送逻辑
            // 可以使用JavaMail API或Spring Mail等
            
            logger.info("Sending email notification: {}", message.getTitle());
            
            // 模拟发送成功
            return NotifyResult.success("email_" + System.currentTimeMillis());
            
        } catch (Exception e) {
            logger.error("Failed to send email notification", e);
            return NotifyResult.failure(e.getMessage());
        }
    }
    
    @Override
    public NotifyResult testConnection() {
        try {
            // 测试SMTP连接
            String smtpHost = config.getParam("smtp.host");
            String smtpPort = config.getParam("smtp.port");
            
            logger.info("Testing email connection to {}:{}", smtpHost, smtpPort);
            
            // 模拟测试成功
            return NotifyResult.success("Connection test successful");
            
        } catch (Exception e) {
            logger.error("Email connection test failed", e);
            return NotifyResult.failure(e.getMessage());
        }
    }
    
    @Override
    public NotifyMessage renderMessage(NotifyTemplate template, AlertData alertData) {
        NotifyMessage message = new NotifyMessage();
        
        // 设置消息类型
        message.setType("email");
        
        // 渲染标题
        String title = renderTemplate(template.getTitleTemplate(), alertData);
        message.setTitle(title);
        
        // 渲染内容
        String content = renderTemplate(template.getContentTemplate(), alertData);
        message.setContent(content);
        
        return message;
    }
    
    /**
     * 渲染模板
     * 
     * @param template 模板内容
     * @param alertData 告警数据
     * @return 渲染后的内容
     */
    private String renderTemplate(String template, AlertData alertData) {
        if (template == null || template.isEmpty()) {
            return "";
        }
        
        // 构建模板变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("alert", alertData);
        
        // 简单的模板替换实现
        // 实际项目中可以使用Thymeleaf、Freemarker等模板引擎
        String result = template;
        
        // 替换告警ID
        result = result.replace("${alert.id}", alertData.getId());
        
        // 替换告警标题
        result = result.replace("${alert.title}", alertData.getTitle());
        
        // 替换告警描述
        if (alertData.getDescription() != null) {
            result = result.replace("${alert.description}", alertData.getDescription());
        }
        
        // 替换告警级别
        if (alertData.getSeverity() != null) {
            result = result.replace("${alert.severity}", alertData.getSeverity());
        }
        
        // 替换告警时间
        if (alertData.getTimestamp() != null) {
            result = result.replace("${alert.timestamp}", alertData.getTimestamp().toString());
        }
        
        return result;
    }
}
