package com.wiwj.securio.alert.integration.flashduty.model;

import java.util.List;

/**
 * 故障时间线查询请求
 */
public class IncidentFeedRequest {
    
    /**
     * 告警ID（必需）
     */
    private String incident_id;
    
    /**
     * 类型列表（可选）
     */
    private List<String> types;
    
    /**
     * 页码，从1开始（可选）
     */
    private Integer p;
    
    /**
     * 分页条数（可选）
     */
    private Integer limit;
    
    /**
     * 是否升序（可选）
     */
    private Boolean asc;

    public String getIncident_id() {
        return incident_id;
    }

    public void setIncident_id(String incident_id) {
        this.incident_id = incident_id;
    }

    public List<String> getTypes() {
        return types;
    }

    public void setTypes(List<String> types) {
        this.types = types;
    }

    public Integer getP() {
        return p;
    }

    public void setP(Integer p) {
        this.p = p;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Boolean getAsc() {
        return asc;
    }

    public void setAsc(Boolean asc) {
        this.asc = asc;
    }
} 