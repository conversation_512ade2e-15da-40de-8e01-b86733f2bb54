package com.wiwj.securio.alert.integration.flashduty.model;

import java.util.Map;

import lombok.Data;

/**
 * FlashDuty API响应
 * 
 * <AUTHOR>
 */
@Data
public class FlashDutyResponse<T> {
    
    /** 请求ID */
    private String request_id;
    
    /** 数据 */
    private T data;
    
    /** 错误 */
    private FlashDutyError error;
    

    /**
     * 获取错误信息
     * 
     * @return 错误信息
     */
    public String getError() {
        if (error != null) {
            return error.getMessage();
        }
        return null;
    }
    
    /**
     * FlashDuty错误
     */
    @Data
    public static class FlashDutyError {
        /** 错误码 */
        private String code;
        
        /** 错误信息 */
        private String message;
    }
}
