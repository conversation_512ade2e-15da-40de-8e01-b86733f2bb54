package com.wiwj.securio.alert.enums;

import lombok.Getter;

/**
 * 数据获取方式枚举
 *
 * <AUTHOR>
 */
@Getter
public enum DataCollectionTypeEnum {

    PUSH("push", "推送"),
    PULL("pull", "拉取");

    /** 类型编码 */
    private final String code;

    /** 类型名称 */
    private final String name;

    DataCollectionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     */
    public static DataCollectionTypeEnum getByCode(String code) {
        for (DataCollectionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
