package com.wiwj.securio.alert.service;

import java.util.List;
import com.wiwj.securio.alert.domain.AlertOutputConfig;

/**
 * 告警输出配置服务接口
 *
 * <AUTHOR>
 */
public interface IAlertOutputConfigService {
    /**
     * 查询告警输出配置
     *
     * @param id 告警输出配置ID
     * @return 告警输出配置
     */
    public AlertOutputConfig selectOutputConfigById(Long id);

    /**
     * 查询告警输出配置列表
     *
     * @param outputConfig 告警输出配置
     * @return 告警输出配置集合
     */
    public List<AlertOutputConfig> selectOutputConfigList(AlertOutputConfig outputConfig);

    /**
     * 根据告警源ID查询告警输出配置
     * 注意：此方法已废弃，保留是为了兼容旧的代码
     *
     * @param sourceId 告警源ID
     * @return 告警输出配置集合
     */
    @Deprecated
    public List<AlertOutputConfig> selectOutputConfigsBySourceId(Long sourceId);

    /**
     * 查询匹配的告警输出配置
     *
     * @param queryConfig 查询条件
     * @return 匹配的告警输出配置列表
     */
    public List<AlertOutputConfig> selectMatchingOutputConfigs(AlertOutputConfig queryConfig);

    /**
     * 新增告警输出配置
     *
     * @param outputConfig 告警输出配置
     * @return 结果
     */
    public int insertOutputConfig(AlertOutputConfig outputConfig);

    /**
     * 修改告警输出配置
     *
     * @param outputConfig 告警输出配置
     * @return 结果
     */
    public int updateOutputConfig(AlertOutputConfig outputConfig);

    /**
     * 批量删除告警输出配置
     *
     * @param ids 需要删除的告警输出配置ID
     * @return 结果
     */
    public int deleteOutputConfigByIds(Long[] ids);

    /**
     * 删除告警输出配置信息
     *
     * @param id 告警输出配置ID
     * @return 结果
     */
    public int deleteOutputConfigById(Long id);

    /**
     * 测试告警输出配置
     *
     * @param id 告警输出配置ID
     * @param testData 测试数据
     * @return 结果
     */
    public boolean testOutputConfig(Long id, Object testData);
}
