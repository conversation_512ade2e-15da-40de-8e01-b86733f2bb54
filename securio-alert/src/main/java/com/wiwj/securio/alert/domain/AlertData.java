package com.wiwj.securio.alert.domain;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 告警数据实体，用于适配器处理过程中的中间数据模型
 *
 * <AUTHOR>
 */
@Data
public class AlertData {
    /** 告警ID */
    private String id;

    /** 告警标题 */
    private String title;

    /** 告警描述 */
    private String description;

    /** 告警来源信息 */
    private AlertSource source;

    /** 严重程度 */
    private String severity;

    /** 告警状态 */
    private String status;

    /** 告警类型 */
    private String alertType;

    /** 攻击类型 */
    private String attackType;

    /** 源IP地址 */
    private String sourceIp;

    /** 目标IP地址 */
    private String targetIp;

    /** URL路径 */
    private String url;

    /** 事件ID，源系统中的唯一标识 */
    private String eventId;

    /** 告警时间（时间戳，毫秒） */
    private Long timestamp;

    /** 告警标签 */
    private List<String> tags;

    /** 告警属性 */
    private Map<String, Object> attributes;

    /** 原始数据 */
    private Map<String, Object> rawData;

    /**
     * 告警来源信息
     */
    @Data
    public static class AlertSource {
        /** 来源类型 */
        private String type;

        /** 来源名称 */
        private String name;

        /** 来源实例 */
        private String instance;
    }
}
