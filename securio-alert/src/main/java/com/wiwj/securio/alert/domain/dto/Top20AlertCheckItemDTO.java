package com.wiwj.securio.alert.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * Top20告警检查项导出DTO
 * 
 * <AUTHOR>
 */
@Data
public class Top20AlertCheckItemDTO {

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", index = 0)
    @ColumnWidth(20)
    private String type;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容", index = 1)
    @ColumnWidth(50)
    private String content;

    /**
     * 告警数量
     */
    @ExcelProperty(value = "告警数量", index = 2)
    @ColumnWidth(12)
    private Integer count;

    /**
     * 告警原因
     */
    @ExcelProperty(value = "告警原因", index = 3)
    @ColumnWidth(40)
    private String alertReason;

    /**
     * 告警是否处理
     */
    @ExcelProperty(value = "告警是否处理", index = 4)
    @ColumnWidth(15)
    private String processStatus;

    /**
     * 告警处理方法
     */
    @ExcelProperty(value = "告警处理方法", index = 5)
    @ColumnWidth(40)
    private String processMethod;
} 