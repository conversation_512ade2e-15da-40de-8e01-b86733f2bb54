package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 故障时间线响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IncidentFeedResponse extends FlashDutyResponse<IncidentFeedResponse.IncidentFeedData>{
    


    /**
     * 故障时间线数据
     */
    public static class IncidentFeedData {

        /**
         * 故障操作记录列表
         */
        private List<IncidentAction> items;

        public List<IncidentAction> getItems() {
            return items;
        }

        public void setItems(List<IncidentAction> items) {
            this.items = items;
        }
    }


} 