package com.wiwj.securio.alert.domain;

import lombok.Data;
import java.util.List;

/**
 * 部门统计实体（运维部）
 *
 * <AUTHOR>
 */
@Data
public class DepartmentStatistics {

    /**
     * 部门名称（固定为运维部）
     */
    private String deptName = "运维部";

    /**
     * 故障总量
     */
    private Integer totalAlerts;

    /**
     * 严重告警数量（critical）
     */
    private Integer criticalAlerts;

    /**
     * 待处理数量
     */
    private Integer pendingAlerts;

    /**
     * 已解决数量
     */
    private Integer resolvedAlerts;

    /**
     * 安全类告警数量
     */
    private Integer securityAlerts;

    /**
     * 运维类告警数量
     */
    private Integer opsAlerts;

    /**
     * 平均认领时间（小时）
     */
    private Double mtta;

    /**
     * 平均恢复时间（小时）
     */
    private Double mttr;

    /**
     * 团队列表
     */
    private List<TeamStatistics> teams;
} 