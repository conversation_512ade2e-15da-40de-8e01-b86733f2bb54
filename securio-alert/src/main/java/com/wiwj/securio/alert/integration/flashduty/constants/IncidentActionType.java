package com.wiwj.securio.alert.integration.flashduty.constants;

/**
 * 故障操作类型常量
 */
public class IncidentActionType {
    
    /** 故障.评论 */
    public static final String I_COMM = "i_comm";
    
    /** 故障.通知 */
    public static final String I_NOTIFY = "i_notify";
    
    /** 故障.触发 */
    public static final String I_NEW = "i_new";
    
    /** 故障.分派 */
    public static final String I_ASSIGN = "i_assign";
    
    /** 故障.添加处理人 */
    public static final String I_A_RSPD = "i_a_rspd";
    
    /** 故障.开始处理 */
    public static final String I_ACK = "i_ack";
    
    /** 故障.取消处理 */
    public static final String I_UNACK = "i_unack";
    
    /** 故障.暂缓处理 */
    public static final String I_SNOOZE = "i_snooze";
    
    /** 故障.取消暂缓 */
    public static final String I_WAKE = "i_wake";
    
    /** 故障.解决 */
    public static final String I_RSLV = "i_rslv";
    
    /** 故障.重开 */
    public static final String I_REOPEN = "i_reopen";
    
    /** 故障.合并 */
    public static final String I_MERGE = "i_merge";
    
    /** 故障.触发静默 */
    public static final String I_M_SILENCE = "i_m_silence";
    
    /** 故障.触发抑制 */
    public static final String I_M_INHIBAT = "i_m_inhibat";
    
    /** 故障.触发收敛 */
    public static final String I_M_FLAPPING = "i_m_flapping";
    
    /** 故障.触发风暴 */
    public static final String I_STORM = "i_storm";
    
    /** 故障.更新根本原因 */
    public static final String I_R_RC = "i_r_rc";
    
    /** 故障.更新描述 */
    public static final String I_R_DESC = "i_r_desc";
    
    /** 故障.更新解决方案 */
    public static final String I_R_RSLTN = "i_r_rsltn";
    
    /** 故障.更新处理人 */
    public static final String I_R_RESP = "i_r_resp";
    
    /** 故障.更新影响 */
    public static final String I_R_IMPACT = "i_r_impact";
    
    /** 故障.更新标题 */
    public static final String I_R_TITLE = "i_r_title";
    
    /** 故障.更新故障严重程度 */
    public static final String I_R_SEVERITY = "i_r_severity";
    
    /** 故障.更新自定义字段 */
    public static final String I_R_FIELD = "i_r_field";
    
    /** 故障.触发自定义操作 */
    public static final String I_CUSTOM = "i_custom";

    /**
     * 获取操作类型的中文描述
     */
    public static String getTypeDescription(String type) {
        if (type == null) {
            return "未知操作";
        }
        
        switch (type) {
            case I_COMM: return "故障.评论";
            case I_NOTIFY: return "故障.通知";
            case I_NEW: return "故障.触发";
            case I_ASSIGN: return "故障.分派";
            case I_A_RSPD: return "故障.添加处理人";
            case I_ACK: return "故障.开始处理";
            case I_UNACK: return "故障.取消处理";
            case I_SNOOZE: return "故障.暂缓处理";
            case I_WAKE: return "故障.取消暂缓";
            case I_RSLV: return "故障.解决";
            case I_REOPEN: return "故障.重开";
            case I_MERGE: return "故障.合并";
            case I_M_SILENCE: return "故障.触发静默";
            case I_M_INHIBAT: return "故障.触发抑制";
            case I_M_FLAPPING: return "故障.触发收敛";
            case I_STORM: return "故障.触发风暴";
            case I_R_RC: return "故障.更新根本原因";
            case I_R_DESC: return "故障.更新描述";
            case I_R_RSLTN: return "故障.更新解决方案";
            case I_R_RESP: return "故障.更新处理人";
            case I_R_IMPACT: return "故障.更新影响";
            case I_R_TITLE: return "故障.更新标题";
            case I_R_SEVERITY: return "故障.更新故障严重程度";
            case I_R_FIELD: return "故障.更新自定义字段";
            case I_CUSTOM: return "故障.触发自定义操作";
            default: return "未知操作: " + type;
        }
    }
} 