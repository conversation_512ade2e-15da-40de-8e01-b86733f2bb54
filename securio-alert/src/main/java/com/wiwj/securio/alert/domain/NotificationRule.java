package com.wiwj.securio.alert.domain;

import com.wiwj.common.annotation.Excel;

import lombok.Data;

/**
 * 通知规则对象 alert_notification_rule
 * 
 * <AUTHOR>
 */
@Data
public class NotificationRule {
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long id;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String name;

    /** 规则描述 */
    @Excel(name = "规则描述")
    private String description;

    /** 告警源ID */
    @Excel(name = "告警源ID")
    private Long sourceId;

    /** 告警级别(critical/high/medium/low/info) */
    @Excel(name = "告警级别", readConverterExp = "critical=严重,high=高危,medium=中危,low=低危,info=信息")
    private String severity;

    /** 通知渠道ID */
    @Excel(name = "通知渠道ID")
    private Long channelId;

    /** 通知模板ID */
    @Excel(name = "通知模板ID")
    private Long templateId;

    /** 接收人JSON */
    private String receiversJson;

    /** 时间范围JSON */
    private String timeRangeJson;

    /** 规则条件JSON */
    private String conditionJson;

    /** 状态(enabled/disabled) */
    @Excel(name = "状态", readConverterExp = "enabled=启用,disabled=禁用")
    private String status;

    /** 创建时间 */
    private Long createAt;

    /** 创建人ID */
    private String createBy;

    /** 创建人名称 */
    private String createName;

    /** 更新时间 */
    private Long updateAt;

    /** 更新人ID */
    private String updateBy;

    /** 更新人名称 */
    private String updateName;

    /** 是否删除(0-未删除 1-已删除) */
    private Integer isDel;
}
