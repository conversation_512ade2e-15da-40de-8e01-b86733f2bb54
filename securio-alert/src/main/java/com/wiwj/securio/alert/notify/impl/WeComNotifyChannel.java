package com.wiwj.securio.alert.notify.impl;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.wiwj.securio.alert.domain.AlertData;
import com.wiwj.securio.alert.notify.NotifyChannel;
import com.wiwj.securio.alert.notify.NotifyChannelConfig;
import com.wiwj.securio.alert.notify.NotifyMessage;
import com.wiwj.securio.alert.notify.NotifyResult;
import com.wiwj.securio.alert.notify.NotifyTemplate;

/**
 * 企业微信通知渠道实现
 * 
 * <AUTHOR>
 */
@Component
public class WeComNotifyChannel implements NotifyChannel {
    
    private static final Logger logger = LoggerFactory.getLogger(WeComNotifyChannel.class);
    
    /** 渠道配置 */
    private NotifyChannelConfig config;
    
    @Override
    public String getType() {
        return "wecom";
    }
    
    @Override
    public void initialize(NotifyChannelConfig config) throws Exception {
        if (config == null) {
            throw new IllegalArgumentException("WeCom notify channel config cannot be null");
        }
        
        // 验证必要参数
        String corpId = config.getParam("corpId");
        String corpSecret = config.getParam("corpSecret");
        String agentId = config.getParam("agentId");
        
        if (corpId == null || corpSecret == null || agentId == null) {
            throw new IllegalArgumentException("Missing required WeCom configuration parameters");
        }
        
        this.config = config;
    }
    
    @Override
    public NotifyResult send(NotifyMessage message) {
        try {
            // 这里实现实际的企业微信发送逻辑
            // 可以使用HTTP客户端调用企业微信API
            
            logger.info("Sending WeCom notification: {}", message.getTitle());
            
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("msgtype", "markdown");
            
            Map<String, String> markdown = new HashMap<>();
            markdown.put("content", "### " + message.getTitle() + "\n" + message.getContent());
            params.put("markdown", markdown);
            
            // 添加接收人
            if (message.getReceivers() != null && !message.getReceivers().isEmpty()) {
                params.put("touser", String.join("|", message.getReceivers()));
            }
            
            // 添加应用ID
            params.put("agentid", config.getParam("agentId"));
            
            // 转换为JSON
            String requestBody = JSON.toJSONString(params);
            
            logger.debug("WeCom request body: {}", requestBody);
            
            // 模拟发送成功
            return NotifyResult.success("wecom_" + System.currentTimeMillis());
            
        } catch (Exception e) {
            logger.error("Failed to send WeCom notification", e);
            return NotifyResult.failure(e.getMessage());
        }
    }
    
    @Override
    public NotifyResult testConnection() {
        try {
            // 测试企业微信API连接
            String corpId = config.getParam("corpId");
            String corpSecret = config.getParam("corpSecret");
            
            logger.info("Testing WeCom connection with corpId: {}", corpId);
            
            // 模拟测试成功
            return NotifyResult.success("Connection test successful");
            
        } catch (Exception e) {
            logger.error("WeCom connection test failed", e);
            return NotifyResult.failure(e.getMessage());
        }
    }
    
    @Override
    public NotifyMessage renderMessage(NotifyTemplate template, AlertData alertData) {
        NotifyMessage message = new NotifyMessage();
        
        // 设置消息类型
        message.setType("wecom");
        
        // 渲染标题
        String title = renderTemplate(template.getTitleTemplate(), alertData);
        message.setTitle(title);
        
        // 渲染内容
        String content = renderTemplate(template.getContentTemplate(), alertData);
        message.setContent(content);
        
        return message;
    }
    
    /**
     * 渲染模板
     * 
     * @param template 模板内容
     * @param alertData 告警数据
     * @return 渲染后的内容
     */
    private String renderTemplate(String template, AlertData alertData) {
        if (template == null || template.isEmpty()) {
            return "";
        }
        
        // 构建模板变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("alert", alertData);
        
        // 简单的模板替换实现
        String result = template;
        
        // 替换告警ID
        result = result.replace("${alert.id}", alertData.getId());
        
        // 替换告警标题
        result = result.replace("${alert.title}", alertData.getTitle());
        
        // 替换告警描述
        if (alertData.getDescription() != null) {
            result = result.replace("${alert.description}", alertData.getDescription());
        }
        
        // 替换告警级别
        if (alertData.getSeverity() != null) {
            result = result.replace("${alert.severity}", alertData.getSeverity());
        }
        
        // 替换告警时间
        if (alertData.getTimestamp() != null) {
            result = result.replace("${alert.timestamp}", alertData.getTimestamp().toString());
        }
        
        // 企业微信支持Markdown格式，可以添加一些格式化
        result = result.replace("\n", "\n\n");
        
        return result;
    }
}
