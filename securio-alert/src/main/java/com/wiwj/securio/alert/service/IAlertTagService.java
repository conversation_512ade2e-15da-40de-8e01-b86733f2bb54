package com.wiwj.securio.alert.service;

import java.util.Map;

/**
 * 告警标签服务接口
 *
 * <AUTHOR>
 */
public interface IAlertTagService {

    /**
     * 获取可用的标签列表
     * 从现有告警数据中提取所有可用的标签
     *
     * @return 标签分类数据
     */
    Map<String, Object> getAvailableTags();

    /**
     * 获取标签统计信息
     * 返回各个标签的使用频率
     *
     * @return 标签统计数据
     */
    Map<String, Object> getTagStatistics();

    /**
     * 搜索标签建议
     * 根据输入的关键词返回匹配的标签
     *
     * @param keyword 搜索关键词
     * @return 标签建议数据
     */
    Map<String, Object> getTagSuggestions(String keyword);

    /**
     * 根据来源类型获取可用的子类型
     *
     * @param sourceType 来源类型
     * @return 子类型数据
     */
    Map<String, Object> getSourceSubTypes(String sourceType);
}
