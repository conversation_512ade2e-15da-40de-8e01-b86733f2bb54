package com.wiwj.securio.alert.service;

import com.wiwj.securio.alert.domain.SysUserAccount;

import java.util.List;

/**
 * 系统用户账号Service接口
 *
 * <AUTHOR>
 */
public interface ISysUserAccountService {
    /**
     * 查询系统用户账号
     *
     * @param id 系统用户账号主键
     * @return 系统用户账号
     */
    SysUserAccount selectSysUserAccountById(Long id);

    /**
     * 查询系统用户账号列表
     *
     * @param sysUserAccount 系统用户账号
     * @return 系统用户账号集合
     */
    List<SysUserAccount> selectSysUserAccountList(SysUserAccount sysUserAccount);

    /**
     * 新增系统用户账号
     *
     * @param sysUserAccount 系统用户账号
     * @return 结果
     */
    int insertSysUserAccount(SysUserAccount sysUserAccount);

    /**
     * 修改系统用户账号
     *
     * @param sysUserAccount 系统用户账号
     * @return 结果
     */
    int updateSysUserAccount(SysUserAccount sysUserAccount);

    /**
     * 批量删除系统用户账号
     *
     * @param ids 需要删除的系统用户账号主键集合
     * @return 结果
     */
    int deleteSysUserAccountByIds(Long[] ids);

    /**
     * 删除系统用户账号信息
     *
     * @param id 系统用户账号主键
     * @return 结果
     */
    int deleteSysUserAccountById(Long id);

    /**
     * 根据账号ID查询系统用户账号
     *
     * @param accountId 账号ID
     * @return 系统用户账号
     */
    SysUserAccount selectSysUserAccountByAccountId(String accountId);

    /**
     * 根据渠道名称查询系统用户账号列表
     *
     * @param chanel 渠道名称
     * @return 系统用户账号集合
     */
    List<SysUserAccount> selectSysUserAccountByChanel(String chanel);

    /**
     * 根据账号ID列表批量查询系统用户账号
     *
     * @param accountIds 账号ID列表
     * @return 系统用户账号集合
     */
    List<SysUserAccount> selectSysUserAccountByAccountIds(List<String> accountIds);

    /**
     * 根据用户ID查询appKey
     *
     * @param userId 用户ID
     * @return appKey
     */
    String getAppKeyByUserId(Long userId);
} 