package com.wiwj.securio.alert.enums;

/**
 * 性能等级枚举
 *
 * <AUTHOR>
 */
public enum PerformanceLevel {
    GOLD("🥇", "金牌处理员", "MTTR < 4小时 且 MTTA < 1小时"),
    SILVER("🥈", "银牌处理员", "MTTR < 6小时 且 MTTA < 2小时"),
    BRONZE("🥉", "铜牌处理员", "MTTR < 8小时 且 MTTA < 3小时"),
    NORMAL("", "正常处理员", "其他情况"),
    WARNING("⚠️", "需要关注", "MTTR > 12小时 或 MTTA > 6小时"),
    CRITICAL("🚨", "紧急处理", "MTTR > 24小时 或 MTTA > 12小时");

    private final String icon;
    private final String title;
    private final String description;

    PerformanceLevel(String icon, String title, String description) {
        this.icon = icon;
        this.title = title;
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public String getTitle() {
        return title;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断性能等级
     */
    public static PerformanceLevel getLevel(Double mtta, Double mttr) {
        if (mtta == null || mttr == null) {
            return NORMAL;
        }
        
        // 没有告警数据的情况
        if (mtta == 0 && mttr == 0) {
            return NORMAL;  // 没有数据时显示为正常等级，不显示特殊图标
        }
        
        // 紧急处理
        if (mttr > 24 || mtta > 12) {
            return CRITICAL;
        }
        
        // 需要关注
        if (mttr > 12 || mtta > 6) {
            return WARNING;
        }
        
        // 金牌处理员
        if (mttr < 4 && mtta < 1) {
            return GOLD;
        }
        
        // 银牌处理员
        if (mttr < 6 && mtta < 2) {
            return SILVER;
        }
        
        // 铜牌处理员
        if (mttr < 8 && mtta < 3) {
            return BRONZE;
        }
        
        return NORMAL;
    }
} 