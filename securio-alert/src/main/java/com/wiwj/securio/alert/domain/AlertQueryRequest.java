package com.wiwj.securio.alert.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 告警查询请求参数
 * 继承Alert类，添加查询相关的参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlertQueryRequest extends Alert {

    /**
     * 查询开始时间（毫秒时间戳）
     */
    private Long startTime;

    /**
     * 查询结束时间（毫秒时间戳）
     */
    private Long endTime;

    /**
     * 标签搜索内容
     */
    private String tagSearch;

    /**
     * 标签搜索类型 (contains/exact)
     */
    private String tagSearchType;

    /**
     * 服务名称搜索（通过tags.service搜索）
     */
    private String serviceName;

    /**
     * 时间范围数组（前端传递的时间选择器值）
     */
    private Long[] timeRange;

    /**
     * 认领时间范围数组（前端传递的时间选择器值）
     */
    private Long[] ackedTimeRange;

    /**
     * 解决时间范围数组（前端传递的时间选择器值）
     */
    private Long[] resolvedTimeRange;

    /**
     * 认领开始时间（毫秒时间戳）
     */
    private Long ackedStartTime;

    /**
     * 认领结束时间（毫秒时间戳）
     */
    private Long ackedEndTime;

    /**
     * 解决开始时间（毫秒时间戳）
     */
    private Long resolvedStartTime;

    /**
     * 解决结束时间（毫秒时间戳）
     */
    private Long resolvedEndTime;

    /**
     * 告警源类型列表（支持多选）
     */
    private String[] sourceTypes;

    /**
     * 严重程度列表（支持多选）
     */
    private String[] severities;

    /**
     * 告警状态列表（支持多选）
     */
    private String[] statuses;

    /**
     * 处理时间范围参数
     * 将timeRange转换为startTime和endTime
     */
    public void processTimeRange() {
        if (timeRange != null && timeRange.length == 2) {
            this.startTime = timeRange[0];
            this.endTime = timeRange[1];
        }
    }

    /**
     * 处理认领时间范围参数
     * 将ackedTimeRange转换为ackedStartTime和ackedEndTime
     */
    public void processAckedTimeRange() {
        if (ackedTimeRange != null && ackedTimeRange.length == 2) {
            this.ackedStartTime = ackedTimeRange[0];
            this.ackedEndTime = ackedTimeRange[1];
        }
    }

    /**
     * 处理解决时间范围参数
     * 将resolvedTimeRange转换为resolvedStartTime和resolvedEndTime
     */
    public void processResolvedTimeRange() {
        if (resolvedTimeRange != null && resolvedTimeRange.length == 2) {
            this.resolvedStartTime = resolvedTimeRange[0];
            this.resolvedEndTime = resolvedTimeRange[1];
        }
    }

    /**
     * 处理标签搜索参数
     */
    public void processTagSearch() {
        if (tagSearch != null && !tagSearch.trim().isEmpty()) {
            this.tagSearch = tagSearch.trim();
            if (this.tagSearchType == null || this.tagSearchType.trim().isEmpty()) {
                this.tagSearchType = "contains";
            }
            
            // 将标签搜索参数设置到父类Alert中，确保AlertServiceImpl能够访问
            super.setTagSearch(this.tagSearch);
            super.setTagSearchType(this.tagSearchType);
        }
    }

    /**
     * 处理服务名称搜索参数
     */
    public void processServiceName() {
        if (serviceName != null && !serviceName.trim().isEmpty()) {
            this.serviceName = serviceName.trim();
            // 将服务名称搜索参数设置到父类Alert中
            super.setServiceName(this.serviceName);
        }
    }

    /**
     * 处理多选参数
     * 如果多选数组只有一个值，则设置到父类的对应字段中
     */
    public void processMultiSelectParams() {
        // 处理告警源类型多选
        if (sourceTypes != null && sourceTypes.length == 1) {
            super.setSourceType(sourceTypes[0]);
        }
        
        // 处理严重程度多选
        if (severities != null && severities.length == 1) {
            super.setSeverity(severities[0]);
        }
        
        // 处理告警状态多选
        if (statuses != null && statuses.length == 1) {
            super.setStatus(statuses[0]);
        }
    }

    /**
     * 处理所有查询参数
     */
    public void processAllParams() {
        processTimeRange();
        processAckedTimeRange();
        processResolvedTimeRange();
        processTagSearch();
        processServiceName();
        processMultiSelectParams();
        
        // 同步时间范围参数到父类
        if (this.startTime != null) {
            super.setStartTime(this.startTime);
        }
        if (this.endTime != null) {
            super.setEndTime(this.endTime);
        }
    }
} 