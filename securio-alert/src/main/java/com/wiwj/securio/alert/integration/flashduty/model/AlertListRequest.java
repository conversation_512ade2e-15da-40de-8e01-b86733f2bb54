package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * FlashDuty告警列表查询请求参数
 * 对应接口：https://api.flashcat.cloud/alert/list
 *
 * <AUTHOR>
 */
@Data
public class AlertListRequest {
    /**
     * 游标分页，不设置为第一页。与p参数二选一
     */
    private String search_after_ctx;
    
    /**
     * 传统分页，不设置从第一页开始，默认值为1，与search_after_ctx参数二选一
     */
    private Integer p = 1;
    
    /**
     * 分页条数，默认20
     * 取值范围：0-100
     */
    private Integer limit = 20;
    
    /**
     * 是否升序，默认按照created_at倒序
     */
    private Boolean asc;
    
    /**
     * 排序依据，支持created_at updated_at，默认为created_at
     */
    private String orderby;
    
    /**
     * 标题筛选
     * 支持精确匹配、正则匹配和通配匹配
     */
    private String title;
    
    /**
     * 处理状态：Triggered,Closed
     * 全部（progress不传）
     * 未关闭（progress传Triggered）
     * 已关闭（progress传Closed）
     */
    private String progress;
    
    /**
     * 等级：Critical，Info， Warning
     * 支持传多个，用逗号分割
     */
    private String alert_severity;
    
    /**
     * 标签筛选
     * 支持精确匹配、正则匹配和通配匹配
     */
    private Map<String, String> labels;
    
    /**
     * 协作空间列表
     */
    private List<Integer> channel_ids;
    
    /**
     * 集成来源列表
     */
    private List<Integer> data_source_ids;
    
    /**
     * alertKey列表
     */
    private List<String> alert_keys;
    
    /**
     * 是否被静默或抑制过
     * 不传查全部，传true仅返回被静默或抑制的告警，传false仅返回未被抑制过的告警
     */
    private Boolean ever_muted;
    
    /**
     * 开始时间戳（秒）
     * 该时间为query的start_time，与告警的start_time无关
     * 实际检索条件为：alert.start_time<=query.end_time and alert.last_time>=query.start_time
     */
    private Long start_time;
    
    /**
     * 结束时间戳（秒）
     * 该时间为query的end_time，与告警的end_time无关
     * 实际检索条件为：alert.start_time<=query.end_time and alert.last_time>=query.start_time
     */
    private Long end_time;
} 