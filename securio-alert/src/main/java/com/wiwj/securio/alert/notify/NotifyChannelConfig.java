package com.wiwj.securio.alert.notify;

import java.util.Map;

/**
 * 通知渠道配置
 * 
 * <AUTHOR>
 */
public class NotifyChannelConfig {
    
    /** 渠道名称 */
    private String name;
    
    /** 渠道类型 */
    private String type;
    
    /** 渠道参数 */
    private Map<String, String> params;
    
    /** 是否启用 */
    private boolean enabled;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    /**
     * 获取参数值
     * 
     * @param key 参数键
     * @return 参数值
     */
    public String getParam(String key) {
        return params != null ? params.get(key) : null;
    }
    
    /**
     * 获取参数值，如果不存在则返回默认值
     * 
     * @param key 参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    public String getParam(String key, String defaultValue) {
        String value = getParam(key);
        return value != null ? value : defaultValue;
    }
}
