package com.wiwj.securio.alert.domain;

import com.wiwj.common.annotation.Excel;

import lombok.Data;

/**
 * 通知模板对象 alert_notification_template
 * 
 * <AUTHOR>
 */
@Data
public class NotificationTemplate {
    private static final long serialVersionUID = 1L;

    /** 模板ID */
    private Long id;

    /** 模板名称 */
    @Excel(name = "模板名称")
    private String name;

    /** 模板类型 */
    @Excel(name = "模板类型")
    private String type;

    /** 标题模板 */
    private String titleTemplate;

    /** 内容模板 */
    private String contentTemplate;

    /** 模板参数JSON */
    private String paramsJson;

    /** 状态(enabled/disabled) */
    @Excel(name = "状态", readConverterExp = "enabled=启用,disabled=禁用")
    private String status;

    /** 创建时间 */
    private Long createAt;

    /** 创建人ID */
    private String createBy;

    /** 创建人名称 */
    private String createName;

    /** 更新时间 */
    private Long updateAt;

    /** 更新人ID */
    private String updateBy;

    /** 更新人名称 */
    private String updateName;

    /** 是否删除(0-未删除 1-已删除) */
    private Integer isDel;
}
