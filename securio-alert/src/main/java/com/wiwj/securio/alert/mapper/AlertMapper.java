package com.wiwj.securio.alert.mapper;

import java.util.List;
import java.util.Map;
import com.wiwj.securio.alert.domain.Alert;
import org.apache.ibatis.annotations.Param;

/**
 * 告警Mapper接口
 *
 * <AUTHOR>
 */
public interface AlertMapper
{
    /**
     * 查询告警
     *
     * @param id 告警ID
     * @return 告警
     */
    public Alert selectAlertById(Long id);

    /**
     * 查询告警列表
     *
     * @param alert 告警
     * @return 告警集合
     */
    public List<Alert> selectAlertList(Alert alert);

    /**
     * 新增告警
     *
     * @param alert 告警
     * @return 结果
     */
    public int insertAlert(Alert alert);

    /**
     * 修改告警
     *
     * @param alert 告警
     * @return 结果
     */
    public int updateAlert(Alert alert);

    /**
     * 删除告警
     *
     * @param id 告警ID
     * @return 结果
     */
    public int deleteAlertById(Long id);

    /**
     * 批量删除告警
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteAlertByIds(Long[] ids);

    /**
     * 逻辑删除告警
     *
     * @param id 告警ID
     * @return 结果
     */
    public int logicDeleteAlertById(Long id);

    /**
     * 批量逻辑删除告警
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int logicDeleteAlertByIds(Long[] ids);

    /**
     * 获取所有非空的标签数据
     *
     * @return 标签JSON字符串列表
     */
    public List<String> selectAllTags();

    // ==================== 统计相关方法 ====================

    /**
     * 查询告警总数
     *
     * @return 告警总数
     */
    public int selectTotalCount();

    /**
     * 根据严重程度查询告警数量
     *
     * @param severity 严重程度
     * @return 告警数量
     */
    public int selectCountBySeverity(String severity);

    /**
     * 根据状态查询告警数量
     *
     * @param status 状态
     * @return 告警数量
     */
    public int selectCountByStatus(String status);

    /**
     * 根据状态列表查询告警数量
     *
     * @param statusList 状态列表
     * @return 告警数量
     */
    public int selectCountByStatusIn(String[] statusList);

    /**
     * 查询今日新增告警数量
     *
     * @return 今日新增告警数量
     */
    public int selectTodayCount();

    /**
     * 查询平均处理时间（小时）
     *
     * @return 平均处理时间
     */
    public Double selectAvgResolutionTime();

    /**
     * 查询告警趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> selectAlertTrendByDays(Integer days);

    /**
     * 根据严重程度查询告警分布
     *
     * @return 分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionBySeverity();

    /**
     * 根据状态查询告警分布
     *
     * @return 分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionByStatus();

    /**
     * 根据告警源类型查询告警分布
     *
     * @return 分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionBySourceType();

    /**
     * 根据告警类型查询告警分布
     *
     * @return 分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionByAlertType();

    /**
     * 根据目标IP查询告警分布
     *
     * @return 分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionByTargetIp();

    /**
     * 查询热点告警源
     *
     * @param limit 返回数量限制
     * @return 热点告警源列表
     */
    public List<Map<String, Object>> selectTopAlertSources(Integer limit);

    /**
     * 根据天数查询已解决告警数量
     *
     * @param days 天数
     * @return 已解决告警数量
     */
    public int selectResolvedCountByDays(Integer days);

    /**
     * 根据天数查询平均处理时间
     *
     * @param days 天数
     * @return 平均处理时间
     */
    public Double selectAvgResolutionTimeByDays(Integer days);

    /**
     * 根据天数和处理人查询解决统计
     *
     * @param days 天数
     * @return 处理人统计数据
     */
    public List<Map<String, Object>> selectResolutionStatsByResolver(Integer days);

    /**
     * 根据天数查询每日处理统计
     *
     * @param days 天数
     * @return 每日处理统计数据
     */
    public List<Map<String, Object>> selectDailyResolutionStats(Integer days);

    Alert selectAlertByEventId(String eventId);

    List<Alert> selectUnResolveAlertByEventId(String eventId);

    /**
     * 获取每日告警统计数据
     * 按天分组统计告警数量
     *
     * @param days 统计天数
     * @return 每日统计数据列表
     */
    public List<Map<String, Object>> selectDailyAlertStatistics(Integer days);

    /**
     * 获取最近的告警事件
     * 按发生时间倒序返回指定数量的告警
     *
     * @param limit 返回数量限制
     * @return 最近告警列表
     */
    public List<Alert> selectRecentAlerts(Integer limit);

    /**
     * 获取人员处理统计（近一周）
     * 统计每个人解决告警的数量和平均解决时间
     *
     * @return 人员处理统计数据
     */
    public List<Map<String, Object>> getPersonnelResolutionStats();

    /**
     * 获取告警对象TOP排行
     * 统计告警对象的出现频次
     *
     * @param limit 返回数量限制
     * @return 告警对象TOP排行
     */
    public List<Map<String, Object>> getTopTargets(Integer limit);

    /**
     * 根据告警源标识查询告警列表
     *
     * @param sourceIdent 告警源标识
     * @return 告警列表
     */
    List<Alert> selectAlertBySourceIdent(String sourceIdent);

    /**
     * 根据输入标识查询告警列表
     *
     * @param inputIdent 输入标识
     * @return 告警列表
     */
    List<Alert> selectAlertByInputIdent(String inputIdent);

    /**
     * 根据告警源标识和输入标识查询告警列表
     *
     * @param sourceIdent 告警源标识
     * @param inputIdent 输入标识
     * @return 告警列表
     */
    List<Alert> selectAlertBySourceIdentAndInputIdent(@Param("sourceIdent") String sourceIdent, @Param("inputIdent") String inputIdent);

    /**
     * 根据告警分类查询告警分布
     *
     * @return 告警分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionByCategory();

    // ==================== 工单统计相关方法 ====================

    /**
     * 获取运维部整体统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 部门统计数据
     */
    public Map<String, Object> selectDepartmentStatistics(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取团队统计列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 团队统计数据列表
     */
    public List<Map<String, Object>> selectTeamStatistics(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取指定团队的人员统计
     *
     * @param teamId 团队ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 人员统计数据列表
     */
    public List<Map<String, Object>> selectUserStatisticsByTeam(@Param("teamId") String teamId, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取指定人员的告警明细
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 告警列表
     */
    public List<Alert> selectUserAlertDetails(@Param("userId") String userId, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 获取用户列表（用于下拉选择）
     */
    List<Map<String, Object>> selectUserList();

    /**
     * 查询最近三天需要FlashDuty同步的告警
     * 查询条件：fdIncidentId为空 或 assignedUserIds为空
     * 
     * @param startTime 开始时间（毫秒时间戳）
     * @param endTime 结束时间（毫秒时间戳）
     * @return 需要同步的告警列表
     */
    List<Alert> selectRecentAlertsNeedingFlashDutySync(@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}
