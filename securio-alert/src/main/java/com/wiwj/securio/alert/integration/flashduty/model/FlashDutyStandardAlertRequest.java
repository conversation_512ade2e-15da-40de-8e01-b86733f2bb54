package com.wiwj.securio.alert.integration.flashduty.model;

import java.util.HashMap;
import java.util.Map;

import lombok.Data;

/**
 * FlashDuty标准告警事件请求
 * 
 * <AUTHOR>
 */
@Data
public class FlashDutyStandardAlertRequest {
    
    /** 告警标题，不超过512个字符 */
    private String title_rule;
    
    /** 告警状态：Critical（严重）、Warning（警告）、Info（提醒）、Ok（恢复） */
    private String event_status;
    
    /** 告警标识，用于对已经存在的告警进行更新或自动恢复，不超过255个字符 */
    private String alert_key;
    
    /** 告警描述，不超过2048个字符 */
    private String description;
    
    /** 告警标签集合，key为标签名称，value为标签值 */
    private Map<String, String> labels;
    
    public FlashDutyStandardAlertRequest() {
        this.labels = new HashMap<>();
    }
    
    /**
     * 添加标签
     * 
     * @param key 标签名称
     * @param value 标签值
     */
    public void addLabel(String key, String value) {
        if (key != null && value != null) {
            this.labels.put(key, value);
        }
    }
}
