package com.wiwj.securio.alert.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.alert.enums.AlertTagEnum;
import com.wiwj.securio.alert.enums.AlertTypeTagsEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 告警标签提取器
 * 用于从不同格式的告警数据中提取标准化的标签
 * 
 * <AUTHOR>
 */
public class AlertTagExtractor {
    
    private static final Logger logger = LoggerFactory.getLogger(AlertTagExtractor.class);
    
    // IP地址正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    // 域名正则表达式
    private static final Pattern DOMAIN_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$"
    );

    /**
     * 从Grafana告警数据中提取标准标签
     */
    public static Map<String, String> extractFromGrafana(JSONObject grafanaData) {
        Map<String, String> tags = new HashMap<>();
        
        try {
            // 从labels中提取
            JSONObject labels = grafanaData.getJSONObject("labels");
            if (labels != null) {
                extractCommonLabels(labels, tags);
                
                // Grafana特有字段映射
                mapIfExists(labels, "alertname", AlertTagEnum.METRIC_NAME, tags);
                mapIfExists(labels, "job", AlertTagEnum.SERVICE, tags);
                mapIfExists(labels, "instance", AlertTagEnum.HOSTNAME, tags);
                mapIfExists(labels, "severity", AlertTagEnum.URGENCY, tags);
            }
            
            // 从annotations中提取
            JSONObject annotations = grafanaData.getJSONObject("annotations");
            if (annotations != null) {
                mapIfExists(annotations, "summary", AlertTagEnum.ERROR_MESSAGE, tags);
            }
            
            // 设置监控系统标识
            tags.put(AlertTagEnum.MONITOR_SYSTEM.getKey(), "grafana");
            
        } catch (Exception e) {
            logger.error("提取Grafana标签失败", e);
        }
        
        return tags;
    }

    /**
     * 从N9E告警数据中提取标准标签
     */
    public static Map<String, String> extractFromN9E(JSONObject n9eData) {
        Map<String, String> tags = new HashMap<>();
        
        try {
            // 从tags中提取
            JSONObject tagsObj = n9eData.getJSONObject("tags");
            if (tagsObj != null) {
                extractCommonLabels(tagsObj, tags);
            }
            
            // N9E特有字段映射
            mapIfExists(n9eData, "rule_name", AlertTagEnum.METRIC_NAME, tags);
            mapIfExists(n9eData, "target_ident", AlertTagEnum.HOSTNAME, tags);
            mapIfExists(n9eData, "target_note", AlertTagEnum.INSTANCE_ID, tags);
            
            // 严重程度映射
            Integer severity = n9eData.getInteger("severity");
            if (severity != null) {
                String urgency = mapN9ESeverity(severity);
                tags.put(AlertTagEnum.URGENCY.getKey(), urgency);
            }
            
            // 设置监控系统标识
            tags.put(AlertTagEnum.MONITOR_SYSTEM.getKey(), "n9e");
            
        } catch (Exception e) {
            logger.error("提取N9E标签失败", e);
        }
        
        return tags;
    }

    /**
     * 从Prometheus告警数据中提取标准标签
     */
    public static Map<String, String> extractFromPrometheus(JSONObject prometheusData) {
        Map<String, String> tags = new HashMap<>();
        
        try {
            // 从labels中提取
            JSONObject labels = prometheusData.getJSONObject("labels");
            if (labels != null) {
                extractCommonLabels(labels, tags);
                
                // Prometheus特有字段映射
                mapIfExists(labels, "alertname", AlertTagEnum.METRIC_NAME, tags);
                mapIfExists(labels, "job", AlertTagEnum.SERVICE, tags);
                mapIfExists(labels, "instance", AlertTagEnum.HOSTNAME, tags);
                mapIfExists(labels, "severity", AlertTagEnum.URGENCY, tags);
                mapIfExists(labels, "__name__", AlertTagEnum.METRIC_NAME, tags);
            }
            
            // 设置监控系统标识
            tags.put(AlertTagEnum.MONITOR_SYSTEM.getKey(), "prometheus");
            
        } catch (Exception e) {
            logger.error("提取Prometheus标签失败", e);
        }
        
        return tags;
    }

    /**
     * 从Zabbix告警数据中提取标准标签
     */
    public static Map<String, String> extractFromZabbix(JSONObject zabbixData) {
        Map<String, String> tags = new HashMap<>();
        
        try {
            // Zabbix字段映射
            mapIfExists(zabbixData, "host", AlertTagEnum.HOSTNAME, tags);
            mapIfExists(zabbixData, "item", AlertTagEnum.METRIC_NAME, tags);
            mapIfExists(zabbixData, "trigger", AlertTagEnum.METRIC_NAME, tags);
            mapIfExists(zabbixData, "hostgroup", AlertTagEnum.TEAM, tags);
            
            // 严重程度映射
            String priority = zabbixData.getString("priority");
            if (priority != null) {
                String urgency = mapZabbixPriority(priority);
                tags.put(AlertTagEnum.URGENCY.getKey(), urgency);
            }
            
            // 设置监控系统标识
            tags.put(AlertTagEnum.MONITOR_SYSTEM.getKey(), "zabbix");
            
        } catch (Exception e) {
            logger.error("提取Zabbix标签失败", e);
        }
        
        return tags;
    }

    /**
     * 从通用JSON数据中提取标准标签
     */
    public static Map<String, String> extractFromGeneric(JSONObject genericData) {
        Map<String, String> tags = new HashMap<>();
        
        try {
            extractCommonLabels(genericData, tags);
        } catch (Exception e) {
            logger.error("提取通用标签失败", e);
        }
        
        return tags;
    }

    /**
     * 提取通用标签
     */
    private static void extractCommonLabels(JSONObject data, Map<String, String> tags) {
        // 环境相关
        mapIfExists(data, "environment", AlertTagEnum.ENVIRONMENT, tags);
        mapIfExists(data, "env", AlertTagEnum.ENVIRONMENT, tags);
        mapIfExists(data, "datacenter", AlertTagEnum.DATACENTER, tags);
        mapIfExists(data, "dc", AlertTagEnum.DATACENTER, tags);
        mapIfExists(data, "region", AlertTagEnum.REGION, tags);
        mapIfExists(data, "zone", AlertTagEnum.AVAILABILITY_ZONE, tags);
        mapIfExists(data, "cluster", AlertTagEnum.CLUSTER, tags);
        
        // 服务相关
        mapIfExists(data, "service", AlertTagEnum.SERVICE, tags);
        mapIfExists(data, "service_name", AlertTagEnum.SERVICE, tags);
        mapIfExists(data, "application", AlertTagEnum.APPLICATION, tags);
        mapIfExists(data, "app", AlertTagEnum.APPLICATION, tags);
        mapIfExists(data, "component", AlertTagEnum.COMPONENT, tags);
        mapIfExists(data, "module", AlertTagEnum.MODULE, tags);
        mapIfExists(data, "version", AlertTagEnum.VERSION, tags);
        mapIfExists(data, "namespace", AlertTagEnum.NAMESPACE, tags);
        
        // 基础设施相关
        mapIfExists(data, "hostname", AlertTagEnum.HOSTNAME, tags);
        mapIfExists(data, "host", AlertTagEnum.HOSTNAME, tags);
        mapIfExists(data, "instance_id", AlertTagEnum.INSTANCE_ID, tags);
        mapIfExists(data, "instance", AlertTagEnum.INSTANCE_ID, tags);
        mapIfExists(data, "node", AlertTagEnum.NODE, tags);
        mapIfExists(data, "container", AlertTagEnum.CONTAINER, tags);
        mapIfExists(data, "pod", AlertTagEnum.POD, tags);
        
        // 网络相关
        mapIfExists(data, "source_ip", AlertTagEnum.SOURCE_IP, tags);
        mapIfExists(data, "src_ip", AlertTagEnum.SOURCE_IP, tags);
        mapIfExists(data, "target_ip", AlertTagEnum.TARGET_IP, tags);
        mapIfExists(data, "dst_ip", AlertTagEnum.TARGET_IP, tags);
        mapIfExists(data, "port", AlertTagEnum.PORT, tags);
        mapIfExists(data, "protocol", AlertTagEnum.PROTOCOL, tags);
        mapIfExists(data, "domain", AlertTagEnum.DOMAIN, tags);
        mapIfExists(data, "url", AlertTagEnum.URL_PATH, tags);
        
        // 业务相关
        mapIfExists(data, "business_line", AlertTagEnum.BUSINESS_LINE, tags);
        mapIfExists(data, "product", AlertTagEnum.PRODUCT, tags);
        mapIfExists(data, "team", AlertTagEnum.TEAM, tags);
        mapIfExists(data, "owner", AlertTagEnum.OWNER, tags);
        mapIfExists(data, "project", AlertTagEnum.PROJECT, tags);
        
        // 安全相关
        mapIfExists(data, "attack_type", AlertTagEnum.ATTACK_TYPE, tags);
        mapIfExists(data, "threat_level", AlertTagEnum.THREAT_LEVEL, tags);
        mapIfExists(data, "attacker_ip", AlertTagEnum.ATTACKER_IP, tags);
        mapIfExists(data, "user_agent", AlertTagEnum.USER_AGENT, tags);
        
        // 自动检测和提取IP地址
        extractIpAddresses(data, tags);
        
        // 自动检测和提取域名
        extractDomains(data, tags);
    }

    /**
     * 映射字段值（如果存在）
     */
    private static void mapIfExists(JSONObject data, String sourceKey, AlertTagEnum targetTag, Map<String, String> tags) {
        String value = data.getString(sourceKey);
        if (value != null && !value.trim().isEmpty()) {
            tags.put(targetTag.getKey(), value.trim());
        }
    }

    /**
     * 自动提取IP地址
     */
    private static void extractIpAddresses(JSONObject data, Map<String, String> tags) {
        for (String key : data.keySet()) {
            String value = data.getString(key);
            if (value != null && IP_PATTERN.matcher(value).matches()) {
                if (key.toLowerCase().contains("source") || key.toLowerCase().contains("src")) {
                    tags.put(AlertTagEnum.SOURCE_IP.getKey(), value);
                } else if (key.toLowerCase().contains("target") || key.toLowerCase().contains("dst") || key.toLowerCase().contains("dest")) {
                    tags.put(AlertTagEnum.TARGET_IP.getKey(), value);
                }
            }
        }
    }

    /**
     * 自动提取域名
     */
    private static void extractDomains(JSONObject data, Map<String, String> tags) {
        for (String key : data.keySet()) {
            String value = data.getString(key);
            if (value != null && DOMAIN_PATTERN.matcher(value).matches()) {
                if (key.toLowerCase().contains("domain") || key.toLowerCase().contains("host")) {
                    tags.put(AlertTagEnum.DOMAIN.getKey(), value);
                    break;
                }
            }
        }
    }

    /**
     * 映射N9E严重程度
     */
    private static String mapN9ESeverity(Integer severity) {
        switch (severity) {
            case 1: return "critical";
            case 2: return "high";
            case 3: return "medium";
            default: return "low";
        }
    }

    /**
     * 映射Zabbix优先级
     */
    private static String mapZabbixPriority(String priority) {
        switch (priority.toLowerCase()) {
            case "disaster":
            case "high": return "critical";
            case "average": return "medium";
            case "warning": return "low";
            case "information": return "low";
            default: return "medium";
        }
    }

    /**
     * 根据告警类型获取推荐的标签模板
     */
    public static Map<String, String> getTagTemplate(String alertType) {
        Map<String, String> template = new HashMap<>();
        
        AlertTypeTagsEnum typeEnum = AlertTypeTagsEnum.getByType(alertType);
        if (typeEnum != null) {
            for (AlertTagEnum tag : typeEnum.getRecommendedTags()) {
                template.put(tag.getKey(), ""); // 空值作为模板
            }
        }
        
        return template;
    }

    /**
     * 验证标签是否符合告警类型的推荐标签
     */
    public static boolean validateTags(String alertType, Map<String, String> tags) {
        AlertTypeTagsEnum typeEnum = AlertTypeTagsEnum.getByType(alertType);
        if (typeEnum == null) {
            return true; // 未知类型，不验证
        }
        
        // 检查是否包含推荐的核心标签
        String[] coreKeys = {
            AlertTagEnum.ENVIRONMENT.getKey(),
            AlertTagEnum.SERVICE.getKey(),
            AlertTagEnum.TEAM.getKey()
        };
        
        for (String coreKey : coreKeys) {
            if (typeEnum.isRecommendedTag(coreKey) && !tags.containsKey(coreKey)) {
                logger.warn("告警类型 {} 缺少推荐的核心标签: {}", alertType, coreKey);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 将Map转换为JSON字符串
     */
    public static String tagsToJson(Map<String, String> tags) {
        try {
            return JSON.toJSONString(tags);
        } catch (Exception e) {
            logger.error("标签转换为JSON失败", e);
            return "{}";
        }
    }

    /**
     * 将JSON字符串转换为Map
     */
    public static Map<String, String> jsonToTags(String json) {
        try {
            if (json == null || json.trim().isEmpty()) {
                return new HashMap<>();
            }
            return JSON.parseObject(json, Map.class);
        } catch (Exception e) {
            logger.error("JSON转换为标签失败: {}", json, e);
            return new HashMap<>();
        }
    }
}
