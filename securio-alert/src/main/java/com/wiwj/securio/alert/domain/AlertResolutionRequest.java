package com.wiwj.securio.alert.domain;

import lombok.Data;

/**
 * 告警解决请求参数
 *
 * <AUTHOR>
 */
@Data
public class AlertResolutionRequest {
    
    /**
     * 告警ID
     */
    private Long alertId;
    
    /**
     * 处理人ID
     */
    private String resolvedBy;
    
    /**
     * 处理人名称
     */
    private String resolvedByName;
    
    /**
     * 处理说明
     */
    private String resolutionNote;
    
    /**
     * 根因分析
     */
    private String rootCause;
    
    /**
     * 处理动作 (resolve/ignore)
     */
    private String action;
    
    /**
     * FlashDuty 事件ID（可选）
     * 如果提供此字段，则会调用 FlashDuty 解决接口
     */
    private String flashDutyIncidentId;
    
    /**
     * 构造方法
     */
    public AlertResolutionRequest() {
    }
    
    /**
     * 全参数构造方法
     */
    public AlertResolutionRequest(Long alertId, String resolvedBy, String resolvedByName, 
                                String resolutionNote, String rootCause, String action, 
                                String flashDutyIncidentId) {
        this.alertId = alertId;
        this.resolvedBy = resolvedBy;
        this.resolvedByName = resolvedByName;
        this.resolutionNote = resolutionNote;
        this.rootCause = rootCause;
        this.action = action;
        this.flashDutyIncidentId = flashDutyIncidentId;
    }
    
    /**
     * 静态工厂方法 - 创建解决告警请求
     */
    public static AlertResolutionRequest createResolveRequest(Long alertId, String resolvedBy, 
                                                            String resolvedByName, String resolutionNote, 
                                                            String rootCause) {
        AlertResolutionRequest request = new AlertResolutionRequest();
        request.setAlertId(alertId);
        request.setResolvedBy(resolvedBy);
        request.setResolvedByName(resolvedByName);
        request.setResolutionNote(resolutionNote);
        request.setRootCause(rootCause);
        request.setAction("resolve");
        return request;
    }
    
    /**
     * 静态工厂方法 - 创建解决告警请求（包含FlashDuty事件ID）
     */
    public static AlertResolutionRequest createResolveRequest(Long alertId, String resolvedBy, 
                                                            String resolvedByName, String resolutionNote, 
                                                            String rootCause, String flashDutyIncidentId) {
        AlertResolutionRequest request = new AlertResolutionRequest();
        request.setAlertId(alertId);
        request.setResolvedBy(resolvedBy);
        request.setResolvedByName(resolvedByName);
        request.setResolutionNote(resolutionNote);
        request.setRootCause(rootCause);
        request.setAction("resolve");
        request.setFlashDutyIncidentId(flashDutyIncidentId);
        return request;
    }
    
    /**
     * 静态工厂方法 - 创建忽略告警请求
     */
    public static AlertResolutionRequest createIgnoreRequest(Long alertId, String resolvedBy, 
                                                           String resolvedByName, String resolutionNote) {
        AlertResolutionRequest request = new AlertResolutionRequest();
        request.setAlertId(alertId);
        request.setResolvedBy(resolvedBy);
        request.setResolvedByName(resolvedByName);
        request.setResolutionNote(resolutionNote);
        request.setAction("ignore");
        return request;
    }
    
    /**
     * 静态工厂方法 - 创建忽略告警请求（包含FlashDuty事件ID）
     */
    public static AlertResolutionRequest createIgnoreRequest(Long alertId, String resolvedBy, 
                                                           String resolvedByName, String resolutionNote, 
                                                           String flashDutyIncidentId) {
        AlertResolutionRequest request = new AlertResolutionRequest();
        request.setAlertId(alertId);
        request.setResolvedBy(resolvedBy);
        request.setResolvedByName(resolvedByName);
        request.setResolutionNote(resolutionNote);
        request.setAction("ignore");
        request.setFlashDutyIncidentId(flashDutyIncidentId);
        return request;
    }
    
    /**
     * 检查是否需要调用FlashDuty接口
     */
    public boolean shouldCallFlashDuty() {
        return flashDutyIncidentId != null && !flashDutyIncidentId.trim().isEmpty();
    }
} 