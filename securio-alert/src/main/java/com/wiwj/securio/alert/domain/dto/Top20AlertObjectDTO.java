package com.wiwj.securio.alert.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * Top20告警对象导出DTO
 * 
 * <AUTHOR>
 */
@Data
public class Top20AlertObjectDTO {

    /**
     * 告警对象
     */
    @ExcelProperty(value = "告警对象", index = 0)
    @ColumnWidth(25)
    private String alertObject;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量", index = 1)
    @ColumnWidth(10)
    private Integer count;

    /**
     * 标签
     */
    @ExcelProperty(value = "标签", index = 2)
    @ColumnWidth(50)
    private String labels;

    /**
     * 告警原因
     */
    @ExcelProperty(value = "告警原因", index = 3)
    @ColumnWidth(40)
    private String alertReason;

    /**
     * 告警是否处理
     */
    @ExcelProperty(value = "告警是否处理", index = 4)
    @ColumnWidth(15)
    private String processStatus;

    /**
     * 告警处理方法
     */
    @ExcelProperty(value = "告警处理方法", index = 5)
    @ColumnWidth(40)
    private String processMethod;
} 