package com.wiwj.securio.alert.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 告警导出DTO
 * <AUTHOR>
 */
@Data
public class AlertExportDTO {

    @ExcelProperty("告警ID")
    @ColumnWidth(20)
    private String incidentId;

    @ExcelProperty("告警标题")
    @ColumnWidth(30)
    private String title;

    @ExcelProperty("告警级别")
    @ColumnWidth(15)
    private String incidentSeverity;

    @ExcelProperty("告警处理进度")
    @ColumnWidth(15)
    private String progress;

    @ExcelProperty("开始时间")
    @ColumnWidth(20)
    private String startTime;

    @ExcelProperty("确认时间")
    @ColumnWidth(20)
    private String ackTime;

    @ExcelProperty("认领人")
    @ColumnWidth(15)
    private String acknowledgerName;

    @ExcelProperty("关闭时间")
    @ColumnWidth(20)
    private String closeTime;

    @ExcelProperty("关闭人")
    @ColumnWidth(15)
    private String closerName;

    @ExcelProperty("MTTA")
    @ColumnWidth(15)
    private String mtta;

    @ExcelProperty("MTTR")
    @ColumnWidth(15)
    private String mttr;

    @ExcelProperty("告警根因")
    @ColumnWidth(30)
    private String rootCause;

    @ExcelProperty("解决方案")
    @ColumnWidth(30)
    private String resolution;

    @ExcelProperty("告警规则分组")
    @ColumnWidth(20)
    private String groupName;

    @ExcelProperty("告警对象(ident)")
    @ColumnWidth(25)
    private String ident;

    @ExcelProperty("告警区域(region)")
    @ColumnWidth(15)
    private String region;

    @ExcelProperty("告警环境(env)")
    @ColumnWidth(15)
    private String env;

    @ExcelProperty("告警业务(business)")
    @ColumnWidth(20)
    private String business;

    @ExcelProperty("告警部门(department)")
    @ColumnWidth(20)
    private String department;
} 