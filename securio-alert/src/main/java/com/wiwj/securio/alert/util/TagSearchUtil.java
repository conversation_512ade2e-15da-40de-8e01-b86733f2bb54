package com.wiwj.securio.alert.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 标签搜索工具类
 * 支持复杂的标签搜索表达式解析，包括 AND、OR 逻辑操作符
 * 
 * 支持的搜索格式：
 * 1. 单个标签：environment:production
 * 2. AND 操作：environment:production AND service:user-api
 * 3. OR 操作：environment:production OR environment:staging
 * 4. 混合操作：(environment:production OR environment:staging) AND service:user-api
 * 5. 文本搜索：user-api（在所有标签值中搜索）
 * 
 * <AUTHOR>
 */
public class TagSearchUtil {
    private static final Logger logger = LoggerFactory.getLogger(TagSearchUtil.class);
    
    // 标签格式的正则表达式：key:value
    private static final Pattern TAG_PATTERN = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*):([^\\s]+)");
    
    // 逻辑操作符
    private static final String AND_OPERATOR = "AND";
    private static final String OR_OPERATOR = "OR";
    
    /**
     * 解析标签搜索表达式并生成 SQL 条件
     * 
     * @param tagSearch 标签搜索表达式
     * @param searchType 搜索类型：exact（精确）或 contains（包含）
     * @return SQL 条件片段
     */
    public static String parseTagSearchToSql(String tagSearch, String searchType) {
        if (tagSearch == null || tagSearch.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 预处理搜索表达式
            String processedSearch = preprocessSearchExpression(tagSearch.trim());
            
            // 解析表达式
            TagSearchExpression expression = parseExpression(processedSearch);
            
            // 生成 SQL 条件
            return generateSqlCondition(expression, searchType);
            
        } catch (Exception e) {
            logger.warn("解析标签搜索表达式失败: {}, 降级为简单文本搜索", tagSearch, e);
            // 降级为简单的文本搜索
            return "tags LIKE CONCAT('%', '" + escapeSql(tagSearch) + "', '%')";
        }
    }
    
    /**
     * 预处理搜索表达式
     * 处理括号、操作符等
     */
    private static String preprocessSearchExpression(String search) {
        // 标准化操作符
        search = search.replaceAll("\\s+(?i)and\\s+", " AND ");
        search = search.replaceAll("\\s+(?i)or\\s+", " OR ");
        
        // 移除多余的空格
        search = search.replaceAll("\\s+", " ");
        
        return search;
    }
    
    /**
     * 解析搜索表达式
     */
    private static TagSearchExpression parseExpression(String search) {
        // 简化的解析器，支持基本的 AND/OR 操作
        List<String> tokens = tokenize(search);
        return parseTokens(tokens);
    }
    
    /**
     * 将搜索表达式分词
     */
    private static List<String> tokenize(String search) {
        List<String> tokens = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        boolean inQuotes = false;
        
        for (char c : search.toCharArray()) {
            if (c == '"') {
                inQuotes = !inQuotes;
                current.append(c);
            } else if (c == ' ' && !inQuotes) {
                if (current.length() > 0) {
                    tokens.add(current.toString());
                    current.setLength(0);
                }
            } else {
                current.append(c);
            }
        }
        
        if (current.length() > 0) {
            tokens.add(current.toString());
        }
        
        return tokens;
    }
    
    /**
     * 解析令牌列表
     */
    private static TagSearchExpression parseTokens(List<String> tokens) {
        if (tokens.isEmpty()) {
            return new TagSearchExpression(TagSearchExpression.Type.EMPTY);
        }
        
        // 查找最低优先级的操作符（OR 优先级低于 AND）
        int orIndex = findOperator(tokens, OR_OPERATOR);
        if (orIndex != -1) {
            TagSearchExpression left = parseTokens(tokens.subList(0, orIndex));
            TagSearchExpression right = parseTokens(tokens.subList(orIndex + 1, tokens.size()));
            return new TagSearchExpression(TagSearchExpression.Type.OR, left, right);
        }
        
        int andIndex = findOperator(tokens, AND_OPERATOR);
        if (andIndex != -1) {
            TagSearchExpression left = parseTokens(tokens.subList(0, andIndex));
            TagSearchExpression right = parseTokens(tokens.subList(andIndex + 1, tokens.size()));
            return new TagSearchExpression(TagSearchExpression.Type.AND, left, right);
        }
        
        // 如果只有一个令牌，处理为单个条件
        if (tokens.size() == 1) {
            String token = tokens.get(0);
            Matcher matcher = TAG_PATTERN.matcher(token);
            if (matcher.matches()) {
                String key = matcher.group(1);
                String value = matcher.group(2);
                return new TagSearchExpression(TagSearchExpression.Type.TAG, key, value);
            } else {
                // 文本搜索
                return new TagSearchExpression(TagSearchExpression.Type.TEXT, token);
            }
        }
        
        // 多个令牌但没有操作符，默认用 AND 连接
        TagSearchExpression result = null;
        for (String token : tokens) {
            TagSearchExpression current;
            Matcher matcher = TAG_PATTERN.matcher(token);
            if (matcher.matches()) {
                String key = matcher.group(1);
                String value = matcher.group(2);
                current = new TagSearchExpression(TagSearchExpression.Type.TAG, key, value);
            } else {
                current = new TagSearchExpression(TagSearchExpression.Type.TEXT, token);
            }
            
            if (result == null) {
                result = current;
            } else {
                result = new TagSearchExpression(TagSearchExpression.Type.AND, result, current);
            }
        }
        
        return result;
    }
    
    /**
     * 查找操作符的位置
     */
    private static int findOperator(List<String> tokens, String operator) {
        for (int i = 0; i < tokens.size(); i++) {
            if (operator.equals(tokens.get(i))) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 生成 SQL 条件
     */
    private static String generateSqlCondition(TagSearchExpression expression, String searchType) {
        switch (expression.getType()) {
            case EMPTY:
                return null;
                
            case TAG:
                return generateTagCondition(expression.getKey(), expression.getValue(), searchType);
                
            case TEXT:
                return "tags LIKE CONCAT('%', '" + escapeSql(expression.getValue()) + "', '%')";
                
            case AND:
                String leftAnd = generateSqlCondition(expression.getLeft(), searchType);
                String rightAnd = generateSqlCondition(expression.getRight(), searchType);
                if (leftAnd == null || rightAnd == null) {
                    return leftAnd != null ? leftAnd : rightAnd;
                }
                return "(" + leftAnd + " AND " + rightAnd + ")";
                
            case OR:
                String leftOr = generateSqlCondition(expression.getLeft(), searchType);
                String rightOr = generateSqlCondition(expression.getRight(), searchType);
                if (leftOr == null || rightOr == null) {
                    return leftOr != null ? leftOr : rightOr;
                }
                return "(" + leftOr + " OR " + rightOr + ")";
                
            default:
                return null;
        }
    }
    
    /**
     * 生成单个标签的 SQL 条件
     */
    private static String generateTagCondition(String key, String value, String searchType) {
        String escapedKey = escapeJsonPath(key);
        String escapedValue = escapeSql(value);
        
        if ("exact".equals(searchType)) {
            // 使用JSON_UNQUOTE和JSON_EXTRACT，并正确处理键名转义
            return "JSON_UNQUOTE(JSON_EXTRACT(tags, '$." + escapedKey + "')) = '" + escapedValue + "'";
        } else {
            return "JSON_UNQUOTE(JSON_EXTRACT(tags, '$." + escapedKey + "')) LIKE CONCAT('%', '" + escapedValue + "', '%')";
        }
    }
    
    /**
     * SQL 注入防护
     */
    private static String escapeSql(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("'", "''")
                   .replace("\\", "\\\\")
                   .replace("%", "\\%")
                   .replace("_", "\\_");
    }
    
    /**
     * JSON路径键名转义
     * 处理JSON路径中的特殊字符，如下划线、连字符等
     */
    private static String escapeJsonPath(String key) {
        if (key == null) {
            return "";
        }
        
        // 对于JSON路径，我们需要用双引号包围包含特殊字符的键名
        // 如果键名包含下划线、连字符、空格等特殊字符，需要用引号包围
        if (key.matches(".*[\\s\\-\\.\\$\\[\\]\\*\\?\\+\\{\\}\\(\\)\\|\\\\\\^].*")) {
            // 如果包含特殊字符，用双引号包围并转义内部的双引号
            return "\"" + key.replace("\"", "\\\"") + "\"";
        }
        
        // 普通字符（字母、数字、下划线）可以直接使用
        return key;
    }
    
    /**
     * 标签搜索表达式类
     */
    public static class TagSearchExpression {
        public enum Type {
            EMPTY, TAG, TEXT, AND, OR
        }
        
        private Type type;
        private String key;
        private String value;
        private TagSearchExpression left;
        private TagSearchExpression right;
        
        // 构造函数
        public TagSearchExpression(Type type) {
            this.type = type;
        }
        
        public TagSearchExpression(Type type, String value) {
            this.type = type;
            this.value = value;
        }
        
        public TagSearchExpression(Type type, String key, String value) {
            this.type = type;
            this.key = key;
            this.value = value;
        }
        
        public TagSearchExpression(Type type, TagSearchExpression left, TagSearchExpression right) {
            this.type = type;
            this.left = left;
            this.right = right;
        }
        
        // Getters
        public Type getType() { return type; }
        public String getKey() { return key; }
        public String getValue() { return value; }
        public TagSearchExpression getLeft() { return left; }
        public TagSearchExpression getRight() { return right; }
    }
    
    /**
     * 提取搜索表达式中的所有标签键
     * 用于分析和优化查询
     */
    public static Set<String> extractTagKeys(String tagSearch) {
        Set<String> keys = new HashSet<>();
        if (tagSearch == null || tagSearch.trim().isEmpty()) {
            return keys;
        }
        
        Matcher matcher = TAG_PATTERN.matcher(tagSearch);
        while (matcher.find()) {
            keys.add(matcher.group(1));
        }
        
        return keys;
    }
    
    /**
     * 验证标签搜索表达式是否有效
     */
    public static boolean isValidTagSearchExpression(String tagSearch) {
        if (tagSearch == null || tagSearch.trim().isEmpty()) {
            return true;
        }
        
        try {
            parseTagSearchToSql(tagSearch, "contains");
            return true;
        } catch (Exception e) {
            return false;
        }
    }
} 