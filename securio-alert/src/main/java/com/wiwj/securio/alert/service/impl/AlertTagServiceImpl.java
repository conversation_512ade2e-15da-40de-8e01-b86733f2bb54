package com.wiwj.securio.alert.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.alert.mapper.AlertMapper;
import com.wiwj.securio.alert.service.IAlertTagService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 告警标签服务实现
 *
 * <AUTHOR>
 */
@Service
public class AlertTagServiceImpl implements IAlertTagService {

    private static final Logger logger = LoggerFactory.getLogger(AlertTagServiceImpl.class);

    @Autowired
    private AlertMapper alertMapper;

    @Override
    public Map<String, Object> getAvailableTags() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取所有非空的标签数据
            List<String> allTagsJson = alertMapper.selectAllTags();

            // 解析标签并分类
            Map<String, Set<String>> tagCategories = parseAndCategorizeTagsFromJson(allTagsJson);

            // 转换为前端需要的格式
            result.put("environment", new ArrayList<>(tagCategories.getOrDefault("environment", new HashSet<>())));
            result.put("service", new ArrayList<>(tagCategories.getOrDefault("service", new HashSet<>())));
            result.put("team", new ArrayList<>(tagCategories.getOrDefault("team", new HashSet<>())));
            result.put("monitor_system", new ArrayList<>(tagCategories.getOrDefault("monitor_system", new HashSet<>())));
            result.put("severity", new ArrayList<>(tagCategories.getOrDefault("severity", new HashSet<>())));
            result.put("datacenter", new ArrayList<>(tagCategories.getOrDefault("datacenter", new HashSet<>())));
            result.put("cluster", new ArrayList<>(tagCategories.getOrDefault("cluster", new HashSet<>())));

        } catch (Exception e) {
            logger.error("获取可用标签失败", e);
            // 返回默认标签
            result = getDefaultTags();
        }

        return result;
    }

    @Override
    public Map<String, Object> getTagStatistics() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取所有标签数据
            List<String> allTagsJson = alertMapper.selectAllTags();

            // 统计标签使用频率
            Map<String, Integer> tagFrequency = new HashMap<>();
            Map<String, Map<String, Integer>> categoryFrequency = new HashMap<>();

            for (String tagsJson : allTagsJson) {
                if (StringUtils.hasText(tagsJson)) {
                    try {
                        JSONObject tags = JSON.parseObject(tagsJson);
                        for (String key : tags.keySet()) {
                            String value = tags.getString(key);
                            if (StringUtils.hasText(value)) {
                                String tagKey = key + ":" + value;
                                tagFrequency.put(tagKey, tagFrequency.getOrDefault(tagKey, 0) + 1);

                                // 按类别统计
                                categoryFrequency.computeIfAbsent(key, k -> new HashMap<>())
                                    .put(value, categoryFrequency.getOrDefault(key, new HashMap<>()).getOrDefault(value, 0) + 1);
                            }
                        }
                    } catch (Exception e) {
                        logger.warn("解析标签JSON失败: {}", tagsJson, e);
                    }
                }
            }

            // 排序并返回前20个最常用的标签
            List<Map.Entry<String, Integer>> sortedTags = tagFrequency.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(20)
                .collect(Collectors.toList());

            result.put("topTags", sortedTags.stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    (e1, e2) -> e1,
                    LinkedHashMap::new
                )));

            result.put("categoryStats", categoryFrequency);
            result.put("totalTags", tagFrequency.size());

        } catch (Exception e) {
            logger.error("获取标签统计失败", e);
        }

        return result;
    }

    @Override
    public Map<String, Object> getTagSuggestions(String keyword) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(keyword)) {
            return result;
        }

        try {
            // 获取所有标签数据
            List<String> allTagsJson = alertMapper.selectAllTags();

            // 搜索匹配的标签
            Set<String> suggestions = new HashSet<>();
            String lowerKeyword = keyword.toLowerCase();

            for (String tagsJson : allTagsJson) {
                if (StringUtils.hasText(tagsJson)) {
                    try {
                        JSONObject tags = JSON.parseObject(tagsJson);
                        for (String key : tags.keySet()) {
                            String value = tags.getString(key);
                            if (StringUtils.hasText(value)) {
                                String tagKey = key + ":" + value;
                                // 检查键或值是否包含关键词
                                if (key.toLowerCase().contains(lowerKeyword) ||
                                    value.toLowerCase().contains(lowerKeyword)) {
                                    suggestions.add(tagKey);
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.warn("解析标签JSON失败: {}", tagsJson, e);
                    }
                }
            }

            // 限制返回数量并排序
            List<String> sortedSuggestions = suggestions.stream()
                .sorted()
                .limit(10)
                .collect(Collectors.toList());

            result.put("suggestions", sortedSuggestions);
            result.put("total", suggestions.size());

        } catch (Exception e) {
            logger.error("获取标签建议失败", e);
        }

        return result;
    }

    /**
     * 从JSON标签数据中解析并分类标签
     */
    private Map<String, Set<String>> parseAndCategorizeTagsFromJson(List<String> allTagsJson) {
        Map<String, Set<String>> categories = new HashMap<>();

        for (String tagsJson : allTagsJson) {
            if (StringUtils.hasText(tagsJson)) {
                try {
                    JSONObject tags = JSON.parseObject(tagsJson);
                    for (String key : tags.keySet()) {
                        String value = tags.getString(key);
                        if (StringUtils.hasText(value)) {
                            categories.computeIfAbsent(key, k -> new HashSet<>()).add(value);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("解析标签JSON失败: {}", tagsJson, e);
                }
            }
        }

        return categories;
    }

    /**
     * 获取默认标签（备用方案）
     */
    private Map<String, Object> getDefaultTags() {
        Map<String, Object> defaultTags = new HashMap<>();

        defaultTags.put("environment", Arrays.asList("production", "staging", "test", "development"));
        defaultTags.put("service", Arrays.asList("api", "web", "database", "cache", "queue"));
        defaultTags.put("team", Arrays.asList("backend", "frontend", "devops", "security", "qa"));
        defaultTags.put("monitor_system", Arrays.asList("grafana", "prometheus", "zabbix", "n9e"));
        defaultTags.put("severity", Arrays.asList("critical", "high", "medium", "low", "info"));
        defaultTags.put("datacenter", Arrays.asList("dc1", "dc2", "aws-us-east-1", "aws-us-west-2"));
        defaultTags.put("cluster", Arrays.asList("prod-cluster", "test-cluster", "k8s-prod"));

        return defaultTags;
    }

    @Override
    public Map<String, Object> getSourceSubTypes(String sourceType) {
        Map<String, Object> result = new HashMap<>();

        try {
            if (!StringUtils.hasText(sourceType)) {
                // 返回所有子类型
                result.put("subTypes", getAllSubTypes());
            } else {
                // 返回指定来源类型的子类型
                result.put("subTypes", getSubTypesBySourceType(sourceType));
            }

        } catch (Exception e) {
            logger.error("获取来源子类型失败", e);
        }

        return result;
    }

    /**
     * 获取所有子类型
     */
    private List<Map<String, String>> getAllSubTypes() {
        List<Map<String, String>> subTypes = new ArrayList<>();

        // 从枚举中获取所有子类型
        for (com.wiwj.securio.alert.enums.AlertSourceSubTypeEnum subType :
             com.wiwj.securio.alert.enums.AlertSourceSubTypeEnum.values()) {
            if (!"unknown".equals(subType.getParentType())) {
                Map<String, String> subTypeMap = new HashMap<>();
                subTypeMap.put("value", subType.getCode());
                subTypeMap.put("label", subType.getDescription());
                subTypeMap.put("parentType", subType.getParentType());
                subTypes.add(subTypeMap);
            }
        }

        return subTypes;
    }

    /**
     * 根据来源类型获取子类型
     */
    private List<Map<String, String>> getSubTypesBySourceType(String sourceType) {
        List<Map<String, String>> subTypes = new ArrayList<>();

        // 从枚举中获取指定来源类型的子类型
        for (com.wiwj.securio.alert.enums.AlertSourceSubTypeEnum subType :
             com.wiwj.securio.alert.enums.AlertSourceSubTypeEnum.values()) {
            if (subType.getParentType().equals(sourceType)) {
                Map<String, String> subTypeMap = new HashMap<>();
                subTypeMap.put("value", subType.getCode());
                subTypeMap.put("label", subType.getDescription());
                subTypes.add(subTypeMap);
            }
        }

        return subTypes;
    }
}
