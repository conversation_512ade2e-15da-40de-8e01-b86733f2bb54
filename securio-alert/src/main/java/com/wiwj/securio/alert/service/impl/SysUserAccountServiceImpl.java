package com.wiwj.securio.alert.service.impl;

import com.wiwj.securio.alert.domain.SysUserAccount;
import com.wiwj.securio.alert.mapper.SysUserAccountMapper;
import com.wiwj.securio.alert.service.ISysUserAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统用户账号Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserAccountServiceImpl implements ISysUserAccountService {
    @Autowired
    private SysUserAccountMapper sysUserAccountMapper;

    /**
     * 查询系统用户账号
     *
     * @param id 系统用户账号主键
     * @return 系统用户账号
     */
    @Override
    public SysUserAccount selectSysUserAccountById(Long id) {
        return sysUserAccountMapper.selectSysUserAccountById(id);
    }

    /**
     * 查询系统用户账号列表
     *
     * @param sysUserAccount 系统用户账号
     * @return 系统用户账号
     */
    @Override
    public List<SysUserAccount> selectSysUserAccountList(SysUserAccount sysUserAccount) {
        return sysUserAccountMapper.selectSysUserAccountList(sysUserAccount);
    }

    /**
     * 新增系统用户账号
     *
     * @param sysUserAccount 系统用户账号
     * @return 结果
     */
    @Override
    public int insertSysUserAccount(SysUserAccount sysUserAccount) {
        return sysUserAccountMapper.insertSysUserAccount(sysUserAccount);
    }

    /**
     * 修改系统用户账号
     *
     * @param sysUserAccount 系统用户账号
     * @return 结果
     */
    @Override
    public int updateSysUserAccount(SysUserAccount sysUserAccount) {
        return sysUserAccountMapper.updateSysUserAccount(sysUserAccount);
    }

    /**
     * 批量删除系统用户账号
     *
     * @param ids 需要删除的系统用户账号主键
     * @return 结果
     */
    @Override
    public int deleteSysUserAccountByIds(Long[] ids) {
        return sysUserAccountMapper.deleteSysUserAccountByIds(ids);
    }

    /**
     * 删除系统用户账号信息
     *
     * @param id 系统用户账号主键
     * @return 结果
     */
    @Override
    public int deleteSysUserAccountById(Long id) {
        return sysUserAccountMapper.deleteSysUserAccountById(id);
    }

    /**
     * 根据账号ID查询系统用户账号
     *
     * @param accountId 账号ID
     * @return 系统用户账号
     */
    @Override
    public SysUserAccount selectSysUserAccountByAccountId(String accountId) {
        return sysUserAccountMapper.selectSysUserAccountByAccountId(accountId);
    }

    /**
     * 根据渠道名称查询系统用户账号列表
     *
     * @param chanel 渠道名称
     * @return 系统用户账号集合
     */
    @Override
    public List<SysUserAccount> selectSysUserAccountByChanel(String chanel) {
        return sysUserAccountMapper.selectSysUserAccountByChanel(chanel);
    }

    /**
     * 根据账号ID列表批量查询系统用户账号
     *
     * @param accountIds 账号ID列表
     * @return 系统用户账号集合
     */
    @Override
    public List<SysUserAccount> selectSysUserAccountByAccountIds(List<String> accountIds) {
        return sysUserAccountMapper.selectSysUserAccountByAccountIds(accountIds);
    }

    /**
     * 根据用户ID查询appKey
     *
     * @param userId 用户ID
     * @return appKey
     */
    @Override
    public String getAppKeyByUserId(Long userId) {
        return sysUserAccountMapper.selectAppKeyByUserId(userId);
    }
} 