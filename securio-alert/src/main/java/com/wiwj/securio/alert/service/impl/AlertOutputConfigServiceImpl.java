package com.wiwj.securio.alert.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.alibaba.fastjson2.JSON;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.mapper.AlertOutputConfigMapper;
import com.wiwj.securio.alert.domain.AlertOutputConfig;
import com.wiwj.securio.alert.integration.flashduty.service.FlashDutyService;
import com.wiwj.securio.alert.service.IAlertOutputConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.wiwj.common.utils.SecurityUtils.getUserId;
import static com.wiwj.common.utils.SecurityUtils.getUsername;

/**
 * 告警输出配置服务实现
 *
 * <AUTHOR>
 */
@Service
public class AlertOutputConfigServiceImpl implements IAlertOutputConfigService {
    private static final Logger logger = LoggerFactory.getLogger(AlertOutputConfigServiceImpl.class);

    @Autowired
    private AlertOutputConfigMapper alertOutputConfigMapper;

    @Autowired
    private FlashDutyService flashDutyService;

    /**
     * 查询告警输出配置
     *
     * @param id 告警输出配置ID
     * @return 告警输出配置
     */
    @Override
    public AlertOutputConfig selectOutputConfigById(Long id) {
        return alertOutputConfigMapper.selectOutputConfigById(id);
    }

    /**
     * 查询告警输出配置列表
     *
     * @param outputConfig 告警输出配置
     * @return 告警输出配置
     */
    @Override
    public List<AlertOutputConfig> selectOutputConfigList(AlertOutputConfig outputConfig) {
        return alertOutputConfigMapper.selectOutputConfigList(outputConfig);
    }

    /**
     * 根据告警源ID查询告警输出配置
     * 注意：此方法已废弃，保留是为了兼容旧的代码
     *
     * @param sourceId 告警源ID
     * @return 告警输出配置集合
     */
    @Override
    @Deprecated
    public List<AlertOutputConfig> selectOutputConfigsBySourceId(Long sourceId) {
        return alertOutputConfigMapper.selectOutputConfigsBySourceId(sourceId);
    }

    /**
     * 查询匹配的告警输出配置
     *
     * @param queryConfig 查询条件
     * @return 匹配的告警输出配置列表
     */
    @Override
    public List<AlertOutputConfig> selectMatchingOutputConfigs(AlertOutputConfig queryConfig) {
        return alertOutputConfigMapper.selectMatchingOutputConfigs(queryConfig);
    }

    /**
     * 新增告警输出配置
     *
     * @param outputConfig 告警输出配置
     * @return 结果
     */
    @Override
    public int insertOutputConfig(AlertOutputConfig outputConfig) {
        outputConfig.setCreateAt(System.currentTimeMillis());
        outputConfig.setCreateName(getUsername());
        outputConfig.setCreateBy(getUserId()+"");
        outputConfig.setIsDel(0);
        return alertOutputConfigMapper.insertOutputConfig(outputConfig);
    }

    /**
     * 修改告警输出配置
     *
     * @param outputConfig 告警输出配置
     * @return 结果
     */
    @Override
    public int updateOutputConfig(AlertOutputConfig outputConfig) {
        outputConfig.setUpdateAt(System.currentTimeMillis());
        outputConfig.setUpdateName(getUsername());
        outputConfig.setUpdateBy(getUserId()+"");
        return alertOutputConfigMapper.updateOutputConfig(outputConfig);
    }

    /**
     * 批量删除告警输出配置
     *
     * @param ids 需要删除的告警输出配置ID
     * @return 结果
     */
    @Override
    public int deleteOutputConfigByIds(Long[] ids) {
        return alertOutputConfigMapper.deleteOutputConfigByIds(ids);
    }

    /**
     * 删除告警输出配置信息
     *
     * @param id 告警输出配置ID
     * @return 结果
     */
    @Override
    public int deleteOutputConfigById(Long id) {
        return alertOutputConfigMapper.deleteOutputConfigById(id);
    }

    /**
     * 测试告警输出配置
     *
     * @param id 告警输出配置ID
     * @param testData 测试数据
     * @return 结果
     */
    @Override
    public boolean testOutputConfig(Long id, Object testData) {
        try {
            // 查询告警输出配置
            AlertOutputConfig config = alertOutputConfigMapper.selectOutputConfigById(id);
            if (config == null) {
                throw new RuntimeException("告警输出配置不存在");
            }

            // 检查配置状态
            if (!"enabled".equals(config.getStatus())) {
                throw new RuntimeException("告警输出配置已禁用");
            }

            // 如果有测试数据，则使用测试数据发送告警
            if (testData != null) {
                return sendTestAlert(config, testData);
            } else {
                // 否则生成默认测试数据
                return sendDefaultTestAlert(config);
            }
        } catch (Exception e) {
            logger.error("测试告警输出配置失败", e);
            throw new RuntimeException("测试失败：" + e.getMessage());
        }
    }

    /**
     * 发送测试告警
     */
    private boolean sendTestAlert(AlertOutputConfig config, Object testData) {
        try {
            // 解析测试数据
            Map<String, Object> alertData;
            if (testData instanceof Map) {
                alertData = (Map<String, Object>) testData;
            } else {
                // 尝试解析为JSON
                ObjectMapper mapper = new ObjectMapper();
                String jsonStr = mapper.writeValueAsString(testData);
                alertData = mapper.readValue(jsonStr, Map.class);
            }

            // 根据输出方式处理
            if ("forward_raw".equals(config.getOutputType())) {
                // 转发原始数据
                String rawData = (String) alertData.get("rawData");
                if (rawData == null || rawData.isEmpty()) {
                    // 如果没有原始数据，则使用整个测试数据作为原始数据
                    ObjectMapper mapper = new ObjectMapper();
                    rawData = mapper.writeValueAsString(alertData);
                }
                return flashDutyService.forwardRawDataToFlashDuty(rawData, config.getOutputWebhookUrl(), null);
            } else {
                // 发送标准告警
                Alert alert = convertToAlert(alertData);
                return flashDutyService.sendFlashDutyStandardAlert(alert, config.getOutputWebhookUrl());
            }
        } catch (Exception e) {
            logger.error("发送测试告警失败", e);
            return false;
        }
    }

    /**
     * 发送默认测试告警
     */
    private boolean sendDefaultTestAlert(AlertOutputConfig config) {
        try {
            long now = System.currentTimeMillis();
            String testId = "test_" + now;

            Alert testAlert = new Alert();
            testAlert.setTitle("[测试] " + config.getName() + " - 测试告警");
            testAlert.setDescription("这是一条由系统生成的测试告警数据，用于验证输出配置是否正常工作。");
            testAlert.setSeverity("warning");
            testAlert.setStatus("firing");
            testAlert.setAlertType("test");
            testAlert.setSourceType(config.getAlertSourceType() != null ? config.getAlertSourceType() : "test");
            testAlert.setSourceIdent(config.getAlertSourceIdent() != null ? config.getAlertSourceIdent() : "test-instance");
            testAlert.setRuleName(config.getAlertRuleName() != null ? config.getAlertRuleName() : "test-rule");
            testAlert.setGroupName(config.getGroupName() != null ? config.getGroupName() : "test-group");
            testAlert.setEventId(testId);
            testAlert.setDetectedAt(now);
            testAlert.setOccurredAt(now);
            testAlert.setCreateAt(now);
            testAlert.setUpdateAt(now);

            // 设置测试标签
            Map<String, String> tags = new HashMap<>();
            tags.put("test", "true");
            tags.put("config_id", config.getId().toString());
            tags.put("config_name", config.getName());
            tags.put("generated_at", new Date(now).toString());
            testAlert.setTags(JSON.toJSONString(tags));

            // 计算持续时间（如果已解决）
            if (testAlert.getResolvedAt() != null) {
                testAlert.calculateDuration();
            }

            return flashDutyService.sendFlashDutyStandardAlert(testAlert, config.getOutputWebhookUrl());
        } catch (Exception e) {
            logger.error("发送默认测试告警失败", e);
            return false;
        }
    }

    /**
     * 将Map数据转换为Alert对象
     */
    private Alert convertToAlert(Map<String, Object> alertData) {
        Alert alert = new Alert();

        // 基本字段
        alert.setTitle((String) alertData.get("title"));
        alert.setDescription((String) alertData.get("description"));
        alert.setSeverity((String) alertData.get("severity"));
        alert.setStatus((String) alertData.get("status"));
        alert.setAlertType((String) alertData.get("alertType"));
        alert.setSourceType((String) alertData.get("sourceType"));
        alert.setSourceIdent((String) alertData.get("sourceIdent"));
        alert.setRuleName((String) alertData.get("ruleName"));
        alert.setGroupName((String) alertData.get("groupName"));
        alert.setEventId((String) alertData.get("eventId"));
        alert.setTags((String) alertData.get("tags"));
        alert.setRawData((String) alertData.get("rawData"));

        // 时间字段
        if (alertData.get("detectedAt") != null) {
            alert.setDetectedAt(((Number) alertData.get("detectedAt")).longValue());
        }
        if (alertData.get("occurredAt") != null) {
            alert.setOccurredAt(((Number) alertData.get("occurredAt")).longValue());
        }
        if (alertData.get("createAt") != null) {
            alert.setCreateAt(((Number) alertData.get("createAt")).longValue());
        }
        if (alertData.get("updateAt") != null) {
            alert.setUpdateAt(((Number) alertData.get("updateAt")).longValue());
        }

        return alert;
    }
}
