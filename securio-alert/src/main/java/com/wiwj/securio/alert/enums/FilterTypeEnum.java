package com.wiwj.securio.alert.enums;

import lombok.Getter;

/**
 * 数据筛选方式枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum FilterTypeEnum {
    
    TAG_FILTER("tag_filter", "基于标签过滤"),
    FORWARD_RAW("forward_raw", "转发元数据");
    
    /** 类型编码 */
    private final String code;
    
    /** 类型名称 */
    private final String name;
    
    FilterTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
