package com.wiwj.securio.alert.example;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.alert.enums.AlertTagEnum;
import com.wiwj.securio.alert.enums.AlertTypeTagsEnum;
import com.wiwj.securio.alert.utils.AlertTagExtractor;

import java.util.HashMap;
import java.util.Map;

/**
 * 告警标签使用示例
 * 展示如何在实际项目中使用告警标签枚举
 *
 * <AUTHOR>
 */
public class AlertTagUsageExample {

    /**
     * 示例1：从Grafana告警中提取标准标签
     */
    public static void exampleGrafanaExtraction() {
        // 模拟Grafana告警数据
        String grafanaJson = "{" +
            "\"labels\": {" +
                "\"alertname\": \"HighCPUUsage\"," +
                "\"instance\": \"web-server-01:9100\"," +
                "\"job\": \"node-exporter\"," +
                "\"severity\": \"critical\"," +
                "\"environment\": \"production\"," +
                "\"team\": \"platform\"" +
            "}," +
            "\"annotations\": {" +
                "\"summary\": \"CPU usage is above 90%\"," +
                "\"description\": \"CPU usage has been above 90% for more than 5 minutes\"" +
            "}" +
        "}";

        JSONObject grafanaData = JSON.parseObject(grafanaJson);
        Map<String, String> extractedTags = AlertTagExtractor.extractFromGrafana(grafanaData);

        System.out.println("从Grafana提取的标签:");
        extractedTags.forEach((key, value) ->
            System.out.println("  " + key + ": " + value));
    }

    /**
     * 示例2：从N9E告警中提取标准标签
     */
    public static void exampleN9EExtraction() {
        // 模拟N9E告警数据
        String n9eJson = "{" +
            "\"rule_name\": \"内存使用率过高\"," +
            "\"target_ident\": \"*************\"," +
            "\"target_note\": \"Web服务器01\"," +
            "\"severity\": 1," +
            "\"tags\": {" +
                "\"environment\": \"production\"," +
                "\"service\": \"web-service\"," +
                "\"team\": \"backend\"" +
            "}" +
        "}";

        JSONObject n9eData = JSON.parseObject(n9eJson);
        Map<String, String> extractedTags = AlertTagExtractor.extractFromN9E(n9eData);

        System.out.println("从N9E提取的标签:");
        extractedTags.forEach((key, value) ->
            System.out.println("  " + key + ": " + value));
    }

    /**
     * 示例3：创建系统性能告警的标准标签
     */
    public static void exampleSystemPerformanceAlert() {
        Map<String, String> tags = new HashMap<>();

        // 使用枚举创建标准标签
        tags.put(AlertTagEnum.ENVIRONMENT.getKey(), "production");
        tags.put(AlertTagEnum.HOSTNAME.getKey(), "web-server-01");
        tags.put(AlertTagEnum.SERVICE.getKey(), "nginx");
        tags.put(AlertTagEnum.METRIC_NAME.getKey(), "cpu_usage");
        tags.put(AlertTagEnum.THRESHOLD.getKey(), "80%");
        tags.put(AlertTagEnum.CURRENT_VALUE.getKey(), "95%");
        tags.put(AlertTagEnum.TEAM.getKey(), "platform");
        tags.put(AlertTagEnum.URGENCY.getKey(), "high");
        tags.put(AlertTagEnum.MONITOR_SYSTEM.getKey(), "prometheus");

        System.out.println("系统性能告警标签:");
        tags.forEach((key, value) ->
            System.out.println("  " + key + ": " + value));

        // 验证标签是否符合系统性能告警的推荐标签
        boolean isValid = AlertTagExtractor.validateTags("system_performance", tags);
        System.out.println("标签验证结果: " + (isValid ? "通过" : "不通过"));
    }

    /**
     * 示例4：创建Web安全告警的标准标签
     */
    public static void exampleWebSecurityAlert() {
        Map<String, String> tags = new HashMap<>();

        // 使用枚举创建Web安全告警标签
        tags.put(AlertTagEnum.ATTACK_TYPE.getKey(), "sql_injection");
        tags.put(AlertTagEnum.SOURCE_IP.getKey(), "************");
        tags.put(AlertTagEnum.TARGET_IP.getKey(), "************");
        tags.put(AlertTagEnum.DOMAIN.getKey(), "api.example.com");
        tags.put(AlertTagEnum.URL_PATH.getKey(), "/api/v1/users");
        tags.put(AlertTagEnum.USER_AGENT.getKey(), "Mozilla/5.0...");
        tags.put(AlertTagEnum.SECURITY_RULE.getKey(), "WAF-001");
        tags.put(AlertTagEnum.THREAT_LEVEL.getKey(), "high");
        tags.put(AlertTagEnum.ENVIRONMENT.getKey(), "production");
        tags.put(AlertTagEnum.TEAM.getKey(), "security");

        System.out.println("Web安全告警标签:");
        tags.forEach((key, value) ->
            System.out.println("  " + key + ": " + value));
    }

    /**
     * 示例5：获取不同告警类型的推荐标签模板
     */
    public static void exampleTagTemplates() {
        System.out.println("不同告警类型的推荐标签:");

        for (AlertTypeTagsEnum alertType : AlertTypeTagsEnum.values()) {
            System.out.println("\n" + alertType.getDescription() + ":");
            String[] recommendedKeys = alertType.getRecommendedTagKeys();
            for (String key : recommendedKeys) {
                AlertTagEnum tag = AlertTagEnum.getByKey(key);
                if (tag != null) {
                    System.out.println("  " + key + " - " + tag.getDescription() +
                                     " (示例: " + tag.getExamples() + ")");
                }
            }
        }
    }

    /**
     * 示例6：标签的JSON序列化和反序列化
     */
    public static void exampleJsonSerialization() {
        // 创建标签Map
        Map<String, String> tags = new HashMap<>();
        tags.put(AlertTagEnum.ENVIRONMENT.getKey(), "production");
        tags.put(AlertTagEnum.SERVICE.getKey(), "user-service");
        tags.put(AlertTagEnum.SEVERITY.getKey(), "critical");
        tags.put(AlertTagEnum.TEAM.getKey(), "backend");

        // 转换为JSON
        String json = AlertTagExtractor.tagsToJson(tags);
        System.out.println("标签JSON: " + json);

        // 从JSON转换回Map
        Map<String, String> parsedTags = AlertTagExtractor.jsonToTags(json);
        System.out.println("解析后的标签:");
        parsedTags.forEach((key, value) ->
            System.out.println("  " + key + ": " + value));
    }

    /**
     * 示例7：动态标签提取
     */
    public static void exampleDynamicExtraction() {
        // 模拟包含多种信息的原始数据
        String rawJson = "{" +
            "\"alert_name\": \"Database Connection Error\"," +
            "\"host\": \"db-server-02\"," +
            "\"service_name\": \"mysql\"," +
            "\"environment\": \"staging\"," +
            "\"error_code\": \"2003\"," +
            "\"source_ip\": \"*************\"," +
            "\"target_ip\": \"*************\"," +
            "\"port\": \"3306\"," +
            "\"team\": \"database\"," +
            "\"custom_field\": \"some_value\"" +
        "}";

        JSONObject rawData = JSON.parseObject(rawJson);
        Map<String, String> extractedTags = AlertTagExtractor.extractFromGeneric(rawData);

        System.out.println("动态提取的标签:");
        extractedTags.forEach((key, value) -> {
            AlertTagEnum tag = AlertTagEnum.getByKey(key);
            String description = tag != null ? tag.getDescription() : "自定义标签";
            System.out.println("  " + key + ": " + value + " (" + description + ")");
        });
    }

    public static void main(String[] args) {
        System.out.println("=== 告警标签使用示例 ===\n");

        exampleGrafanaExtraction();
        System.out.println();

        exampleN9EExtraction();
        System.out.println();

        exampleSystemPerformanceAlert();
        System.out.println();

        exampleWebSecurityAlert();
        System.out.println();

        exampleTagTemplates();
        System.out.println();

        exampleJsonSerialization();
        System.out.println();

        exampleDynamicExtraction();
    }
}
