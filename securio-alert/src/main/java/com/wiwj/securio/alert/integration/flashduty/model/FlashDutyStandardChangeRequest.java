package com.wiwj.securio.alert.integration.flashduty.model;

import java.util.HashMap;
import java.util.Map;

import lombok.Data;

/**
 * FlashDuty标准变更事件请求
 * 
 * <AUTHOR>
 */
@Data
public class FlashDutyStandardChangeRequest {
    
    /** 变更状态：Planned（已提单）、Ready（即将开始）、Processing（进行中）、Canceled（已取消）、Done（已完成） */
    private String change_status;
    
    /** 变更唯一key，一般是变更单号，用于将不同的变更event合并至一个变更 */
    private String change_key;
    
    /** 变更标题 */
    private String title;
    
    /** 变更event发生时间戳，单位"秒" */
    private Long event_time;
    
    /** 变更描述，不超过2048个字符 */
    private String description;
    
    /** 变更单地址，用于跳转到变更详情 */
    private String link;
    
    /** 变更标签集合，key为标签名称，value为标签值 */
    private Map<String, String> labels;
    
    public FlashDutyStandardChangeRequest() {
        this.labels = new HashMap<>();
    }
    
    /**
     * 添加标签
     * 
     * @param key 标签名称
     * @param value 标签值
     */
    public void addLabel(String key, String value) {
        if (key != null && value != null) {
            this.labels.put(key, value);
        }
    }
}
