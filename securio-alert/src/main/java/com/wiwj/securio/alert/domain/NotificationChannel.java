package com.wiwj.securio.alert.domain;

import com.wiwj.common.annotation.Excel;

import lombok.Data;

/**
 * 通知渠道对象 alert_notification_channel
 * 
 * <AUTHOR>
 */
@Data
public class NotificationChannel {
    private static final long serialVersionUID = 1L;

    /** 渠道ID */
    private Long id;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    private String name;

    /** 渠道类型 */
    @Excel(name = "渠道类型")
    private String type;

    /** 渠道描述 */
    @Excel(name = "渠道描述")
    private String description;

    /** 渠道配置JSON */
    private String configJson;

    /** 状态(enabled/disabled) */
    @Excel(name = "状态", readConverterExp = "enabled=启用,disabled=禁用")
    private String status;

    /** 创建时间 */
    private Long createAt;

    /** 创建人ID */
    private String createBy;

    /** 创建人名称 */
    private String createName;

    /** 更新时间 */
    private Long updateAt;

    /** 更新人ID */
    private String updateBy;

    /** 更新人名称 */
    private String updateName;

    /** 是否删除(0-未删除 1-已删除) */
    private Integer isDel;
}
