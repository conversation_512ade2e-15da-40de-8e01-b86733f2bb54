package com.wiwj.securio.alert.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.core.page.TableDataInfo;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.common.utils.SecurityUtils;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.AlertAckRequest;
import com.wiwj.securio.alert.domain.AlertQueryRequest;
import com.wiwj.securio.alert.domain.AlertResolutionRequest;
import com.wiwj.securio.alert.exception.AppKeyNotConfiguredException;
import com.wiwj.securio.alert.integration.flashduty.model.*;
import com.wiwj.securio.alert.integration.flashduty.service.FlashDutyService;
import com.wiwj.securio.alert.service.IAlertExportService;
import com.wiwj.securio.alert.service.IAlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 告警管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/alert")
public class AlertController extends BaseController {

    @Autowired
    private IAlertService alertService;

    @Autowired
    private IAlertExportService alertExportService;

    @Autowired
    private FlashDutyService flashDutyService;

    /**
     * 查询告警列表
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/list")
    public TableDataInfo list(AlertQueryRequest queryRequest) {

        // 处理所有查询参数
        queryRequest.processAllParams();

        // 记录查询日志
        if (queryRequest.getStartTime() != null || queryRequest.getEndTime() != null) {
            log.debug("时间范围查询: startTime={}, endTime={}", queryRequest.getStartTime(), queryRequest.getEndTime());
        }
        if (queryRequest.getTagSearch() != null && !queryRequest.getTagSearch().isEmpty()) {
            log.debug("标签搜索: tagSearch={}, tagSearchType={}", queryRequest.getTagSearch(), queryRequest.getTagSearchType());
        }

        startPage();
        List<Alert> list = alertService.selectAlertList(queryRequest);
        return getDataTable(list);
    }

    /**
     * 导出告警列表
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(Alert alert) {
        List<Alert> list = alertService.selectAlertList(alert);
        ExcelUtil<Alert> util = new ExcelUtil<Alert>(Alert.class);
        return util.exportExcel(list, "告警数据");
    }

    /**
     * 导出FlashDuty告警列表到Excel
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警导出", businessType = BusinessType.EXPORT)
    @GetMapping("/export-flashduty")
    public void exportFlashDutyAlerts(
            @RequestParam(value = "startTime", required = false) Long startTime,
            @RequestParam(value = "endTime", required = false) Long endTime,
            HttpServletResponse response) throws IOException {

        // 默认导出近一周的数据
        if (endTime == null) {
            endTime = System.currentTimeMillis() / 1000;
        }
        if (startTime == null) {
            startTime = endTime - 3600 * 24 * 7; // 默认近一周
        }

        log.info("导出FlashDuty告警列表: startTime={}, endTime={}", startTime, endTime);

        alertExportService.exportAlertsToExcel(startTime, endTime, response);
    }

    /**
     * 导出Top20告警数据到Excel
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "Top20告警导出", businessType = BusinessType.EXPORT)
    @GetMapping("/export-top20")
    public void exportTop20Alerts(
            @RequestParam(value = "startTime", required = false) Long startTime,
            @RequestParam(value = "endTime", required = false) Long endTime,
            HttpServletResponse response) throws IOException {

        // 默认导出近一周的数据
        if (endTime == null) {
            endTime = System.currentTimeMillis() / 1000;
        }
        if (startTime == null) {
            startTime = endTime - 3600 * 24 * 7; // 默认近一周
        }

        log.info("导出Top20告警数据: startTime={}, endTime={}", startTime, endTime);

        alertExportService.exportTop20AlertsToExcel(startTime, endTime, response);
    }

    /**
     * 获取告警详细信息
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(alertService.selectAlertById(id));
    }

    /**
     * 新增告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Alert alert) {
        alert.setCreateBy(SecurityUtils.getUserId() + "");
        alert.setCreateName(SecurityUtils.getUsername());
        return toAjax(alertService.insertAlert(alert));
    }

    /**
     * 修改告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Alert alert) {
        alert.setUpdateBy(SecurityUtils.getUserId() + "");
        alert.setUpdateName(SecurityUtils.getUsername());
        return toAjax(alertService.updateAlert(alert));
    }

    /**
     * 删除告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(alertService.logicDeleteAlertByIds(ids));
    }

    /**
     * 处理告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警", businessType = BusinessType.UPDATE)
    @PutMapping("/resolve/{id}")
    public AjaxResult resolve(@PathVariable("id") Long id, @RequestBody Map<String, String> params) {
        String resolutionNote = params.getOrDefault("resolutionNote", "");
        String rootCause = params.getOrDefault("rootCause", "");
        String flashDutyIncidentId = params.getOrDefault("flashDutyIncidentId", "");

        AlertResolutionRequest request = AlertResolutionRequest.createResolveRequest(
                id, SecurityUtils.getUsername(), SecurityUtils.getLoginUser().getUser().getNickName(), resolutionNote, rootCause, flashDutyIncidentId);

        try {
            return toAjax(alertService.resolveAlert(request));
        } catch (AppKeyNotConfiguredException e) {
            log.error("用户未配置AppKey，无法解决告警: {}", e.getMessage());
            return AjaxResult.error("解决失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("解决告警失败: {}", e.getMessage(), e);
            return AjaxResult.error("解决失败: " + e.getMessage());
        }
    }

    /**
     * 忽略告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警", businessType = BusinessType.UPDATE)
    @PutMapping("/ignore/{id}")
    public AjaxResult ignore(@PathVariable("id") Long id, @RequestBody Map<String, String> params) {
        String resolutionNote = params.getOrDefault("resolutionNote", "");
        String flashDutyIncidentId = params.getOrDefault("flashDutyIncidentId", "");

        AlertResolutionRequest request = AlertResolutionRequest.createIgnoreRequest(
                id, SecurityUtils.getUsername() + "", SecurityUtils.getLoginUser().getUser().getNickName(), resolutionNote, flashDutyIncidentId);

        try {
            return toAjax(alertService.ignoreAlert(request));
        } catch (AppKeyNotConfiguredException e) {
            log.error("用户未配置AppKey，无法忽略告警: {}", e.getMessage());
            return AjaxResult.error("忽略失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("忽略告警失败: {}", e.getMessage(), e);
            return AjaxResult.error("忽略失败: " + e.getMessage());
        }
    }

    /**
     * 认领告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警", businessType = BusinessType.UPDATE)
    @PutMapping("/acknowledge")
    public AjaxResult acknowledge(@RequestParam("alertIds") String alertIdsStr) {
        // 解析告警ID列表
        List<Long> alertIds = Arrays.stream(alertIdsStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());

        if (alertIds.isEmpty()) {
            return AjaxResult.error("请选择要认领的告警");
        }

        log.info("批量认领告警，告警ID列表: {}", alertIds);

        try {
            // 使用 AlertAckRequest 结构体
            AlertAckRequest request = AlertAckRequest.createAckRequest(
                    alertIds, SecurityUtils.getUsername() , SecurityUtils.getLoginUser().getUser().getNickName(), null);

            return toAjax(alertService.acknowledgeAlerts(request));
        } catch (AppKeyNotConfiguredException e) {
            log.error("用户未配置AppKey，无法认领告警: {}", e.getMessage());
            return AjaxResult.error("认领失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("认领告警失败: {}", e.getMessage(), e);
            return AjaxResult.error("认领失败: " + e.getMessage());
        }
    }

    /**
     * 批量处理告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "告警", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-resolve")
    public AjaxResult batchResolve(@RequestBody Map<String, Object> params) {
        try {
            // 从参数中获取逗号分隔的ID字符串
            String idsStr = (String) params.get("ids");
            String resolutionNote = (String) params.getOrDefault("resolutionNote", "批量处理");
            String rootCause = (String) params.getOrDefault("rootCause", "");
            String action = (String) params.getOrDefault("action", "resolve"); // resolve 或 ignore
            String flashDutyIncidentId = (String) params.getOrDefault("flashDutyIncidentId", "");

            if (idsStr == null || idsStr.trim().isEmpty()) {
                return AjaxResult.error("请选择要处理的告警");
            }

            // 解析逗号分隔的ID字符串为List<Long>
            List<Long> ids = new ArrayList<>();
            try {
                String[] idArray = idsStr.split(",");
                for (String idStr : idArray) {
                    if (idStr != null && !idStr.trim().isEmpty()) {
                        ids.add(Long.parseLong(idStr.trim()));
                    }
                }
            } catch (NumberFormatException e) {
                log.error("解析告警ID失败: {}", idsStr, e);
                return AjaxResult.error("告警ID格式错误");
            }

            if (ids.isEmpty()) {
                return AjaxResult.error("请选择要处理的告警");
            }

            // 使用 AlertResolutionRequest 结构体创建请求列表
            List<AlertResolutionRequest> requests = new ArrayList<>();
            String resolvedBy = SecurityUtils.getUsername();
            String resolvedByName = SecurityUtils.getLoginUser().getUser().getNickName();

            for (Long id : ids) {
                AlertResolutionRequest request;
                if ("resolve".equals(action)) {
                    request = AlertResolutionRequest.createResolveRequest(
                            id, resolvedBy, resolvedByName, resolutionNote, rootCause, flashDutyIncidentId);
                } else {
                    request = AlertResolutionRequest.createIgnoreRequest(
                            id, resolvedBy, resolvedByName, resolutionNote, flashDutyIncidentId);
                }
                requests.add(request);
            }

            Map<String, Object> result = alertService.batchProcessAlerts(requests);

            return AjaxResult.success("批量处理完成", result);
        } catch (AppKeyNotConfiguredException e) {
            log.error("用户未配置AppKey，无法批量处理告警: {}", e.getMessage());
            return AjaxResult.error("批量处理失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("批量处理告警失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量处理失败: " + e.getMessage());
        }
    }

    /**
     * 接收告警
     *
     * @param jsonData 告警对象
     * @return 处理结果
     */
    @PostMapping("/push")
    public AjaxResult push(@RequestBody String jsonData) {
        log.info("接收到告警: {}", jsonData);

        return AjaxResult.success();
    }

    /**
     * 接收告警
     *
     * @param jsonData 告警对象
     * @return 处理结果
     */
    @PostMapping("/receive")
    public AjaxResult receiveAlert(@RequestBody String jsonData) {
        log.info("接收到告警: {}", jsonData);

        return AjaxResult.success();
    }

    /**
     * 批量接收告警
     *
     * @param alerts 告警对象数组
     * @return 处理结果
     */
    @PostMapping("/batch-receive")
    public AjaxResult receiveBatchAlerts(@RequestBody Alert[] alerts) {
        log.info("接收到批量告警, 数量: {}", alerts.length);

        try {
            // 批量接收告警
            Map<String, Object> resultData = alertService.receiveBatchAlerts(alerts);

            // 返回成功结果
            return AjaxResult.success("成功接收批量告警", resultData);
        } catch (Exception e) {
            log.error("批量接收告警时发生错误: {}", e.getMessage(), e);
            return AjaxResult.error("处理告警失败: " + e.getMessage());
        }
    }

    /**
     * 获取告警统计数据
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            Map<String, Object> statistics = alertService.getAlertStatistics();
            return AjaxResult.success("获取统计数据成功", statistics);
        } catch (Exception e) {
            log.error("获取告警统计数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取统计数据失败");
        }
    }

    /**
     * 获取告警趋势数据
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/trend")
    public AjaxResult getTrend(@RequestParam(value = "days", defaultValue = "7") Integer days) {
        try {
            List<Map<String, Object>> trendData = alertService.getAlertTrend(days);
            return AjaxResult.success("获取趋势数据成功", trendData);
        } catch (Exception e) {
            log.error("获取告警趋势数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取趋势数据失败");
        }
    }

    /**
     * 获取告警分布数据
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/distribution")
    public AjaxResult getDistribution(@RequestParam(value = "type", defaultValue = "severity") String type) {
        try {
            List<Map<String, Object>> distributionData = alertService.getAlertDistribution(type);
            return AjaxResult.success("获取分布数据成功", distributionData);
        } catch (Exception e) {
            log.error("获取告警分布数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取分布数据失败");
        }
    }

    /**
     * 获取热点告警源
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/top-sources")
    public AjaxResult getTopSources(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> topSources = alertService.getTopAlertSources(limit);
            return AjaxResult.success("获取热点告警源成功", topSources);
        } catch (Exception e) {
            log.error("获取热点告警源失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取热点告警源失败");
        }
    }

    /**
     * 获取告警处理统计
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/resolution-stats")
    public AjaxResult getResolutionStats(@RequestParam(value = "days", defaultValue = "30") Integer days) {
        try {
            Map<String, Object> resolutionStats = alertService.getResolutionStatistics(days);
            return AjaxResult.success("获取处理统计成功", resolutionStats);
        } catch (Exception e) {
            log.error("获取告警处理统计失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取处理统计失败");
        }
    }

    /**
     * 获取可用标签
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/available-tags")
    public AjaxResult getAvailableTags() {
        try {
            Map<String, List<String>> availableTags = alertService.getAvailableTags();
            return AjaxResult.success("获取成功", availableTags);
        } catch (Exception e) {
            log.error("获取可用标签失败", e);
            return AjaxResult.error("获取可用标签失败");
        }
    }

    /**
     * 生成测试告警数据（开发测试用）
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @PostMapping("/generate-test-data")
    public AjaxResult generateTestData(@RequestParam(value = "count", defaultValue = "10") Integer count) {
        try {
            List<Alert> testAlerts = new ArrayList<>();
            long currentTime = System.currentTimeMillis();
            String[] severities = {"critical", "high", "medium", "low", "info"};
            String[] environments = {"production", "staging", "test", "development"};
            String[] services = {"user-api", "order-service", "payment-gateway", "notification-service"};
            String[] teams = {"backend", "frontend", "devops", "qa"};
            String[] sourceTypes = {"grafana", "prometheus", "zabbix", "n9e"};

            Random random = new Random();

            for (int i = 0; i < count; i++) {
                Alert alert = new Alert();
                alert.setTitle("测试告警 " + (i + 1) + " - " + severities[random.nextInt(severities.length)]);
                alert.setDescription("这是一个测试告警，用于验证时间范围查询功能");
                alert.setSeverity(severities[random.nextInt(severities.length)]);
                alert.setStatus(random.nextBoolean() ? "new" : "resolved");
                alert.setSourceType(sourceTypes[random.nextInt(sourceTypes.length)]);

                // 生成随机时间（过去7天内）
                long randomOffset = random.nextLong() % (7 * 24 * 60 * 60 * 1000L);
                alert.setOccurredAt(currentTime - Math.abs(randomOffset));
                alert.setDetectedAt(alert.getOccurredAt() + random.nextInt(5 * 60 * 1000)); // 5分钟内检测到

                // 生成标签
                Map<String, Object> tags = new HashMap<>();
                tags.put("environment", environments[random.nextInt(environments.length)]);
                tags.put("service", services[random.nextInt(services.length)]);
                tags.put("team", teams[random.nextInt(teams.length)]);
                tags.put("datacenter", "dc" + (random.nextInt(3) + 1));
                tags.put("version", "v" + random.nextInt(10) + "." + random.nextInt(10));

                try {
                    ObjectMapper mapper = new ObjectMapper();
                    alert.setTags(mapper.writeValueAsString(tags));
                } catch (Exception e) {
                    log.warn("生成测试标签失败", e);
                }

                alert.setCreateBy(SecurityUtils.getUserId() + "");
                alert.setCreateName(SecurityUtils.getUsername());
                alert.setCreateAt(System.currentTimeMillis());

                testAlerts.add(alert);
            }

            // 批量插入测试数据
            int successCount = 0;
            for (Alert alert : testAlerts) {
                try {
                    alertService.insertAlert(alert);
                    successCount++;
                } catch (Exception e) {
                    log.warn("插入测试告警失败", e);
                }
            }

            Map<String, Integer> result = new HashMap<>();
            result.put("total", count);
            result.put("success", successCount);
            result.put("failed", count - successCount);

            return AjaxResult.success("生成测试数据完成", result);

        } catch (Exception e) {
            log.error("生成测试数据失败", e);
            return AjaxResult.error("生成测试数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取30天内新增事件统计
     * 按天分组统计告警数量
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/daily-statistics")
    public AjaxResult getDailyStatistics(@RequestParam(value = "days", defaultValue = "30") Integer days) {
        try {
            List<Map<String, Object>> dailyStats = alertService.getDailyAlertStatistics(days);
            return AjaxResult.success(dailyStats);
        } catch (Exception e) {
            log.error("获取每日告警统计失败", e);
            return AjaxResult.error("获取每日告警统计失败");
        }
    }

    /**
     * 获取最近入侵事件列表
     * 返回最近的告警事件，用于主机概览页面
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/recent-intrusion-events")
    public AjaxResult getRecentIntrusionEvents(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> recentEvents = alertService.getRecentIntrusionEvents(limit);
            return AjaxResult.success(recentEvents);
        } catch (Exception e) {
            log.error("获取最近入侵事件失败", e);
            return AjaxResult.error("获取最近入侵事件失败");
        }
    }

    /**
     * 查询故障列表（通过eventId查询incident_id）
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @PostMapping("/incidents/list")
    public AjaxResult queryIncidentList(@RequestBody Map<String, Object> requestBody) {
        try {
            log.info("查询故障列表请求: {}", requestBody);

            // 构建请求对象
            IncidentListRequest request = new IncidentListRequest();

            // 设置labels
            if (requestBody.containsKey("labels")) {
                Map<String, Object> labels = (Map<String, Object>) requestBody.get("labels");
                Map<String, String> labelMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : labels.entrySet()) {
                    labelMap.put(entry.getKey(), String.valueOf(entry.getValue()));
                }
                request.setLabels(labelMap);
            }

            // 设置时间范围（必传参数）
            if (requestBody.containsKey("startTime") && requestBody.containsKey("endTime")) {
                Long startTime = ((Number) requestBody.get("startTime")).longValue();
                Long endTime = ((Number) requestBody.get("endTime")).longValue();
                request.setStart_time(startTime);
                request.setEnd_time(endTime);
            } else {
                return AjaxResult.error("开始时间和结束时间是必传参数");
            }

            // 设置分页参数
            if (requestBody.containsKey("limit")) {
                request.setLimit((Integer) requestBody.get("limit"));
            }
            if (requestBody.containsKey("p")) {
                request.setP((Integer) requestBody.get("p"));
            }

            IncidentListResponse response = flashDutyService.queryIncidentList(request);

            return AjaxResult.success(response);

        } catch (Exception e) {
            log.error("查询故障列表失败", e);
            return AjaxResult.error("查询故障列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询故障时间线
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @PostMapping("/incident-feed/query")
    public AjaxResult queryIncidentFeed(@RequestBody IncidentFeedRequest request) {
        try {
            log.info("查询故障时间线请求: {}", request);

            // 参数校验
            if (request.getIncident_id() == null || request.getIncident_id().trim().isEmpty()) {
                return AjaxResult.error("告警ID不能为空");
            }

            IncidentFeedResponse response = flashDutyService.queryIncidentFeed(request);

            if (response == null) {
                return AjaxResult.error("查询故障时间线失败");
            }

            if (response.getError() != null) {
                return AjaxResult.error("查询故障时间线失败: " + response.getError());
            }

            return AjaxResult.success(response);

        } catch (Exception e) {
            log.error("查询故障时间线失败: incident_id={}", request.getIncident_id(), e);
            return AjaxResult.error("查询故障时间线失败: " + e.getMessage());
        }
    }

    /**
     * 根据告警ID查询故障时间线（GET方式）
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/incident-feed/query/{incidentId}")
    public AjaxResult queryIncidentFeedByGet(@PathVariable String incidentId,
                                             @RequestParam(required = false) List<String> types,
                                             @RequestParam(defaultValue = "1") Integer p,
                                             @RequestParam(defaultValue = "50") Integer limit,
                                             @RequestParam(defaultValue = "false") Boolean asc) {

        IncidentFeedRequest request = new IncidentFeedRequest();
        request.setIncident_id(incidentId);
        request.setTypes(types);
        request.setP(p);
        request.setLimit(limit);
        request.setAsc(asc);

        return queryIncidentFeed(request);
    }

    /**
     * 获取支持的操作类型列表
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/incident-feed/action-types")
    public AjaxResult getActionTypes() {
        List<Map<String, String>> actionTypes = Arrays.asList(
                createActionType("i_comm", "故障.评论"),
                createActionType("i_notify", "故障.通知"),
                createActionType("i_new", "故障.触发"),
                createActionType("i_assign", "故障.分派"),
                createActionType("i_a_rspd", "故障.添加处理人"),
                createActionType("i_ack", "故障.开始处理"),
                createActionType("i_unack", "故障.取消处理"),
                createActionType("i_snooze", "故障.暂缓处理"),
                createActionType("i_wake", "故障.取消暂缓"),
                createActionType("i_rslv", "故障.解决"),
                createActionType("i_reopen", "故障.重开"),
                createActionType("i_merge", "故障.合并"),
                createActionType("i_m_silence", "故障.触发静默"),
                createActionType("i_m_inhibat", "故障.触发抑制"),
                createActionType("i_m_flapping", "故障.触发收敛"),
                createActionType("i_storm", "故障.触发风暴"),
                createActionType("i_r_rc", "故障.更新根本原因"),
                createActionType("i_r_desc", "故障.更新描述"),
                createActionType("i_r_rsltn", "故障.更新解决方案"),
                createActionType("i_r_resp", "故障.更新处理人"),
                createActionType("i_r_impact", "故障.更新影响"),
                createActionType("i_r_title", "故障.更新标题"),
                createActionType("i_r_severity", "故障.更新故障严重程度"),
                createActionType("i_r_field", "故障.更新自定义字段"),
                createActionType("i_custom", "故障.触发自定义操作")
        );

        return AjaxResult.success(actionTypes);
    }

    /**
     * 根据人员ID获取人员姓名（批量）
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @PostMapping("/incident-feed/person-names")
    public AjaxResult getPersonNames(@RequestBody Map<String, Object> requestBody) {
        try {
            @SuppressWarnings("unchecked")
            List<String> personIds = (List<String>) requestBody.get("personIds");

            if (personIds == null || personIds.isEmpty()) {
                return AjaxResult.success(new HashMap<>());
            }

            Map<String, String> personNames = new HashMap<>();

            // 调用FlashDuty的成员列表接口获取人员姓名
            MemberListRequest memberRequest = new MemberListRequest();
            memberRequest.setP(1);
            memberRequest.setLimit(100); // 设置较大的限制以获取更多成员
            memberRequest.setAsc(true);
//            memberRequest.setOrderby("member_id");

            MemberListResponse memberResponse = flashDutyService.queryMemberList(memberRequest);

            if (memberResponse != null && memberResponse.getData() != null &&
                    memberResponse.getData().getItems() != null) {

                // 创建ID到姓名的映射
                Map<String, String> idToNameMap = new HashMap<>();
                for (PageMemberResponseData.MemberItem member : memberResponse.getData().getItems()) {
                    if (member.getMember_id() != null && member.getMember_name() != null) {
                        idToNameMap.put(String.valueOf(member.getMember_id()), member.getMember_name());
                    }
                }

                // 匹配请求的人员ID
                for (String personId : personIds) {
                    String name = idToNameMap.get(personId);
                    if (name != null && !name.isEmpty()) {
                        personNames.put(personId, name);
                    } else {
                        personNames.put(personId, "用户" + personId); // 找不到时使用默认格式
                    }
                }

                log.info("成功获取人员姓名映射，总共 {} 个成员，匹配 {} 个请求",
                        idToNameMap.size(), personNames.size());
            } else {
                // 如果获取成员列表失败，使用默认格式
                for (String personId : personIds) {
                    personNames.put(personId, "用户" + personId);
                }
                log.warn("获取FlashDuty成员列表失败，使用默认人员姓名格式");
            }

            return AjaxResult.success(personNames);

        } catch (Exception e) {
            log.error("获取人员姓名失败", e);

            // 发生异常时，返回默认格式的姓名
            @SuppressWarnings("unchecked")
            List<String> personIds = (List<String>) requestBody.get("personIds");
            Map<String, String> fallbackNames = new HashMap<>();
            if (personIds != null) {
                for (String personId : personIds) {
                    fallbackNames.put(personId, "用户" + personId);
                }
            }

            return AjaxResult.success(fallbackNames);
        }
    }

    /**
     * 删除故障
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "删除故障", businessType = BusinessType.DELETE)
    @PostMapping("/incident/remove")
    public AjaxResult removeIncidents(@RequestBody IncidentRemoveRequest request) {
        try {
            log.info("删除故障请求: {}", request);

            // 参数校验
            if (request.getIncident_ids() == null || request.getIncident_ids().isEmpty()) {
                return AjaxResult.error("故障ID列表不能为空");
            }

            // 记录操作日志
            log.warn("执行删除故障操作，故障ID: {}, 操作人: {}", request.getIncident_ids(), SecurityUtils.getUsername());

            IncidentRemoveResponse response = flashDutyService.removeIncidents(request);

            if (response == null) {
                return AjaxResult.error("删除故障失败：响应为空");
            }

            if (response.getError() != null) {
                String errorMsg = "删除故障失败: " + response.getError();
                log.error("删除故障失败: {}", errorMsg);
                return AjaxResult.error(errorMsg);
            }

            log.info("删除故障成功，故障ID: {}", request.getIncident_ids());
            return AjaxResult.success("删除故障成功");

        } catch (Exception e) {
            log.error("删除故障失败: incident_ids={}", request.getIncident_ids(), e);
            return AjaxResult.error("删除故障失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除故障（通过incident_ids数组）
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @Log(title = "批量删除故障", businessType = BusinessType.DELETE)
    @DeleteMapping("/incident/remove/{incidentIds}")
    public AjaxResult removeIncidentsByIds(@PathVariable String[] incidentIds) {
        try {
            if (incidentIds == null || incidentIds.length == 0) {
                return AjaxResult.error("故障ID列表不能为空");
            }

            IncidentRemoveRequest request = new IncidentRemoveRequest();
            request.setIncident_ids(Arrays.asList(incidentIds));

            return removeIncidents(request);

        } catch (Exception e) {
            log.error("批量删除故障失败: incident_ids={}", Arrays.toString(incidentIds), e);
            return AjaxResult.error("批量删除故障失败: " + e.getMessage());
        }
    }

    /**
     * 获取人员处理统计（近一周）
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/personnel-resolution-stats")
    public AjaxResult getPersonnelResolutionStats() {
        try {
            List<Map<String, Object>> stats = alertService.getPersonnelResolutionStats();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            log.error("获取人员处理统计失败", e);
            return AjaxResult.error("获取人员处理统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取告警对象TOP10（原目标IP TOP10）
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/top-targets")
    public AjaxResult getTopTargets(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> topTargets = alertService.getTopTargets(limit);
            return AjaxResult.success(topTargets);
        } catch (Exception e) {
            log.error("获取告警对象TOP{}失败", limit, e);
            return AjaxResult.error("获取告警对象TOP排行失败: " + e.getMessage());
        }
    }

    /**
     * 创建操作类型对象
     */
    private Map<String, String> createActionType(String code, String description) {
        Map<String, String> actionType = new HashMap<>();
        actionType.put("code", code);
        actionType.put("description", description);
        return actionType;
    }
}
