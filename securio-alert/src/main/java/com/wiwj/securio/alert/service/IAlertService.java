package com.wiwj.securio.alert.service;

import java.util.List;
import java.util.Map;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.AlertResolutionRequest;
import com.wiwj.securio.alert.domain.AlertAckRequest;

/**
 * 告警Service接口
 *
 * <AUTHOR>
 */
public interface IAlertService
{
    /**
     * 查询告警
     *
     * @param id 告警ID
     * @return 告警
     */
    public Alert selectAlertById(Long id);

    /**
     * 查询告警列表
     *
     * @param alert 告警
     * @return 告警集合
     */
    public List<Alert> selectAlertList(Alert alert);

    /**
     * 新增告警
     *
     * @param alert 告警
     * @return 结果
     */
    public int insertAlert(Alert alert);

    /**
     * 添加或更新告警信息
     * @param alert
     * @return
     */
    int insertOrUpdateAlert(Alert alert);

    /**
     * 修改告警
     *
     * @param alert 告警
     * @return 结果
     */
    public int updateAlert(Alert alert);

    /**
     * 批量删除告警
     *
     * @param ids 需要删除的告警ID
     * @return 结果
     */
    public int deleteAlertByIds(Long[] ids);

    /**
     * 删除告警信息
     *
     * @param id 告警ID
     * @return 结果
     */
    public int deleteAlertById(Long id);

    /**
     * 批量逻辑删除告警
     *
     * @param ids 需要逻辑删除的告警ID
     * @return 结果
     */
    public int logicDeleteAlertByIds(Long[] ids);



    /**
     * 处理告警 - 使用结构体参数
     *
     * @param request 告警解决请求
     * @return 结果
     */
    public int resolveAlert(AlertResolutionRequest request);


    /**
     * 忽略告警 - 使用结构体参数
     *
     * @param request 告警解决请求
     * @return 结果
     */
    public int ignoreAlert(AlertResolutionRequest request);

    /**
     * 认领告警 - 使用结构体参数
     *
     * @param request 告警认领请求
     * @return 结果
     */
    public int acknowledgeAlerts(AlertAckRequest request);


    /**
     * 批量处理告警 - 使用结构体参数
     *
     * @param requests 告警解决请求列表
     * @return 处理结果
     */
    public Map<String, Object> batchProcessAlerts(List<AlertResolutionRequest> requests);

    /**
     * 处理Webhook推送的告警数据
     *
     * @param token 推送URL令牌
     * @param jsonData JSON数据
     * @return 处理结果数据
     */
    public Map<String, Object> processWebhookAlert(String token, String jsonData) throws Exception;

    /**
     * 接收单个告警
     *
     * @param alert 告警对象
     * @return 处理结果数据
     */
    public Map<String, Object> receiveAlert(Alert alert) throws Exception;

    /**
     * 批量接收告警
     *
     * @param alerts 告警对象数组
     * @return 处理结果数据
     */
    public Map<String, Object> receiveBatchAlerts(Alert[] alerts) throws Exception;

    /**
     * 获取告警统计数据
     *
     * @return 统计数据
     */
    public Map<String, Object> getAlertStatistics();

    /**
     * 获取告警趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> getAlertTrend(Integer days);

    /**
     * 获取告警分布数据
     *
     * @param type 分布类型（severity, status, sourceType等）
     * @return 分布数据
     */
    public List<Map<String, Object>> getAlertDistribution(String type);

    /**
     * 获取热点告警源
     *
     * @param limit 返回数量限制
     * @return 热点告警源列表
     */
    public List<Map<String, Object>> getTopAlertSources(Integer limit);

    /**
     * 获取告警处理统计
     *
     * @param days 统计天数
     * @return 处理统计数据
     */
    public Map<String, Object> getResolutionStatistics(Integer days);

    /**
     * 获取可用标签
     * 从现有告警中提取标签信息，用于前端选择器
     * 
     * @return 按类别分组的标签列表
     */
    public Map<String, List<String>> getAvailableTags();

    /**
     * 获取每日告警统计数据
     * 按天分组统计告警数量，用于30天内新增事件图表
     * 
     * @param days 统计天数
     * @return 每日统计数据列表
     */
    public List<Map<String, Object>> getDailyAlertStatistics(Integer days);

    /**
     * 获取最近入侵事件列表
     * 返回最近的告警事件，显示告警类型中文名和目标主机IP
     * 
     * @param limit 返回数量限制
     * @return 最近事件列表
     */
    public List<Map<String, Object>> getRecentIntrusionEvents(Integer limit);

    /**
     * 获取人员处理统计（近一周）
     * 统计每个人解决告警的数量和平均解决时间
     * 
     * @return 人员处理统计数据
     */
    public List<Map<String, Object>> getPersonnelResolutionStats();

    /**
     * 获取告警对象TOP排行
     * 统计告警对象的出现频次
     * 
     * @param limit 返回数量限制
     * @return 告警对象TOP排行
     */
    public List<Map<String, Object>> getTopTargets(Integer limit);

    /**
     * 根据告警源标识查询告警列表
     *
     * @param sourceIdent 告警源标识
     * @return 告警列表
     */
    List<Alert> selectAlertBySourceIdent(String sourceIdent);

    /**
     * 根据输入标识查询告警列表
     *
     * @param inputIdent 输入标识
     * @return 告警列表
     */
    List<Alert> selectAlertByInputIdent(String inputIdent);

    /**
     * 根据告警源标识和输入标识查询告警列表
     *
     * @param sourceIdent 告警源标识
     * @param inputIdent 输入标识
     * @return 告警列表
     */
    List<Alert> selectAlertBySourceIdentAndInputIdent(String sourceIdent, String inputIdent);
}
