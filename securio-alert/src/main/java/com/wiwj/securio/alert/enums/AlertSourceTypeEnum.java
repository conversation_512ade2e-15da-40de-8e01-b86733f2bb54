package com.wiwj.securio.alert.enums;

import lombok.Getter;
import java.util.Arrays;

/**
 * 告警源类型枚举
 * 每个告警源类型对应一个特定的适配器实现
 *
 * <AUTHOR>
 */
@Getter
public enum AlertSourceTypeEnum {
    //标准告警事件
    STANDARD("standard", "标准告警事件", DataCollectionTypeEnum.PUSH, false),

    //flashDuty 集成事件
    GRAFANA("grafana", "Grafana", DataCollectionTypeEnum.PUSH, false),
    ZABBIX("zabbix", "Zabbix", DataCollectionTypeEnum.PUSH, false),
    N9E("n9e", "夜莺监控", DataCollectionTypeEnum.PUSH, true),
    HUAWEI_CES("huawei_ces", "华为云CES", DataCollectionTypeEnum.PUSH, false),
    SKYWALKING("skywalking", "SkyWalking", DataCollectionTypeEnum.PUSH, false),

    //其他安全平台告警事件
    WANGSU_WAF("wangsu_waf", "网宿WAF", DataCollectionTypeEnum.PUSH, false),
    SAFELINE_WAF("safeline_waf", "雷池WAF", DataCollectionTypeEnum.PUSH, false),
    HONEYPOT("honeypot", "蜜罐", DataCollectionTypeEnum.PUSH, false),
    MUYUN("muyun", "牧云", DataCollectionTypeEnum.PUSH, false),
    TDP("tdp", "微步TDP", DataCollectionTypeEnum.PUSH, false),
    ZEEK("zeek", "Zeek", DataCollectionTypeEnum.PULL, false),
    SURICATA("suricata", "Suricata", DataCollectionTypeEnum.PULL, false);

    /** 类型编码 */
    private final String code;

    /** 类型名称 */
    private final String name;

    /** 数据获取方式 */
    private final DataCollectionTypeEnum collectionType;
    
    /** 是否支持自动恢复 */
    private final boolean supportAutoResolve;

    AlertSourceTypeEnum(String code, String name, DataCollectionTypeEnum collectionType, boolean supportAutoResolve) {
        this.code = code;
        this.name = name;
        this.collectionType = collectionType;
        this.supportAutoResolve = supportAutoResolve;
    }

    /**
     * 根据编码获取枚举
     */
    public static AlertSourceTypeEnum getByCode(String code) {
        for (AlertSourceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取字典数据
     */
    public static AlertSourceTypeEnum[] getDictData() {
        return values();
    }
}
