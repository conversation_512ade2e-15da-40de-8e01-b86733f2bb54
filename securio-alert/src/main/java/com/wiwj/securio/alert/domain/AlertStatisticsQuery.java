package com.wiwj.securio.alert.domain;

import lombok.Data;

/**
 * 告警统计查询参数
 *
 * <AUTHOR>
 */
@Data
public class AlertStatisticsQuery {

    /**
     * 查询开始时间（毫秒时间戳）
     */
    private Long startTime;

    /**
     * 查询结束时间（毫秒时间戳）
     */
    private Long endTime;

    /**
     * 时间范围数组（前端传递的时间选择器值）
     */
    private Long[] timeRange;

    /**
     * 处理时间范围参数
     * 将timeRange转换为startTime和endTime
     */
    public void processTimeRange() {
        if (timeRange != null && timeRange.length == 2) {
            this.startTime = timeRange[0];
            this.endTime = timeRange[1];
        }
    }
} 