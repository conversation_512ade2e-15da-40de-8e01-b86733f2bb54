package com.wiwj.securio.alert.util;

import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.HashMap;

@Slf4j
public class UrlParameterExtractor {
    
    /**
     * 从URL中提取指定参数的值
     * @param url 完整的URL字符串
     * @param paramName 要提取的参数名（如"ident"）
     * @return 参数值（如果存在），否则返回null
     */
    public static String extractParameter(String url, String paramName) {
        // 解析URL的查询部分
        URI uri = null;
        try {
            uri = new URI(url);
        } catch (URISyntaxException e) {
            log.error("url无效：",e);
            return "";
        }
        String query = uri.getQuery();
        
        if (query == null || query.isEmpty()) {
            return null;
        }
        
        // 分割查询参数
        String[] params = query.split("&");
        Map<String, String> paramMap = new HashMap<>();
        
        for (String param : params) {
            String[] keyValue = param.split("=");
            if (keyValue.length == 2) {
                paramMap.put(keyValue[0], keyValue[1]);
            }
        }
        
        // 返回指定参数值
        return paramMap.get(paramName);
    }

    public static void main(String[] args) {
        String testUrl = "https://example.com/api?user=123&ident=abc456&lang=en";
        String identValue = extractParameter(testUrl, "ident");
        System.out.println("提取的ident值: " + identValue);  // 输出: abc456
    }
}