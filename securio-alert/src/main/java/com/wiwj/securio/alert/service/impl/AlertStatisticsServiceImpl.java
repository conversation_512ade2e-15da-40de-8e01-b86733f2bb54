package com.wiwj.securio.alert.service.impl;

import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.AlertStatisticsQuery;
import com.wiwj.securio.alert.domain.DepartmentStatistics;
import com.wiwj.securio.alert.domain.TeamStatistics;
import com.wiwj.securio.alert.domain.UserStatistics;
import com.wiwj.securio.alert.enums.PerformanceLevel;
import com.wiwj.securio.alert.mapper.AlertMapper;
import com.wiwj.securio.alert.service.AlertStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 告警统计服务实现类
 *
 * <AUTHOR>
 */
@Service
public class AlertStatisticsServiceImpl implements AlertStatisticsService {

    @Autowired
    private AlertMapper alertMapper;

    @Override
    public DepartmentStatistics getDepartmentStatistics(AlertStatisticsQuery query) {
        // 处理时间范围参数
        query.processTimeRange();
        
        // 获取部门整体统计
        Map<String, Object> deptStats = alertMapper.selectDepartmentStatistics(query.getStartTime(), query.getEndTime());
        
        // 获取团队统计列表
        List<Map<String, Object>> teamStatsList = alertMapper.selectTeamStatistics(query.getStartTime(), query.getEndTime());
        
        // 构建部门统计对象
        DepartmentStatistics departmentStatistics = new DepartmentStatistics();
        departmentStatistics.setTotalAlerts(getIntegerValue(deptStats, "totalAlerts"));
        departmentStatistics.setCriticalAlerts(getIntegerValue(deptStats, "criticalAlerts"));
        departmentStatistics.setPendingAlerts(getIntegerValue(deptStats, "pendingAlerts"));
        departmentStatistics.setResolvedAlerts(getIntegerValue(deptStats, "resolvedAlerts"));
        departmentStatistics.setSecurityAlerts(getIntegerValue(deptStats, "securityAlerts"));
        departmentStatistics.setOpsAlerts(getIntegerValue(deptStats, "opsAlerts"));
        departmentStatistics.setMtta(getDoubleValue(deptStats, "mtta"));
        departmentStatistics.setMttr(getDoubleValue(deptStats, "mttr"));
        
        // 构建团队统计列表
        List<TeamStatistics> teams = new ArrayList<>();
        for (Map<String, Object> teamMap : teamStatsList) {
            TeamStatistics teamStatistics = buildTeamStatistics(teamMap, query);
            teams.add(teamStatistics);
        }
        departmentStatistics.setTeams(teams);
        
        return departmentStatistics;
    }

    @Override
    public List<Alert> getUserAlertDetails(String userId, AlertStatisticsQuery query) {
        // 处理时间范围参数
        query.processTimeRange();
        
        // 获取指定人员的告警明细
        return alertMapper.selectUserAlertDetails(userId, query.getStartTime(), query.getEndTime());
    }

    @Override
    public List<Map<String, Object>> getUserList() {
        return alertMapper.selectUserList();
    }

    /**
     * 构建团队统计对象
     */
    private TeamStatistics buildTeamStatistics(Map<String, Object> teamMap, AlertStatisticsQuery query) {
        TeamStatistics teamStatistics = new TeamStatistics();
        teamStatistics.setTeamId(getStringValue(teamMap, "teamId"));
        teamStatistics.setTeamName(getStringValue(teamMap, "teamName"));
        teamStatistics.setTotalAlerts(getIntegerValue(teamMap, "totalAlerts"));
        teamStatistics.setCriticalAlerts(getIntegerValue(teamMap, "criticalAlerts"));
        teamStatistics.setPendingAlerts(getIntegerValue(teamMap, "pendingAlerts"));
        teamStatistics.setResolvedAlerts(getIntegerValue(teamMap, "resolvedAlerts"));
        teamStatistics.setMtta(getDoubleValue(teamMap, "mtta"));
        teamStatistics.setMttr(getDoubleValue(teamMap, "mttr"));
        
        // 获取团队下的人员统计
        List<Map<String, Object>> userStatsList = alertMapper.selectUserStatisticsByTeam(
            teamStatistics.getTeamId(), query.getStartTime(), query.getEndTime());
        
        List<UserStatistics> users = new ArrayList<>();
        for (Map<String, Object> userMap : userStatsList) {
            UserStatistics userStatistics = buildUserStatistics(userMap);
            users.add(userStatistics);
        }
        teamStatistics.setUsers(users);
        
        return teamStatistics;
    }

    /**
     * 构建人员统计对象
     */
    private UserStatistics buildUserStatistics(Map<String, Object> userMap) {
        UserStatistics userStatistics = new UserStatistics();
        userStatistics.setUserId(getStringValue(userMap, "userId"));
        userStatistics.setUserName(getStringValue(userMap, "userName"));
        userStatistics.setTeamId(getStringValue(userMap, "teamId"));
        userStatistics.setTeamName(getStringValue(userMap, "teamName"));
        userStatistics.setTotalAlerts(getIntegerValue(userMap, "totalAlerts"));
        userStatistics.setCriticalAlerts(getIntegerValue(userMap, "criticalAlerts"));
        userStatistics.setPendingAlerts(getIntegerValue(userMap, "pendingAlerts"));
        userStatistics.setResolvedAlerts(getIntegerValue(userMap, "resolvedAlerts"));
        userStatistics.setMtta(getDoubleValue(userMap, "mtta"));
        userStatistics.setMttr(getDoubleValue(userMap, "mttr"));
        
        // 设置性能等级
        userStatistics.setPerformanceLevel();
        
        return userStatistics;
    }

    /**
     * 安全获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 安全获取Double值
     */
    private Double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0.0;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 安全获取String值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value == null ? "" : value.toString();
    }
} 