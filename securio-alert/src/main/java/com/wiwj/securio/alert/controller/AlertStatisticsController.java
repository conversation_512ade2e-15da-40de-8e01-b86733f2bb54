package com.wiwj.securio.alert.controller;

import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.AlertStatisticsQuery;
import com.wiwj.securio.alert.domain.DepartmentStatistics;
import com.wiwj.securio.alert.service.AlertStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 告警统计Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/alert/statistics")
@Slf4j
public class AlertStatisticsController extends BaseController {

    @Autowired
    private AlertStatisticsService alertStatisticsService;

    /**
     * 获取运维部整体统计
     */
    @PreAuthorize("@ss.hasPermi('alert:statistics:list')")
    @GetMapping("/department")
    public AjaxResult getDepartmentStatistics(AlertStatisticsQuery query) {
        try {
            DepartmentStatistics statistics = alertStatisticsService.getDepartmentStatistics(query);
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            logger.error("获取部门统计失败", e);
            return AjaxResult.error("获取部门统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定人员的告警明细
     */
    @PreAuthorize("@ss.hasPermi('alert:statistics:list')")
    @GetMapping("/user/{userId}/details")
    public AjaxResult getUserAlertDetails(@PathVariable String userId, AlertStatisticsQuery query) {
        try {
            List<Alert> alerts = alertStatisticsService.getUserAlertDetails(userId, query);
            return AjaxResult.success(alerts);
        } catch (Exception e) {
            logger.error("获取人员告警明细失败", e);
            return AjaxResult.error("获取人员告警明细失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户列表（用于下拉选择）
     */
    @GetMapping("/users")
    public AjaxResult getUserList() {
        try {
            List<Map<String, Object>> users = alertStatisticsService.getUserList();
            return AjaxResult.success(users);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return AjaxResult.error("获取用户列表失败");
        }
    }
} 