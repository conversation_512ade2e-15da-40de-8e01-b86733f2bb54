package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;

import java.util.List;

/**
 * FlashDuty故障解决请求
 *
 * <AUTHOR>
 */
@Data
public class FlashDutyIncidentResolveRequest {

    /** 故障ID列表，必需，最多100项 */
    private List<String> incident_ids;

    /** 根因，可选 */
    private String root_cause;

    /** 解决办法，可选 */
    private String resolution;

    /**
     * 构造函数
     */
    public FlashDutyIncidentResolveRequest() {
    }

    /**
     * 构造函数
     * 
     * @param incident_ids 故障ID列表
     * @param root_cause 根因
     * @param resolution 解决办法
     */
    public FlashDutyIncidentResolveRequest(List<String> incident_ids, String root_cause, String resolution) {
        this.incident_ids = incident_ids;
        this.root_cause = root_cause;
        this.resolution = resolution;
    }
} 