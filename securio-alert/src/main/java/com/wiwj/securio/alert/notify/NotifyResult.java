package com.wiwj.securio.alert.notify;

/**
 * 通知结果
 * 
 * <AUTHOR>
 */
public class NotifyResult {
    
    /** 是否成功 */
    private boolean success;
    
    /** 消息ID */
    private String messageId;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 原始响应 */
    private String rawResponse;

    /**
     * 创建成功结果
     * 
     * @param messageId 消息ID
     * @return 通知结果
     */
    public static NotifyResult success(String messageId) {
        NotifyResult result = new NotifyResult();
        result.setSuccess(true);
        result.setMessageId(messageId);
        return result;
    }
    
    /**
     * 创建失败结果
     * 
     * @param errorMessage 错误信息
     * @return 通知结果
     */
    public static NotifyResult failure(String errorMessage) {
        NotifyResult result = new NotifyResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getRawResponse() {
        return rawResponse;
    }

    public void setRawResponse(String rawResponse) {
        this.rawResponse = rawResponse;
    }
}
