package com.wiwj.securio.alert.enums;

import lombok.Getter;

/**
 * 告警状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AlertStatusEnum {

    TRIGGERED("triggered", "触发", "告警规则被激活，尚未处理"),
    ACKNOWLEDGED("acknowledged", "确认", "运维人员已确认告警，正在处理中"),
    RESOLVED("resolved", "已解决", "问题修复，告警关闭"),
    SUPPRESSED("suppressed", "沉默", "告警被临时抑制或静默");

    /** 状态编码 */
    private final String code;

    /** 状态名称 */
    private final String name;

    /** 状态描述 */
    private final String description;

    AlertStatusEnum(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据编码获取枚举
     */
    public static AlertStatusEnum getByCode(String code) {
        for (AlertStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断告警是否已结束
     */
    public boolean isEnded() {
        return this == RESOLVED || this == SUPPRESSED;
    }

    /**
     * 判断告警是否需要处理
     */
    public boolean needsAction() {
        return this == TRIGGERED;
    }
}
