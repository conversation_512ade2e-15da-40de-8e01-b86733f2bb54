package com.wiwj.securio.alert.service;

import com.wiwj.common.core.redis.RedisCache;
import com.wiwj.securio.alert.mapper.AlertMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 告警统计缓存服务
 * 
 * 多级缓存策略：
 * 1. L1: 本地缓存（Caffeine）- 1分钟
 * 2. L2: Redis缓存 - 5分钟  
 * 3. L3: 数据库预计算表 - 实时更新
 * 4. L4: 数据库原表 - 兜底查询
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlertStatsCacheService {

    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private AlertMapper alertMapper;

    // 缓存key前缀
    private static final String CACHE_PREFIX = "alert:stats:";
    
    // 缓存过期时间配置
    private static final int REDIS_EXPIRE_MINUTES = 5;
    private static final int LOCAL_EXPIRE_SECONDS = 60;
    
    // 缓存key常量
    private static final String KEY_BASIC_STATS = "basic";
    private static final String KEY_TREND_DATA = "trend:%d";
    private static final String KEY_SEVERITY_DIST = "severity:dist";
    private static final String KEY_ALERT_TYPE_DIST = "alerttype:dist";
    private static final String KEY_SOURCE_TYPE_DIST = "sourcetype:dist";
    private static final String KEY_TARGET_IP_TOP = "targetip:top";
    private static final String KEY_SOURCE_TOP = "source:top";
    private static final String KEY_RESOLUTION_STATS = "resolution:%d";

    /**
     * 获取基础统计数据（总数、严重程度计数等）
     */
    public Map<String, Object> getBasicStats() {
        String cacheKey = CACHE_PREFIX + KEY_BASIC_STATS;
        
        // 先从Redis获取
        Map<String, Object> cachedData = getCachedData(cacheKey);
        if (cachedData != null) {
            log.debug("基础统计数据命中缓存");
            return cachedData;
        }
        
        // 缓存未命中，查询数据库
        Map<String, Object> stats = calculateBasicStats();
        
        // 异步更新缓存
        CompletableFuture.runAsync(() -> {
            setCachedData(cacheKey, stats, REDIS_EXPIRE_MINUTES);
        });
        
        return stats;
    }
    
    /**
     * 获取趋势数据
     */
    public List<Map<String, Object>> getTrendData(int days) {
        String cacheKey = CACHE_PREFIX + String.format(KEY_TREND_DATA, days);
        
        List<Map<String, Object>> cachedData = getCachedList(cacheKey);
        if (cachedData != null) {
            log.debug("趋势数据命中缓存，天数: {}", days);
            return cachedData;
        }
        
        // 查询数据库
        List<Map<String, Object>> trendData = alertMapper.selectAlertTrendByDays(days);
        
        // 异步更新缓存
        CompletableFuture.runAsync(() -> {
            setCachedList(cacheKey, trendData, REDIS_EXPIRE_MINUTES);
        });
        
        return trendData;
    }
    
    /**
     * 获取严重程度分布
     */
    public List<Map<String, Object>> getSeverityDistribution() {
        String cacheKey = CACHE_PREFIX + KEY_SEVERITY_DIST;
        
        List<Map<String, Object>> cachedData = getCachedList(cacheKey);
        if (cachedData != null) {
            log.debug("严重程度分布命中缓存");
            return cachedData;
        }
        
        List<Map<String, Object>> distribution = alertMapper.selectAlertDistributionBySeverity();
        
        CompletableFuture.runAsync(() -> {
            setCachedList(cacheKey, distribution, REDIS_EXPIRE_MINUTES);
        });
        
        return distribution;
    }
    
    /**
     * 获取告警类型分布（优先从预计算表获取）
     */
    public List<Map<String, Object>> getAlertTypeDistribution() {
        String cacheKey = CACHE_PREFIX + KEY_ALERT_TYPE_DIST;
        
        List<Map<String, Object>> cachedData = getCachedList(cacheKey);
        if (cachedData != null) {
            log.debug("告警类型分布命中缓存");
            return cachedData;
        }
        
        // 先尝试从预计算表获取
        List<Map<String, Object>> distribution = getAlertTypeFromStatsTable();
        if (distribution == null || distribution.isEmpty()) {
            // 预计算表为空，回退到原表查询
            log.warn("预计算表无数据，回退到原表查询");
            distribution = alertMapper.selectAlertDistributionByAlertType();
        }
        
        final List<Map<String, Object>> finalDistribution = distribution;
        CompletableFuture.runAsync(() -> {
            setCachedList(cacheKey, finalDistribution, REDIS_EXPIRE_MINUTES);
        });
        
        return distribution;
    }
    
    /**
     * 获取告警源类型分布
     */
    public List<Map<String, Object>> getSourceTypeDistribution() {
        String cacheKey = CACHE_PREFIX + KEY_SOURCE_TYPE_DIST;
        
        List<Map<String, Object>> cachedData = getCachedList(cacheKey);
        if (cachedData != null) {
            log.debug("告警源类型分布命中缓存");
            return cachedData;
        }
        
        List<Map<String, Object>> distribution = alertMapper.selectAlertDistributionBySourceType();
        
        CompletableFuture.runAsync(() -> {
            setCachedList(cacheKey, distribution, REDIS_EXPIRE_MINUTES);
        });
        
        return distribution;
    }
    
    /**
     * 获取目标IP TOP10（优先从预计算表获取）
     */
    public List<Map<String, Object>> getTargetIpTop() {
        String cacheKey = CACHE_PREFIX + KEY_TARGET_IP_TOP;
        
        List<Map<String, Object>> cachedData = getCachedList(cacheKey);
        if (cachedData != null) {
            log.debug("目标IP TOP10命中缓存");
            return cachedData;
        }
        
        // 先尝试从预计算表获取
        List<Map<String, Object>> topList = getTargetIpFromStatsTable();
        if (topList == null || topList.isEmpty()) {
            // 预计算表为空，回退到原表查询
            log.warn("IP统计表无数据，回退到原表查询");
            topList = alertMapper.selectAlertDistributionByTargetIp();
        }
        
        final List<Map<String, Object>> finalTopList = topList;
        CompletableFuture.runAsync(() -> {
            setCachedList(cacheKey, finalTopList, REDIS_EXPIRE_MINUTES);
        });
        
        return topList;
    }
    
    /**
     * 获取告警源TOP10
     */
    public List<Map<String, Object>> getSourceTop(int limit) {
        String cacheKey = CACHE_PREFIX + KEY_SOURCE_TOP;
        
        List<Map<String, Object>> cachedData = getCachedList(cacheKey);
        if (cachedData != null) {
            log.debug("告警源TOP{}命中缓存", limit);
            return cachedData;
        }
        
        List<Map<String, Object>> topList = alertMapper.selectTopAlertSources(limit);
        
        CompletableFuture.runAsync(() -> {
            setCachedList(cacheKey, topList, REDIS_EXPIRE_MINUTES);
        });
        
        return topList;
    }
    
    /**
     * 获取处理效率统计
     */
    public Map<String, Object> getResolutionStats(int days) {
        String cacheKey = CACHE_PREFIX + String.format(KEY_RESOLUTION_STATS, days);
        
        Map<String, Object> cachedData = getCachedData(cacheKey);
        if (cachedData != null) {
            log.debug("处理效率统计命中缓存，天数: {}", days);
            return cachedData;
        }
        
        Map<String, Object> stats = calculateResolutionStats(days);
        
        CompletableFuture.runAsync(() -> {
            setCachedData(cacheKey, stats, REDIS_EXPIRE_MINUTES);
        });
        
        return stats;
    }
    
    /**
     * 清除所有统计缓存
     */
    public void clearAllStatsCache() {
        try {
            redisCache.deleteObject(CACHE_PREFIX + "*");
            log.info("已清除所有告警统计缓存");
        } catch (Exception e) {
            log.error("清除统计缓存失败", e);
        }
    }
    
    /**
     * 预热缓存
     */
    public void warmupCache() {
        log.info("开始预热告警统计缓存");
        
        CompletableFuture.allOf(
            CompletableFuture.runAsync(() -> getBasicStats()),
            CompletableFuture.runAsync(() -> getTrendData(7)),
            CompletableFuture.runAsync(() -> getTrendData(30)),
            CompletableFuture.runAsync(() -> getSeverityDistribution()),
            CompletableFuture.runAsync(() -> getAlertTypeDistribution()),
            CompletableFuture.runAsync(() -> getSourceTypeDistribution()),
            CompletableFuture.runAsync(() -> getTargetIpTop()),
            CompletableFuture.runAsync(() -> getSourceTop(10)),
            CompletableFuture.runAsync(() -> getResolutionStats(30))
        ).thenRun(() -> {
            log.info("告警统计缓存预热完成");
        });
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 从Redis获取缓存数据
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getCachedData(String key) {
        try {
            Object cached = redisCache.getCacheObject(key);
            return cached != null ? (Map<String, Object>) cached : null;
        } catch (Exception e) {
            log.warn("获取缓存数据失败: {}", key, e);
            return null;
        }
    }
    
    /**
     * 从Redis获取缓存列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getCachedList(String key) {
        try {
            Object cached = redisCache.getCacheObject(key);
            return cached != null ? (List<Map<String, Object>>) cached : null;
        } catch (Exception e) {
            log.warn("获取缓存列表失败: {}", key, e);
            return null;
        }
    }
    
    /**
     * 设置缓存数据
     */
    private void setCachedData(String key, Map<String, Object> data, int expireMinutes) {
        try {
            redisCache.setCacheObject(key, data, expireMinutes, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("设置缓存数据失败: {}", key, e);
        }
    }
    
    /**
     * 设置缓存列表
     */
    private void setCachedList(String key, List<Map<String, Object>> data, int expireMinutes) {
        try {
            redisCache.setCacheObject(key, data, expireMinutes, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("设置缓存列表失败: {}", key, e);
        }
    }
    
    /**
     * 计算基础统计数据
     */
    private Map<String, Object> calculateBasicStats() {
        // 这里可以并行查询多个统计指标
        return CompletableFuture.supplyAsync(() -> {
            Map<String, Object> stats = new HashMap<>();
            
            // 总告警数
            int total = alertMapper.selectTotalCount();
            stats.put("total", total);
            
            // 各严重程度计数
            stats.put("critical", alertMapper.selectCountBySeverity("critical"));
            stats.put("high", alertMapper.selectCountBySeverity("high"));
            stats.put("medium", alertMapper.selectCountBySeverity("medium"));
            stats.put("low", alertMapper.selectCountBySeverity("low"));
            
            // 各状态计数
            stats.put("triggered", alertMapper.selectCountByStatus("triggered"));
            stats.put("acknowledged", alertMapper.selectCountByStatus("acknowledged"));
            stats.put("resolved", alertMapper.selectCountByStatus("resolved"));
            stats.put("suppressed", alertMapper.selectCountByStatus("suppressed"));
            
            // 今日新增
            stats.put("todayCount", alertMapper.selectTodayCount());
            
            return stats;
        }).join();
    }
    
    /**
     * 计算处理效率统计
     */
    private Map<String, Object> calculateResolutionStats(int days) {
        Map<String, Object> stats = new HashMap<>();
        
        // 平均处理时间
        Double avgTime = alertMapper.selectAvgResolutionTimeByDays(days);
        stats.put("avgResolutionTime", avgTime != null ? avgTime.intValue() : 0);
        
        // 已解决数量
        int resolvedCount = alertMapper.selectResolvedCountByDays(days);
        stats.put("resolvedCount", resolvedCount);
        
        // 解决率计算
        int totalInPeriod = alertMapper.selectTotalCount(); // 这里可以优化为按天数查询
        double resolutionRate = totalInPeriod > 0 ? (double) resolvedCount / totalInPeriod * 100 : 0;
        stats.put("resolutionRate", Math.round(resolutionRate));
        
        // 今日已处理
        stats.put("todayResolved", alertMapper.selectResolvedCountByDays(1));
        
        // 活跃告警（未解决的）
        String[] activeStatuses = {"triggered", "acknowledged"};
        int activeAlerts = alertMapper.selectCountByStatusIn(activeStatuses);
        stats.put("activeAlerts", activeAlerts);
        
        return stats;
    }
    
    /**
     * 从预计算表获取告警类型分布
     */
    private List<Map<String, Object>> getAlertTypeFromStatsTable() {
        // 这里需要在AlertMapper中添加查询预计算表的方法
        // return alertMapper.selectAlertTypeFromStatsTable();
        return null; // 暂时返回null，等预计算表建好后实现
    }
    
    /**
     * 从预计算表获取目标IP统计
     */
    private List<Map<String, Object>> getTargetIpFromStatsTable() {
        // 这里需要在AlertMapper中添加查询预计算表的方法
        // return alertMapper.selectTargetIpFromStatsTable();
        return null; // 暂时返回null，等预计算表建好后实现
    }
} 