package com.wiwj.securio.alert.service;

import com.alibaba.fastjson2.JSON;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.AlertOutputConfig;
import com.wiwj.securio.alert.integration.flashduty.service.FlashDutyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 告警输出处理服务
 *
 * <AUTHOR>
 */
@Service
public class AlertOutputService {

    private static final Logger logger = LoggerFactory.getLogger(AlertOutputService.class);

    @Autowired
    private IAlertOutputConfigService alertOutputConfigService;

    @Autowired
    private FlashDutyService flashDutyService;


    /**
     * 处理告警输出
     * 注意：这个方法已经不再使用，保留是为了兼容旧的代码
     *
     * @param alert 告警对象
     * @return 处理结果
     */
    @Deprecated
    public boolean processAlertOutput(Alert alert) {
        logger.warn("调用了已废弃的方法 processAlertOutput，告警ID: {}", alert.getId());
        return false;
    }

    /**
     * 查询匹配的告警输出配置
     *
     * @param queryConfig 查询条件
     * @return 匹配的告警输出配置列表
     */
    public List<AlertOutputConfig> selectMatchingOutputConfigs(AlertOutputConfig queryConfig) {
        return alertOutputConfigService.selectMatchingOutputConfigs(queryConfig);
    }

    /**
     * 检查告警是否匹配标签过滤规则
     *
     * @param alert          告警对象
     * @param tagFilterRules 标签过滤规则
     * @return 是否匹配
     */
    private boolean matchTagFilter(Alert alert, String tagFilterRules) {
        if (tagFilterRules == null || tagFilterRules.isEmpty()) {
            return true;
        }

        try {
            // 解析标签过滤规则
            Map<String, Object> rules = JSON.parseObject(tagFilterRules);

            // 实现标签匹配逻辑
            // 示例：检查告警严重程度是否匹配
            if (rules.containsKey("severity")) {
                String severity = rules.get("severity").toString();
                if (!severity.equals(alert.getSeverity())) {
                    return false;
                }
            }

            // 示例：检查告警状态是否匹配
            if (rules.containsKey("status")) {
                String status = rules.get("status").toString();
                if (!status.equals(alert.getStatus())) {
                    return false;
                }
            }

            // 检查告警标签是否匹配
            if (rules.containsKey("tags") && alert.getTags() != null) {
                Map<String, String> tagRules = (Map<String, String>) rules.get("tags");
                Map<String, String> alertTags = JSON.parseObject(alert.getTags(), Map.class);

                for (Map.Entry<String, String> entry : tagRules.entrySet()) {
                    String tagKey = entry.getKey();
                    String tagValue = entry.getValue();

                    if (!alertTags.containsKey(tagKey) || !tagValue.equals(alertTags.get(tagKey))) {
                        return false;
                    }
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("解析标签过滤规则时发生错误", e);
            return false;
        }
    }

    /**
     * 发送告警
     *
     * @param alert        告警对象
     * @param outputConfig 输出配置
     * @return 是否成功
     */
    private boolean sendAlert(Alert alert, AlertOutputConfig outputConfig) {
        // 直接发送到 FlashDuty
        String webhookUrl = outputConfig.getOutputWebhookUrl();
        if (webhookUrl == null || webhookUrl.isEmpty()) {
            logger.error("未配置 Webhook URL，输出配置ID: {}", outputConfig.getId());
            return false;
        }

        return flashDutyService.sendFlashDutyStandardAlert(alert, webhookUrl);
    }

    /**
     * 转发原始数据
     *
     * @param alert        告警对象
     * @param outputConfig 输出配置
     * @return 是否成功
     */
    private boolean forwardRawData(Alert alert, AlertOutputConfig outputConfig) {
        // 获取原始数据
        String rawData = alert.getRawData();
        if (rawData == null || rawData.isEmpty()) {
            logger.warn("告警原始数据为空，无法转发，告警ID: {}", alert.getId());
            return false;
        }

        // 获取 Webhook URL
        String webhookUrl = outputConfig.getOutputWebhookUrl();
        if (webhookUrl == null || webhookUrl.isEmpty()) {
            logger.error("未配置 Webhook URL，输出配置ID: {}", outputConfig.getId());
            return false;
        }

        // 调用 FlashDutyService 的方法转发原始数据
        return flashDutyService.forwardRawDataToFlashDuty(rawData, webhookUrl, alert.getId());
    }


}
