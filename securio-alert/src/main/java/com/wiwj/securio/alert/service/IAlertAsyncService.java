package com.wiwj.securio.alert.service;

import com.wiwj.securio.alert.domain.Alert;

/**
 * 告警异步任务服务接口
 *
 * <AUTHOR>
 */
public interface IAlertAsyncService {

    /**
     * 异步拉取FlashDuty故障信息并回填告警
     *
     * @param alert 告警信息
     */
    void asyncSyncFlashDutyIncident(Alert alert);

    /**
     * 根据eventId查询FlashDuty故障信息并回填告警
     *
     * @param alertId 告警ID
     * @param eventId 事件ID
     * @return true 如果成功同步（包含分派人员），false 如果同步失败或没有分派人员
     */
    boolean syncFlashDutyIncidentByEventId(Long alertId, String eventId);

    /**
     * 批量同步最近三天告警的FlashDuty故障信息
     * 查询条件：fdIncidentId为空 或 assignedUserIds为空
     * 用于定时调度任务
     *
     * @return 同步处理的告警数量
     */
    int batchSyncFlashDutyIncidentsForRecentAlerts();
} 