package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class PageIncidentResponseData {

    /**
     * 告警项列表
     */
    private List<IncidentItem> items;

    /**
     * 游标分页上下文
     */
    private String search_after_ctx;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 是否有下一页
     */
    private Boolean has_next_page;

    @Data
    public static class IncidentItem {
        /**
         * 故障ID
         */
        private String incident_id;

        /**
         * 开始时间(秒)
         */
        private Long start_time;

        /**
         * 结束时间(秒)
         */
        private Long end_time;

        /**
         * 最后更新时间(秒)
         */
        private Long last_time;

        /**
         * 确认时间(秒)
         */
        private Long ack_time;

        /**
         * 关闭时间(秒)
         */
        private Long close_time;

        /**
         * 暂缓截止时间(秒)
         */
        private Long snoozed_before;

        /**
         * 故障状态
         */
        private String incident_status;

        /**
         * 严重程度：Critical,Warning,Info
         */
        private String incident_severity;

        /**
         * 处理进度：Triggered,Processing,Closed
         */
        private String progress;

        /**
         * 创建人ID
         */
        private Integer creator_id;

        /**
         * 关闭人ID
         */
        private Long closer_id;

        /**
         * 分配信息
         */
        private AssignedTo assigned_to;

        /**
         * 处理人列表
         */
        private List<Responder> responders;

        /**
         * 告警数量
         */
        private Integer alert_cnt;

        /**
         * 标题
         */
        private String title;

        /**
         * 描述
         */
        private String description;

        /**
         * 影响
         */
        private String impact;

        /**
         * 根因
         */
        private String root_cause;

        /**
         * 解决方案
         */
        private String resolution;

        /**
         * 编号
         */
        private String num;

        /**
         * 标签
         */
        private Map<String, String> labels;

        /**
         * 协作空间ID
         */
        private Long channel_id;

        /**
         * 协作空间名称
         */
        private String channel_name;

        /**
         * 数据源ID
         */
        private Long data_source_id;

        /**
         * 数据源类型
         */
        private String data_source_type;

        /**
         * 去重键
         */
        private String dedup_key;

        /**
         * 分组方法
         */
        private String group_method;

        /**
         * 是否曾经被静音
         */
        private Boolean ever_muted;

        /**
         * 频率
         */
        private String frequency;
    }

    @Data
    public static class AssignedTo {
        /**
         * 人员ID列表
         */
        private List<Long> person_ids;

        /**
         * 升级规则ID
         */
        private String escalate_rule_id;

        /**
         * 升级规则名称
         */
        private String escalate_rule_name;

        /**
         * 层级索引
         */
        private Integer layer_idx;

        /**
         * 层级人员重置信息
         */
        private Map<String, LayerPersonReset> layer_person_reset;

        /**
         * 类型：assign
         */
        private String type;
    }

    @Data
    public static class LayerPersonReset {
        /**
         * 团队ID列表
         */
        private List<Long> team_ids;

        /**
         * 人员ID列表
         */
        private List<Long> person_ids;

        /**
         * 排班到角色ID的映射
         */
        private Map<String, List<Integer>> schedule_to_role_ids;
    }

    @Data
    public static class Responder {
        /**
         * 人员ID
         */
        private Long person_id;

        /**
         * 分配时间(秒)
         */
        private Long assigned_at;

        /**
         * 确认时间(秒)
         */
        private Long acknowledged_at;
    }
}
