package com.wiwj.securio.alert.domain;

import lombok.Data;

import java.util.List;

/**
 * 告警认领请求参数
 *
 * <AUTHOR>
 */
@Data
public class AlertAckRequest {
    
    /**
     * 告警ID列表
     */
    private List<Long> alertIds;
    
    /**
     * 认领人ID
     */
    private String ackedBy;
    
    /**
     * 认领人名称
     */
    private String ackedByName;
    
    /**
     * FlashDuty 事件ID列表（可选）
     * 如果提供此字段，则会调用 FlashDuty 认领接口
     */
    private List<String> flashDutyIncidentIds;
    
    /**
     * 构造方法
     */
    public AlertAckRequest() {
    }
    
    /**
     * 全参数构造方法
     */
    public AlertAckRequest(List<Long> alertIds, String ackedBy, String ackedByName, List<String> flashDutyIncidentIds) {
        this.alertIds = alertIds;
        this.ackedBy = ackedBy;
        this.ackedByName = ackedByName;
        this.flashDutyIncidentIds = flashDutyIncidentIds;
    }
    
    /**
     * 静态工厂方法 - 创建认领告警请求
     */
    public static AlertAckRequest createAckRequest(List<Long> alertIds, String ackedBy, String ackedByName) {
        AlertAckRequest request = new AlertAckRequest();
        request.setAlertIds(alertIds);
        request.setAckedBy(ackedBy);
        request.setAckedByName(ackedByName);
        return request;
    }
    
    /**
     * 静态工厂方法 - 创建认领告警请求（包含FlashDuty事件ID）
     */
    public static AlertAckRequest createAckRequest(List<Long> alertIds, String ackedBy, String ackedByName, List<String> flashDutyIncidentIds) {
        AlertAckRequest request = new AlertAckRequest();
        request.setAlertIds(alertIds);
        request.setAckedBy(ackedBy);
        request.setAckedByName(ackedByName);
        request.setFlashDutyIncidentIds(flashDutyIncidentIds);
        return request;
    }
    
    /**
     * 检查是否需要调用FlashDuty接口
     */
    public boolean shouldCallFlashDuty() {
        return flashDutyIncidentIds != null && !flashDutyIncidentIds.isEmpty();
    }
} 