package com.wiwj.securio.alert.integration.flashduty.model;

import lombok.Data;

import java.util.List;

/**
 * FlashDuty故障认领请求
 *
 * <AUTHOR>
 */
@Data
public class FlashDutyIncidentAckRequest {

    /** 故障ID列表，必需 */
    private List<String> incident_ids;

    /**
     * 构造函数
     */
    public FlashDutyIncidentAckRequest() {
    }

    /**
     * 构造函数
     * 
     * @param incident_ids 故障ID列表
     */
    public FlashDutyIncidentAckRequest(List<String> incident_ids) {
        this.incident_ids = incident_ids;
    }
} 