package com.wiwj.securio.alert.enums;

import lombok.Getter;

/**
 * 告警类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum AlertTypeEnum {
    
    // ==================== 基础设施类型 ====================
    /** 系统性能 */
    SYSTEM_PERFORMANCE("system_performance", "系统性能", "CPU、内存、磁盘等系统资源告警"),
    
    /** 网络监控 */
    NETWORK_MONITORING("network_monitoring", "网络监控", "网络连接、带宽、延迟等网络告警"),
    
    /** 基础设施 */
    INFRASTRUCTURE("infrastructure", "基础设施", "服务器、存储、网络设备等基础设施告警"),
    
    // ==================== 应用类型 ====================
    /** 应用性能 */
    APPLICATION_PERFORMANCE("application_performance", "应用性能", "应用响应时间、吞吐量等性能告警"),
    
    /** 微服务 */
    MICROSERVICE("microservice", "微服务", "微服务架构相关的告警"),
    
    /** API监控 */
    API_MONITORING("api_monitoring", "API监控", "API接口可用性、性能等告警"),
    
    /** 业务监控 */
    BUSINESS("business", "业务监控", "业务指标、用户体验等业务告警"),
    
    // ==================== 数据库类型 ====================
    /** 数据库性能 */
    DATABASE_PERFORMANCE("database_performance", "数据库性能", "数据库性能、连接数等告警"),
    
    /** 数据库连接 */
    DATABASE_CONNECTION("database_connection", "数据库连接", "数据库连接异常告警"),
    
    // ==================== 安全类型 ====================
    /** Web安全 */
    WEB_SECURITY("web_security", "Web安全", "Web应用安全威胁告警"),
    
    /** 网络安全 */
    NETWORK_SECURITY("network_security", "网络安全", "网络层安全威胁告警"),
    
    /** 主机安全 */
    HOST_SECURITY("host_security", "主机安全", "主机入侵、恶意软件等安全告警"),
    
    /** 安全事件 */
    SECURITY("security", "安全事件", "通用安全事件告警"),
    
    // ==================== 容器和云原生类型 ====================
    /** Kubernetes */
    KUBERNETES("kubernetes", "Kubernetes", "K8s集群、Pod、Service等告警"),
    
    /** 容器监控 */
    CONTAINER_MONITORING("container_monitoring", "容器监控", "Docker容器相关告警"),
    
    // ==================== 日志和错误类型 ====================
    /** 错误日志 */
    ERROR_LOG("error_log", "错误日志", "应用错误日志告警"),
    
    /** 异常监控 */
    EXCEPTION_MONITORING("exception_monitoring", "异常监控", "应用异常、崩溃等告警"),
    
    // ==================== 构建和部署类型 ====================
    /** 构建失败 */
    BUILD("build", "构建失败", "CI/CD构建、部署失败告警"),
    
    /** 部署监控 */
    DEPLOYMENT("deployment", "部署监控", "应用部署相关告警"),
    
    // ==================== 其他类型 ====================
    /** 通用系统 */
    GENERIC_SYSTEM("generic_system", "通用系统", "通用系统告警"),
    
    /** 测试告警 */
    TEST_ALERT("test_alert", "测试告警", "测试用途的告警"),
    
    /** 自定义 */
    CUSTOM("custom", "自定义", "用户自定义类型告警"),
    
    /** 未知类型 */
    UNKNOWN("unknown", "未知类型", "未分类的告警类型");
    
    /** 类型编码 */
    private final String code;
    
    /** 类型名称 */
    private final String name;
    
    /** 类型描述 */
    private final String description;
    
    AlertTypeEnum(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据编码获取枚举
     */
    public static AlertTypeEnum getByCode(String code) {
        for (AlertTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return UNKNOWN; // 默认返回未知类型
    }
    
    /**
     * 获取 ElementUI 的标签类型
     */
    public String getElementUIType() {
        switch (this) {
            case SECURITY:
            case WEB_SECURITY:
            case NETWORK_SECURITY:
            case HOST_SECURITY:
                return "danger";
            case SYSTEM_PERFORMANCE:
            case DATABASE_PERFORMANCE:
            case APPLICATION_PERFORMANCE:
                return "warning";
            case MICROSERVICE:
            case API_MONITORING:
            case KUBERNETES:
                return "primary";
            case BUSINESS:
            case BUILD:
            case DEPLOYMENT:
                return "success";
            case TEST_ALERT:
                return "info";
            default:
                return "";
        }
    }
}
