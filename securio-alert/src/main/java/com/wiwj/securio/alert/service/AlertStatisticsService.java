package com.wiwj.securio.alert.service;

import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.AlertStatisticsQuery;
import com.wiwj.securio.alert.domain.DepartmentStatistics;

import java.util.List;
import java.util.Map;

/**
 * 告警统计服务接口
 *
 * <AUTHOR>
 */
public interface AlertStatisticsService {

    /**
     * 获取运维部整体统计
     */
    DepartmentStatistics getDepartmentStatistics(AlertStatisticsQuery query);

    /**
     * 获取指定人员的告警明细
     */
    List<Alert> getUserAlertDetails(String userId, AlertStatisticsQuery query);

    /**
     * 获取用户列表（用于下拉选择）
     */
    List<Map<String, Object>> getUserList();
} 