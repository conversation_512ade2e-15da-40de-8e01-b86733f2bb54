package com.wiwj.securio.alert.notify;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.wiwj.securio.alert.domain.AlertData;
import com.wiwj.securio.alert.domain.NotificationChannel;
import com.wiwj.securio.alert.domain.NotificationRule;
import com.wiwj.securio.alert.domain.NotificationTemplate;

/**
 * 通知管理器
 *
 * <AUTHOR>
 */
@Service
public class NotifyManager {

    private static final Logger logger = LoggerFactory.getLogger(NotifyManager.class);

    /** 通知渠道映射 */
    private final Map<String, NotifyChannel> channels = new ConcurrentHashMap<>();

    /** 通知渠道配置映射 */
    private final Map<Long, NotifyChannelConfig> channelConfigs = new ConcurrentHashMap<>();

    /** 通知模板映射 */
    private final Map<Long, NotifyTemplate> templates = new ConcurrentHashMap<>();

    @Autowired
    private List<NotifyChannel> channelList;

    /**
     * 初始化通知渠道
     */
    @PostConstruct
    public void init() {
        // 注册所有通知渠道
        for (NotifyChannel channel : channelList) {
            registerChannel(channel);
            logger.info("Registered notify channel: {}", channel.getType());
        }
    }

    /**
     * 注册通知渠道
     *
     * @param channel 通知渠道
     */
    public void registerChannel(NotifyChannel channel) {
        if (channel != null) {
            channels.put(channel.getType(), channel);
        }
    }

    /**
     * 加载通知渠道配置
     *
     * @param channelId 渠道ID
     * @param channelEntity 渠道实体
     * @throws Exception 初始化异常
     */
    public void loadChannelConfig(Long channelId, NotificationChannel channelEntity) throws Exception {
        String type = channelEntity.getType();
        NotifyChannel channel = channels.get(type);

        if (channel == null) {
            throw new IllegalArgumentException("Notify channel not found for type: " + type);
        }

        // 解析配置JSON
        NotifyChannelConfig config = new NotifyChannelConfig();
        config.setName(channelEntity.getName());
        config.setType(type);
        config.setEnabled("enabled".equals(channelEntity.getStatus()));

        if (channelEntity.getConfigJson() != null && !channelEntity.getConfigJson().isEmpty()) {
            Map<String, String> params = JSON.parseObject(channelEntity.getConfigJson(),
                    new TypeReference<Map<String, String>>() {});
            config.setParams(params);
        }

        // 初始化渠道
        channel.initialize(config);

        // 保存配置
        channelConfigs.put(channelId, config);

        logger.info("Loaded configuration for notify channel: {}", type);
    }

    /**
     * 加载通知模板
     *
     * @param templateId 模板ID
     * @param templateEntity 模板实体
     */
    public void loadTemplate(Long templateId, NotificationTemplate templateEntity) {
        NotifyTemplate template = new NotifyTemplate();
        template.setId(templateId.toString());
        template.setName(templateEntity.getName());
        template.setType(templateEntity.getType());

        // 解析模板内容
        String content = templateEntity.getContentTemplate();
        if (content != null && !content.isEmpty()) {
            // 假设模板内容格式为JSON，包含title和content字段
            try {
                Map<String, String> contentMap = JSON.parseObject(content,
                        new TypeReference<Map<String, String>>() {});

                template.setTitleTemplate(contentMap.get("title"));
                template.setContentTemplate(contentMap.get("content"));
            } catch (Exception e) {
                // 如果解析失败，直接使用原始内容作为内容模板
                template.setContentTemplate(content);
            }
        }

        // 解析模板参数
        if (templateEntity.getParamsJson() != null && !templateEntity.getParamsJson().isEmpty()) {
            Map<String, Object> params = JSON.parseObject(templateEntity.getParamsJson(),
                    new TypeReference<Map<String, Object>>() {});
            template.setParams(params);
        }

        // 保存模板
        templates.put(templateId, template);

        logger.info("Loaded notify template: {}", templateId);
    }

    /**
     * 处理告警通知
     *
     * @param alertData 告警数据
     * @param rules 通知规则列表
     * @return 通知结果映射
     */
    public Map<Long, NotifyResult> processAlertNotify(AlertData alertData, List<NotificationRule> rules) {
        Map<Long, NotifyResult> results = new HashMap<>();

        if (rules == null || rules.isEmpty()) {
            return results;
        }

        for (NotificationRule rule : rules) {
            try {
                // 检查规则是否启用
                if (!"enabled".equals(rule.getStatus())) {
                    continue;
                }

                // 检查告警级别是否匹配
                if (rule.getSeverity() != null && !rule.getSeverity().isEmpty()
                        && !rule.getSeverity().equals(alertData.getSeverity())) {
                    continue;
                }

                // 获取通知渠道
                Long channelId = rule.getChannelId();
                NotifyChannelConfig channelConfig = channelConfigs.get(channelId);
                if (channelConfig == null) {
                    logger.warn("Notify channel config not found for ID: {}", channelId);
                    continue;
                }

                NotifyChannel channel = channels.get(channelConfig.getType());
                if (channel == null) {
                    logger.warn("Notify channel not found for type: {}", channelConfig.getType());
                    continue;
                }

                // 获取通知模板
                Long templateId = rule.getTemplateId();
                NotifyTemplate template = templates.get(templateId);
                if (template == null) {
                    logger.warn("Notify template not found for ID: {}", templateId);
                    continue;
                }

                // 渲染通知消息
                NotifyMessage message = channel.renderMessage(template, alertData);

                // 设置接收人
                if (rule.getReceiversJson() != null && !rule.getReceiversJson().isEmpty()) {
                    List<String> receivers = JSON.parseArray(rule.getReceiversJson(), String.class);
                    message.setReceivers(receivers);
                }

                // 发送通知
                NotifyResult result = channel.send(message);
                results.put(rule.getId(), result);

                logger.info("Sent notification for rule {}: {}", rule.getId(), result.isSuccess());

            } catch (Exception e) {
                logger.error("Failed to process notify rule: " + rule.getId(), e);
                results.put(rule.getId(), NotifyResult.failure(e.getMessage()));
            }
        }

        return results;
    }

    /**
     * 测试通知渠道连接
     *
     * @param channelId 渠道ID
     * @return 测试结果
     */
    public NotifyResult testChannelConnection(Long channelId) {
        NotifyChannelConfig config = channelConfigs.get(channelId);
        if (config == null) {
            return NotifyResult.failure("Channel config not found for ID: " + channelId);
        }

        NotifyChannel channel = channels.get(config.getType());
        if (channel == null) {
            return NotifyResult.failure("Channel not found for type: " + config.getType());
        }

        return channel.testConnection();
    }

    /**
     * 获取所有通知渠道类型
     *
     * @return 通知渠道类型映射
     */
    public Map<String, NotifyChannel> getAllChannelTypes() {
        return new HashMap<>(channels);
    }
}
