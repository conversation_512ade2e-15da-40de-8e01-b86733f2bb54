package com.wiwj.securio.alert.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.alert.domain.AlertOutputConfig;
import com.wiwj.securio.alert.service.IAlertOutputConfigService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警输出配置Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/alert/output")
public class AlertOutputConfigController extends BaseController {
    @Autowired
    private IAlertOutputConfigService alertOutputConfigService;

    /**
     * 查询告警输出配置列表
     */
    @PreAuthorize("@ss.hasPermi('alert:output:list')")
    @GetMapping("/list")
    public TableDataInfo list(AlertOutputConfig outputConfig) {
        startPage();
        List<AlertOutputConfig> list = alertOutputConfigService.selectOutputConfigList(outputConfig);
        return getDataTable(list);
    }

    /**
     * 导出告警输出配置列表
     */
    @PreAuthorize("@ss.hasPermi('alert:output:list')")
    @Log(title = "告警输出配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(AlertOutputConfig outputConfig) {
        List<AlertOutputConfig> list = alertOutputConfigService.selectOutputConfigList(outputConfig);
        ExcelUtil<AlertOutputConfig> util = new ExcelUtil<AlertOutputConfig>(AlertOutputConfig.class);
        return util.exportExcel(list, "告警输出配置数据");
    }

    /**
     * 获取告警输出配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('alert:output:list')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(alertOutputConfigService.selectOutputConfigById(id));
    }

    /**
     * 新增告警输出配置
     */
    @PreAuthorize("@ss.hasPermi('alert:output:list')")
    @Log(title = "告警输出配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AlertOutputConfig outputConfig) {
        return toAjax(alertOutputConfigService.insertOutputConfig(outputConfig));
    }

    /**
     * 修改告警输出配置
     */
    @PreAuthorize("@ss.hasPermi('alert:output:list')")
    @Log(title = "告警输出配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AlertOutputConfig outputConfig) {
        return toAjax(alertOutputConfigService.updateOutputConfig(outputConfig));
    }

    /**
     * 删除告警输出配置
     */
    @PreAuthorize("@ss.hasPermi('alert:output:list')")
    @Log(title = "告警输出配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(alertOutputConfigService.deleteOutputConfigByIds(ids));
    }

    /**
     * 根据告警源ID查询告警输出配置
     */
    @PreAuthorize("@ss.hasPermi('alert:output:list')")
    @GetMapping("/source/{sourceId}")
    public AjaxResult getBySourceId(@PathVariable("sourceId") Long sourceId) {
        List<AlertOutputConfig> list = alertOutputConfigService.selectOutputConfigsBySourceId(sourceId);
        return success(list);
    }

    /**
     * 测试告警输出配置
     */
    @PreAuthorize("@ss.hasPermi('alert:output:list')")
    @PostMapping("/test/{id}")
    public AjaxResult testOutputConfig(@PathVariable("id") Long id, @RequestBody(required = false) Object testData) {
        try {
            // 测试告警输出配置
            boolean result = alertOutputConfigService.testOutputConfig(id, testData);
            if (result) {
                return success("测试成功");
            } else {
                return error("测试失败");
            }
        } catch (Exception e) {
            log.error("测试告警输出配置失败", e);
            return error("测试失败：" + e.getMessage());
        }
    }
}
