package com.wiwj.securio.alert.service.impl;

import com.alibaba.excel.EasyExcel;
import com.wiwj.securio.alert.domain.dto.AlertExportDTO;
import com.wiwj.securio.alert.domain.dto.Top20AlertCheckItemDTO;
import com.wiwj.securio.alert.domain.dto.Top20AlertObjectDTO;
import com.wiwj.securio.alert.integration.flashduty.constants.FlashDutyConstants;
import com.wiwj.securio.alert.integration.flashduty.model.*;
import com.wiwj.securio.alert.integration.flashduty.service.FlashDutyService;
import com.wiwj.securio.alert.service.IAlertExportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 告警导出服务实现类
 *
 * <AUTHOR>
 */
@Service
public class AlertExportServiceImpl implements IAlertExportService {

    private static final Logger logger = LoggerFactory.getLogger(AlertExportServiceImpl.class);

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private FlashDutyService flashDutyService;

    @Override
    public List<PageIncidentResponseData.IncidentItem> queryAllIncidentsInTimeRange(Long startTime, Long endTime) {
        List<PageIncidentResponseData.IncidentItem> allIncidents = new ArrayList<>();

        IncidentListRequest request = new IncidentListRequest();
        request.setStart_time(startTime);
        request.setEnd_time(endTime);
        request.setLimit(100); // 每页100条
        request.setP(1); // 从第一页开始

        // 设置渠道ID
//        List<Long> channelIds = Arrays.asList(2664529391785L, 2662743577785L, 2663447718785L);
//        request.setChannel_ids(channelIds);

        int currentPage = 1;
        boolean hasNextPage = true;

        while (hasNextPage) {
            request.setP(currentPage);

            try {
                IncidentListResponse response = flashDutyService.queryIncidentList(request);

                if (response != null && response.getData() != null && response.getData().getItems() != null) {
                    allIncidents.addAll(response.getData().getItems());

                    // 检查是否有下一页
                    hasNextPage = response.getData().getHas_next_page() != null && response.getData().getHas_next_page();
                    currentPage++;

                    logger.info("Successfully fetched page {} with {} incidents", currentPage - 1, response.getData().getItems().size());
                } else {
                    hasNextPage = false;
                    logger.warn("Received empty response for page {}", currentPage);
                }

                // 防止请求过快
                Thread.sleep(100);

            } catch (Exception e) {
                logger.error("Error fetching incidents for page {}: {}", currentPage, e.getMessage());
                hasNextPage = false;
            }
        }

        logger.info("Total incidents fetched: {}", allIncidents.size());
        return allIncidents;
    }

    @Override
    public void exportAlertsToExcel(Long startTime, Long endTime, HttpServletResponse response) throws IOException {
        // 先获取所有成员信息，用于后续ID到姓名的映射
        Map<Long, String> memberNameMap = getMemberNameMap();

        // 查询数据
        List<PageIncidentResponseData.IncidentItem> incidents = queryAllIncidentsInTimeRange(startTime, endTime);

        // 应用筛选条件
        List<PageIncidentResponseData.IncidentItem> filteredIncidents = applyExportFilter(incidents);

        // 转换为导出DTO
        List<AlertExportDTO> exportData = convertToExportData(filteredIncidents, memberNameMap);

        // 设置响应头
        String fileName = "alerts_export_" + System.currentTimeMillis() + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));

        // 导出Excel
        EasyExcel.write(response.getOutputStream(), AlertExportDTO.class)
                .sheet("告警列表")
                .doWrite(exportData);

        logger.info("Successfully exported {} alerts to Excel", exportData.size());
    }

    /**
     * 获取所有成员信息的ID到姓名映射
     */
    private Map<Long, String> getMemberNameMap() {
        Map<Long, String> memberNameMap = new HashMap<>();

        try {
            // 创建成员列表查询请求，一次获取100个成员（第一页）
            MemberListRequest request = new MemberListRequest();
            request.setP(1); // 第一页
            request.setLimit(100); // 每页100条
            request.setAsc(true); // 升序
            request.setOrderby("created_at"); // 按创建时间排序

            logger.info("正在获取成员列表用于姓名映射...");

            // 调用接口获取成员列表
            MemberListResponse response = flashDutyService.queryMemberList(request);

            if (response != null && response.getData() != null && response.getData().getItems() != null) {
                for (PageMemberResponseData.MemberItem member : response.getData().getItems()) {
                    if (member.getMember_id() != null && member.getMember_name() != null) {
                        memberNameMap.put(member.getMember_id(), member.getMember_name());
                    }
                }
                logger.info("成功获取 {} 个成员的姓名映射", memberNameMap.size());
            } else {
                logger.warn("获取成员列表为空");
            }

        } catch (Exception e) {
            logger.error("获取成员列表失败，将使用ID显示关闭人", e);
        }

        return memberNameMap;
    }

    /**
     * 应用导出筛选条件
     * 筛选条件：
     * 1. labels.env 为 空或者None或者prod,或者sit2 的数据
     * 2. labels.department 不等于 经纪平台质量部 或者 相寓IT技术中心
     * 3. 按incident_id去重
     */
    private List<PageIncidentResponseData.IncidentItem> applyExportFilter(List<PageIncidentResponseData.IncidentItem> incidents) {
        int originalCount = incidents.size();

        // 先按标签条件筛选
        List<PageIncidentResponseData.IncidentItem> labelFilteredIncidents = incidents.stream()
                .filter(incident -> {
                    Map<String, String> labels = incident.getLabels();
                    if (labels == null) {
                        return true; // 如果没有标签，则不包含该记录
                    }


                    // 筛选条件1：labels.env 为 空或者None或者prod,或者sit2
                    String env = labels.get("env");
                    boolean envMatch = env == null ||
                            env.trim().isEmpty() ||
                            "None".equalsIgnoreCase(env) ||
                            "prod".equalsIgnoreCase(env) ||
                            "sit2".equalsIgnoreCase(env);

                    // 筛选条件2：labels.department 不等于 经纪平台质量部 或者 相寓IT技术中心
                    String department = labels.get("department");
                    boolean departmentMatch = department == null ||
                            (!("经纪平台质量部".equals(department) || "相寓IT技术中心".equals(department)));

                    return envMatch && departmentMatch;
                })
                .collect(java.util.stream.Collectors.toList());

        int labelFilteredCount = labelFilteredIncidents.size();

        // 再按incident_id去重，保留第一个出现的记录
        Map<String, PageIncidentResponseData.IncidentItem> incidentMap = new LinkedHashMap<>();
        for (PageIncidentResponseData.IncidentItem incident : labelFilteredIncidents) {
            if (incident.getIncident_id() != null && !incidentMap.containsKey(incident.getIncident_id())) {
                incidentMap.put(incident.getIncident_id(), incident);
            }
        }
        List<PageIncidentResponseData.IncidentItem> deduplicatedIncidents = new ArrayList<>(incidentMap.values());

        int finalCount = deduplicatedIncidents.size();

        logger.info("应用导出筛选条件：原始数据 {} 条，标签筛选后 {} 条，去重后 {} 条，共过滤了 {} 条",
                originalCount, labelFilteredCount, finalCount, originalCount - finalCount);

        return deduplicatedIncidents;
    }

    /**
     * 转换告警数据为导出DTO
     */
    private List<AlertExportDTO> convertToExportData(List<PageIncidentResponseData.IncidentItem> incidents, Map<Long, String> memberNameMap) {
        List<AlertExportDTO> exportList = new ArrayList<>();

        for (PageIncidentResponseData.IncidentItem incident : incidents) {
            AlertExportDTO dto = new AlertExportDTO();

            // 基本信息
            dto.setIncidentId(incident.getIncident_id());
            dto.setTitle(incident.getTitle());
            dto.setIncidentSeverity(translateSeverity(incident.getIncident_severity()));
            dto.setProgress(translateProgress(incident.getProgress()));

            // 时间字段
            dto.setStartTime(formatTimestamp(incident.getStart_time()));
            dto.setAckTime(formatTimestamp(incident.getAck_time()));
            dto.setCloseTime(formatTimestamp(incident.getClose_time()));

            // 认领人（从responders中获取第一个acknowledged_at不为0的记录的person_id）
            String acknowledgerName = "";
            if (incident.getResponders() != null && !incident.getResponders().isEmpty()) {
                for (PageIncidentResponseData.Responder responder : incident.getResponders()) {
                    if (responder.getAcknowledged_at() != null && responder.getAcknowledged_at() > 0) {
                        Long acknowledgerId = responder.getPerson_id();
                        if (acknowledgerId != null) {
                            acknowledgerName = memberNameMap.getOrDefault(acknowledgerId, String.valueOf(acknowledgerId));
                        }
                        break; // 取第一个认领的人
                    }
                }
            }
            dto.setAcknowledgerName(acknowledgerName);

            // 关闭人（根据ID获取中文姓名）
            String closerName = "";
            if (incident.getClose_time() != null && incident.getClose_time() > 0) {
                if (incident.getCloser_id() != null && incident.getCloser_id() > 0) {
                    closerName = memberNameMap.getOrDefault(incident.getCloser_id(), String.valueOf(incident.getCloser_id()));
                } else {
                    closerName = "系统关闭";
                }
            }
            dto.setCloserName(closerName);

            // 计算MTTA和MTTR（注意：MTTA是平均认领耗时，MTTR是平均恢复耗时）
            dto.setMtta(calculateMTTA(incident.getStart_time(), incident.getAck_time()));
            dto.setMttr(calculateMTTR(incident.getStart_time(), incident.getClose_time()));

            // 根因和解决方案
            dto.setRootCause(incident.getRoot_cause() != null ? incident.getRoot_cause() : "");
            dto.setResolution(incident.getResolution() != null ? incident.getResolution() : "");

            // 标签信息
            Map<String, String> labels = incident.getLabels();
            if (labels != null) {
                dto.setGroupName(labels.get("group_name"));
                dto.setIdent(labels.get("ident"));
                dto.setRegion(labels.get("region"));
                dto.setEnv(labels.get("env"));
                dto.setBusiness(labels.get("business"));
                dto.setDepartment(labels.get("department"));
            }

            exportList.add(dto);
        }

        return exportList;
    }

    /**
     * 格式化时间戳
     */
    private String formatTimestamp(Long timestamp) {
        if (timestamp == null || timestamp == 0) {
            return "";
        }
        return DATE_FORMAT.format(new Date(timestamp * 1000)); // 转换为毫秒
    }

    /**
     * 翻译严重程度
     */
    private String translateSeverity(String severity) {
        if (severity == null) return "";
        switch (severity.toLowerCase()) {
            case "critical":
                return "严重";
            case "warning":
                return "警告";
            case "info":
                return "信息";
            default:
                return severity;
        }
    }

    /**
     * 翻译处理进度
     */
    private String translateProgress(String progress) {
        if (progress == null) return "";
        switch (progress.toLowerCase()) {
            case "triggered":
                return "已触发";
            case "processing":
                return "处理中";
            case "closed":
                return "已关闭";
            default:
                return progress;
        }
    }

    /**
     * 计算MTTA (平均认领耗时)
     */
    private String calculateMTTA(Long startTime, Long ackTime) {
        if (startTime == null) return "";
        if (ackTime == null || ackTime == 0) return "未认领";

        long durationSeconds = ackTime - startTime;
        return formatDuration(durationSeconds);
    }

    /**
     * 计算MTTR (平均恢复用时)
     */
    private String calculateMTTR(Long startTime, Long closeTime) {
        if (startTime == null) return "";
        if (closeTime == null || closeTime == 0) return "未恢复";

        long durationSeconds = closeTime - startTime;
        return formatDuration(durationSeconds);
    }

    /**
     * 格式化持续时间
     */
    private String formatDuration(long seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分钟";
        } else if (seconds < 86400) {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            return hours + "小时" + (minutes > 0 ? minutes + "分钟" : "");
        } else {
            long days = seconds / 86400;
            long hours = (seconds % 86400) / 3600;
            return days + "天" + (hours > 0 ? hours + "小时" : "");
        }
    }

    @Override
    public void exportTop20AlertsToExcel(Long startTime, Long endTime, HttpServletResponse response) throws IOException {
        // 查询时间范围内的所有告警数据
        List<PageIncidentResponseData.IncidentItem> incidents = queryAllIncidentsInTimeRange(startTime, endTime);

        // 应用筛选条件
        List<PageIncidentResponseData.IncidentItem> filteredIncidents = applyExportFilter(incidents);

        //筛选严重告警
        filteredIncidents = filteredIncidents.stream().filter(incidentItem -> FlashDutyConstants.EVENT_STATUS_CRITICAL.equals(incidentItem.getIncident_severity())).collect(Collectors.toList());   //匹配告警等级为严重的告警


        // 生成Top20告警对象数据
        List<Top20AlertObjectDTO> top20Objects = generateTop20AlertObjects(filteredIncidents);

        // 生成Top20告警检查项数据  
        List<Top20AlertCheckItemDTO> top20CheckItems = generateTop20AlertCheckItems(filteredIncidents);

        // 设置响应头
        String fileName = "top20_alerts_export_" + System.currentTimeMillis() + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));

        // 导出Excel，包含两个sheet
        com.alibaba.excel.ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();

        // 第一个sheet：Top20告警对象
        com.alibaba.excel.write.metadata.WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "Top20告警对象")
                .head(Top20AlertObjectDTO.class).build();
        excelWriter.write(top20Objects, writeSheet1);

        // 第二个sheet：Top20告警检查项
        com.alibaba.excel.write.metadata.WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "Top20告警检查项")
                .head(Top20AlertCheckItemDTO.class).build();
        excelWriter.write(top20CheckItems, writeSheet2);

        // 关闭writer
        excelWriter.finish();

        logger.info("Successfully exported Top20 alerts: {} objects, {} check items", top20Objects.size(), top20CheckItems.size());
    }

    /**
     * 生成Top20告警对象数据
     */
    private List<Top20AlertObjectDTO> generateTop20AlertObjects(List<PageIncidentResponseData.IncidentItem> incidents) {
        // 筛选labels.ident不为空的数据
        List<PageIncidentResponseData.IncidentItem> validIncidents = incidents.stream()
                .filter(incident -> incident.getLabels() != null &&
                        incident.getLabels().get("ident") != null &&
                        !incident.getLabels().get("ident").trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());

        // 按labels.ident分组
        Map<String, List<PageIncidentResponseData.IncidentItem>> groupedByIdent = validIncidents.stream()
                .collect(java.util.stream.Collectors.groupingBy(incident -> incident.getLabels().get("ident")));

        List<Top20AlertObjectDTO> result = new ArrayList<>();

        for (Map.Entry<String, List<PageIncidentResponseData.IncidentItem>> entry : groupedByIdent.entrySet()) {
            String ident = entry.getKey();
            List<PageIncidentResponseData.IncidentItem> groupIncidents = entry.getValue();

            Top20AlertObjectDTO dto = new Top20AlertObjectDTO();

            // 告警对象
            dto.setAlertObject(ident);

            // 数量
            dto.setCount(groupIncidents.size());

            // 标签（取第一个数据的指定标签字段）
            PageIncidentResponseData.IncidentItem firstIncident = groupIncidents.get(0);
            dto.setLabels(convertTop20LabelsToString(firstIncident.getLabels()));

            // 告警原因（取第一个数据的告警标题）
            dto.setAlertReason(firstIncident.getTitle());

            // 告警是否处理
            dto.setProcessStatus(determineProcessStatus(groupIncidents));

            // 告警处理方法（取第一条告警的root_cause + resolution）
            String processMethod = "";
            if (firstIncident.getRoot_cause() != null && !firstIncident.getRoot_cause().trim().isEmpty()) {
                processMethod += firstIncident.getRoot_cause();
            }
            if (firstIncident.getResolution() != null && !firstIncident.getResolution().trim().isEmpty()) {
                if (!processMethod.isEmpty()) {
                    processMethod += " + ";
                }
                processMethod += firstIncident.getResolution();
            }
            dto.setProcessMethod(processMethod.isEmpty() ? "待确认" : processMethod);

            result.add(dto);
        }

        // 按数量降序排序，取前20
        result.sort((a, b) -> Integer.compare(b.getCount(), a.getCount()));
        return result.stream().limit(20).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 生成Top20告警检查项数据
     */
    private List<Top20AlertCheckItemDTO> generateTop20AlertCheckItems(List<PageIncidentResponseData.IncidentItem> incidents) {
        // 按告警标题分组
        Map<String, List<PageIncidentResponseData.IncidentItem>> groupedByTitle = incidents.stream()
                .collect(java.util.stream.Collectors.groupingBy(PageIncidentResponseData.IncidentItem::getTitle));

        List<Top20AlertCheckItemDTO> result = new ArrayList<>();

        for (Map.Entry<String, List<PageIncidentResponseData.IncidentItem>> entry : groupedByTitle.entrySet()) {
            String title = entry.getKey();
            List<PageIncidentResponseData.IncidentItem> groupIncidents = entry.getValue();

            Top20AlertCheckItemDTO dto = new Top20AlertCheckItemDTO();

            // 类型（根据标题判断）
            dto.setType(determineAlertType(title));

            // 内容（告警标题）
            dto.setContent(title);

            // 告警数量
            dto.setCount(groupIncidents.size());

            // 告警原因（取标题'/'前面的字符串）
            dto.setAlertReason(extractAlertReason(title));

            // 告警是否处理
            dto.setProcessStatus(determineProcessStatus(groupIncidents));

            // 告警处理方法（取第一条告警的resolution）
            PageIncidentResponseData.IncidentItem firstIncident = groupIncidents.get(0);
            String resolution = firstIncident.getResolution();
            dto.setProcessMethod((resolution != null && !resolution.trim().isEmpty()) ? resolution : "待确认");

            result.add(dto);
        }

        // 按数量降序排序，取前20
        result.sort((a, b) -> Integer.compare(b.getCount(), a.getCount()));
        return result.stream().limit(20).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 将标签Map转换为字符串
     */
    private String convertLabelsToString(Map<String, String> labels) {
        if (labels == null || labels.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : labels.entrySet()) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append(entry.getKey()).append(": ").append(entry.getValue());
        }
        return sb.toString();
    }

    /**
     * 将Top20告警对象的标签Map转换为字符串
     * 只显示指定的标签字段：env,machineIP,application,busigroup,business,department,owner,role,cpu_bound
     * 用分号分隔
     */
    private String convertTop20LabelsToString(Map<String, String> labels) {
        if (labels == null || labels.isEmpty()) {
            return "";
        }

        // 指定要显示的标签字段
        String[] targetLabels = {"env", "machineIP", "application", "busigroup", "business", "department", "owner", "role", "cpu_bound"};
        
        StringBuilder sb = new StringBuilder();
        for (String labelKey : targetLabels) {
            String labelValue = labels.get(labelKey);
            if (labelValue != null && !labelValue.trim().isEmpty()) {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append(labelKey).append(":").append(labelValue);
            }
        }
        
        return sb.toString();
    }

    /**
     * 判断告警处理状态
     */
    private String determineProcessStatus(List<PageIncidentResponseData.IncidentItem> incidents) {
        long closedCount = incidents.stream()
                .filter(incident -> "closed".equalsIgnoreCase(incident.getProgress()))
                .count();

        if (closedCount == incidents.size()) {
            return "已处理";
        } else if (closedCount == 0) {
            return "未处理";
        } else {
            return "部分处理";
        }
    }

    /**
     * 根据告警标题判断类型
     */
    private String determineAlertType(String title) {
        if (title == null) {
            return "待确认";
        }

        // 检查是否包含中文中括号
        if (title.contains("【") && title.contains("】")) {
            int start = title.indexOf("【");
            int end = title.indexOf("】", start);
            if (end > start) {
                return title.substring(start, end + 1);
            }
        }

        // 根据关键词判断类型
        if (title.contains("主机监控服务失联")) {
            return "【主机故障告警】";
        } else if (title.matches(".*QH-S\\d+-.*-H\\d+-\\d+U.*")) {
            return "【秦淮机房网络设备】";
        } else if (title.contains("主机：") && title.contains("重启")) {
            return "【系统告警】";
        } else if (title.contains("关系型数据库")) {
            return "【数据库】";
        } else if (title.contains("Unavailable by ICMP ping")) {
            return "【辰运大厦监控摄像头】";
        } else {
            return "待确认";
        }
    }

    /**
     * 提取告警原因（取'/'前面的字符串）
     */
    private String extractAlertReason(String title) {
        if (title == null) {
            return "待确认";
        }

//        int slashIndex = title.indexOf('/');
//        if (slashIndex > 0) {
//            return title.substring(0, slashIndex).trim();
//        } else {
//            return "待确认";
//        }
        return title;
    }
} 