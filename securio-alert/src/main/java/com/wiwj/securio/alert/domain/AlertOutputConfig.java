package com.wiwj.securio.alert.domain;

import com.wiwj.common.annotation.Excel;
import lombok.Data;

/**
 * 告警输出配置对象 alert_output_config
 *
 * <AUTHOR>
 */
@Data
public class AlertOutputConfig {

    /** 配置ID */
    private Long id;

    /** 告警输出名称 */
    @Excel(name = "告警输出名称")
    private String name;

    /** 告警类型 */
    @Excel(name = "告警类型")
    private String alertSourceType;

    /** 告警源实例唯一标识 */
    @Excel(name = "告警源实例唯一标识")
    private String alertSourceIdent;

    /** 告警源实例名称 */
    @Excel(name = "告警源实例名称")
    private String alertSourceName;

    /** 告警规则名称 */
    @Excel(name = "告警规则名称")
    private String alertRuleName;

    /** 告警组名称 */
    @Excel(name = "告警组名称")
    private String groupName;

    /** 告警信息筛选规则 */
    private String alertRuleFilter;

    /** 告警对象字段key，逗号分隔字符串 */
    @Excel(name = "告警对象字段key")
    private String alertObjectKey;

    /** 告警输出目标 */
    @Excel(name = "告警输出目标")
    private String outputTarget;

    /** 告警输出方式 */
    @Excel(name = "告警输出方式", readConverterExp = "forward_raw=转发元数据,send_standard_alert=发送标准事件")
    private String outputType;

    /** 告警输出webhookUrl */
    @Excel(name = "告警输出webhookUrl")
    private String outputWebhookUrl;

    /** 输入推送URL */
    @Excel(name = "输入推送URL")
    private String inputPushUrl;

    /** 状态(enabled/disabled) */
    @Excel(name = "状态", readConverterExp = "enabled=启用,disabled=禁用")
    private String status;

    /** 创建时间 */
    private Long createAt;

    /** 创建人ID */
    private String createBy;

    /** 创建人名称 */
    private String createName;

    /** 更新时间 */
    private Long updateAt;

    /** 更新人ID */
    private String updateBy;

    /** 更新人名称 */
    private String updateName;

    /** 是否删除(0-未删除 1-已删除) */
    private Integer isDel;
}
