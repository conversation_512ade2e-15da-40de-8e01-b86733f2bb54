package com.wiwj.securio.alert.domain;

import com.wiwj.common.annotation.Excel;
import lombok.Data;

/**
 * 系统用户账号对象 sys_user_account
 *
 * <AUTHOR>
 */
@Data
public class SysUserAccount {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 系统用户id
     */
    @Excel(name = "系统用户ID")
    private String userId;

    /**
     * 账号id
     */
    @Excel(name = "账号ID")
    private String accountId;

    /**
     * 账号名称
     */
    @Excel(name = "账号名称")
    private String accountName;

    /**
     * 用户状态
     */
    @Excel(name = "用户状态")
    private String status;

    /**
     * 渠道名称
     */
    @Excel(name = "渠道名称")
    private String chanel;

    /**
     * 团队ID
     */
    @Excel(name = "团队ID")
    private String teamId;

    /**
     * 团队名称
     */
    @Excel(name = "团队名称")
    private String teamName;

    /**
     * FlashDuty应用密钥
     */
    @Excel(name = "FlashDuty应用密钥")
    private String appKey;

    /**
     * 是否删除
     */
    private Integer isDel;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long createAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long updateAt;
} 