package com.wiwj.securio.alert.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.SysUserAccount;
import com.wiwj.securio.alert.integration.flashduty.model.IncidentListRequest;
import com.wiwj.securio.alert.integration.flashduty.model.IncidentListResponse;
import com.wiwj.securio.alert.integration.flashduty.model.PageIncidentResponseData;
import com.wiwj.securio.alert.integration.flashduty.service.FlashDutyService;
import com.wiwj.securio.alert.mapper.AlertMapper;
import com.wiwj.securio.alert.service.IAlertAsyncService;
import com.wiwj.securio.alert.service.IAlertService;
import com.wiwj.securio.alert.service.ISysUserAccountService;
import com.wiwj.securio.alert.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 告警异步任务服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlertAsyncServiceImpl implements IAlertAsyncService {

    // 重试配置常量
    private static final long MAX_DURATION_MS = 10000; // 10秒
    private static final long INITIAL_DELAY_MS = 2000; // 初始延迟2秒
    private static final long RETRY_INTERVAL_MS = 500; // 重试间隔500ms

    @Autowired
    private FlashDutyService flashDutyService;

    @Autowired
    private ISysUserAccountService sysUserAccountService;

    @Autowired
    private AlertMapper alertMapper;

    @Override
    @Async("flashDutyTaskExecutor")
    public void asyncSyncFlashDutyIncident(Alert alert) {
        try {
            log.info("开始异步同步FlashDuty故障信息，告警ID: {}, eventId: {}", alert.getId(), alert.getEventId());

            if (isInvalidEventId(alert.getEventId())) {
                log.warn("告警eventId为空，跳过FlashDuty同步，告警ID: {}", alert.getId());
                return;
            }

            // 在异步线程中执行同步重试逻辑
            executeSyncWithRetry(alert.getId(), alert.getEventId());

        } catch (Exception e) {
            log.error("异步同步FlashDuty故障信息失败，告警ID: {}, eventId: {}", alert.getId(), alert.getEventId(), e);
        }
    }

    @Override
    public boolean syncFlashDutyIncidentByEventId(Long alertId, String eventId) {
        try {
            log.info("开始同步FlashDuty故障信息，告警ID: {}, eventId: {}", alertId, eventId);

            // 查询FlashDuty故障
            PageIncidentResponseData.IncidentItem matchedIncident = queryAndFindIncident(alertId, eventId);
            if (matchedIncident == null) {
                return false;
            }

            // 更新告警信息
            return updateAlertWithFlashDutyInfo(alertId, matchedIncident);

        } catch (Exception e) {
            log.error("同步FlashDuty故障信息失败，告警ID: {}, eventId: {}", alertId, eventId, e);
            return false;
        }
    }

    /**
     * 执行同步重试逻辑
     */
    private void executeSyncWithRetry(Long alertId, String eventId) {
        long startTime = System.currentTimeMillis();
        boolean success = false;

        try {
            // 初始延迟
            Thread.sleep(INITIAL_DELAY_MS);

            while (!success && (System.currentTimeMillis() - startTime) < MAX_DURATION_MS) {
                try {
                    log.info("尝试同步FlashDuty故障信息，告警ID: {}, eventId: {}, 已耗时: {}ms",
                            alertId, eventId, System.currentTimeMillis() - startTime);

                    // 直接调用同步方法
                    boolean syncResult = syncFlashDutyIncidentByEventId(alertId, eventId);
                    if (syncResult) {
                        success = true;
                        log.info("成功同步FlashDuty故障信息，告警ID: {}, eventId: {}", alertId, eventId);
                    } else {
                        log.warn("同步FlashDuty故障信息失败（无分派人员），准备重试，告警ID: {}, eventId: {}",
                                alertId, eventId);
                        throw new RuntimeException("同步失败，需要重试");
                    }

                } catch (Exception e) {
                    long elapsed = System.currentTimeMillis() - startTime;
                    if (elapsed + RETRY_INTERVAL_MS >= MAX_DURATION_MS) {
                        log.error("同步FlashDuty故障信息失败，已达到最大重试时间，告警ID: {}, eventId: {}, 总耗时: {}ms",
                                alertId, eventId, elapsed, e);
                        break;
                    }

                    log.warn("同步FlashDuty故障信息失败，准备重试，告警ID: {}, eventId: {}, 已耗时: {}ms",
                            alertId, eventId, elapsed, e);

                    Thread.sleep(RETRY_INTERVAL_MS);
                }
            }

            if (!success) {
                log.error("同步FlashDuty故障信息最终失败，告警ID: {}, eventId: {}, 总耗时: {}ms",
                        alertId, eventId, System.currentTimeMillis() - startTime);
            }

        } catch (InterruptedException e) {
            log.error("同步FlashDuty故障信息被中断，告警ID: {}, eventId: {}", alertId, eventId, e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 检查eventId是否无效
     */
    private boolean isInvalidEventId(String eventId) {
        return eventId == null || eventId.trim().isEmpty();
    }

    /**
     * 查询并查找匹配的故障
     */
    private PageIncidentResponseData.IncidentItem queryAndFindIncident(Long alertId, String eventId) {
        // 构建查询请求
        IncidentListRequest request = buildIncidentListRequest(eventId);

        // 查询FlashDuty故障列表
        IncidentListResponse response = flashDutyService.queryIncidentList(request);

        if (isInvalidResponse(response)) {
            log.warn("FlashDuty故障列表为空，告警ID: {}, eventId: {}", alertId, eventId);
            return null;
        }

        List<PageIncidentResponseData.IncidentItem> incidents = response.getData().getItems();
        log.info("查询到FlashDuty故障数量: {}, 告警ID: {}, eventId: {}", incidents.size(), alertId, eventId);

        // 查找匹配的故障
        PageIncidentResponseData.IncidentItem matchedIncident = findIncidentByEventId(incidents, eventId);

        if (matchedIncident == null) {
            log.warn("未找到匹配的FlashDuty故障，告警ID: {}, eventId: {}", alertId, eventId);
        }

        return matchedIncident;
    }

    /**
     * 构建故障列表查询请求
     */
    private IncidentListRequest buildIncidentListRequest(String eventId) {
        // 计算当天的时间范围
        long currentTime = System.currentTimeMillis();
        long[] dayStartAndEndInSeconds = TimeUtils.getDayStartAndEndInSeconds(currentTime);
        long startOfDay = dayStartAndEndInSeconds[0] / 1000;
        long endOfDay = dayStartAndEndInSeconds[1] / 1000;

        // 构建查询请求
        IncidentListRequest request = new IncidentListRequest();
        request.setLimit(100);
        request.setP(1);
        request.setStart_time(startOfDay);
        request.setEnd_time(endOfDay);
        request.setAsc(false);

        // 设置eventId标签查询
        Map<String, String> labels = new HashMap<>();
        labels.put("eventId", eventId);
        request.setLabels(labels);

        log.info("查询FlashDuty故障，时间范围: {} - {}, eventId: {}",
                new Date(startOfDay * 1000), new Date(endOfDay * 1000), eventId);

        return request;
    }

    /**
     * 检查响应是否无效
     */
    private boolean isInvalidResponse(IncidentListResponse response) {
        return response == null || response.getData() == null || response.getData().getItems() == null;
    }

    /**
     * 根据eventId查找匹配的故障
     */
    private PageIncidentResponseData.IncidentItem findIncidentByEventId(List<PageIncidentResponseData.IncidentItem> incidents, String eventId) {
        for (PageIncidentResponseData.IncidentItem incident : incidents) {
            try {
                if (isIncidentMatchByLabels(incident, eventId) ||
                        isIncidentMatchByTitle(incident, eventId) ||
                        isIncidentMatchByDescription(incident, eventId)) {
                    return incident;
                }
            } catch (Exception e) {
                log.warn("解析故障标签失败，故障ID: {}, eventId: {}", incident.getIncident_id(), eventId, e);
            }
        }

        log.warn("未找到匹配的故障，查询到的故障数量: {}, eventId: {}", incidents.size(), eventId);
        return null;
    }

    /**
     * 通过标签匹配故障
     */
    private boolean isIncidentMatchByLabels(PageIncidentResponseData.IncidentItem incident, String eventId) {
        if (incident.getLabels() != null && incident.getLabels().containsKey("eventId")) {
            if (eventId.equals(incident.getLabels().get("eventId"))) {
                log.debug("通过标签精确匹配到故障，故障ID: {}, eventId: {}", incident.getIncident_id(), eventId);
                return true;
            }
        }
        return false;
    }

    /**
     * 通过标题匹配故障
     */
    private boolean isIncidentMatchByTitle(PageIncidentResponseData.IncidentItem incident, String eventId) {
        if (incident.getTitle() != null && incident.getTitle().contains(eventId)) {
            log.debug("通过标题匹配到故障，故障ID: {}, eventId: {}", incident.getIncident_id(), eventId);
            return true;
        }
        return false;
    }

    /**
     * 通过描述匹配故障
     */
    private boolean isIncidentMatchByDescription(PageIncidentResponseData.IncidentItem incident, String eventId) {
        if (incident.getDescription() != null && incident.getDescription().contains(eventId)) {
            log.debug("通过描述匹配到故障，故障ID: {}, eventId: {}", incident.getIncident_id(), eventId);
            return true;
        }
        return false;
    }

    /**
     * 使用FlashDuty故障信息更新告警
     *
     * @return true 如果成功查询到分派人员，false 如果没有分派人员
     */
    private boolean updateAlertWithFlashDutyInfo(Long alertId, PageIncidentResponseData.IncidentItem incident) {
        try {
            Alert alert = alertMapper.selectAlertById(alertId);
            if (alert == null) {
                log.warn("告警不存在，无法更新，告警ID: {}", alertId);
                return false;
            }

            // 更新告警基本信息
            updateAlertBasicInfo(alert, incident);

            // 更新分派人员信息
            updateAlertResponders(alert, incident);

            // 保存告警
            int updateResult = alertMapper.updateAlert(alert);
            if (updateResult <= 0) {
                log.warn("更新告警FlashDuty信息失败，告警ID: {}", alertId);
                return false;
            }

            // 检查是否有分派人员
            boolean hasResponders = incident.getResponders() != null && !incident.getResponders().isEmpty();
            if (hasResponders) {
                log.info("查询到分派人员，同步成功，告警ID: {}, 分派人员数量: {}", alertId, incident.getResponders().size());
            } else {
                log.warn("未查询到分派人员，同步失败，告警ID: {}", alertId);
            }

            return hasResponders;

        } catch (Exception e) {
            log.error("更新告警FlashDuty信息异常，告警ID: {}", alertId, e);
            return false;
        }
    }

    /**
     * 更新告警基本信息
     */
    private void updateAlertBasicInfo(Alert alert, PageIncidentResponseData.IncidentItem incident) {
        // 设置FlashDuty故障ID
        if (incident.getIncident_id() != null) {
            alert.setFdIncidentId(incident.getIncident_id());
            log.info("设置FlashDuty故障ID，告警ID: {}, 故障ID: {}", alert.getId(), incident.getIncident_id());
        }

        // 设置协作空间信息
        if (incident.getChannel_id() != null) {
            alert.setChannelId(incident.getChannel_id().toString());
        }
        if (incident.getChannel_name() != null) {
            alert.setChannelName(incident.getChannel_name());
        }
    }

    /**
     * 更新告警分派人员信息
     */
    private void updateAlertResponders(Alert alert, PageIncidentResponseData.IncidentItem incident) {
        if (incident.getResponders() != null && !incident.getResponders().isEmpty()) {
            // 收集所有person_id
            List<String> personIds = incident.getResponders().stream()
                    .map(responder -> responder.getPerson_id().toString())
                    .collect(Collectors.toList());

            // 批量查询用户账号信息
            List<SysUserAccount> userAccounts = sysUserAccountService.selectSysUserAccountByAccountIds(personIds);

            // 设置分派人员
            if (userAccounts != null && !userAccounts.isEmpty()) {
                String assignIds = userAccounts.stream().map(SysUserAccount::getUserId).collect(Collectors.joining(","));
                String assignNames = userAccounts.stream().map(SysUserAccount::getAccountName).collect(Collectors.joining(","));
                alert.setAssignedUserIds(assignIds);
                alert.setAssignedUserNames(assignNames);
            }
        }
    }

    @Override
    public int batchSyncFlashDutyIncidentsForRecentAlerts() {
        log.info("开始批量同步最近三天告警的FlashDuty故障信息");

        try {
            // 查询最近三天需要同步的告警
            List<Alert> alertsToSync = queryRecentAlertsNeedingSync();

            if (alertsToSync.isEmpty()) {
                log.info("没有找到需要同步的告警");
                return 0;
            }

            log.info("查询到{}条需要同步的告警，开始批量处理", alertsToSync.size());

            // 统计计数器
            int totalCount = alertsToSync.size();
            int successCount = 0;
            int failureCount = 0;
            int skipCount = 0;

            // 循环处理每个告警
            for (Alert alert : alertsToSync) {
                try {
                    log.info("开始同步告警 [ID: {}, EventId: {}, Title: {}]",
                            alert.getId(), alert.getEventId(), alert.getTitle());

                    // 检查eventId是否有效
                    if (isInvalidEventId(alert.getEventId())) {
                        log.warn("告警eventId为空，跳过同步 [ID: {}]", alert.getId());
                        skipCount++;
                        continue;
                    }

                    // 调用同步方法
                    boolean syncResult = syncFlashDutyIncidentByEventId(alert.getId(), alert.getEventId());

                    if (syncResult) {
                        successCount++;
                        log.info("成功同步告警 [ID: {}, EventId: {}]", alert.getId(), alert.getEventId());
                    } else {
                        failureCount++;
                        log.warn("同步失败告警 [ID: {}, EventId: {}] - 可能无分派人员或故障不存在",
                                alert.getId(), alert.getEventId());
                    }

                } catch (Exception e) {
                    failureCount++;
                    log.error("同步告警异常 [ID: {}, EventId: {}]", alert.getId(), alert.getEventId(), e);
                }

                // 添加短暂延迟，避免过于频繁的API调用
                try {
                    Thread.sleep(100); // 100ms延迟
                } catch (InterruptedException e) {
                    log.warn("批量同步被中断");
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            log.info("批量同步完成 - 总计: {}, 成功: {}, 失败: {}, 跳过: {}",
                    totalCount, successCount, failureCount, skipCount);

            return successCount;

        } catch (Exception e) {
            log.error("批量同步FlashDuty故障信息异常", e);
            return 0;
        }
    }

    /**
     * 查询最近三天需要同步的告警
     * 查询条件：fdIncidentId为空 或 assignedUserIds为空
     * 按时间倒序排列，让新发生的告警优先处理
     */
    private List<Alert> queryRecentAlertsNeedingSync() {
        // 计算最近三天的时间范围
        long currentTime = System.currentTimeMillis();
        long threeDaysAgo = currentTime - (3 * 24 * 60 * 60 * 1000L); // 3天前

        log.info("查询时间范围: {} 到 {}", new Date(threeDaysAgo), new Date(currentTime));

        List<Alert> alertsToSync = alertMapper.selectRecentAlertsNeedingFlashDutySync(threeDaysAgo, currentTime);

        log.info("查询到需要同步的告警数量: {} (按时间倒序，新告警优先处理)", alertsToSync.size());

        return alertsToSync;
    }


} 