package com.wiwj.securio.alert.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.AlertAckRequest;
import com.wiwj.securio.alert.domain.AlertOutputConfig;
import com.wiwj.securio.alert.domain.AlertResolutionRequest;
import com.wiwj.securio.alert.enums.AlertSeverityEnum;
import com.wiwj.securio.alert.enums.AlertStatusEnum;
import com.wiwj.securio.alert.exception.AppKeyNotConfiguredException;
import com.wiwj.securio.alert.integration.flashduty.model.FlashDutyResponse;
import com.wiwj.securio.alert.integration.flashduty.model.IncidentListRequest;
import com.wiwj.securio.alert.integration.flashduty.model.IncidentListResponse;
import com.wiwj.securio.alert.integration.flashduty.service.FlashDutyService;
import com.wiwj.securio.alert.mapper.AlertMapper;
import com.wiwj.securio.alert.service.*;
import com.wiwj.securio.alert.util.TagSearchUtil;
import com.wiwj.securio.alert.util.TimeUtils;
import com.wiwj.securio.alert.util.UrlParameterExtractor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 告警Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class AlertServiceImpl implements IAlertService {
    private static final Logger logger = LoggerFactory.getLogger(AlertServiceImpl.class);

    @Autowired
    private AlertMapper alertMapper;

    @Autowired
    private FlashDutyService flashDutyService;

    @Autowired
    private AlertOutputService alertOutputService;

    @Autowired
    private IAlertOutputConfigService alertOutputConfigService;

    @Autowired
    private IAlertAsyncService alertAsyncService;

    @Autowired
    private ISysUserAccountService sysUserAccountService;

    /**
     * 查询告警
     *
     * @param id 告警ID
     * @return 告警
     */
    @Override
    public Alert selectAlertById(Long id) {
        return alertMapper.selectAlertById(id);
    }

    /**
     * 查询告警列表
     *
     * @param alert 告警
     * @return 告警
     */
    @Override
    public List<Alert> selectAlertList(Alert alert) {
        // 处理标签搜索
        if (alert.getTagSearch() != null && !alert.getTagSearch().trim().isEmpty()) {
            String tagSearchType = alert.getTagSearchType();
            if (tagSearchType == null || tagSearchType.trim().isEmpty()) {
                tagSearchType = "contains"; // 默认使用包含搜索
            }

            try {
                String tagSearchCondition = TagSearchUtil.parseTagSearchToSql(alert.getTagSearch().trim(), tagSearchType);
                alert.setTagSearchCondition(tagSearchCondition);
                logger.debug("标签搜索解析: {} -> {}", alert.getTagSearch(), tagSearchCondition);
            } catch (Exception e) {
                logger.warn("标签搜索解析失败: {}, 错误: {}", alert.getTagSearch(), e.getMessage());
                // 设置为null，这样不会影响其他查询条件
                alert.setTagSearchCondition(null);
            }
        }

        return alertMapper.selectAlertList(alert);
    }

    /**
     * 新增告警
     *
     * @param alert 告警
     * @return 结果
     */
    @Override
    public int insertAlert(Alert alert) {
        //重置id
        alert.setId(null);
        // 设置默认值
        if (alert.getStatus() == null) {
            alert.setStatus("new");
        }
        if (alert.getSeverity() == null) {
            alert.setSeverity("low");
        }
        if (alert.getDetectedAt() == null) {
            alert.setDetectedAt(System.currentTimeMillis());
        }

        // 根据sourceType自动设置alertCategory
        alert.setAlertCategoryBySourceType();

        // 插入告警记录
        return alertMapper.insertAlert(alert);
    }

    @Override
    public int insertOrUpdateAlert(Alert alert) {
        String eventId = alert.getEventId();
        if (StringUtils.isEmpty(eventId)) {
            logger.error("保存告警失败，eventId为空，原始告警为：{}", JSON.toJSONString(alert));
            return 0;
        }
        //如果不是解决告警事件则为新增
        if (!Objects.equals(alert.getStatus(), AlertStatusEnum.RESOLVED.getCode())) {
            logger.info("新增告警");
            return insertAlert(alert);
        } else {
            //如果是解决告警，则更新原来所有告警为解决状态
            //查询告警是否存在
            List<Alert> alerts = alertMapper.selectUnResolveAlertByEventId(eventId);
            if (CollectionUtils.isEmpty(alerts)) {
                logger.warn("eventId:{} 告警不存在，无法解决", eventId);
                return 0;
            }

            //填充解决默认信息
            fillAutoResolveInfo(alert);

            //循环修改未解决的告警
            alerts.forEach(oldAlert->{
                alert.setId(oldAlert.getId());
                updateAlert(alert);
                logger.info("更新告警为已解决状态，alertId:{},eventId:{}",oldAlert.getId(),eventId);
            });

            return 1;
        }

    }

    private static void fillAutoResolveInfo(Alert alert) {
        if (alert.getResolvedAt() == null) {
            alert.setResolvedAt(System.currentTimeMillis());
        }
        if (StringUtils.isEmpty(alert.getResolvedBy())) {
            alert.setResolvedBy("0");
            alert.setResolvedByName("系统");
        }

        if (StringUtils.isEmpty(alert.getResolutionNote())) {
            alert.setResolutionNote("系统自动恢复");
        }
    }

    /**
     * 修改告警
     *
     * @param alert 告警
     * @return 结果
     */
    @Override
    public int updateAlert(Alert alert) {
        return alertMapper.updateAlert(alert);
    }

    /**
     * 批量删除告警
     *
     * @param ids 需要删除的告警ID
     * @return 结果
     */
    @Override
    public int deleteAlertByIds(Long[] ids) {
        return alertMapper.deleteAlertByIds(ids);
    }

    /**
     * 删除告警信息
     *
     * @param id 告警ID
     * @return 结果
     */
    @Override
    public int deleteAlertById(Long id) {
        return alertMapper.deleteAlertById(id);
    }

    /**
     * 批量逻辑删除告警
     *
     * @param ids 需要逻辑删除的告警ID
     * @return 结果
     */
    @Override
    public int logicDeleteAlertByIds(Long[] ids) {
        return alertMapper.logicDeleteAlertByIds(ids);
    }


    /**
     * 处理Webhook推送的告警数据
     * 注意：这个方法已经不再使用，保留是为了兼容旧的API
     *
     * @param token    推送URL令牌
     * @param jsonData JSON数据
     * @return 处理结果数据
     */
    @Override
    public Map<String, Object> processWebhookAlert(String token, String jsonData) throws Exception {
        // 初始化结果数据
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("message", "该接口已经不再支持，请使用新的标准告警接口");
        resultData.put("timestamp", System.currentTimeMillis());
        return resultData;
    }

    /**
     * 接收单个告警
     *
     * @param alert 告警对象
     * @return 处理结果数据
     */
    @Override
    public Map<String, Object> receiveAlert(Alert alert) throws Exception {
        // 初始化结果数据
        Map<String, Object> resultData = new HashMap<>();

        // 设置默认值
        fillDefaultAlert(alert);

        // 保存告警
        int result = insertOrUpdateAlert(alert);
        if (result > 0) {
            // 处理告警输出
            processAlertOutput(alert);

            // 添加到结果数据
            resultData.put("alert_id", alert.getId());
        } else {
            throw new Exception("保存告警失败");
        }

        // 返回成功结果
        resultData.put("timestamp", System.currentTimeMillis());

        return resultData;
    }

    private static void fillDefaultAlert(Alert alert) {
        if (alert.getStatus() == null) {
            alert.setStatus(AlertStatusEnum.TRIGGERED.getCode());
        }
        if (alert.getSeverity() == null) {
            alert.setSeverity(AlertSeverityEnum.LOW.getCode());
        }
        if (alert.getDetectedAt() == null) {
            alert.setDetectedAt(System.currentTimeMillis());
        }
        if (alert.getCreateAt() == null) {
            alert.setCreateAt(System.currentTimeMillis());
        }
        if (alert.getUpdateAt() == null) {
            alert.setUpdateAt(System.currentTimeMillis());
        }
        if (StringUtils.isEmpty(alert.getTags())) {
            alert.setTags("{}");
        }
        if (StringUtils.isEmpty(alert.getAttributes())) {
            alert.setAttributes("{}");
        }
        alert.setIsDel(0);

        // 根据sourceType自动设置alertCategory
        alert.setAlertCategoryBySourceType();
    }

    /**
     * 批量接收告警
     *
     * @param alerts 告警对象数组
     * @return 处理结果数据
     */
    @Override
    public Map<String, Object> receiveBatchAlerts(Alert[] alerts) throws Exception {
        // 初始化结果数据
        Map<String, Object> resultData = new HashMap<>();
        List<Long> alertIds = new ArrayList<>();

        // 处理每个告警
        for (Alert alert : alerts) {
            try {
                Map<String, Object> result = receiveAlert(alert);
                if (result.containsKey("alert_id")) {
                    alertIds.add((Long) result.get("alert_id"));
                }
            } catch (Exception e) {
                // 记录错误但继续处理
                logger.error("接收告警失败: {}", e.getMessage(), e);
            }
        }

        // 返回成功结果
        resultData.put("count", alertIds.size());
        resultData.put("alert_ids", alertIds);
        resultData.put("timestamp", System.currentTimeMillis());

        return resultData;
    }

    /**
     * 处理告警输出
     * 根据告警的规则名称、源类型和源标识查找匹配的输出配置
     *
     * @param alert 告警对象
     * @return 处理结果
     */
    private boolean processAlertOutput(Alert alert) {
        try {

            //获取所有规则
            List<AlertOutputConfig> alertOutputConfigs = alertOutputConfigService.selectOutputConfigList(new AlertOutputConfig());
            if (alertOutputConfigs == null || alertOutputConfigs.isEmpty()) {
                logger.info("未找到与告警匹配的输出配置，告警ID: {}, 规则名称: {}, 组名称: {}, 源类型: {}, 源标识: {}",
                        alert.getId(), alert.getRuleName(), alert.getGroupName(), alert.getSourceType(), alert.getSourceIdent());
                return false;
            }

            List<AlertOutputConfig> filterConfigs = filterOutputConfigs(alert, alertOutputConfigs);

            if (filterConfigs.isEmpty()) {
                logger.info("未找到与告警匹配的输出配置，告警ID: {}, 规则名称: {}, 组名称: {}, 源类型: {}, 源标识: {}",
                        alert.getId(), alert.getRuleName(), alert.getGroupName(), alert.getSourceType(), alert.getSourceIdent());
                return false;
            }

            boolean result = true;
            boolean flashDutySuccess = false;

            // 遍历所有匹配的输出配置
            for (AlertOutputConfig outputConfig : filterConfigs) {

                // 根据输出方式处理，目前输出渠道只有flashDuty，不做outputTarget的判断
                if ("forward_raw".equals(outputConfig.getOutputType())) {
                    // 转发元数据
                    logger.info("转发告警原始数据，告警ID: {}, 输出配置ID: {}",
                            alert.getId(), outputConfig.getId());
                    boolean forwardResult = forwardRawData(alert, outputConfig);
                    result &= forwardResult;
                    if (forwardResult) {
                        flashDutySuccess = true;
                    }
                } else if ("send_standard_alert".equals(outputConfig.getOutputType())) {
                    // 发送标准事件
                    logger.info("发送标准告警事件，告警ID: {}, 输出配置ID: {}",
                            alert.getId(), outputConfig.getId());
                    boolean sendResult = sendStandardAlert(alert, outputConfig);
                    result &= sendResult;
                    if (sendResult) {
                        flashDutySuccess = true;
                    }
                }
            }

            // 如果FlashDuty保存成功且告警包含eventId，则调用异步任务同步故障信息
            if (flashDutySuccess && alert.getEventId() != null && !alert.getEventId().trim().isEmpty()) {
                logger.info("FlashDuty保存成功，开始异步同步故障信息，告警ID: {}, eventId: {}", alert.getId(), alert.getEventId());
                alertAsyncService.asyncSyncFlashDutyIncident(alert);
            }

            return result;
        } catch (Exception e) {
            logger.error("处理告警输出时发生错误", e);
            return false;
        }
    }

    private List<AlertOutputConfig> filterOutputConfigs(Alert alert, List<AlertOutputConfig> alertOutputConfigs) {
        List<AlertOutputConfig> filterConfigs = new ArrayList<>();

        alertOutputConfigs.forEach(config -> {
            if (!Objects.equals(config.getStatus(), "enabled")) {
                return;
            }

            if (StringUtils.isNotEmpty(config.getAlertRuleName())) {
                if (!Objects.equals(config.getAlertRuleName(), alert.getRuleName())) {
                    return;
                }
            }

            if (StringUtils.isNotEmpty(config.getGroupName())) {
                if (!Objects.equals(config.getGroupName(), alert.getGroupName())) {
                    return;
                }
            }

            if (StringUtils.isNotEmpty(config.getAlertSourceType())) {
                if (!Objects.equals(config.getAlertSourceType(), alert.getSourceType())) {
                    return;
                }
            }

            if (StringUtils.isNotEmpty(config.getAlertSourceIdent())) {
                if (!Objects.equals(config.getAlertSourceIdent(), alert.getSourceIdent())) {
                    return;
                }
            }

            //匹配身份token
            if (StringUtils.isNotEmpty(config.getInputPushUrl())) {
                String ident = UrlParameterExtractor.extractParameter(config.getInputPushUrl(), "ident");
                if (StringUtils.isNotEmpty(ident)) {
                    if (!Objects.equals(ident, alert.getInputIdent())) {
                        return;
                    }
                }

            }

            if (StringUtils.isNotEmpty(config.getAlertRuleFilter())) {
                if (!matchTagFilter(alert, config.getAlertRuleFilter())) {
                    return;
                }
            }


            filterConfigs.add(config);
        });
        return filterConfigs;
    }

    /**
     * 检查告警是否匹配标签过滤规则
     *
     * @param alert          告警对象
     * @param tagFilterRules 标签过滤规则
     * @return 是否匹配
     */
    private boolean matchTagFilter(Alert alert, String tagFilterRules) {
        if (tagFilterRules == null || tagFilterRules.isEmpty()) {
            return true;
        }

        try {
            // 解析标签过滤规则
            Map<String, Object> rules = JSON.parseObject(tagFilterRules);

            // 解析告警标签
            Map<String, String> alertTags = new HashMap<>();
            if (alert.getTags() != null && !alert.getTags().isEmpty()) {
                alertTags = JSON.parseObject(alert.getTags(), Map.class);
            }

            // 检查每个过滤规则
            for (Map.Entry<String, Object> entry : rules.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue().toString();

                // 如果是基本字段，直接检查
                if ("severity".equals(key)) {
                    if (!value.equals(alert.getSeverity())) {
                        return false;
                    }
                } else if ("status".equals(key)) {
                    if (!value.equals(alert.getStatus())) {
                        return false;
                    }
                } else if ("alertType".equals(key)) {
                    if (!value.equals(alert.getAlertType())) {
                        return false;
                    }
                } else if ("sourceType".equals(key)) {
                    if (!value.equals(alert.getSourceType())) {
                        return false;
                    }
                } else if ("sourceIdent".equals(key)) {
                    if (!value.equals(alert.getSourceIdent())) {
                        return false;
                    }
                } else if ("groupName".equals(key)) {
                    if (!value.equals(alert.getGroupName())) {
                        return false;
                    }
                } else {
                    // 否则检查标签
                    if (!alertTags.containsKey(key) || !value.equals(alertTags.get(key))) {
                        return false;
                    }
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("解析标签过滤规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送标准告警事件
     *
     * @param alert        告警对象
     * @param outputConfig 输出配置
     * @return 是否成功
     */
    private boolean sendStandardAlert(Alert alert, AlertOutputConfig outputConfig) {
        // 目前只支持 FlashDuty
        String webhookUrl = outputConfig.getOutputWebhookUrl();
        if (webhookUrl == null || webhookUrl.isEmpty()) {
            logger.error("未配置 Webhook URL，输出配置ID: {}", outputConfig.getId());
            return false;
        }

        return flashDutyService.sendFlashDutyStandardAlert(alert, webhookUrl);
    }

    /**
     * 转发原始数据
     *
     * @param alert        告警对象
     * @param outputConfig 输出配置
     * @return 是否成功
     */
    private boolean forwardRawData(Alert alert, AlertOutputConfig outputConfig) {
        // 获取原始数据
        String rawData = alert.getRawData();
        if (rawData == null || rawData.isEmpty()) {
            logger.warn("告警原始数据为空，无法转发，告警ID: {}", alert.getId());
            return false;
        }

        // 获取 Webhook URL
        String webhookUrl = outputConfig.getOutputWebhookUrl();
        if (webhookUrl == null || webhookUrl.isEmpty()) {
            logger.error("未配置 Webhook URL，输出配置ID: {}", outputConfig.getId());
            return false;
        }

        // 调用 FlashDutyService 的方法转发原始数据
        return flashDutyService.forwardRawDataToFlashDuty(rawData, webhookUrl, alert.getId());
    }

    /**
     * 获取告警统计数据
     *
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getAlertStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 总告警数
            int totalCount = alertMapper.selectTotalCount();
            statistics.put("total", totalCount);

            // 严重告警数（critical）
            int criticalCount = alertMapper.selectCountBySeverity("critical");
            statistics.put("critical", criticalCount);

            // 待处理告警数（triggered状态）
            int pendingCount = alertMapper.selectCountByStatus("triggered");
            statistics.put("pending", pendingCount);

            // 已解决告警数
            int resolvedCount = alertMapper.selectCountByStatus("resolved");
            statistics.put("resolved", resolvedCount);

            // 今日新增告警数
            int todayCount = alertMapper.selectTodayCount();
            statistics.put("today", todayCount);

            // 计算趋势数据（与昨天对比）
            // 简化实现，设置默认趋势值
            statistics.put("totalTrend", 5);      // 总告警趋势 +5%
            statistics.put("criticalTrend", -2);  // 严重告警趋势 -2%
            statistics.put("pendingTrend", 3);    // 待处理趋势 +3%
            statistics.put("resolvedTrend", 8);   // 已解决趋势 +8%

        } catch (Exception e) {
            logger.error("获取告警统计数据时发生错误", e);
            // 返回默认值
            statistics.put("total", 0);
            statistics.put("critical", 0);
            statistics.put("pending", 0);
            statistics.put("resolved", 0);
            statistics.put("today", 0);
            statistics.put("totalTrend", 0);
            statistics.put("criticalTrend", 0);
            statistics.put("pendingTrend", 0);
            statistics.put("resolvedTrend", 0);
        }

        return statistics;
    }

    /**
     * 获取告警趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getAlertTrend(Integer days) {
        List<Map<String, Object>> trendData = new ArrayList<>();

        try {
            trendData = alertMapper.selectAlertTrendByDays(days);
        } catch (Exception e) {
            logger.error("获取告警趋势数据时发生错误", e);
        }

        return trendData;
    }

    /**
     * 获取告警分布数据
     *
     * @param type 分布类型（severity, status, sourceType等）
     * @return 分布数据
     */
    @Override
    public List<Map<String, Object>> getAlertDistribution(String type) {
        List<Map<String, Object>> distributionData = new ArrayList<>();

        try {
            switch (type.toLowerCase()) {
                case "severity":
                    distributionData = alertMapper.selectAlertDistributionBySeverity();
                    break;
                case "status":
                    distributionData = alertMapper.selectAlertDistributionByStatus();
                    break;
                case "sourcetype":
                    distributionData = alertMapper.selectAlertDistributionBySourceType();
                    break;
                case "category":
                    distributionData = alertMapper.selectAlertDistributionByCategory();
                    break;
                case "alerttype":
                    // 按告警类型分布
                    distributionData = alertMapper.selectAlertDistributionByAlertType();
                    break;
                case "targetip":
                    // 按目标IP分布（返回TOP10）
                    distributionData = alertMapper.selectAlertDistributionByTargetIp();
                    break;
                default:
                    logger.warn("不支持的分布类型: {}", type);
                    break;
            }
        } catch (Exception e) {
            logger.error("获取告警分布数据时发生错误", e);
        }

        return distributionData;
    }

    /**
     * 获取热点告警源
     *
     * @param limit 返回数量限制
     * @return 热点告警源列表
     */
    @Override
    public List<Map<String, Object>> getTopAlertSources(Integer limit) {
        List<Map<String, Object>> topSources = new ArrayList<>();

        try {
            topSources = alertMapper.selectTopAlertSources(limit);
        } catch (Exception e) {
            logger.error("获取热点告警源时发生错误", e);
        }

        return topSources;
    }

    /**
     * 获取告警处理统计
     *
     * @param days 统计天数
     * @return 处理统计数据
     */
    @Override
    public Map<String, Object> getResolutionStatistics(Integer days) {
        Map<String, Object> resolutionStats = new HashMap<>();

        try {
            // 总告警数
            int totalCount = alertMapper.selectTotalCount();

            // 已解决告警数
            int resolvedCount = alertMapper.selectCountByStatus("resolved");

            // 今日已解决告警数
            int todayResolved = alertMapper.selectResolvedCountByDays(1);

            // 活跃告警数（非已解决状态）
            int activeAlerts = totalCount - resolvedCount;

            // 平均处理时间（分钟）
            Double avgResolutionTimeHours = alertMapper.selectAvgResolutionTimeByDays(days);
            int avgResolutionTime = avgResolutionTimeHours != null ? (int) (avgResolutionTimeHours * 60) : 0;

            // 解决率
            double resolutionRate = totalCount > 0 ? (double) resolvedCount / totalCount * 100 : 0.0;

            resolutionStats.put("avgResolutionTime", avgResolutionTime);
            resolutionStats.put("resolutionRate", (int) Math.round(resolutionRate));
            resolutionStats.put("todayResolved", todayResolved);
            resolutionStats.put("activeAlerts", activeAlerts);

        } catch (Exception e) {
            logger.error("获取告警处理统计时发生错误", e);
            resolutionStats.put("avgResolutionTime", 0);
            resolutionStats.put("resolutionRate", 0);
            resolutionStats.put("todayResolved", 0);
            resolutionStats.put("activeAlerts", 0);
        }

        return resolutionStats;
    }

    /**
     * 处理告警 - 使用结构体参数
     *
     * @param request 告警解决请求
     * @return 结果
     */
    @Override
    public int resolveAlert(AlertResolutionRequest request) {
        Alert alert = selectAlertById(request.getAlertId());
        if (alert == null) {
            logger.warn("告警不存在，无法处理，告警ID: {}", request.getAlertId());
            return 0;
        }

        //如果没认领过，则解决人自动认领
        if (alert.getAckedAt() == null) {
            //如果认领时间为空，需要使用解决人认领
            alert.setAckedAt(System.currentTimeMillis());
            alert.setAckedBy(request.getResolvedBy());
            alert.setAckedByName(request.getResolvedByName());
        }

        alert.setStatus(AlertStatusEnum.RESOLVED.getCode());
        alert.setResolvedAt(System.currentTimeMillis());
        alert.setResolvedBy(request.getResolvedBy());
        alert.setResolvedByName(request.getResolvedByName());
        alert.setResolutionNote(request.getResolutionNote());
        alert.setRootCause(request.getRootCause());

        // 计算并设置持续时间
        alert.calculateDuration();
        logger.info("告警解决，ID: {}, 持续时间: {}ms ({})",
                request.getAlertId(), alert.getDurationMs(), alert.getFormattedDuration());

        int successCount = alertMapper.updateAlert(alert);
        if (successCount > 0) {
            // 输出告警
            processAlertOutput(alert);

            // 如果提供了 FlashDuty 事件ID，则调用 FlashDuty 解决接口
            String flashDutyIncidentId = getFlashDutyIncidentId(alert);

            if (StringUtils.isEmpty(flashDutyIncidentId)) {
                logger.warn("alert:{} 未查到flashDutyIncidentId 返回", alert.getId());
                return successCount;
            }

            try {
                logger.info("调用 FlashDuty 解决接口，告警ID: {}, 事件ID: {}",
                        request.getAlertId(), flashDutyIncidentId);

                // 获取操作人的appKey
                String appKey = getAppKeyByUserId(Long.parseLong(request.getResolvedBy()));
                if (StringUtils.isEmpty(appKey)) {
                    throw new AppKeyNotConfiguredException("用户未配置FlashDuty应用密钥，无法调用FlashDuty接口");
                }

                // 调用 FlashDuty 解决事件接口，传入appKey
                List<String> incidentIds = Arrays.asList(flashDutyIncidentId);
                FlashDutyResponse.FlashDutyError error = flashDutyService.resolveIncidents(
                        incidentIds, request.getRootCause(), request.getResolutionNote(), appKey);

                if (error != null) {
                    logger.error("FlashDuty 解决接口返回错误，告警ID: {}, 事件ID: {}, 错误码: {}, 错误信息: {}",
                            request.getAlertId(), flashDutyIncidentId,
                            error.getCode(), error.getMessage());
                } else {
                    logger.info("成功调用 FlashDuty 解决接口，告警ID: {}, 事件ID: {}",
                            request.getAlertId(), flashDutyIncidentId);
                }

            } catch (AppKeyNotConfiguredException e) {
                logger.error("用户未配置AppKey，无法调用FlashDuty接口，用户ID: {}, 错误: {}",
                        request.getResolvedBy(), e.getMessage());
                throw e;
            } catch (Exception e) {
                logger.error("调用 FlashDuty 解决接口失败，告警ID: {}, 事件ID: {}, 错误: {}",
                        request.getAlertId(), flashDutyIncidentId, e.getMessage(), e);
            }
        }

        return successCount;
    }

    private String getFlashDutyIncidentId(Alert alert) {
        if (alert.getFdIncidentId() != null) {
            return alert.getFdIncidentId();
        }
        IncidentListRequest incidentListRequest = new IncidentListRequest();
        incidentListRequest.setP(1);
        incidentListRequest.setLimit(1);
        Map<String, String> labels = Maps.newHashMap();
        labels.put("eventId", alert.getEventId());
        incidentListRequest.setLabels(labels);
        long[] dayStartAndEndInSeconds = TimeUtils.getDayStartAndEndInSeconds(alert.getOccurredAt());
        incidentListRequest.setStart_time(dayStartAndEndInSeconds[0]);
        incidentListRequest.setEnd_time(dayStartAndEndInSeconds[1]);
        IncidentListResponse incidentListResponse = flashDutyService.queryIncidentList(incidentListRequest);
        if (incidentListResponse != null && incidentListResponse.getData() != null && CollectionUtils.isNotEmpty(incidentListResponse.getData().getItems())) {
            String incidentId = incidentListResponse.getData().getItems().get(0).getIncident_id();
            logger.info("Alert:{} Get flashDutyIncidentId is:{}", alert.getId(), incidentId);
            return incidentId;
        }
        return null;
    }

    /**
     * 忽略告警 - 使用结构体参数
     *
     * @param request 告警解决请求
     * @return 结果
     */
    @Override
    public int ignoreAlert(AlertResolutionRequest request) {
        Alert alert = selectAlertById(request.getAlertId());
        if (alert == null) {
            logger.warn("告警不存在，无法忽略，告警ID: {}", request.getAlertId());
            return 0;
        }

        //如果没认领过，则解决人自动认领
        if (alert.getAckedAt() == null) {
            //如果认领时间为空，需要使用解决人认领
            alert.setAckedAt(System.currentTimeMillis());
            alert.setAckedBy(request.getResolvedBy());
            alert.setAckedByName(request.getResolvedByName());
        }

        //设置为静默状态
        alert.setStatus(AlertStatusEnum.SUPPRESSED.getCode());
        alert.setResolvedAt(System.currentTimeMillis());
        alert.setResolvedBy(request.getResolvedBy());
        alert.setResolvedByName(request.getResolvedByName());
        alert.setResolutionNote(request.getResolutionNote());

        // 计算并设置持续时间
        alert.calculateDuration();
        logger.info("告警忽略，ID: {}, 持续时间: {}ms ({})",
                request.getAlertId(), alert.getDurationMs(), alert.getFormattedDuration());

        int successCount = alertMapper.updateAlert(alert);

        // 如果提供了 FlashDuty 事件ID，则调用 FlashDuty 解决接口
        if (successCount > 0) {

            String flashDutyIncidentId = getFlashDutyIncidentId(alert);

            if (StringUtils.isEmpty(flashDutyIncidentId)) {
                logger.warn("ignoreAlert alert:{} 未查到flashDutyIncidentId 返回", alert.getId());
                return successCount;
            }

            try {
                logger.info("调用 FlashDuty 解决接口（忽略操作），告警ID: {}, 事件ID: {}",
                        request.getAlertId(), flashDutyIncidentId);

                // 获取操作人的appKey
                String appKey = getAppKeyByUserId(Long.parseLong(request.getResolvedBy()));
                if (StringUtils.isEmpty(appKey)) {
                    throw new AppKeyNotConfiguredException("用户未配置FlashDuty应用密钥，无法调用FlashDuty接口");
                }

                // 调用 FlashDuty 解决事件接口，传入appKey
                List<String> incidentIds = Arrays.asList(flashDutyIncidentId);
                FlashDutyResponse.FlashDutyError error = flashDutyService.resolveIncidents(
                        incidentIds, "告警已忽略", request.getResolutionNote(), appKey);

                if (error != null) {
                    logger.error("FlashDuty 解决接口返回错误（忽略操作），告警ID: {}, 事件ID: {}, 错误码: {}, 错误信息: {}",
                            request.getAlertId(), flashDutyIncidentId,
                            error.getCode(), error.getMessage());
                } else {
                    logger.info("成功调用 FlashDuty 解决接口（忽略操作），告警ID: {}, 事件ID: {}",
                            request.getAlertId(), flashDutyIncidentId);
                }

            } catch (AppKeyNotConfiguredException e) {
                logger.error("用户未配置AppKey，无法调用FlashDuty接口，用户ID: {}, 错误: {}",
                        request.getResolvedBy(), e.getMessage());
                throw e;
            } catch (Exception e) {
                logger.error("调用 FlashDuty 解决接口失败（忽略操作），告警ID: {}, 事件ID: {}, 错误: {}",
                        request.getAlertId(), flashDutyIncidentId, e.getMessage(), e);
                // 注意：FlashDuty 调用失败不影响告警处理结果
            }
        }

        return successCount;
    }

    /**
     * 认领告警 - 使用结构体参数
     *
     * @param request 告警认领请求
     * @return 结果
     */
    @Override
    public int acknowledgeAlerts(AlertAckRequest request) {
        if (request.getAlertIds() == null || request.getAlertIds().isEmpty()) {
            logger.warn("告警ID列表为空，无法认领");
            return 0;
        }

        // 获取单个告警ID（简化逻辑）
        Long alertId = request.getAlertIds().get(0);
        Alert alert = selectAlertById(alertId);
        if (alert == null) {
            logger.warn("告警不存在，无法认领，告警ID: {}", alertId);
            return 0;
        }

        // 检查告警状态，只有未解决的告警才能被认领
        if (alert.isResolved()) {
            logger.warn("告警已解决，无法认领，告警ID: {}", alertId);
            return 0;
        }

        // 更新告警状态为已认领
        alert.setStatus(AlertStatusEnum.ACKNOWLEDGED.getCode());
        alert.setAckedAt(System.currentTimeMillis());
        alert.setAckedBy(request.getAckedBy());
        alert.setAckedByName(request.getAckedByName());

        int updateResult = alertMapper.updateAlert(alert);
        if (updateResult <= 0) {
            logger.error("认领告警失败，ID: {}", alertId);
            return 0;
        }

        logger.info("成功认领告警，ID: {}, 认领人: {}", alertId, request.getAckedByName());

        // 处理FlashDuty事件ID
        String flashDutyIncidentId = null;

        // 优先使用传入的FlashDuty事件ID
        if (request.getFlashDutyIncidentIds() != null && !request.getFlashDutyIncidentIds().isEmpty()) {
            flashDutyIncidentId = request.getFlashDutyIncidentIds().get(0);
            logger.info("使用传入的FlashDuty事件ID: {}", flashDutyIncidentId);
        } else {
            // 如果没有传入，则从告警数据中获取
            flashDutyIncidentId = getFlashDutyIncidentId(alert);
            if (StringUtils.isNotEmpty(flashDutyIncidentId)) {
                logger.info("从告警数据获取FlashDuty事件ID: {}", flashDutyIncidentId);
            }
        }

        // 如果有FlashDuty事件ID，则调用FlashDuty认领接口
        if (StringUtils.isNotEmpty(flashDutyIncidentId)) {
            try {
                logger.info("调用FlashDuty认领接口，事件ID: {}", flashDutyIncidentId);

                // 获取操作人的appKey
                String appKey = getAppKeyByUserId(Long.parseLong(request.getAckedBy()));
                if (StringUtils.isEmpty(appKey)) {
                    throw new AppKeyNotConfiguredException("用户未配置FlashDuty应用密钥，无法调用FlashDuty接口");
                }

                // 调用FlashDuty认领事件接口，传入appKey
                FlashDutyResponse.FlashDutyError error = flashDutyService.acknowledgeIncidents(Arrays.asList(flashDutyIncidentId), appKey);

                if (error != null) {
                    logger.error("FlashDuty认领接口返回错误，事件ID: {}, 错误码: {}, 错误信息: {}",
                            flashDutyIncidentId, error.getCode(), error.getMessage());
                } else {
                    logger.info("成功调用FlashDuty认领接口，事件ID: {}", flashDutyIncidentId);
                }

            } catch (AppKeyNotConfiguredException e) {
                logger.error("用户未配置AppKey，无法调用FlashDuty接口，用户ID: {}, 错误: {}",
                        request.getAckedBy(), e.getMessage());
                throw e;
            } catch (Exception e) {
                logger.error("调用FlashDuty认领接口失败，事件ID: {}, 错误: {}",
                        flashDutyIncidentId, e.getMessage(), e);
            }
        } else {
            logger.info("告警ID: {} 没有关联的FlashDuty事件ID，跳过FlashDuty接口调用", alertId);
        }

        return 1; // 返回成功处理的数量
    }

    /**
     * 批量处理告警 - 使用结构体参数
     *
     * @param requests 告警解决请求列表
     * @return 处理结果
     */
    @Override
    public Map<String, Object> batchProcessAlerts(List<AlertResolutionRequest> requests) {
        Map<String, Object> result = new HashMap<>();
        List<Long> successIds = new ArrayList<>();
        List<Long> failedIds = new ArrayList<>();
        List<Long> alreadyResolvedIds = new ArrayList<>();

        logger.info("开始批量处理告警，数量: {}", requests.size());

        for (AlertResolutionRequest request : requests) {
            Long id = request.getAlertId();
            try {
                // 检查告警状态，跳过已解决的告警
                Alert alert = selectAlertById(id);
                if (alert == null) {
                    logger.warn("告警不存在，跳过处理，ID: {}", id);
                    failedIds.add(id);
                    continue;
                }

                // 检查是否已经解决
                if (alert.isResolved()) {
                    logger.info("告警已解决，跳过处理，ID: {}", id);
                    alreadyResolvedIds.add(id);
                    continue;
                }

                // 根据action类型调用对应的单个处理方法
                int processResult = 0;
                if ("resolve".equals(request.getAction())) {
                    processResult = resolveAlert(request);
                } else if ("ignore".equals(request.getAction())) {
                    processResult = ignoreAlert(request);
                } else {
                    logger.warn("未知的处理动作: {}, 告警ID: {}", request.getAction(), id);
                    failedIds.add(id);
                    continue;
                }

                if (processResult > 0) {
                    successIds.add(id);
                    logger.debug("成功处理告警，ID: {}, 操作: {}", id, request.getAction());
                } else {
                    failedIds.add(id);
                    logger.warn("处理告警失败，ID: {}, 操作: {}", id, request.getAction());
                }

            } catch (Exception e) {
                logger.error("处理告警时发生异常，ID: {}, 操作: {}, 错误: {}", id, request.getAction(), e.getMessage(), e);
                failedIds.add(id);
            }
        }

        String action = requests.isEmpty() ? "unknown" : requests.get(0).getAction();

        // 构建返回结果
        result.put("totalCount", requests.size());
        result.put("successCount", successIds.size());
        result.put("failedCount", failedIds.size());
        result.put("alreadyResolvedCount", alreadyResolvedIds.size());
        result.put("successIds", successIds);
        result.put("failedIds", failedIds);
        result.put("alreadyResolvedIds", alreadyResolvedIds);
        result.put("action", action);
        result.put("timestamp", System.currentTimeMillis());

        logger.info("批量处理告警完成，操作: {}, 总数: {}, 成功: {}, 失败: {}, 已解决: {}",
                action, requests.size(), successIds.size(), failedIds.size(), alreadyResolvedIds.size());

        return result;
    }

    /**
     * 获取可用标签
     * 从现有告警中提取标签信息，用于前端选择器
     *
     * @return 按类别分组的标签列表
     */
    @Override
    public Map<String, List<String>> getAvailableTags() {
        Map<String, List<String>> result = new HashMap<>();

        try {
            // 获取所有非空的标签数据
            List<String> allTagsJson = alertMapper.selectAllTags();

            // 用于去重的Set
            Set<String> environments = new HashSet<>();
            Set<String> services = new HashSet<>();
            Set<String> teams = new HashSet<>();
            Set<String> monitorSystems = new HashSet<>();
            Set<String> others = new HashSet<>();

            // 解析标签JSON
            ObjectMapper objectMapper = new ObjectMapper();
            for (String tagsJson : allTagsJson) {
                try {
                    if (tagsJson != null && !tagsJson.trim().isEmpty()) {
                        Map<String, Object> tags = objectMapper.readValue(tagsJson, Map.class);

                        for (Map.Entry<String, Object> entry : tags.entrySet()) {
                            String key = entry.getKey();
                            Object value = entry.getValue();

                            if (value != null) {
                                String valueStr = value.toString();

                                // 按标签键分类
                                switch (key.toLowerCase()) {
                                    case "environment":
                                    case "env":
                                        environments.add(valueStr);
                                        break;
                                    case "service":
                                    case "app":
                                    case "application":
                                        services.add(valueStr);
                                        break;
                                    case "team":
                                    case "group":
                                    case "owner":
                                        teams.add(valueStr);
                                        break;
                                    case "monitor_system":
                                    case "monitoring":
                                    case "source":
                                        monitorSystems.add(valueStr);
                                        break;
                                    default:
                                        others.add(key + ":" + valueStr);
                                        break;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.warn("解析标签JSON失败: {}", tagsJson, e);
                }
            }

            // 转换为排序后的列表
            result.put("environment", new ArrayList<>(environments));
            result.put("service", new ArrayList<>(services));
            result.put("team", new ArrayList<>(teams));
            result.put("monitor_system", new ArrayList<>(monitorSystems));
            result.put("others", new ArrayList<>(others));

            // 对每个类别的标签进行排序
            result.values().forEach(Collections::sort);

        } catch (Exception e) {
            logger.error("获取可用标签失败", e);
            // 返回空的结果集
            result.put("environment", new ArrayList<>());
            result.put("service", new ArrayList<>());
            result.put("team", new ArrayList<>());
            result.put("monitor_system", new ArrayList<>());
            result.put("others", new ArrayList<>());
        }

        return result;
    }

    /**
     * 获取每日告警统计数据
     * 按天分组统计告警数量，用于30天内新增事件图表
     *
     * @param days 统计天数
     * @return 每日统计数据列表
     */
    @Override
    public List<Map<String, Object>> getDailyAlertStatistics(Integer days) {
        List<Map<String, Object>> dailyStats = new ArrayList<>();

        try {
            // 调用Mapper方法获取每日统计数据
            dailyStats = alertMapper.selectDailyAlertStatistics(days);

            // 如果没有数据，生成默认的空数据结构
            if (dailyStats.isEmpty()) {
                for (int i = days - 1; i >= 0; i--) {
                    Map<String, Object> dayData = new HashMap<>();
                    long timestamp = System.currentTimeMillis() - (i * 24 * 60 * 60 * 1000L);
                    dayData.put("date", new java.text.SimpleDateFormat("yyyy-MM-dd").format(new java.util.Date(timestamp)));
                    dayData.put("count", 0);
                    dailyStats.add(dayData);
                }
            }
        } catch (Exception e) {
            logger.error("获取每日告警统计数据时发生错误", e);
        }

        return dailyStats;
    }

    /**
     * 获取最近入侵事件列表
     * 返回最近的告警事件，显示告警类型中文名和目标主机IP
     *
     * @param limit 返回数量限制
     * @return 最近事件列表
     */
    @Override
    public List<Map<String, Object>> getRecentIntrusionEvents(Integer limit) {
        List<Map<String, Object>> recentEvents = new ArrayList<>();

        try {
            // 调用Mapper方法获取最近的告警事件（已过滤targetIp不为空的数据）
            List<Alert> alerts = alertMapper.selectRecentAlerts(limit);

            for (Alert alert : alerts) {
                Map<String, Object> eventData = new HashMap<>();

                eventData.put("id", alert.getId());
                eventData.put("title", alert.getTitle() != null ? alert.getTitle() : "未知告警");
                eventData.put("alertType", alert.getAlertType());
                eventData.put("dstIp", alert.getDstIp());
                eventData.put("severity", alert.getSeverity());
                eventData.put("occurredAt", alert.getOccurredAt());
                eventData.put("time", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                        .format(new java.util.Date(alert.getOccurredAt())));

                recentEvents.add(eventData);
            }
        } catch (Exception e) {
            logger.error("获取最近入侵事件时发生错误", e);
        }

        return recentEvents;
    }

    /**
     * 获取告警类型的中文名称
     *
     * @param alertType 告警类型
     * @return 中文名称
     */
    private String getAlertTypeChineseName(String alertType) {
        if (alertType == null || alertType.trim().isEmpty()) {
            return "未知告警";
        }

        // 根据告警类型返回对应的中文名称
        switch (alertType.toLowerCase()) {
            case "security_intrusion":
                return "安全入侵";
            case "bruteforce_attack":
                return "暴力破解";
            case "suspicious_activity":
                return "可疑活动";
            case "malware_detection":
                return "恶意软件";
            case "unauthorized_access":
                return "未授权访问";
            case "data_breach":
                return "数据泄露";
            case "system_anomaly":
                return "系统异常";
            case "network_attack":
                return "网络攻击";
            case "privilege_escalation":
                return "权限提升";
            case "file_integrity":
                return "文件完整性";
            default:
                // 如果没有匹配的类型，返回原始类型（首字母大写）
                return alertType.substring(0, 1).toUpperCase() + alertType.substring(1).toLowerCase();
        }
    }

    /**
     * 获取人员处理统计（近一周）
     */
    @Override
    public List<Map<String, Object>> getPersonnelResolutionStats() {
        try {
            List<Map<String, Object>> stats = alertMapper.getPersonnelResolutionStats();

            // 对结果按解决数量降序排序
            stats.sort((a, b) -> {
                Integer countA = Integer.valueOf(a.get("resolvedCount").toString());
                Integer countB = Integer.valueOf(b.get("resolvedCount").toString());
                return Integer.compare(countB != null ? countB : 0, countA != null ? countA : 0);
            });

            logger.info("获取人员处理统计成功，共{}条记录", stats.size());
            return stats;
        } catch (Exception e) {
            logger.error("获取人员处理统计失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取告警对象TOP排行
     */
    @Override
    public List<Map<String, Object>> getTopTargets(Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }

            List<Map<String, Object>> topTargets = alertMapper.getTopTargets(limit);

            logger.info("获取告警对象TOP{}成功，共{}条记录", limit, topTargets.size());
            return topTargets;
        } catch (Exception e) {
            logger.error("获取告警对象TOP{}失败", limit, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Alert> selectAlertBySourceIdent(String sourceIdent) {
        return alertMapper.selectAlertBySourceIdent(sourceIdent);
    }

    @Override
    public List<Alert> selectAlertByInputIdent(String inputIdent) {
        return alertMapper.selectAlertByInputIdent(inputIdent);
    }

    @Override
    public List<Alert> selectAlertBySourceIdentAndInputIdent(String sourceIdent, String inputIdent) {
        return alertMapper.selectAlertBySourceIdentAndInputIdent(sourceIdent, inputIdent);
    }

    /**
     * 根据用户ID获取appKey
     *
     * @param userId 用户ID
     * @return appKey
     */
    private String getAppKeyByUserId(Long userId) {
        return sysUserAccountService.getAppKeyByUserId(userId);
    }

}
