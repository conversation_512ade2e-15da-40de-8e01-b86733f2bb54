-- ----------------------------
-- Alert 表查询示例
-- 针对新的搜索条件提供 SQL 查询建议
-- ----------------------------

-- 1. 基于 targetIp 的查询
-- 直接字段查询，性能较好
SELECT * FROM alert
WHERE target_ip = '*************'
  AND is_del = 0;

-- IP 地址范围查询
SELECT * FROM alert
WHERE target_ip LIKE '192.168.1.%'
  AND is_del = 0;

-- 2. 基于 tags.service 的服务查询（推荐方式）
-- 精确匹配服务名称
SELECT * FROM alert
WHERE JSON_EXTRACT(tags, '$.service') = 'user-api'
  AND is_del = 0;

-- 模糊匹配服务名称
SELECT * FROM alert
WHERE JSON_EXTRACT(tags, '$.service') LIKE '%api%'
  AND is_del = 0;

-- 2.1 备用方案：基于规则名称或组名称的查询
-- 当 tags.service 不存在时的备用搜索
SELECT * FROM alert
WHERE (rule_name LIKE '%web-service%' OR group_name LIKE '%web-service%')
  AND is_del = 0;

-- 3. 基于 tags JSON 字段的查询
-- 3.1 精确匹配标签值
SELECT * FROM alert
WHERE JSON_EXTRACT(tags, '$.environment') = 'production'
  AND is_del = 0;

-- 3.2 检查标签是否存在
SELECT * FROM alert
WHERE JSON_CONTAINS_PATH(tags, 'one', '$.service')
  AND is_del = 0;

-- 3.3 标签值包含查询
SELECT * FROM alert
WHERE JSON_EXTRACT(tags, '$.service') LIKE '%api%'
  AND is_del = 0;

-- 3.4 多个标签条件组合
SELECT * FROM alert
WHERE JSON_EXTRACT(tags, '$.environment') = 'production'
  AND JSON_EXTRACT(tags, '$.service') LIKE '%web%'
  AND is_del = 0;

-- 3.5 标签键值对搜索（用户输入 key:value 格式）
-- 例如用户输入 "environment:prod"
SELECT * FROM alert
WHERE JSON_EXTRACT(tags, '$.environment') LIKE '%prod%'
  AND is_del = 0;

-- 3.6 复杂标签搜索（包含多个条件）
SELECT * FROM alert
WHERE JSON_CONTAINS(tags, '{"environment": "production", "team": "backend"}')
  AND is_del = 0;

-- 4. 性能优化建议
-- 4.1 为常用的 JSON 查询路径创建虚拟列索引
ALTER TABLE alert ADD COLUMN environment_tag VARCHAR(50)
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.environment'))) VIRTUAL;
CREATE INDEX idx_environment_tag ON alert(environment_tag);

ALTER TABLE alert ADD COLUMN service_tag VARCHAR(100)
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.service'))) VIRTUAL;
CREATE INDEX idx_service_tag ON alert(service_tag);

ALTER TABLE alert ADD COLUMN team_tag VARCHAR(100)
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.team'))) VIRTUAL;
CREATE INDEX idx_team_tag ON alert(team_tag);

-- 4.2 使用虚拟列进行查询（性能更好）
SELECT * FROM alert
WHERE environment_tag = 'production'
  AND service_tag LIKE '%web%'
  AND is_del = 0;

-- 5. 复合查询示例
-- 结合多个搜索条件的完整查询
SELECT * FROM alert
WHERE 1=1
  AND (title LIKE '%CPU%' OR title IS NULL)
  AND (source_type = 'grafana' OR source_type IS NULL)
  AND (severity = 'critical' OR severity IS NULL)
  AND (status = 'new' OR status IS NULL)
  AND (JSON_EXTRACT(tags, '$.service') LIKE '%web%' OR JSON_EXTRACT(tags, '$.service') IS NULL)
  AND (source_ip = '*************' OR source_ip IS NULL)
  AND (target_ip = '*************' OR target_ip IS NULL)
  AND (JSON_EXTRACT(tags, '$.environment') LIKE '%prod%' OR tags IS NULL)
  AND occurred_at BETWEEN 1640995200000 AND 1641081600000
  AND is_del = 0
ORDER BY occurred_at DESC
LIMIT 10 OFFSET 0;

-- 6. 标签搜索的解析逻辑
-- 用户输入格式：
-- - "environment:production" -> JSON_EXTRACT(tags, '$.environment') = 'production'
-- - "service:web" -> JSON_EXTRACT(tags, '$.service') LIKE '%web%'
-- - "team:backend,environment:prod" -> 多个条件 AND 连接

-- 7. 全文搜索建议（可选）
-- 如果需要更复杂的标签搜索，可以考虑使用全文索引
-- ALTER TABLE alert ADD FULLTEXT(tags);
-- SELECT * FROM alert WHERE MATCH(tags) AGAINST('production web backend' IN BOOLEAN MODE);

-- 8. 统计查询示例
-- 按环境统计告警数量
SELECT
  JSON_UNQUOTE(JSON_EXTRACT(tags, '$.environment')) as environment,
  COUNT(*) as alert_count
FROM alert
WHERE is_del = 0
GROUP BY JSON_UNQUOTE(JSON_EXTRACT(tags, '$.environment'));

-- 按服务统计告警数量
SELECT
  JSON_UNQUOTE(JSON_EXTRACT(tags, '$.service')) as service,
  COUNT(*) as alert_count
FROM alert
WHERE is_del = 0
GROUP BY JSON_UNQUOTE(JSON_EXTRACT(tags, '$.service'));

-- 9. 查询性能监控
-- 查看查询执行计划
EXPLAIN SELECT * FROM alert
WHERE JSON_EXTRACT(tags, '$.environment') = 'production'
  AND target_ip = '*************'
  AND is_del = 0;

-- 10. 建议的 MyBatis 动态 SQL 示例
/*
<select id="selectAlertList" parameterType="AlertQuery" resultMap="AlertResult">
    SELECT * FROM alert
    <where>
        is_del = 0
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND source_type = #{sourceType}
        </if>
        <if test="severity != null and severity != ''">
            AND severity = #{severity}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="serviceName != null and serviceName != ''">
            AND JSON_EXTRACT(tags, '$.service') LIKE CONCAT('%', #{serviceName}, '%')
        </if>
        <if test="sourceIp != null and sourceIp != ''">
            AND source_ip = #{sourceIp}
        </if>
        <if test="targetIp != null and targetIp != ''">
            AND target_ip = #{targetIp}
        </if>
        <if test="tagSearch != null and tagSearch != ''">
            <choose>
                <when test="tagSearchType == 'exact'">
                    AND JSON_EXTRACT(tags, CONCAT('$.', SUBSTRING_INDEX(#{tagSearch}, ':', 1))) = SUBSTRING_INDEX(#{tagSearch}, ':', -1)
                </when>
                <otherwise>
                    AND JSON_EXTRACT(tags, CONCAT('$.', SUBSTRING_INDEX(#{tagSearch}, ':', 1))) LIKE CONCAT('%', SUBSTRING_INDEX(#{tagSearch}, ':', -1), '%')
                </otherwise>
            </choose>
        </if>
        <if test="startTime != null and endTime != null">
            AND occurred_at BETWEEN #{startTime} AND #{endTime}
        </if>
    </where>
    ORDER BY occurred_at DESC
</select>
*/
