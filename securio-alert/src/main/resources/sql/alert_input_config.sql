-- ----------------------------
-- 告警输入配置表
-- ----------------------------
drop table if exists alert_input_config;
create table alert_input_config (
  id                bigint(20)      not null auto_increment    comment '配置ID',
  name              varchar(100)    not null                   comment '告警源名称',
  source_type       varchar(50)     not null                   comment '告警源类型',
  collection_type   varchar(50)     not null                   comment '数据获取方式',
  log_type          varchar(50)                                comment '日志类型',
  webhook_url       varchar(255)                               comment 'Webhook URL',
  webhook_token     varchar(100)                               comment 'Webhook Token',
  adapter_type      varchar(50)     not null                   comment '适配器类型',
  config_json       text                                       comment '配置JSON',
  status            varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',
  create_at         bigint(20)      not null                   comment '创建时间',
  create_by         varchar(64)     not null                   comment '创建人ID',
  create_name       varchar(100)    not null                   comment '创建人名称',
  update_at         bigint(20)      not null                   comment '更新时间',
  update_by         varchar(64)     not null                   comment '更新人ID',
  update_name       varchar(100)    not null                   comment '更新人名称',
  is_del            tinyint(1)      default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警输入配置表';

-- ----------------------------
-- 告警输出配置表
-- ----------------------------
drop table if exists alert_output_config;
create table alert_output_config (
  id                bigint(20)      not null auto_increment    comment '配置ID',
  name              varchar(100)    not null                   comment '输出名称',
  source_id         bigint(20)      not null                   comment '关联告警源ID',
  source_name       varchar(100)    not null                   comment '关联告警源名称',
  output_type       varchar(50)     not null                   comment '告警方式',
  webhook_url       varchar(255)                               comment '输出Webhook URL',
  filter_type       varchar(50)     not null                   comment '数据筛选方式',
  tag_filter_rules  text                                       comment '标签过滤规则JSON',
  status            varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',
  create_at         bigint(20)      not null                   comment '创建时间',
  create_by         varchar(64)     not null                   comment '创建人ID',
  create_name       varchar(100)    not null                   comment '创建人名称',
  update_at         bigint(20)      not null                   comment '更新时间',
  update_by         varchar(64)     not null                   comment '更新人ID',
  update_name       varchar(100)    not null                   comment '更新人名称',
  is_del            tinyint(1)      default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警输出配置表';

-- ----------------------------
-- 告警字段映射表
-- ----------------------------
drop table if exists alert_field_mapping;
create table alert_field_mapping (
  id                    bigint(20)      not null auto_increment    comment '映射ID',
  source_id             bigint(20)      not null                   comment '告警源ID',
  source_field          varchar(255)    not null                   comment '源字段路径',
  target_field          varchar(100)    not null                   comment '目标字段',
  field_type            varchar(50)     not null                   comment '字段类型',
  default_value         varchar(255)                               comment '默认值',
  transform_expression  varchar(500)                               comment '转换表达式',
  required              varchar(10)     default 'false'            comment '是否必填(true/false)',
  enum_mapping_json     text                                       comment '枚举映射JSON',
  order_num             int(4)          default 0                  comment '排序',
  status                varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',
  create_at             bigint(20)      not null                   comment '创建时间',
  create_by             varchar(64)     not null                   comment '创建人ID',
  create_name           varchar(100)    not null                   comment '创建人名称',
  update_at             bigint(20)      not null                   comment '更新时间',
  update_by             varchar(64)     not null                   comment '更新人ID',
  update_name           varchar(100)    not null                   comment '更新人名称',
  is_del                tinyint(1)      default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警字段映射表';
