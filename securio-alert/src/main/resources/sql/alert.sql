-- ----------------------------
-- 告警表
-- ----------------------------
drop table if exists alert;
create table alert (
  id                bigint(20)      not null auto_increment    comment '告警ID',
  title             varchar(255)    not null                   comment '告警标题',
  description       text                                       comment '告警详细描述',
  severity          varchar(20)     not null                   comment '严重程度(critical/high/medium/low/info)',
  status            varchar(20)     not null                   comment '状态(new/processing/resolved/closed/ignored)',
  alert_type        varchar(50)                                comment '告警类型',
  attack_type       varchar(50)                                comment '攻击类型',
  source_ip         varchar(50)                                comment '源IP地址',
  target_ip         varchar(50)                                comment '目标IP地址',
  url               varchar(255)                               comment 'URL路径',
  event_id          varchar(100)                               comment '事件ID，源系统中的唯一标识',
  source_id         bigint(20)      not null                   comment '告警源ID',
  source_name       varchar(100)    not null                   comment '告警源名称',
  source_instance   varchar(100)                               comment '告警源实例',
  occurred_at       bigint(20)                                 comment '告警发生时间',
  detected_at       bigint(20)                                 comment '告警检测时间',
  resolved_at       bigint(20)                                 comment '告警解决时间',
  resolved_by       varchar(64)                                comment '解决人ID',
  resolved_by_name  varchar(100)                               comment '解决人名称',
  resolution_note   text                                       comment '解决说明',
  tags              text                                       comment '告警标签，JSON格式',
  attributes        text                                       comment '告警属性，JSON格式',
  raw_data          text                                       comment '原始数据',
  create_at         bigint(20)      not null                   comment '创建时间',
  create_by         varchar(64)     not null                   comment '创建人ID',
  create_name       varchar(100)    not null                   comment '创建人名称',
  update_at         bigint(20)      not null                   comment '更新时间',
  update_by         varchar(64)     not null                   comment '更新人ID',
  update_name       varchar(100)    not null                   comment '更新人名称',
  is_del            tinyint(1)      default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警表';
