-- ==================== 告警统计性能优化方案 ====================
-- 作者: securio
-- 日期: 2024-01-01
-- 描述: 针对大数据量告警统计查询的性能优化

-- ==================== 1. 索引优化 ====================

-- 基础查询索引
CREATE INDEX idx_alert_is_del ON alert(is_del);
CREATE INDEX idx_alert_create_at ON alert(create_at);
CREATE INDEX idx_alert_severity ON alert(severity);
CREATE INDEX idx_alert_status ON alert(status);
CREATE INDEX idx_alert_source_type ON alert(source_type);
CREATE INDEX idx_alert_target_ip ON alert(target_ip);
CREATE INDEX idx_alert_alert_type ON alert(alert_type);

-- 复合索引优化（针对统计查询）
CREATE INDEX idx_alert_stat_main ON alert(is_del, create_at, severity, status);
CREATE INDEX idx_alert_resolved ON alert(is_del, resolved_at, create_at);
CREATE INDEX idx_alert_source_stat ON alert(is_del, source_type, source_name, source_ip);
CREATE INDEX idx_alert_target_stat ON alert(is_del, target_ip, severity);

-- 时间分区索引（如果表支持分区）
-- CREATE INDEX idx_alert_date_part ON alert(date(from_unixtime(create_at/1000)), severity, status);

-- ==================== 2. 统计预计算表设计 ====================

-- 日统计表
CREATE TABLE alert_daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_count INT DEFAULT 0 COMMENT '总告警数',
    critical_count INT DEFAULT 0 COMMENT '严重告警数',
    high_count INT DEFAULT 0 COMMENT '高危告警数',
    medium_count INT DEFAULT 0 COMMENT '中危告警数',
    low_count INT DEFAULT 0 COMMENT '低危告警数',
    info_count INT DEFAULT 0 COMMENT '信息告警数',
    triggered_count INT DEFAULT 0 COMMENT '触发状态数',
    acknowledged_count INT DEFAULT 0 COMMENT '确认状态数',
    resolved_count INT DEFAULT 0 COMMENT '已解决数',
    suppressed_count INT DEFAULT 0 COMMENT '沉默状态数',
    avg_resolution_time DECIMAL(10,2) DEFAULT 0 COMMENT '平均处理时间(小时)',
    create_at BIGINT NOT NULL COMMENT '创建时间',
    update_at BIGINT NOT NULL COMMENT '更新时间',
    INDEX idx_daily_date (stat_date),
    INDEX idx_daily_create (create_at)
) COMMENT '告警日统计表';

-- 告警类型统计表
CREATE TABLE alert_type_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    alert_type VARCHAR(100) NOT NULL COMMENT '告警类型',
    count_1d INT DEFAULT 0 COMMENT '1天内数量',
    count_7d INT DEFAULT 0 COMMENT '7天内数量',
    count_30d INT DEFAULT 0 COMMENT '30天内数量',
    critical_count INT DEFAULT 0 COMMENT '严重告警数',
    last_alert_time BIGINT COMMENT '最近告警时间',
    update_at BIGINT NOT NULL COMMENT '更新时间',
    INDEX idx_type_name (alert_type),
    INDEX idx_type_update (update_at)
) COMMENT '告警类型统计表';

-- 目标IP统计表
CREATE TABLE alert_target_ip_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    target_ip VARCHAR(45) NOT NULL COMMENT '目标IP',
    count_1d INT DEFAULT 0 COMMENT '1天内数量',
    count_7d INT DEFAULT 0 COMMENT '7天内数量',
    count_30d INT DEFAULT 0 COMMENT '30天内数量',
    critical_count INT DEFAULT 0 COMMENT '严重告警数',
    high_count INT DEFAULT 0 COMMENT '高危告警数',
    last_alert_time BIGINT COMMENT '最近告警时间',
    update_at BIGINT NOT NULL COMMENT '更新时间',
    INDEX idx_ip (target_ip),
    INDEX idx_ip_update (update_at),
    INDEX idx_ip_count (count_7d DESC)
) COMMENT '目标IP统计表';

-- 告警源统计表
CREATE TABLE alert_source_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    source_type VARCHAR(50) COMMENT '源类型',
    source_name VARCHAR(200) COMMENT '源名称',
    source_ip VARCHAR(45) COMMENT '源IP',
    count_1d INT DEFAULT 0 COMMENT '1天内数量',
    count_7d INT DEFAULT 0 COMMENT '7天内数量',
    count_30d INT DEFAULT 0 COMMENT '30天内数量',
    critical_count INT DEFAULT 0 COMMENT '严重告警数',
    last_alert_time BIGINT COMMENT '最近告警时间',
    update_at BIGINT NOT NULL COMMENT '更新时间',
    INDEX idx_source_type (source_type),
    INDEX idx_source_name (source_name),
    INDEX idx_source_count (count_7d DESC),
    INDEX idx_source_update (update_at)
) COMMENT '告警源统计表';

-- ==================== 3. 存储过程 - 统计数据预计算 ====================

DELIMITER $$

-- 日统计数据计算存储过程
CREATE PROCEDURE calculate_daily_stats(IN target_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 删除当日数据（重新计算）
    DELETE FROM alert_daily_stats WHERE stat_date = target_date;

    -- 插入当日统计数据
    INSERT INTO alert_daily_stats (
        stat_date, total_count, critical_count, high_count, medium_count, low_count, info_count,
        triggered_count, acknowledged_count, resolved_count, suppressed_count,
        avg_resolution_time, create_at, update_at
    )
    SELECT 
        target_date as stat_date,
        COUNT(1) as total_count,
        SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as critical_count,
        SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as high_count,
        SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as medium_count,
        SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as low_count,
        SUM(CASE WHEN severity = 'info' THEN 1 ELSE 0 END) as info_count,
        SUM(CASE WHEN status = 'triggered' THEN 1 ELSE 0 END) as triggered_count,
        SUM(CASE WHEN status = 'acknowledged' THEN 1 ELSE 0 END) as acknowledged_count,
        SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_count,
        SUM(CASE WHEN status = 'suppressed' THEN 1 ELSE 0 END) as suppressed_count,
        AVG(CASE WHEN resolved_at IS NOT NULL AND resolved_at > create_at 
            THEN (resolved_at - create_at) / (1000 * 60 * 60) ELSE NULL END) as avg_resolution_time,
        UNIX_TIMESTAMP() * 1000 as create_at,
        UNIX_TIMESTAMP() * 1000 as update_at
    FROM alert 
    WHERE DATE(FROM_UNIXTIME(create_at/1000)) = target_date 
      AND is_del = 0;

    COMMIT;
END$$

-- 告警类型统计计算存储过程
CREATE PROCEDURE calculate_alert_type_stats()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 清空表
    TRUNCATE TABLE alert_type_stats;

    -- 重新计算
    INSERT INTO alert_type_stats (
        alert_type, count_1d, count_7d, count_30d, critical_count, last_alert_time, update_at
    )
    SELECT 
        COALESCE(alert_type, '未知类型') as alert_type,
        SUM(CASE WHEN create_at >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 1 DAY)) * 1000 THEN 1 ELSE 0 END) as count_1d,
        SUM(CASE WHEN create_at >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 7 DAY)) * 1000 THEN 1 ELSE 0 END) as count_7d,
        SUM(CASE WHEN create_at >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 30 DAY)) * 1000 THEN 1 ELSE 0 END) as count_30d,
        SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as critical_count,
        MAX(create_at) as last_alert_time,
        UNIX_TIMESTAMP() * 1000 as update_at
    FROM alert 
    WHERE is_del = 0
    GROUP BY alert_type;

    COMMIT;
END$$

-- 目标IP统计计算存储过程
CREATE PROCEDURE calculate_target_ip_stats()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 清空表
    TRUNCATE TABLE alert_target_ip_stats;

    -- 重新计算TOP100
    INSERT INTO alert_target_ip_stats (
        target_ip, count_1d, count_7d, count_30d, critical_count, high_count, last_alert_time, update_at
    )
    SELECT 
        target_ip,
        SUM(CASE WHEN create_at >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 1 DAY)) * 1000 THEN 1 ELSE 0 END) as count_1d,
        SUM(CASE WHEN create_at >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 7 DAY)) * 1000 THEN 1 ELSE 0 END) as count_7d,
        SUM(CASE WHEN create_at >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 30 DAY)) * 1000 THEN 1 ELSE 0 END) as count_30d,
        SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as critical_count,
        SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as high_count,
        MAX(create_at) as last_alert_time,
        UNIX_TIMESTAMP() * 1000 as update_at
    FROM alert 
    WHERE is_del = 0 
      AND target_ip IS NOT NULL 
      AND target_ip != ''
    GROUP BY target_ip
    ORDER BY count_7d DESC
    LIMIT 100;

    COMMIT;
END$$

DELIMITER ;

-- ==================== 4. 定时任务触发器 ====================

-- 创建事件调度器（需要开启事件调度器：SET GLOBAL event_scheduler = ON;）

-- 每小时更新告警类型和IP统计
CREATE EVENT IF NOT EXISTS event_hourly_stats
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    CALL calculate_alert_type_stats();
    CALL calculate_target_ip_stats();
END;

-- 每天凌晨1点计算日统计
CREATE EVENT IF NOT EXISTS event_daily_stats
ON SCHEDULE EVERY 1 DAY
STARTS DATE(NOW()) + INTERVAL 1 DAY + INTERVAL 1 HOUR
DO
BEGIN
    CALL calculate_daily_stats(CURDATE() - INTERVAL 1 DAY);
END;

-- ==================== 5. 数据归档策略 ====================

-- 告警数据归档表（历史数据）
CREATE TABLE alert_archive (
    LIKE alert
) COMMENT '告警归档表';

-- 归档存储过程（将6个月前的数据归档）
DELIMITER $$
CREATE PROCEDURE archive_old_alerts()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 插入归档数据
    INSERT INTO alert_archive 
    SELECT * FROM alert 
    WHERE create_at < UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 6 MONTH)) * 1000
      AND is_del = 0;

    -- 软删除原数据
    UPDATE alert 
    SET is_del = 1, update_at = UNIX_TIMESTAMP() * 1000
    WHERE create_at < UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 6 MONTH)) * 1000
      AND is_del = 0;

    COMMIT;
END$$
DELIMITER ;

-- 每月执行归档
CREATE EVENT IF NOT EXISTS event_monthly_archive
ON SCHEDULE EVERY 1 MONTH
STARTS DATE(NOW()) + INTERVAL 1 MONTH + INTERVAL 2 HOUR
DO
BEGIN
    CALL archive_old_alerts();
END;

-- ==================== 6. 分区表优化（可选） ====================

-- 如果数据量非常大，可以考虑分区表
-- 按月分区的告警表示例
/*
CREATE TABLE alert_partitioned (
    LIKE alert
)
PARTITION BY RANGE (YEAR(FROM_UNIXTIME(create_at/1000)) * 100 + MONTH(FROM_UNIXTIME(create_at/1000))) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    -- ... 更多分区
    PARTITION p999999 VALUES LESS THAN MAXVALUE
);
*/

-- ==================== 7. 缓存相关SQL ====================

-- 创建统计缓存表
CREATE TABLE alert_stats_cache (
    cache_key VARCHAR(100) PRIMARY KEY,
    cache_value TEXT COMMENT '缓存值(JSON)',
    expire_at BIGINT COMMENT '过期时间',
    create_at BIGINT COMMENT '创建时间',
    INDEX idx_cache_expire (expire_at)
) COMMENT '统计缓存表';

-- 清理过期缓存的存储过程
DELIMITER $$
CREATE PROCEDURE clean_expired_cache()
BEGIN
    DELETE FROM alert_stats_cache WHERE expire_at < UNIX_TIMESTAMP() * 1000;
END$$
DELIMITER ;

-- 每小时清理过期缓存
CREATE EVENT IF NOT EXISTS event_clean_cache
ON SCHEDULE EVERY 1 HOUR
DO
BEGIN
    CALL clean_expired_cache();
END; 