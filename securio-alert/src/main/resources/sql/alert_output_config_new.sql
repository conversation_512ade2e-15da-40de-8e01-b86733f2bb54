-- ----------------------------
-- 告警输出配置表（新版）
-- ----------------------------
drop table if exists alert_output_config;
create table alert_output_config (
  id                    bigint(20)      not null auto_increment    comment '配置ID',
  name                  varchar(100)    not null                   comment '告警输出名称',
  alert_source_type     varchar(50)     not null                   comment '告警类型(牧云/网宿/n9e等)',
  alert_source_ident    varchar(100)    not null                   comment '告警源实例唯一标识',
  alert_source_name     varchar(100)    not null                   comment '告警源实例名称',
  alert_rule_name       varchar(255)    not null                   comment '告警规则名称，用于筛选数据',
  group_name            varchar(100)                               comment '告警组名称，用于筛选数据',
  alert_rule_filter     text                                       comment '告警信息筛选规则，基于标签，一组kv的json数据',
  output_target         varchar(50)     not null                   comment '告警输出目标(flashDuty等)',
  output_type           varchar(50)     not null                   comment '告警输出方式(forward_raw/send_standard_alert)',
  output_webhook_url    varchar(255)    not null                   comment '告警输出webhookUrl',
  status                varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',
  create_at             bigint(20)                                 comment '创建时间',
  create_by             varchar(64)                                comment '创建人id',
  create_name           varchar(100)                               comment '创建人名称',
  update_at             bigint(20)                                 comment '更新时间',
  update_by             varchar(64)                                comment '更新人',
  update_name           varchar(100)                               comment '更新人名称',
  is_del                int(1)          default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警输出配置表';

-- 添加索引
create index idx_alert_output_source_type on alert_output_config (alert_source_type);
create index idx_alert_output_source_ident on alert_output_config (alert_source_ident);
create index idx_alert_output_rule_name on alert_output_config (alert_rule_name);
create index idx_alert_output_group_name on alert_output_config (group_name);
create index idx_alert_output_status on alert_output_config (status);
