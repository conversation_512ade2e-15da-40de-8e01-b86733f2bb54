-- ----------------------------
-- 1. 告警表
-- ----------------------------
drop table if exists alert;
create table alert (
  id                 bigint(20)      not null auto_increment    comment '告警ID',
  title              varchar(255)    not null                   comment '告警标题',
  description        text                                       comment '告警详细描述',
  severity           varchar(20)     not null default 'low'     comment '严重程度(critical/high/medium/low/info)',
  status             varchar(20)     not null default 'new'     comment '状态(new/processing/resolved/closed/ignored)',
  source_id          bigint(20)                                 comment '告警源ID',
  source_name        varchar(100)    default null               comment '告警源名称',
  source_instance    varchar(100)    default null               comment '告警源实例',
  occurred_at        datetime                                   comment '告警发生时间',
  detected_at        datetime                                   comment '告警检测时间',
  resolved_at        datetime                                   comment '告警解决时间',
  resolved_by        varchar(64)     default null               comment '解决人ID',
  resolved_by_name   varchar(100)    default null               comment '解决人名称',
  resolution_note    varchar(500)    default null               comment '解决说明',
  raw_data           text                                       comment '原始数据',

  -- 公共字段
  create_at          datetime        not null default current_timestamp    comment '创建时间',
  create_by          varchar(64)     default null                          comment '创建者ID',
  create_name        varchar(100)    default null                          comment '创建者名称',
  update_at          datetime        default null on update current_timestamp comment '更新时间',
  update_by          varchar(64)     default null                          comment '更新者ID',
  update_name        varchar(100)    default null                          comment '更新者名称',
  is_del             tinyint(1)      not null default 0                    comment '是否删除(0-未删除 1-已删除)',

  primary key (id),
  index idx_source_id (source_id),
  index idx_status (status),
  index idx_severity (severity),
  index idx_occurred_at (occurred_at),
  index idx_is_del (is_del)
) engine=innodb auto_increment=100 comment = '告警表';

-- ----------------------------
-- 2. 告警源配置表
-- ----------------------------
drop table if exists alert_source;
create table alert_source (
  id                 bigint(20)      not null auto_increment    comment '告警源ID',
  name               varchar(100)    not null                   comment '告警源名称',
  type               varchar(50)     not null                   comment '告警源类型',
  description        varchar(500)    default null               comment '告警源描述',
  webhook_token      varchar(64)     default null               comment 'Webhook Token',
  webhook_url        varchar(255)    default null               comment 'Webhook URL',
  adapter_type       varchar(50)     not null                   comment '适配器类型',
  status             varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',
  config_json        text                                       comment '配置JSON',

  -- 公共字段
  create_at          datetime        not null default current_timestamp    comment '创建时间',
  create_by          varchar(64)     default null                          comment '创建者ID',
  create_name        varchar(100)    default null                          comment '创建者名称',
  update_at          datetime        default null on update current_timestamp comment '更新时间',
  update_by          varchar(64)     default null                          comment '更新者ID',
  update_name        varchar(100)    default null                          comment '更新者名称',
  is_del             tinyint(1)      not null default 0                    comment '是否删除(0-未删除 1-已删除)',

  primary key (id),
  unique index idx_webhook_token (webhook_token),
  index idx_type (type),
  index idx_status (status),
  index idx_is_del (is_del)
) engine=innodb auto_increment=100 comment = '告警源配置表';

-- ----------------------------
-- 3. 字段映射配置表
-- ----------------------------
drop table if exists alert_field_mapping;
create table alert_field_mapping (
  id                 bigint(20)      not null auto_increment    comment '映射ID',
  source_id          bigint(20)      not null                   comment '告警源ID',
  source_field       varchar(255)    default null               comment '源字段路径',
  target_field       varchar(100)    not null                   comment '目标字段',
  field_type         varchar(50)     default 'string'           comment '字段类型',
  default_value      varchar(255)    default null               comment '默认值',
  transform_expression varchar(500)  default null               comment '转换表达式',
  required           varchar(10)     default 'false'            comment '是否必填(true/false)',
  enum_mapping_json  text                                       comment '枚举映射JSON',
  order_num          int(4)          default 0                  comment '排序',
  status             varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',

  -- 公共字段
  create_at          datetime        not null default current_timestamp    comment '创建时间',
  create_by          varchar(64)     default null                          comment '创建者ID',
  create_name        varchar(100)    default null                          comment '创建者名称',
  update_at          datetime        default null on update current_timestamp comment '更新时间',
  update_by          varchar(64)     default null                          comment '更新者ID',
  update_name        varchar(100)    default null                          comment '更新者名称',
  is_del             tinyint(1)      not null default 0                    comment '是否删除(0-未删除 1-已删除)',

  primary key (id),
  index idx_source_id (source_id),
  index idx_target_field (target_field),
  index idx_status (status),
  index idx_is_del (is_del)
) engine=innodb auto_increment=100 comment = '字段映射配置表';

-- ----------------------------
-- 4. 通知渠道表
-- ----------------------------
drop table if exists alert_notification_channel;
create table alert_notification_channel (
  id                 bigint(20)      not null auto_increment    comment '渠道ID',
  name               varchar(100)    not null                   comment '渠道名称',
  type               varchar(50)     not null                   comment '渠道类型',
  description        varchar(500)    default null               comment '渠道描述',
  config_json        text                                       comment '渠道配置JSON',
  status             varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',

  -- 公共字段
  create_at          datetime        not null default current_timestamp    comment '创建时间',
  create_by          varchar(64)     default null                          comment '创建者ID',
  create_name        varchar(100)    default null                          comment '创建者名称',
  update_at          datetime        default null on update current_timestamp comment '更新时间',
  update_by          varchar(64)     default null                          comment '更新者ID',
  update_name        varchar(100)    default null                          comment '更新者名称',
  is_del             tinyint(1)      not null default 0                    comment '是否删除(0-未删除 1-已删除)',

  primary key (id),
  index idx_type (type),
  index idx_status (status),
  index idx_is_del (is_del)
) engine=innodb auto_increment=100 comment = '通知渠道表';

-- ----------------------------
-- 5. 通知模板表
-- ----------------------------
drop table if exists alert_notification_template;
create table alert_notification_template (
  id                 bigint(20)      not null auto_increment    comment '模板ID',
  name               varchar(100)    not null                   comment '模板名称',
  type               varchar(50)     not null                   comment '模板类型',
  title_template     text                                       comment '标题模板',
  content_template   text                                       comment '内容模板',
  params_json        text                                       comment '模板参数JSON',
  status             varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',

  -- 公共字段
  create_at          datetime        not null default current_timestamp    comment '创建时间',
  create_by          varchar(64)     default null                          comment '创建者ID',
  create_name        varchar(100)    default null                          comment '创建者名称',
  update_at          datetime        default null on update current_timestamp comment '更新时间',
  update_by          varchar(64)     default null                          comment '更新者ID',
  update_name        varchar(100)    default null                          comment '更新者名称',
  is_del             tinyint(1)      not null default 0                    comment '是否删除(0-未删除 1-已删除)',

  primary key (id),
  index idx_type (type),
  index idx_status (status),
  index idx_is_del (is_del)
) engine=innodb auto_increment=100 comment = '通知模板表';

-- ----------------------------
-- 6. 通知规则表
-- ----------------------------
drop table if exists alert_notification_rule;
create table alert_notification_rule (
  id                 bigint(20)      not null auto_increment    comment '规则ID',
  name               varchar(100)    not null                   comment '规则名称',
  description        varchar(500)    default null               comment '规则描述',
  source_id          bigint(20)      default null               comment '告警源ID',
  severity           varchar(20)     default null               comment '告警级别(critical/high/medium/low/info)',
  channel_id         bigint(20)      not null                   comment '通知渠道ID',
  template_id        bigint(20)      not null                   comment '通知模板ID',
  receivers_json     text                                       comment '接收人JSON',
  time_range_json    text                                       comment '时间范围JSON',
  condition_json     text                                       comment '规则条件JSON',
  status             varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',

  -- 公共字段
  create_at          datetime        not null default current_timestamp    comment '创建时间',
  create_by          varchar(64)     default null                          comment '创建者ID',
  create_name        varchar(100)    default null                          comment '创建者名称',
  update_at          datetime        default null on update current_timestamp comment '更新时间',
  update_by          varchar(64)     default null                          comment '更新者ID',
  update_name        varchar(100)    default null                          comment '更新者名称',
  is_del             tinyint(1)      not null default 0                    comment '是否删除(0-未删除 1-已删除)',

  primary key (id),
  index idx_source_id (source_id),
  index idx_channel_id (channel_id),
  index idx_template_id (template_id),
  index idx_severity (severity),
  index idx_status (status),
  index idx_is_del (is_del)
) engine=innodb auto_increment=100 comment = '通知规则表';

-- ----------------------------
-- 7. 通知记录表
-- ----------------------------
drop table if exists alert_notification_record;
create table alert_notification_record (
  id                 bigint(20)      not null auto_increment    comment '记录ID',
  alert_id           bigint(20)      not null                   comment '告警ID',
  rule_id            bigint(20)      not null                   comment '规则ID',
  channel_id         bigint(20)      not null                   comment '渠道ID',
  template_id        bigint(20)      not null                   comment '模板ID',
  title              text                                       comment '通知标题',
  content            text                                       comment '通知内容',
  status             varchar(20)     default 'success'          comment '通知状态(success/failed)',
  error_message      varchar(500)    default null               comment '错误信息',
  sent_at            datetime                                   comment '发送时间',
  response           text                                       comment '响应内容',

  -- 公共字段
  create_at          datetime        not null default current_timestamp    comment '创建时间',
  create_by          varchar(64)     default null                          comment '创建者ID',
  create_name        varchar(100)    default null                          comment '创建者名称',
  update_at          datetime        default null on update current_timestamp comment '更新时间',
  update_by          varchar(64)     default null                          comment '更新者ID',
  update_name        varchar(100)    default null                          comment '更新者名称',
  is_del             tinyint(1)      not null default 0                    comment '是否删除(0-未删除 1-已删除)',

  primary key (id),
  index idx_alert_id (alert_id),
  index idx_rule_id (rule_id),
  index idx_channel_id (channel_id),
  index idx_status (status),
  index idx_sent_at (sent_at),
  index idx_is_del (is_del)
) engine=innodb auto_increment=100 comment = '通知记录表';
