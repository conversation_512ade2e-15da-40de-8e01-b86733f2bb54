-- ----------------------------
-- 为alert表添加索引
-- ----------------------------

-- 添加状态索引，用于快速查询不同状态的告警
ALTER TABLE alert ADD INDEX idx_alert_status (status);

-- 添加严重程度索引，用于快速查询不同严重程度的告警
ALTER TABLE alert ADD INDEX idx_alert_severity (severity);

-- 添加告警类型索引，用于按告警类型筛选
ALTER TABLE alert ADD INDEX idx_alert_type (alert_type);

-- 添加攻击类型索引，用于按攻击类型筛选
ALTER TABLE alert ADD INDEX idx_attack_type (attack_type);

-- 添加源IP索引，用于按源IP查询
ALTER TABLE alert ADD INDEX idx_source_ip (source_ip);

-- 添加目标IP索引，用于按目标IP查询
ALTER TABLE alert ADD INDEX idx_target_ip (target_ip);

-- 添加事件ID索引，用于快速查找特定事件
ALTER TABLE alert ADD INDEX idx_event_id (event_id);

-- 添加告警源ID索引，用于查询特定告警源的告警
ALTER TABLE alert ADD INDEX idx_source_id (source_id);

-- 添加告警发生时间索引，用于时间范围查询
ALTER TABLE alert ADD INDEX idx_occurred_at (occurred_at);

-- 添加创建时间索引，用于时间范围查询
ALTER TABLE alert ADD INDEX idx_create_at (create_at);

-- 添加是否删除索引，用于过滤已删除记录
ALTER TABLE alert ADD INDEX idx_is_del (is_del);

-- 添加复合索引，用于常见的组合查询场景
ALTER TABLE alert ADD INDEX idx_source_severity_status (source_id, severity, status);
ALTER TABLE alert ADD INDEX idx_severity_status_time (severity, status, occurred_at);
