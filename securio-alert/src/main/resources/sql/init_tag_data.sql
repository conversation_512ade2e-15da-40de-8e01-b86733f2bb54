-- ----------------------------
-- 初始化告警标签测试数据
-- 用于测试动态标签获取功能
-- ----------------------------

-- 插入一些带有标签的测试告警数据
INSERT INTO alert (
    title, description, severity, status, alert_type,
    source_type, source_sub_type, source_ident, source_name, rule_name, group_name,
    occurred_at, detected_at, tags, attributes,
    create_at, create_by, create_name, update_at, update_by, update_name, is_del
) VALUES
-- 生产环境 API 服务告警
(
    'API响应时间过高', 'user-api服务响应时间超过阈值', 'high', 'new', 'performance',
    'grafana', 'grafana-alert', 'grafana-001', 'Grafana监控', 'api-response-time', 'api-services',
    UNIX_TIMESTAMP(NOW()) * 1000, UNIX_TIMESTAMP(NOW()) * 1000,
    '{"environment": "production", "service": "user-api", "team": "backend", "monitor_system": "grafana", "datacenter": "dc1", "cluster": "prod-cluster"}',
    '{"response_time": "2.5s", "threshold": "1s", "endpoint": "/api/v1/users"}',
    UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', 0
),

-- 预发环境数据库告警
(
    '数据库连接数过高', 'MySQL连接数接近上限', 'critical', 'processing', 'database',
    'zabbix', 'zabbix-trigger', 'zabbix-db-01', 'Zabbix监控', 'mysql-connections', 'database-services',
    UNIX_TIMESTAMP(NOW()) * 1000, UNIX_TIMESTAMP(NOW()) * 1000,
    '{"environment": "staging", "service": "mysql-db", "team": "dba", "monitor_system": "zabbix", "datacenter": "dc2", "database_type": "mysql"}',
    '{"current_connections": "950", "max_connections": "1000", "usage_percent": "95%"}',
    UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', 0
),

-- 测试环境前端服务告警
(
    '前端构建失败', 'React应用构建过程中出现错误', 'medium', 'new', 'build',
    'jenkins', 'jenkins-build-01', 'Jenkins构建', 'frontend-build', 'frontend-services',
    UNIX_TIMESTAMP(NOW()) * 1000, UNIX_TIMESTAMP(NOW()) * 1000,
    '{"environment": "test", "service": "web-frontend", "team": "frontend", "monitor_system": "jenkins", "build_type": "react"}',
    '{"build_id": "12345", "error_type": "dependency", "failed_step": "npm install"}',
    UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', 0
),

-- 生产环境订单服务告警
(
    '订单处理延迟', '订单服务处理时间异常', 'high', 'new', 'business',
    'prometheus', 'prom-order-01', 'Prometheus监控', 'order-processing-delay', 'business-services',
    UNIX_TIMESTAMP(NOW()) * 1000, UNIX_TIMESTAMP(NOW()) * 1000,
    '{"environment": "production", "service": "order-service", "team": "backend", "monitor_system": "prometheus", "business_line": "ecommerce", "cluster": "prod-cluster"}',
    '{"avg_processing_time": "5.2s", "threshold": "3s", "queue_length": "1250"}',
    UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', 0
),

-- 开发环境缓存服务告警
(
    'Redis内存使用率高', 'Redis缓存内存使用率超过80%', 'medium', 'resolved', 'infrastructure',
    'n9e', 'n9e-redis-01', '夜莺监控', 'redis-memory-usage', 'cache-services',
    UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 HOUR)) * 1000, UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 HOUR)) * 1000,
    '{"environment": "development", "service": "redis-cache", "team": "devops", "monitor_system": "n9e", "datacenter": "dc1", "cache_type": "redis"}',
    '{"memory_usage": "85%", "max_memory": "8GB", "used_memory": "6.8GB"}',
    UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', 0
),

-- 生产环境支付网关告警
(
    '支付接口异常', '第三方支付接口返回错误率上升', 'critical', 'new', 'payment',
    'grafana', 'grafana-payment-01', 'Grafana监控', 'payment-gateway-errors', 'payment-services',
    UNIX_TIMESTAMP(NOW()) * 1000, UNIX_TIMESTAMP(NOW()) * 1000,
    '{"environment": "production", "service": "payment-gateway", "team": "payment", "monitor_system": "grafana", "business_line": "fintech", "provider": "alipay"}',
    '{"error_rate": "15%", "threshold": "5%", "total_requests": "10000", "failed_requests": "1500"}',
    UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', 0
),

-- 预发环境消息队列告警
(
    '消息队列积压', 'RabbitMQ消息积压严重', 'high', 'processing', 'queue',
    'prometheus', 'prom-mq-01', 'Prometheus监控', 'rabbitmq-queue-backlog', 'messaging-services',
    UNIX_TIMESTAMP(NOW()) * 1000, UNIX_TIMESTAMP(NOW()) * 1000,
    '{"environment": "staging", "service": "rabbitmq", "team": "middleware", "monitor_system": "prometheus", "queue_type": "rabbitmq", "datacenter": "dc2"}',
    '{"queue_length": "50000", "threshold": "10000", "consumer_count": "5", "publish_rate": "1000/s"}',
    UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', 0
),

-- 生产环境安全告警
(
    '异常登录检测', '检测到来自异常IP的登录尝试', 'high', 'new', 'security',
    'waf', 'waf-security-01', 'WAF安全', 'suspicious-login', 'security-services',
    UNIX_TIMESTAMP(NOW()) * 1000, UNIX_TIMESTAMP(NOW()) * 1000,
    '{"environment": "production", "service": "auth-service", "team": "security", "monitor_system": "waf", "attack_type": "brute_force", "source_country": "unknown"}',
    '{"source_ip": "*************", "target_ip": "*********", "attempt_count": "50", "time_window": "5min"}',
    UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', UNIX_TIMESTAMP(NOW()) * 1000, 1, 'admin', 0
);

-- 验证插入的数据
SELECT
    title,
    severity,
    status,
    source_type,
    JSON_EXTRACT(tags, '$.environment') as environment,
    JSON_EXTRACT(tags, '$.service') as service,
    JSON_EXTRACT(tags, '$.team') as team,
    JSON_EXTRACT(tags, '$.monitor_system') as monitor_system
FROM alert
WHERE is_del = 0
ORDER BY occurred_at DESC;
