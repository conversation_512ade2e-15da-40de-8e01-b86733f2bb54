-- ----------------------------
-- 为 alert 表添加 source_sub_type 字段
-- 用于支持更细粒度的告警来源分类
-- ----------------------------

-- 添加 source_sub_type 字段
ALTER TABLE alert ADD COLUMN source_sub_type VARCHAR(64) COMMENT '来源子类型' AFTER source_type;

-- 为新字段创建索引
CREATE INDEX idx_alert_source_sub_type ON alert(source_sub_type);

-- 创建复合索引，支持按来源类型和子类型查询
CREATE INDEX idx_alert_source_types ON alert(source_type, source_sub_type);

-- 更新现有数据的示例（可选）
-- 根据实际业务需求更新现有数据
UPDATE alert SET source_sub_type = 'default' WHERE source_sub_type IS NULL AND is_del = 0;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'alert' 
  AND COLUMN_NAME IN ('source_type', 'source_sub_type')
ORDER BY ORDINAL_POSITION;

-- 查看表结构
DESCRIBE alert;
