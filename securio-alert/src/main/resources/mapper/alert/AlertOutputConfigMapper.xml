<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.alert.mapper.AlertOutputConfigMapper">

    <resultMap type="com.wiwj.securio.alert.domain.AlertOutputConfig" id="AlertOutputConfigResult">
        <id     property="id"                 column="id"                     />
        <result property="name"               column="name"                   />
        <result property="alertSourceType"    column="alert_source_type"      />
        <result property="alertSourceIdent"   column="alert_source_ident"     />
        <result property="alertSourceName"    column="alert_source_name"      />
        <result property="alertRuleName"      column="alert_rule_name"        />
        <result property="groupName"          column="group_name"             />
        <result property="alertRuleFilter"    column="alert_rule_filter"      />
        <result property="alertObjectKey"     column="alert_object_key"       />
        <result property="outputTarget"       column="output_target"          />
        <result property="outputType"         column="output_type"            />
        <result property="outputWebhookUrl"   column="output_webhook_url"     />
        <result property="inputPushUrl"       column="input_push_url"         />
        <result property="status"             column="status"                 />
        <result property="createAt"           column="create_at"              />
        <result property="createBy"           column="create_by"              />
        <result property="createName"         column="create_name"            />
        <result property="updateAt"           column="update_at"              />
        <result property="updateBy"           column="update_by"              />
        <result property="updateName"         column="update_name"            />
        <result property="isDel"              column="is_del"                 />
    </resultMap>

    <sql id="selectAlertOutputConfigVo">
        select id, name, alert_source_type, alert_source_ident, alert_source_name,
               alert_rule_name, group_name, alert_rule_filter, alert_object_key, output_target, output_type,
               output_webhook_url, input_push_url, status, create_at, create_by, create_name,
               update_at, update_by, update_name, is_del
        from alert_output_config
    </sql>

    <select id="selectOutputConfigList" parameterType="com.wiwj.securio.alert.domain.AlertOutputConfig" resultMap="AlertOutputConfigResult">
        <include refid="selectAlertOutputConfigVo"/>
        <where>
            is_del = 0
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="alertSourceType != null and alertSourceType != ''">
                AND alert_source_type = #{alertSourceType}
            </if>
            <if test="alertSourceIdent != null and alertSourceIdent != ''">
                AND alert_source_ident = #{alertSourceIdent}
            </if>
            <if test="alertSourceName != null and alertSourceName != ''">
                AND alert_source_name like concat('%', #{alertSourceName}, '%')
            </if>
            <if test="alertRuleName != null and alertRuleName != ''">
                AND alert_rule_name = #{alertRuleName}
            </if>
            <if test="groupName != null and groupName != ''">
                AND group_name = #{groupName}
            </if>
            <if test="outputTarget != null and outputTarget != ''">
                AND output_target = #{outputTarget}
            </if>
            <if test="outputType != null and outputType != ''">
                AND output_type = #{outputType}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by create_at desc
    </select>

    <select id="selectOutputConfigById" parameterType="Long" resultMap="AlertOutputConfigResult">
        <include refid="selectAlertOutputConfigVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectOutputConfigsBySourceId" parameterType="Long" resultMap="AlertOutputConfigResult">
        <include refid="selectAlertOutputConfigVo"/>
        where source_id = #{sourceId} and status = 'enabled' and is_del = 0
    </select>

    <select id="selectMatchingOutputConfigs" parameterType="com.wiwj.securio.alert.domain.AlertOutputConfig" resultMap="AlertOutputConfigResult">
        <include refid="selectAlertOutputConfigVo"/>
        <where>
            is_del = 0
            <if test="alertRuleName != null and alertRuleName != ''">
                AND alert_rule_name = #{alertRuleName}
            </if>
            <if test="groupName != null and groupName != ''">
                AND group_name = #{groupName}
            </if>
            <if test="alertSourceType != null and alertSourceType != ''">
                AND alert_source_type = #{alertSourceType}
            </if>
            <if test="alertSourceIdent != null and alertSourceIdent != ''">
                AND alert_source_ident = #{alertSourceIdent}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by create_at desc
    </select>

    <insert id="insertOutputConfig" parameterType="com.wiwj.securio.alert.domain.AlertOutputConfig" useGeneratedKeys="true" keyProperty="id">
        insert into alert_output_config (
            name, alert_source_type, alert_source_ident, alert_source_name,
            alert_rule_name, group_name, alert_rule_filter, alert_object_key, output_target, output_type,
            output_webhook_url, input_push_url, status, create_at, create_by, create_name,
            update_at, update_by, update_name
        ) values (
            #{name}, #{alertSourceType}, #{alertSourceIdent}, #{alertSourceName},
            #{alertRuleName}, #{groupName}, #{alertRuleFilter}, #{alertObjectKey}, #{outputTarget}, #{outputType},
            #{outputWebhookUrl}, #{inputPushUrl}, #{status}, #{createAt}, #{createBy}, #{createName},
            #{updateAt}, #{updateBy}, #{updateName}
        )
    </insert>

    <update id="updateOutputConfig" parameterType="com.wiwj.securio.alert.domain.AlertOutputConfig">
        update alert_output_config
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="alertSourceType != null">alert_source_type = #{alertSourceType},</if>
            <if test="alertSourceIdent != null">alert_source_ident = #{alertSourceIdent},</if>
            <if test="alertSourceName != null">alert_source_name = #{alertSourceName},</if>
            <if test="alertRuleName != null">alert_rule_name = #{alertRuleName},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="alertRuleFilter != null">alert_rule_filter = #{alertRuleFilter},</if>
            <if test="alertObjectKey != null">alert_object_key = #{alertObjectKey},</if>
            <if test="outputTarget != null">output_target = #{outputTarget},</if>
            <if test="outputType != null">output_type = #{outputType},</if>
            <if test="outputWebhookUrl != null">output_webhook_url = #{outputWebhookUrl},</if>
            <if test="inputPushUrl != null">input_push_url = #{inputPushUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
        </set>
        where id = #{id} and is_del = 0
    </update>

    <update id="deleteOutputConfigById" parameterType="Long">
        update alert_output_config set is_del = 1 where id = #{id}
    </update>

    <update id="deleteOutputConfigByIds" parameterType="Long">
        update alert_output_config set is_del = 1 where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
