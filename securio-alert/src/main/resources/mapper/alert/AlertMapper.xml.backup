<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.alert.mapper.AlertMapper">

    <resultMap type="com.wiwj.securio.alert.domain.Alert" id="AlertResult">
        <result property="id"                column="id"                />
        <result property="title"             column="title"             />
        <result property="description"       column="description"       />
        <result property="severity"          column="severity"          />
        <result property="status"            column="status"            />
        <result property="alertType"         column="alert_type"        />
        <result property="srcIp"             column="src_ip"            />
        <result property="srcPort"           column="src_port"          />
        <result property="dstIp"             column="dst_ip"            />
        <result property="dstPort"           column="dst_port"          />
        <result property="url"               column="url"               />
        <result property="eventId"           column="event_id"          />
        <result property="fdIncidentId"      column="fd_incident_id"    />
        <result property="sourceType"        column="source_type"       />
        <result property="alertCategory"     column="alert_category"    />
        <result property="sourceSubType"     column="source_sub_type"   />
        <result property="sourceIdent"       column="source_ident"      />
        <result property="sourceName"        column="source_name"       />
        <result property="ruleName"          column="rule_name"         />
        <result property="groupName"         column="group_name"        />
        <result property="occurredAt"        column="occurred_at"       />
        <result property="detectedAt"        column="detected_at"       />
        <result property="resolvedAt"        column="resolved_at"       />
        <result property="ackedAt"           column="acked_at"          />
        <result property="ackedBy"           column="acked_by"          />
        <result property="ackedByName"       column="acked_by_name"     />
        <result property="assignedUserIds"   column="assigned_user_ids" />
        <result property="assignedUserNames" column="assigned_user_names" />
        <result property="channelId"         column="channel_id"        />
        <result property="channelName"       column="channel_name"      />
        <result property="resolvedBy"        column="resolved_by"       />
        <result property="resolvedByName"    column="resolved_by_name"  />
        <result property="resolutionNote"    column="resolution_note"   />
        <result property="rootCause"         column="root_cause"        />
        <result property="durationMs"        column="duration_ms"       />
        <result property="tags"              column="tags"              />
        <result property="attributes"        column="attributes"        />
        <result property="rawData"           column="raw_data"          />
        <result property="createAt"          column="create_at"         />
        <result property="createBy"          column="create_by"         />
        <result property="createName"        column="create_name"       />
        <result property="updateAt"          column="update_at"         />
        <result property="updateBy"          column="update_by"         />
        <result property="updateName"        column="update_name"       />
        <result property="isDel"             column="is_del"            />
        <result property="inputIdent"        column="input_ident"       />
    </resultMap>

    <sql id="selectAlertVo">
        select a.id, a.title, a.description, a.severity, a.status, a.alert_type, a.src_ip, a.src_port, a.dst_ip, a.dst_port, a.url,
               a.event_id, a.fd_incident_id, a.source_type, a.alert_category, a.source_sub_type, a.source_ident, a.source_name, a.rule_name, a.group_name,
               a.occurred_at, a.detected_at, a.resolved_at, a.acked_at, a.acked_by, a.acked_by_name, a.assigned_user_ids, a.assigned_user_names,
               a.channel_id, a.channel_name, a.resolved_by, a.resolved_by_name, a.resolution_note,
               a.root_cause, a.duration_ms, a.tags, a.attributes, a.raw_data,
               a.create_at, a.create_by, a.create_name, a.update_at, a.update_by, a.update_name, a.is_del, a.input_ident
        from alert a
    </sql>

    <select id="selectAlertList" parameterType="com.wiwj.securio.alert.domain.Alert" resultMap="AlertResult">
        <include refid="selectAlertVo"/>
        <where>
            a.is_del = 0
            <if test="id != null "> and a.id = #{id}</if>
            <if test="title != null  and title != ''"> and a.title like concat('%', #{title}, '%')</if>
            <if test="description != null  and description != ''"> and a.description like concat('%', #{description}, '%')</if>
            <if test="severity != null  and severity != ''"> and a.severity = #{severity}</if>
            <if test="status != null  and status != ''"> and a.status = #{status}</if>
            <if test="alertType != null  and alertType != ''"> and a.alert_type = #{alertType}</if>
            <if test="sourceType != null  and sourceType != ''"> and a.source_type = #{sourceType}</if>
            <if test="alertCategory != null  and alertCategory != ''"> and a.alert_category = #{alertCategory}</if>
            <if test="sourceSubType != null  and sourceSubType != ''"> and a.source_sub_type = #{sourceSubType}</if>
            <if test="sourceIdent != null  and sourceIdent != ''"> and a.source_ident = #{sourceIdent}</if>
            <if test="sourceName != null  and sourceName != ''"> and a.source_name like concat('%', #{sourceName}, '%')</if>
            <if test="ruleName != null  and ruleName != ''"> and a.rule_name = #{ruleName}</if>
            <if test="groupName != null  and groupName != ''"> and a.group_name = #{groupName}</if>
            <if test="eventId != null  and eventId != ''"> and a.event_id = #{eventId}</if>
            <if test="srcIp != null  and srcIp != ''"> and a.src_ip = #{srcIp}</if>
            <if test="srcPort != null"> and a.src_port = #{srcPort}</if>
            <if test="dstIp != null  and dstIp != ''"> and a.dst_ip = #{dstIp}</if>
            <if test="dstPort != null"> and a.dst_port = #{dstPort}</if>
            <if test="occurredAt != null "> and a.occurred_at = #{occurredAt}</if>
            <if test="detectedAt != null "> and a.detected_at = #{detectedAt}</if>
            <if test="resolvedAt != null "> and a.resolved_at = #{resolvedAt}</if>
            <if test="resolvedBy != null  and resolvedBy != ''"> and a.resolved_by = #{resolvedBy}</if>
            <if test="resolvedByName != null  and resolvedByName != ''"> and a.resolved_by_name like concat('%', #{resolvedByName}, '%')</if>
            
            <!-- 时间范围查询 -->
            <if test="startTime != null and endTime != null">
                and a.occurred_at between #{startTime} and #{endTime}
            </if>
            <if test="startTime != null and endTime == null">
                and a.occurred_at &gt;= #{startTime}
            </if>
            <if test="startTime == null and endTime != null">
                and a.occurred_at &lt;= #{endTime}
            </if>
            
            <!-- 认领时间范围查询 -->
            <!-- <if test="ackedStartTime != null and ackedEndTime != null">
                and a.acked_at between #{ackedStartTime} and #{ackedEndTime}
            </if>
            <if test="ackedStartTime != null and ackedEndTime == null">
                and a.acked_at &gt;= #{ackedStartTime}
            </if>
            <if test="ackedStartTime == null and ackedEndTime != null">
                and a.acked_at &lt;= #{ackedEndTime}
            </if> -->
            
            <!-- 解决时间范围查询 -->
            <!-- <if test="resolvedStartTime != null and resolvedEndTime != null">
                and a.resolved_at between #{resolvedStartTime} and #{resolvedEndTime}
            </if>
            <if test="resolvedStartTime != null and resolvedEndTime == null">
                and a.resolved_at &gt;= #{resolvedStartTime}
            </if>
            <if test="resolvedStartTime == null and resolvedEndTime != null">
                and a.resolved_at &lt;= #{resolvedEndTime}
            </if> -->
            
            <!-- 标签搜索 - 使用新的 TagSearchUtil 解析 -->
            <if test="tagSearchCondition != null and tagSearchCondition != ''">
                and (${tagSearchCondition})
            </if>
            
            <!-- 服务名称搜索（通过tags.service字段） -->
            <if test="serviceName != null and serviceName != ''">
                and a.tags like concat('%"service":"', #{serviceName}, '"%')
            </if>
        </where>
        order by a.occurred_at desc, a.id desc
    </select>

    <select id="selectAlertById" parameterType="Long" resultMap="AlertResult">
        <include refid="selectAlertVo"/>
        where a.id = #{id} and a.is_del = 0
    </select>

    <insert id="insertAlert" parameterType="com.wiwj.securio.alert.domain.Alert" useGeneratedKeys="true" keyProperty="id">
        insert into alert
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="severity != null">severity,</if>
            <if test="status != null">status,</if>
            <if test="alertType != null">alert_type,</if>
            <if test="srcIp != null">src_ip,</if>
            <if test="srcPort != null">src_port,</if>
            <if test="dstIp != null">dst_ip,</if>
            <if test="dstPort != null">dst_port,</if>
            <if test="url != null">url,</if>
            <if test="eventId != null">event_id,</if>
            <if test="fdIncidentId != null">fd_incident_id,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="alertCategory != null">alert_category,</if>
            <if test="sourceSubType != null">source_sub_type,</if>
            <if test="sourceIdent != null">source_ident,</if>
            <if test="sourceName != null">source_name,</if>
            <if test="ruleName != null">rule_name,</if>
            <if test="groupName != null">group_name,</if>
            <if test="occurredAt != null">occurred_at,</if>
            <if test="detectedAt != null">detected_at,</if>
            <if test="resolvedAt != null">resolved_at,</if>
            <if test="ackedAt != null">acked_at,</if>
            <if test="ackedBy != null">acked_by,</if>
            <if test="ackedByName != null">acked_by_name,</if>
            <if test="assignedUserIds != null">assigned_user_ids,</if>
            <if test="assignedUserNames != null">assigned_user_names,</if>
            <if test="channelId != null">channel_id,</if>
            <if test="channelName != null">channel_name,</if>
            <if test="resolvedBy != null">resolved_by,</if>
            <if test="resolvedByName != null">resolved_by_name,</if>
            <if test="resolutionNote != null">resolution_note,</if>
            <if test="rootCause != null">root_cause,</if>
            <if test="durationMs != null">duration_ms,</if>
            <if test="tags != null">tags,</if>
            <if test="attributes != null">attributes,</if>
            <if test="rawData != null">raw_data,</if>
            <if test="createAt != null">create_at,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createName != null">create_name,</if>
            <if test="updateAt != null">update_at,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateName != null">update_name,</if>
            <if test="isDel != null">is_del,</if>
            <if test="inputIdent != null">input_ident,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="severity != null">#{severity},</if>
            <if test="status != null">#{status},</if>
            <if test="alertType != null">#{alertType},</if>
            <if test="srcIp != null">#{srcIp},</if>
            <if test="srcPort != null">#{srcPort},</if>
            <if test="dstIp != null">#{dstIp},</if>
            <if test="dstPort != null">#{dstPort},</if>
            <if test="url != null">#{url},</if>
            <if test="eventId != null">#{eventId},</if>
            <if test="fdIncidentId != null">#{fdIncidentId},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="alertCategory != null">#{alertCategory},</if>
            <if test="sourceSubType != null">#{sourceSubType},</if>
            <if test="sourceIdent != null">#{sourceIdent},</if>
            <if test="sourceName != null">#{sourceName},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="occurredAt != null">#{occurredAt},</if>
            <if test="detectedAt != null">#{detectedAt},</if>
            <if test="resolvedAt != null">#{resolvedAt},</if>
            <if test="ackedAt != null">#{ackedAt},</if>
            <if test="ackedBy != null">#{ackedBy},</if>
            <if test="ackedByName != null">#{ackedByName},</if>
            <if test="assignedUserIds != null">#{assignedUserIds},</if>
            <if test="assignedUserNames != null">#{assignedUserNames},</if>
            <if test="channelId != null">#{channelId},</if>
            <if test="channelName != null">#{channelName},</if>
            <if test="resolvedBy != null">#{resolvedBy},</if>
            <if test="resolvedByName != null">#{resolvedByName},</if>
            <if test="resolutionNote != null">#{resolutionNote},</if>
            <if test="rootCause != null">#{rootCause},</if>
            <if test="durationMs != null">#{durationMs},</if>
            <if test="tags != null">#{tags},</if>
            <if test="attributes != null">#{attributes},</if>
            <if test="rawData != null">#{rawData},</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createName != null">#{createName},</if>
            <if test="updateAt != null">#{updateAt},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="inputIdent != null">#{inputIdent},</if>
         </trim>
    </insert>

    <update id="updateAlert" parameterType="com.wiwj.securio.alert.domain.Alert">
        update alert
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="status != null">status = #{status},</if>
            <if test="alertType != null">alert_type = #{alertType},</if>
            <if test="srcIp != null">src_ip = #{srcIp},</if>
            <if test="srcPort != null">src_port = #{srcPort},</if>
            <if test="dstIp != null">dst_ip = #{dstIp},</if>
            <if test="dstPort != null">dst_port = #{dstPort},</if>
            <if test="url != null">url = #{url},</if>
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="fdIncidentId != null">fd_incident_id = #{fdIncidentId},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="alertCategory != null">alert_category = #{alertCategory},</if>
            <if test="sourceSubType != null">source_sub_type = #{sourceSubType},</if>
            <if test="sourceIdent != null">source_ident = #{sourceIdent},</if>
            <if test="sourceName != null">source_name = #{sourceName},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="occurredAt != null">occurred_at = #{occurredAt},</if>
            <if test="detectedAt != null">detected_at = #{detectedAt},</if>
            <if test="resolvedAt != null">resolved_at = #{resolvedAt},</if>
            <if test="ackedAt != null">acked_at = #{ackedAt},</if>
            <if test="ackedBy != null">acked_by = #{ackedBy},</if>
            <if test="ackedByName != null">acked_by_name = #{ackedByName},</if>
            <if test="assignedUserIds != null">assigned_user_ids = #{assignedUserIds},</if>
            <if test="assignedUserNames != null">assigned_user_names = #{assignedUserNames},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="channelName != null">channel_name = #{channelName},</if>
            <if test="resolvedBy != null">resolved_by = #{resolvedBy},</if>
            <if test="resolvedByName != null">resolved_by_name = #{resolvedByName},</if>
            <if test="resolutionNote != null">resolution_note = #{resolutionNote},</if>
            <if test="rootCause != null">root_cause = #{rootCause},</if>
            <if test="durationMs != null">duration_ms = #{durationMs},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="attributes != null">attributes = #{attributes},</if>
            <if test="rawData != null">raw_data = #{rawData},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="inputIdent != null">input_ident = #{inputIdent},</if>
        </trim>
        where id = #{id} and is_del = 0
    </update>

    <delete id="deleteAlertById" parameterType="Long">
        delete from alert where id = #{id}
    </delete>

    <delete id="deleteAlertByIds" parameterType="String">
        delete from alert where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="logicDeleteAlertById" parameterType="Long">
        update alert set is_del = 1, update_at = now() where id = #{id} and is_del = 0
    </update>

    <update id="logicDeleteAlertByIds">
        update alert set is_del = 1, update_at = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>

    <!-- 获取所有非空的标签数据 -->
    <select id="selectAllTags" resultType="String">
        select distinct tags
        from alert
        where tags is not null
          and tags != ''
          and tags != '{}'
          and is_del = 0
    </select>

    <!-- ==================== 统计相关SQL ==================== -->

    <!-- 查询告警总数 -->
    <select id="selectTotalCount" resultType="int">
        select count(1)
        from alert
        where is_del = 0
    </select>

    <!-- 根据严重程度查询告警数量 -->
    <select id="selectCountBySeverity" parameterType="String" resultType="int">
        select count(1)
        from alert
        where severity = #{severity}
          and is_del = 0
    </select>

    <!-- 根据状态查询告警数量 -->
    <select id="selectCountByStatus" parameterType="String" resultType="int">
        select count(1)
        from alert
        where status = #{status}
          and is_del = 0
    </select>

    <!-- 根据状态列表查询告警数量 -->
    <select id="selectCountByStatusIn" resultType="int">
        select count(1)
        from alert
        where status in
        <foreach item="status" collection="array" open="(" separator="," close=")">
            #{status}
        </foreach>
        and is_del = 0
    </select>

    <!-- 查询今日新增告警数量 -->
    <select id="selectTodayCount" resultType="int">
        select count(1)
        from alert
        where date(from_unixtime(create_at/1000)) = curdate()
          and is_del = 0
    </select>

    <!-- 查询平均处理时间（小时） -->
    <select id="selectAvgResolutionTime" resultType="Double">
        select avg((resolved_at - create_at) / (1000 * 60 * 60)) as avg_resolution_time
        from alert
        where resolved_at is not null
          and create_at is not null
          and resolved_at > create_at
          and is_del = 0
    </select>

    <!-- 查询告警趋势数据 -->
    <select id="selectAlertTrendByDays" parameterType="Integer" resultType="Map">
        select
            date(from_unixtime(create_at/1000)) as date,
            count(1) as count,
            sum(case when severity = 'critical' then 1 else 0 end) as critical_count,
            sum(case when severity = 'high' then 1 else 0 end) as high_count,
            sum(case when severity = 'medium' then 1 else 0 end) as medium_count,
            sum(case when severity = 'low' then 1 else 0 end) as low_count
        from alert
        where create_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
        group by date(from_unixtime(create_at/1000))
        order by date desc
    </select>

    <!-- 根据严重程度查询告警分布 -->
    <select id="selectAlertDistributionBySeverity" resultType="Map">
        select
            severity as name,
            count(1) as value
        from alert
        where is_del = 0
        group by severity
        order by value desc
    </select>

    <!-- 根据状态查询告警分布 -->
    <select id="selectAlertDistributionByStatus" resultType="Map">
        select
            status as name,
            count(1) as value
        from alert
        where is_del = 0
        group by status
        order by value desc
    </select>

    <!-- 根据告警源类型查询告警分布 -->
    <select id="selectAlertDistributionBySourceType" resultType="Map">
        select
            source_type as name,
            count(1) as value
        from alert
        where is_del = 0
          and source_type is not null
        group by source_type
        order by value desc
    </select>

    <!-- 根据告警分类查询告警分布 -->
    <select id="selectAlertDistributionByCategory" resultType="Map">
        select
            alert_category as name,
            count(1) as value
        from alert
        where is_del = 0
          and alert_category is not null
        group by alert_category
        order by value desc
    </select>

    <!-- 查询热点告警源 -->
    <select id="selectTopAlertSources" parameterType="Integer" resultType="Map">
        select
            coalesce(source_name, source_ident, source_type, '未知') as sourceName,
            source_type as sourceType,
            src_ip as sourceIp,
            count(1) as count,
            sum(case when severity = 'critical' then 1 else 0 end) as criticalCount,
            max(create_at) as lastAlertTime
        from alert
        where is_del = 0
          and create_at >= unix_timestamp(date_sub(curdate(), interval 7 day)) * 1000
        group by coalesce(source_name, source_ident, source_type), source_type, src_ip
        order by count desc
        limit #{limit}
    </select>

    <!-- 根据天数查询已解决告警数量 -->
    <select id="selectResolvedCountByDays" parameterType="Integer" resultType="int">
        select count(1)
        from alert
        where resolved_at is not null
          and resolved_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
    </select>

    <!-- 根据天数查询平均处理时间 -->
    <select id="selectAvgResolutionTimeByDays" parameterType="Integer" resultType="Double">
        select avg((resolved_at - create_at) / (1000 * 60 * 60)) as avg_resolution_time
        from alert
        where resolved_at is not null
          and create_at is not null
          and resolved_at > create_at
          and resolved_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
    </select>

    <!-- 根据天数和处理人查询解决统计 -->
    <select id="selectResolutionStatsByResolver" parameterType="Integer" resultType="Map">
        select
            resolved_by_name as resolver_name,
            count(1) as resolved_count,
            avg((resolved_at - create_at) / (1000 * 60 * 60)) as avg_resolution_time
        from alert
        where resolved_at is not null
          and resolved_by_name is not null
          and resolved_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
        group by resolved_by_name
        order by resolved_count desc
    </select>

    <!-- 根据天数查询每日处理统计 -->
    <select id="selectDailyResolutionStats" parameterType="Integer" resultType="Map">
        select
            date(from_unixtime(resolved_at/1000)) as date,
            count(1) as resolved_count,
            avg((resolved_at - create_at) / (1000 * 60 * 60)) as avg_resolution_time
        from alert
        where resolved_at is not null
          and create_at is not null
          and resolved_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
        group by date(from_unixtime(resolved_at/1000))
        order by date desc
    </select>

    <!-- 根据告警类型查询告警分布 -->
    <select id="selectAlertDistributionByAlertType" resultType="Map">
        select
            coalesce(alert_type, '未知类型') as alertType,
            count(1) as count
        from alert
        where is_del = 0
        group by alert_type
        order by count desc
    </select>

    <!-- 根据目标IP查询告警分布（TOP10） -->
    <select id="selectAlertDistributionByTargetIp" resultType="Map">
        select
            dst_ip as targetIp,
            count(1) as count,
            sum(case when severity = 'critical' then 1 else 0 end) as criticalCount
        from alert
        where is_del = 0
          and dst_ip is not null
          and dst_ip != ''
        group by dst_ip
        order by count desc
        limit 10
    </select>

    <select id="selectAlertByEventId" resultMap="AlertResult">
        select * from alert where event_id = #{eventId} and is_del = 0 limit 1
    </select>

    <!-- 获取每日告警统计数据 -->
    <select id="selectDailyAlertStatistics" resultType="Map">
        SELECT 
            DATE(FROM_UNIXTIME(occurred_at/1000)) as date,
            COUNT(*) as count
        FROM alert 
        WHERE is_del = 0
          AND occurred_at >= (UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL #{days} DAY)) * 1000)
        GROUP BY DATE(FROM_UNIXTIME(occurred_at/1000))
        ORDER BY date ASC
    </select>

    <!-- 获取最近的告警事件 -->
    <select id="selectRecentAlerts" resultMap="AlertResult">
        SELECT 
            id, title, alert_type, dst_ip, severity, occurred_at, source_type
        FROM alert 
        WHERE is_del = 0
          AND dst_ip IS NOT NULL 
          AND dst_ip != ''
        ORDER BY occurred_at DESC
        LIMIT #{limit}
    </select>

    <!-- 获取人员处理统计（近一周） -->
    <select id="getPersonnelResolutionStats" resultType="Map">
        SELECT 
            resolved_by_name as createName,
            resolved_by as createBy,
            COUNT(*) as resolvedCount,
            ROUND(AVG((resolved_at - create_at) / (1000 * 60)), 2) as avgResolutionTime,
            ROUND(MIN((resolved_at - create_at) / (1000 * 60)), 2) as minResolutionTime,
            ROUND(MAX((resolved_at - create_at) / (1000 * 60)), 2) as maxResolutionTime,
            MAX(resolved_at) as lastResolvedTime
        FROM alert 
        WHERE is_del = 0
          AND status IN ('resolved', 'ignored')
          AND resolved_at IS NOT NULL
          AND create_at IS NOT NULL
          AND resolved_by IS NOT NULL
          AND resolved_by NOT IN (0, -1)
          AND resolved_by_name IS NOT NULL
          AND resolved_by_name != ''
          AND resolved_at >= (UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 7 DAY)) * 1000)
        GROUP BY resolved_by, resolved_by_name
        HAVING resolvedCount > 0
        ORDER BY resolvedCount DESC, avgResolutionTime ASC
    </select>

    <!-- 获取告警对象TOP排行 -->
    <select id="getTopTargets" resultType="Map">
        SELECT 
            COALESCE(dst_ip, '未知对象') as dstIp,
            COUNT(*) as count,
            SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as criticalCount,
            MAX(occurred_at) as lastAlertTime
        FROM alert 
        WHERE is_del = 0
          AND dst_ip IS NOT NULL 
          AND dst_ip != ''
          AND occurred_at >= (UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 30 DAY)) * 1000)
        GROUP BY dst_ip
        ORDER BY count DESC, criticalCount DESC
        LIMIT #{limit}
    </select>

    <select id="selectAlertBySourceIdent" parameterType="String" resultMap="AlertResult">
        <include refid="selectAlertVo"/>
        where source_ident = #{sourceIdent} and is_del = 0
    </select>

    <select id="selectAlertByInputIdent" parameterType="String" resultMap="AlertResult">
        <include refid="selectAlertVo"/>
        where input_ident = #{inputIdent} and is_del = 0
    </select>

    <select id="selectAlertBySourceIdentAndInputIdent" resultMap="AlertResult">
        <include refid="selectAlertVo"/>
        where source_ident = #{sourceIdent} and input_ident = #{inputIdent} and is_del = 0
    </select>

    <!-- ==================== 工单统计相关查询 ==================== -->

    <!-- 获取运维部整体统计 -->
    <select id="selectDepartmentStatistics" resultType="Map">
        SELECT 
            COUNT(*) as totalAlerts,
            COUNT(CASE WHEN a.severity = 'critical' THEN 1 END) as criticalAlerts,
            COUNT(CASE WHEN a.status IN ('triggered', 'acknowledged') THEN 1 END) as pendingAlerts,
            COUNT(CASE WHEN a.status = 'resolved' THEN 1 END) as resolvedAlerts,
            ROUND(AVG(CASE WHEN a.acked_at IS NOT NULL THEN (a.acked_at - a.occurred_at) / (1000 * 60 * 60) END), 2) as mtta,
            ROUND(AVG(CASE WHEN a.resolved_at IS NOT NULL THEN (a.resolved_at - a.occurred_at) / (1000 * 60 * 60) END), 2) as mttr
        FROM alert a
        WHERE a.is_del = 0
          AND EXISTS (
            SELECT 1 FROM sys_user_account u 
            WHERE FIND_IN_SET(u.user_id, a.assigned_user_ids) 
            AND u.team_id IS NOT NULL
          )
          <if test="startTime != null and endTime != null">
            AND a.occurred_at BETWEEN #{startTime} AND #{endTime}
          </if>
          <if test="startTime != null and endTime == null">
            AND a.occurred_at >= #{startTime}
          </if>
          <if test="startTime == null and endTime != null">
            AND a.occurred_at <= #{endTime}
          </if>
    </select>

    <!-- 获取团队统计列表 -->
    <select id="selectTeamStatistics" resultType="Map">
        SELECT 
            u.team_id as teamId,
            u.team_name as teamName,
            COUNT(*) as totalAlerts,
            COUNT(CASE WHEN a.severity = 'critical' THEN 1 END) as criticalAlerts,
            COUNT(CASE WHEN a.status IN ('triggered', 'acknowledged') THEN 1 END) as pendingAlerts,
            COUNT(CASE WHEN a.status = 'resolved' THEN 1 END) as resolvedAlerts,
            ROUND(AVG(CASE WHEN a.acked_at IS NOT NULL THEN (a.acked_at - a.occurred_at) / (1000 * 60 * 60) END), 2) as mtta,
            ROUND(AVG(CASE WHEN a.resolved_at IS NOT NULL THEN (a.resolved_at - a.occurred_at) / (1000 * 60 * 60) END), 2) as mttr
        FROM alert a
        JOIN sys_user_account u ON FIND_IN_SET(u.user_id, a.assigned_user_ids)
        WHERE a.is_del = 0 
          AND u.team_id IS NOT NULL
          <if test="startTime != null and endTime != null">
            AND a.occurred_at BETWEEN #{startTime} AND #{endTime}
          </if>
          <if test="startTime != null and endTime == null">
            AND a.occurred_at >= #{startTime}
          </if>
          <if test="startTime == null and endTime != null">
            AND a.occurred_at <= #{endTime}
          </if>
        GROUP BY u.team_id, u.team_name
        ORDER BY u.team_id
    </select>

    <!-- 获取指定团队的人员统计 -->
    <select id="selectUserStatisticsByTeam" resultType="Map">
        SELECT 
            u.user_id as userId,
            u.user_name as userName,
            u.team_id as teamId,
            u.team_name as teamName,
            COUNT(*) as totalAlerts,
            COUNT(CASE WHEN a.severity = 'critical' THEN 1 END) as criticalAlerts,
            COUNT(CASE WHEN a.status IN ('triggered', 'acknowledged') THEN 1 END) as pendingAlerts,
            COUNT(CASE WHEN a.status = 'resolved' THEN 1 END) as resolvedAlerts,
            ROUND(AVG(CASE WHEN a.acked_at IS NOT NULL THEN (a.acked_at - a.occurred_at) / (1000 * 60 * 60) END), 2) as mtta,
            ROUND(AVG(CASE WHEN a.resolved_at IS NOT NULL THEN (a.resolved_at - a.occurred_at) / (1000 * 60 * 60) END), 2) as mttr
        FROM alert a
        JOIN sys_user_account u ON FIND_IN_SET(u.user_id, a.assigned_user_ids)
        WHERE a.is_del = 0 
          AND u.team_id = #{teamId}
          <if test="startTime != null and endTime != null">
            AND a.occurred_at BETWEEN #{startTime} AND #{endTime}
          </if>
          <if test="startTime != null and endTime == null">
            AND a.occurred_at >= #{startTime}
          </if>
          <if test="startTime == null and endTime != null">
            AND a.occurred_at <= #{endTime}
          </if>
        GROUP BY u.user_id, u.user_name, u.team_id, u.team_name
        ORDER BY mttr ASC, mtta ASC
    </select>

    <!-- 获取指定人员的告警明细 -->
    <select id="selectUserAlertDetails" resultMap="AlertResult">
        <include refid="selectAlertVo"/>
        WHERE a.is_del = 0
          AND FIND_IN_SET(#{userId}, a.assigned_user_ids)
          <if test="startTime != null and endTime != null">
            AND a.occurred_at BETWEEN #{startTime} AND #{endTime}
          </if>
          <if test="startTime != null and endTime == null">
            AND a.occurred_at >= #{startTime}
          </if>
          <if test="startTime == null and endTime != null">
            AND a.occurred_at <= #{endTime}
          </if>
        ORDER BY a.occurred_at DESC
    </select>

</mapper>
