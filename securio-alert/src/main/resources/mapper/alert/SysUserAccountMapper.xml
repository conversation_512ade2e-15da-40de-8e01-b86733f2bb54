<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.alert.mapper.SysUserAccountMapper">

    <resultMap type="com.wiwj.securio.alert.domain.SysUserAccount" id="SysUserAccountResult">
        <result property="id"           column="id"           />
        <result property="userId"       column="user_id"      />
        <result property="accountId"    column="account_id"   />
        <result property="accountName"  column="account_name" />
        <result property="status"       column="status"       />
        <result property="chanel"       column="chanel"       />
        <result property="teamId"       column="team_id"      />
        <result property="teamName"     column="team_name"    />
        <result property="appKey"       column="app_key"      />
        <result property="isDel"        column="is_del"       />
        <result property="createAt"     column="create_at"    />
        <result property="updateAt"     column="update_at"    />
    </resultMap>

    <sql id="selectSysUserAccountVo">
        select id, user_id, account_id, account_name, status, chanel, team_id, team_name, app_key, is_del, create_at, update_at
        from sys_user_account
    </sql>

    <select id="selectSysUserAccountList" parameterType="com.wiwj.securio.alert.domain.SysUserAccount" resultMap="SysUserAccountResult">
        <include refid="selectSysUserAccountVo"/>
        <where>
            is_del = 0
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="chanel != null  and chanel != ''"> and chanel = #{chanel}</if>
        </where>
        order by create_at desc
    </select>

    <select id="selectSysUserAccountById" parameterType="Long" resultMap="SysUserAccountResult">
        <include refid="selectSysUserAccountVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectSysUserAccountByAccountId" parameterType="String" resultMap="SysUserAccountResult">
        select id, user_id, account_id, account_name, chanel, team_id, team_name, create_at, update_at
        from sys_user_account
        where account_id = #{accountId} and status = 'enabled' and is_del = 0
    </select>

    <select id="selectSysUserAccountByChanel" parameterType="String" resultMap="SysUserAccountResult">
        select id, user_id, account_id, account_name, chanel, team_id, team_name, create_at, update_at
        from sys_user_account
        where chanel = #{chanel} and status = 'enabled' and is_del = 0
    </select>

    <insert id="insertSysUserAccount" parameterType="com.wiwj.securio.alert.domain.SysUserAccount" useGeneratedKeys="true" keyProperty="id">
        insert into sys_user_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="accountId != null">account_id,</if>
            <if test="accountName != null">account_name,</if>
            <if test="status != null">status,</if>
            <if test="chanel != null">chanel,</if>
            <if test="teamId != null">team_id,</if>
            <if test="teamName != null">team_name,</if>
            <if test="appKey != null">app_key,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createAt != null">create_at,</if>
            <if test="updateAt != null">update_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="accountId != null">#{accountId},</if>
            <if test="accountName != null">#{accountName},</if>
            <if test="status != null">#{status},</if>
            <if test="chanel != null">#{chanel},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="appKey != null">#{appKey},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="updateAt != null">#{updateAt},</if>
         </trim>
    </insert>

    <update id="updateSysUserAccount" parameterType="com.wiwj.securio.alert.domain.SysUserAccount">
        update sys_user_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="chanel != null">chanel = #{chanel},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="appKey != null">app_key = #{appKey},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysUserAccountById" parameterType="Long">
        delete from sys_user_account where id = #{id}
    </delete>

    <delete id="deleteSysUserAccountByIds" parameterType="String">
        delete from sys_user_account where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据账号ID列表批量查询系统用户账号 -->
    <select id="selectSysUserAccountByAccountIds" parameterType="java.util.List" resultMap="SysUserAccountResult">
        select id, user_id, account_id, account_name, chanel, team_id, team_name, create_at, update_at
        from sys_user_account
        where account_id in
        <foreach collection="list" item="accountId" open="(" separator="," close=")">
            #{accountId}
        </foreach>
        and status = 'enabled' and is_del = 0
    </select>

    <!-- 根据用户ID查询appKey -->
    <select id="selectAppKeyByUserId" parameterType="Long" resultType="String">
        select app_key
        from sys_user_account
        where user_id = #{userId} and status = 'enabled' and is_del = 0
        limit 1
    </select>

</mapper> 