package com.wiwj.securio.alert.util;

import org.junit.Test;
import org.junit.Assert;
import java.util.Set;

/**
 * TagSearchUtil 测试类
 */
public class TagSearchUtilTest {

    @Test
    public void testSingleTagSearch() {
        String tagSearch = "environment:production";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "exact");
        System.out.println("单标签搜索(精确): " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("JSON_UNQUOTE"));
        Assert.assertTrue(result.contains("environment"));
        Assert.assertTrue(result.contains("production"));
    }

    @Test
    public void testAndSearch() {
        String tagSearch = "environment:production AND service:user-api";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "contains");
        System.out.println("AND搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("AND"));
        Assert.assertTrue(result.contains("environment"));
        Assert.assertTrue(result.contains("service"));
    }

    @Test
    public void testOrSearch() {
        String tagSearch = "environment:production OR environment:staging";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "exact");
        System.out.println("OR搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("OR"));
        Assert.assertTrue(result.contains("production"));
        Assert.assertTrue(result.contains("staging"));
    }

    @Test
    public void testUnderscoreInTagKey() {
        String tagSearch = "user_id:12345";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "exact");
        System.out.println("下划线键名搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("user_id"));
        Assert.assertTrue(result.contains("12345"));
        // 验证生成的SQL不会引起JSON路径错误
        Assert.assertFalse(result.contains("CONCAT"));
    }

    @Test
    public void testSpecialCharacterInTagKey() {
        String tagSearch = "app-name:my-service";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "exact");
        System.out.println("特殊字符键名搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("app-name"));
        Assert.assertTrue(result.contains("my-service"));
    }

    @Test
    public void testComplexTagSearchWithUnderscore() {
        String tagSearch = "service_name:user-api AND user_type:admin";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "contains");
        System.out.println("复杂下划线搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("AND"));
        Assert.assertTrue(result.contains("service_name"));
        Assert.assertTrue(result.contains("user_type"));
    }

    @Test
    public void testComplexSearch() {
        String tagSearch = "environment:production AND (service:user-api OR service:order-service)";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "contains");
        System.out.println("复杂搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        // 注意：当前实现不支持括号，会降级处理
    }

    @Test
    public void testTextSearch() {
        String tagSearch = "user-api";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "contains");
        System.out.println("文本搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("LIKE"));
        Assert.assertTrue(result.contains("user-api"));
    }

    @Test
    public void testEmptySearch() {
        String tagSearch = "";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "contains");
        System.out.println("空搜索: " + tagSearch + " -> " + result);
        Assert.assertNull(result);
    }

    @Test
    public void testInvalidSearch() {
        String tagSearch = "invalid::search::with::colons";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "contains");
        System.out.println("无效搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        // 应该降级为文本搜索
        Assert.assertTrue(result.contains("LIKE"));
    }

    @Test
    public void testMixedSearch() {
        String tagSearch = "environment:production backend";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "contains");
        System.out.println("混合搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("AND"));
    }

    @Test
    public void testExtractTagKeys() {
        String tagSearch = "environment:production AND service:user-api OR team:backend";
        Set<String> keys = TagSearchUtil.extractTagKeys(tagSearch);
        System.out.println("提取标签键: " + tagSearch + " -> " + keys);
        Assert.assertEquals(3, keys.size());
        Assert.assertTrue(keys.contains("environment"));
        Assert.assertTrue(keys.contains("service"));
        Assert.assertTrue(keys.contains("team"));
    }

    @Test
    public void testValidation() {
        String validSearch = "environment:production AND service:user-api";
        Assert.assertTrue("有效搜索应该通过验证", 
            TagSearchUtil.isValidTagSearchExpression(validSearch));

        String emptySearch = "";
        Assert.assertTrue("空搜索应该通过验证", 
            TagSearchUtil.isValidTagSearchExpression(emptySearch));

        String nullSearch = null;
        Assert.assertTrue("null搜索应该通过验证", 
            TagSearchUtil.isValidTagSearchExpression(nullSearch));
    }

    @Test
    public void testSpecialCharacters() {
        String tagSearch = "service:user's-api";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "exact");
        System.out.println("特殊字符搜索: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        // 应该正确转义单引号
        Assert.assertTrue(result.contains("user''s-api"));
    }

    @Test
    public void testCaseInsensitiveOperators() {
        String tagSearch = "environment:production and service:user-api";
        String result = TagSearchUtil.parseTagSearchToSql(tagSearch, "contains");
        System.out.println("小写操作符: " + tagSearch + " -> " + result);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.contains("AND"));
    }

    public static void main(String[] args) {
        TagSearchUtilTest test = new TagSearchUtilTest();
        
        System.out.println("=== TagSearchUtil 功能测试 ===\n");
        
        test.testSingleTagSearch();
        System.out.println();
        
        test.testAndSearch();
        System.out.println();
        
        test.testOrSearch();
        System.out.println();
        
        test.testComplexSearch();
        System.out.println();
        
        test.testTextSearch();
        System.out.println();
        
        test.testMixedSearch();
        System.out.println();
        
        test.testExtractTagKeys();
        System.out.println();
        
        test.testSpecialCharacters();
        System.out.println();
        
        test.testCaseInsensitiveOperators();
        System.out.println();
        
        System.out.println("=== 所有测试完成 ===");
    }
} 