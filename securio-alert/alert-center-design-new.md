# 通用告警中心架构设计文档


## 1. 系统概述

### 1.1 设计目标
- 构建统一的告警事件收集、处理和通知平台
- 支持多种数据源的告警事件接入
- 实现可扩展、可插拔的架构设计
- 提供灵活的告警分析和统计功能
- 支持多种通知渠道的集成

### 1.2 主要功能
1. 告警数据采集
   - 支持多种数据源接入（Webhook、安全平台收集组件）
   - 统一数据格式转换
   - 灵活的数据收集方式

2. 告警事件处理
   - 事件去重和基础关联
   - 告警级别划分
   - 告警规则配置
   - 告警事件记录

3. 数据分析与统计
   - 基础维度统计
   - 告警趋势展示
   - 自定义报表生成

4. 告警通知
   - 多渠道通知支持（FlushDuty、企业微信、邮件等）
   - 通知规则配置
   - 通知模板管理

## 2. 系统架构

### 2.1 整体架构
```mermaid
graph TD
    %% 数据源定义
    subgraph 数据源[安全数据源]
        TDP[微步TDP]
        HoneyPot[蜜罐]
        WAF_OS[长亭WAF开源版]
        MuYun[牧云]
        WS_WAF[网宿WAF]
        Zeek[Zeek]
        Grafana[Grafana]
        HuaweiCloud[华为云]
        AliCloud[阿里云]
    end

    %% 数据收集方式
    subgraph 数据接入[数据接入方式]
        SEC[安全平台收集组件]
        WH[Webhook接入]
    end

    %% 统一告警中心
    subgraph AlertCenter[统一告警中心]
        %% 接收层
        API[通用告警接口]
        
        %% 安全应用中心
        subgraph AppCenter[安全应用中心]
            AppMgmt[应用管理]
            AuthMgmt[认证管理]
            FieldMapping[字段映射配置]
        end

        %% 标签库
        subgraph TagLib[标签库]
            TagConfig[标签配置]
            TagExtract[特征提取规则]
            TagMatch[标签匹配引擎]
        end
        
        %% 处理层
        subgraph Process[事件处理模块]
            Parser[数据解析标准化<br>基于适配器模式]
            Storage[(事件存储)]
        end

        %% 分析层
        subgraph Analytics[事件分析模块]
            TimeAnalysis[时间维度分析]
            IPAnalysis[IP维度分析]
            Rule[规则引擎]
        end
        
        %% 通知层
        subgraph Notify[事件通知模块]
            NManager[通知管理器]
            Template[通知模板]
        end

        %% 统计报表模块
        subgraph Report[统计报表模块]
            DataCollector[数据采集器]
            ReportEngine[报表引擎]
            subgraph ReportTypes[报表类型]
                DutyReport[值班数据分析]
                AlertReport[告警事件报表]
                TrendReport[趋势分析报表]
            end
        end

        %% IP阻断模块
        subgraph Block[IP阻断模块]
            BlockAPI[阻断操作接口]
            BlockManager[阻断策略管理]
            subgraph BlockActions[阻断动作]
                FWBlock[防火墙阻断]
                WAFBlock[WAF阻断]
                SwitchBlock[交换机阻断]
            end
            BlockHistory[阻断历史记录]
        end
    end

    %% 前端展示
    subgraph Frontend[统一告警中心前端]
        %% 告警展示
        subgraph AlertView[告警管理]
            EventList[告警事件列表]
            EventDetail[告警事件详情]
            EventStatus[事件状态管理]
        end

        %% 分析展示
        subgraph AnalyticsView[分析展示]
            TimeView[时间维度视图]
            IPView[IP维度视图]
            CorrelationView[关联分析视图]
            RiskView[风险评估视图]
        end

        %% 报表展示
        subgraph ReportView[报表展示]
            DutyView[值班数据视图]
            AlertStatsView[告警统计视图]
            TrendView[趋势分析视图]
        end

        %% 阻断管理
        subgraph BlockView[阻断管理]
            BlockOpView[阻断操作]
            BlockHistoryView[阻断历史]
            BlockPolicyView[阻断策略配置]
        end
    end

    %% 通知渠道
    subgraph Channel[通知渠道]
        FD[FlushDuty]
        WeCom[企业微信]
        Email[邮件]
    end

    %% 连接关系 - 数据源到接入
    TDP --> SEC
    WAF_OS --> SEC
    Zeek --> SEC
    HoneyPot --> WH
    MuYun --> WH
    WS_WAF --> WH
    Grafana --> WH
    HuaweiCloud --> WH
    AliCloud --> WH
    
    %% 连接关系 - 应用中心
    WH --> AuthMgmt
    AuthMgmt --> API
    API --> FieldMapping
    FieldMapping --> Parser
    
    %% 连接关系 - 标签库
    Parser --> TagMatch
    TagMatch --> TagExtract
    TagExtract --> Storage
    
    %% 连接关系 - 接入到处理
    SEC --> API
    API --> Parser
    Parser --> Storage
    
    %% 连接关系 - 分析流程
    Storage --> TimeAnalysis
    Storage --> IPAnalysis
    TimeAnalysis --> Rule
    
    %% 连接关系 - 通知流程
    Storage --> NManager
    NManager --> Template
    Template --> FD
    Template --> WeCom
    Template --> Email

    %% 连接关系 - 报表生成
    FD -- 时间线数据 --> DataCollector
    Storage --> DataCollector
    DataCollector --> ReportEngine
    ReportEngine --> DutyReport
    ReportEngine --> AlertReport
    ReportEngine --> TrendReport

    %% 连接关系 - IP阻断流程
    WeCom -- 阻断操作 --> BlockAPI
    BlockAPI --> BlockManager
    BlockManager --> FWBlock
    BlockManager --> WAFBlock
    BlockManager --> SwitchBlock
    BlockManager --> BlockHistory

    %% 连接关系 - 前端展示
    Storage --> EventList
    Storage --> EventDetail
    TimeAnalysis & IPAnalysis --> AnalyticsView
    ReportEngine --> ReportView
    BlockHistory --> BlockHistoryView
    BlockManager --> BlockPolicyView

    %% 样式定义
    classDef source fill:#f9f,stroke:#333,stroke-width:2px
    classDef collector fill:#adf,stroke:#333,stroke-width:2px
    classDef process fill:#bbf,stroke:#333,stroke-width:2px
    classDef analytics fill:#dff,stroke:#333,stroke-width:2px
    classDef notify fill:#bfb,stroke:#333,stroke-width:2px
    classDef report fill:#fdb,stroke:#333,stroke-width:2px
    classDef block fill:#fcc,stroke:#333,stroke-width:2px
    classDef frontend fill:#eff,stroke:#333,stroke-width:2px
    classDef channel fill:#feb,stroke:#333,stroke-width:2px
    classDef appcenter fill:#e6ccff,stroke:#333,stroke-width:2px
    classDef taglib fill:#ffccaa,stroke:#333,stroke-width:2px

    %% 应用样式
    class TDP,HoneyPot,WAF_OS,MuYun,WS_WAF,Zeek,Grafana,HuaweiCloud,AliCloud source
    class SEC,WH,API collector
    class Parser,Storage process
    class TimeAnalysis,IPAnalysis,Rule analytics
    class NManager,Template notify
    class DataCollector,ReportEngine,DutyReport,AlertReport,TrendReport report
    class BlockAPI,BlockManager,FWBlock,WAFBlock,SwitchBlock,BlockHistory block
    class EventList,EventDetail,EventStatus,TimeView,IPView,CorrelationView,RiskView,DutyView,AlertStatsView,TrendView,BlockOpView,BlockHistoryView,BlockPolicyView frontend
    class FD,WeCom,Email channel
    class AppMgmt,AuthMgmt,FieldMapping appcenter
    class TagConfig,TagExtract,TagMatch taglib

    %% 布局调整
    linkStyle default stroke-width:2px
```

### 2.2 核心组件

#### 2.2.1 数据接入层
- **数据源适配器**：
  - **微步TDP适配器**
    - 处理TDP特定格式的告警数据
    - 支持威胁情报关联
    - 标准化威胁等级映射
    - 提取关键字段（IP、域名、威胁类型等）
  
  - **蜜罐适配器**
    - 处理蜜罐日志和告警数据
    - 提取攻击特征信息
    - 标准化攻击类型分类
    - 关联攻击源信息
  
  - **WAF适配器**
    - 处理长亭雷池WAF和网宿WAF的告警数据
    - 统一不同WAF的攻击类型
    - 标准化防护动作
    - 提取攻击载荷特征
  
  - **牧云适配器**
    - 处理牧云平台的告警数据
    - 资产信息关联
    - 漏洞信息标准化
    - 风险等级映射
  
  - **Zeek适配器**
    - 处理Zeek的网络流量告警数据
    - 网络协议分析
    - 流量特征提取
    - 会话信息关联

  - **Grafana适配器**
    - 处理Grafana告警数据
    - 支持多种监控指标转换
    - 阈值告警标准化
    - 时序数据关联分析

  - **华为云适配器**
    - 处理华为云安全中心告警
    - 云资产信息关联
    - 云安全事件分类
    - 威胁等级映射

  - **阿里云适配器**
    - 处理阿里云安全中心告警
    - 云资产关联
    - 安全事件分类
    - 威胁评分标准化

- **适配器通用功能**：
  - 数据格式转换
  - 字段标准化
  - 数据清洗
  - 数据验证

- **适配器设计原则**：
  - 插件化设计
  - 配置驱动
  - 松耦合
  - 可扩展

- **数据收集服务**：
  - 基于现有的Go多协议日志收集组件

- **数据验证服务**：
  - 确保数据的完整性
  - 验证数据格式
  - 检查必要字段
  - 数据类型校验
  - 业务规则验证
  - 重复数据检测

#### 2.2.2 数据处理层
- **数据解析器**：解析不同格式的告警数据
- **数据标准化服务**：统一数据格式
- **数据清洗服务**：处理数据质量问题

#### 2.2.3 业务逻辑层
- **告警规则引擎**：处理告警规则的配置和执行
- **事件关联分析**：基于规则的简单关联
- **统计分析服务**：提供基础维度的数据分析功能

#### 2.2.4 通知分发层
- **通知策略管理**：管理不同场景的通知策略
- **通知渠道适配器**：对接不同的通知渠道
- **通知模板管理**：管理不同类型的通知模板

#### 2.2.5 IP阻断模块
- **阻断操作接口**：提供统一的IP阻断操作接口
- **阻断策略管理**：管理不同设备的阻断策略和规则
- **阻断动作执行**：
  - 基于IP阻断：需要对应安全组件支持阻断API调用
  - 其他策略阻断：
- **阻断历史记录**：记录所有阻断操作的历史和状态

#### 2.2.6 安全应用中心
- **应用管理**
  - 安全组件注册与管理
  - 应用配置管理

- **字段映射配置**
  - 数据字段映射规则
  - 字段类型转换
  - 映射模板管理

#### 2.2.7 标签库
- **标签配置**
  - 标签分类管理
  - 标签属性定义
  - 标签关系配置
  - 标签权重设置

- **特征提取规则**
  - 正则表达式规则
  - 关键字匹配规则
  - 上下文关联规则
  - 组合规则配置

- **标签匹配引擎**
  - 实时标签匹配
  - 批量标签处理
  - 标签优先级处理
  - 标签冲突处理

### 2.3 前端展示模块

#### 2.3.1 告警管理
- **告警事件列表**：展示所有告警事件，支持筛选、排序和分页
- **告警事件详情**：展示单个告警的详细信息，包括原始数据和分析结果
- **事件状态管理**：管理告警的处理状态和流转过程

#### 2.3.2 分析展示
- **时间维度视图**：展示不同时间维度的告警分布
- **IP维度视图**：展示IP相关的告警聚合分析
- **关联分析视图**：展示告警事件之间的关联关系
- **风险评估视图**：展示系统的整体安全风险状况

#### 2.3.3 报表展示
- **值班数据视图**：展示值班期间的告警处理情况（基于FlushDuty告警数据和timeline数据）
- **告警统计视图**：展示各类告警的统计信息
- **趋势分析视图**：展示告警趋势和预测分析

#### 2.3.4 阻断管理
- **阻断操作**：提供便捷的阻断操作界面
- **阻断历史**：展示历史阻断记录
- **阻断策略配置**：配置和管理阻断策略

### 2.4 统一告警接口定义

#### 2.4.1 接口规范
```json
{
    "endpoint": "/api/v1/alert/${token}",
    "method": "POST",
    "headers": {
        "Content-Type": "application/json"
    }
}
```

#### 2.4.2 Webhook URL 生成规则
```json
{
    "url_pattern": "https://{domain}/api/v1/alert/{token}",
    "token_format": {
        "length": 32,
        "charset": "a-z0-9",
        "prefix": "wh_"
    },
    "example": "https://alert-center.example.com/api/v1/alert/wh_8e7d3f2a9b1c4e5f8g7h6i5j4k3l2m1n"
}
```

#### 2.4.3 请求数据结构
```json
{
    "alert": {
        "id": "告警ID",
        "title": "告警标题",
        "description": "告警描述",
        "source": {
            "type": "告警源类型",
            "name": "告警源名称",
            "instance": "实例标识"
        },
        "severity": "告警级别",
        "status": "告警状态",
        "timestamp": "告警时间",
        "tags": ["标签1", "标签2"],
        "attributes": {
            "ip": "相关IP",
            "domain": "相关域名",
            "protocol": "协议",
            "port": "端口",
            "attack_type": "攻击类型",
            "risk_level": "风险等级",
            "custom_field": "自定义字段"
        },
        "raw_data": "原始数据"
    }
}
```

#### 2.4.4 响应数据结构
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "alert_id": "系统生成的告警ID",
        "status": "处理状态",
        "timestamp": "处理时间"
    }
}
```

#### 2.4.5 字段说明
1. **必填字段**
   - id: 告警唯一标识
   - title: 告警标题
   - source: 告警来源信息
   - severity: 告警级别
   - timestamp: 告警时间

2. **可选字段**
   - description: 告警详细描述
   - tags: 告警标签列表
   - attributes: 告警属性（可扩展）
   - raw_data: 原始数据（用于追溯）

3. **字段格式**
   - timestamp: ISO 8601格式
   - severity: 枚举值（critical/high/medium/low/info）
   - status: 枚举值（new/processing/resolved/closed）

4. **自定义字段**
   - 支持通过attributes字段扩展
   - 字段映射配置中定义转换规则
   - 支持嵌套结构

### 2.5 标签提取规则详解

#### 2.5.1 标签规则示例
```json
{
    "tag_rules": {
        "rule_type": "composite",
        "rules": [
            {
                "name": "Web攻击识别",
                "type": "regex",
                "patterns": [
                    "SQL\\s*注入",
                    "XSS攻击",
                    "目录遍历",
                    "文件包含"
                ],
                "fields": ["alert.title", "alert.description"],
                "tags": ["Web攻击"],
                "severity": "high"
            },
            {
                "name": "暴力破解识别",
                "type": "threshold",
                "conditions": {
                    "field": "alert.attributes.attempts",
                    "operator": ">=",
                    "value": 100,
                    "timeWindow": "5m"
                },
                "tags": ["暴力破解"],
                "severity": "medium"
            },
            {
                "name": "IP黑名单",
                "type": "lookup",
                "field": "alert.attributes.ip",
                "source": "ip_blacklist",
                "tags": ["黑名单IP"]
            }
        ]
    }
}
```

#### 2.5.2 标签提取示例

1. **Web攻击标签提取**
```json
// 原始告警
{
    "title": "检测到SQL注入攻击",
    "source_ip": "*******",
    "target": "login.php",
    "payload": "' OR '1'='1"
}

// 提取标签
{
    "tags": [
        "攻击类型:Web攻击",
        "攻击手法:SQL注入",
        "攻击目标:登录接口"
    ]
}
```

2. **暴力破解标签提取**
```json
// 原始告警（5分钟内）
{
    "source_ip": "*******",
    "event": "登录失败",
    "count": 150,
    "target_user": "admin"
}

// 提取标签
{
    "tags": [
        "攻击类型:暴力破解",
        "攻击目标:管理员账号",
        "频率:高频"
    ]
}
```

3. **异常访问标签提取**
```json
// 原始告警
{
    "source_ip": "*******",
    "url": "/admin/config",
    "user_agent": "Python-urllib/3.8",
    "response_code": 403
}

// 提取标签
{
    "tags": [
        "行为:未授权访问",
        "工具特征:爬虫",
        "目标类型:管理后台"
    ]
}
```

### 2.6 标签使用场景

#### 2.6.1 告警聚合
- 基于标签的告警分组
  ```json
  {
      "aggregation_rules": [
          {
              "name": "相同源IP攻击聚合",
              "conditions": [
                  "same_source_ip",
                  "same_attack_type_tag",
                  "within_5_minutes"
              ],
              "action": "merge_alerts"
          },
          {
              "name": "目标攻击关联",
              "conditions": [
                  "same_target",
                  "same_attack_method_tag",
                  "within_1_hour"
              ],
              "action": "create_attack_event"
          }
      ]
  }
  ```
- 相似告警的识别
- 重复告警的抑制
- 关联告警的发现

#### 2.6.2 告警分类和路由
```json
{
    "routing_rules": [
        {
            "name": "Web攻击处理",
            "conditions": {
                "tags": ["Web攻击"]
            },
            "route_to": "web_security_team"
        },
        {
            "name": "高优先级暴力破解",
            "conditions": {
                "tags": ["暴力破解", "管理员账号"]
            },
            "priority": "high",
            "route_to": "security_admin"
        },
        {
            "name": "异常访问处理",
            "conditions": {
                "tags": ["异常访问", "管理后台"]
            },
            "route_to": ["security_team", "system_team"]
        }
    ]
}
```

#### 2.6.3 统计分析
- 标签维度统计
  ```json
  {
      "analysis_views": [
          {
              "name": "攻击类型分布",
              "type": "pie_chart",
              "group_by": "attack_type_tag"
          },
          {
              "name": "攻击目标TOP10",
              "type": "bar_chart",
              "group_by": "target_type_tag",
              "limit": 10
          },
          {
              "name": "攻击工具分析",
              "type": "table",
              "group_by": "tool_feature_tag"
          }
      ]
  }
  ```
- 趋势分析
- 攻击类型分布
- 威胁来源分析

#### 2.6.4 自动响应策略
```json
{
    "auto_response_rules": [
        {
            "name": "暴力破解防护",
            "conditions": {
                "tags": ["高频", "暴力破解"]
            },
            "action": "add_to_blacklist",
            "duration": "1h"
        },
        {
            "name": "Web攻击防护",
            "conditions": {
                "tags": ["Web攻击", "高危"]
            },
            "action": "update_waf_rules"
        },
        {
            "name": "爬虫防护",
            "conditions": {
                "tags": ["爬虫", "管理后台"]
            },
            "action": "enable_captcha"
        }
    ]
}
```

### 2.7 数据处理流水线

```mermaid
graph TD
    subgraph 数据接入
        A[原始数据] --> B[字段映射]
        B --> C[数据验证]
    end

    subgraph 标签处理
        C --> D[规则匹配]
        D --> E[特征提取]
        E --> F[标签生成]
    end

    subgraph 分析处理
        F --> G[告警聚合]
        G --> H[关联分析]
        H --> I[风险评估]
    end

    subgraph 响应处理
        I --> J[通知分发]
        I --> K[自动阻断，目前应该只有部分组件支持手动阻断]
        I --> L[告警存储]
    end
```

## 3. 数据流设计

### 3.1 数据采集流程
1. 数据源产生告警事件
2. 通过安全平台收集组件或Webhook接收数据
3. 数据进入统一接入层
4. 进行数据格式验证和转换
5. 存储原始数据

### 3.2 数据处理流程
1. 从数据池获取待处理数据
2. 进行数据清洗和标准化
3. 应用告警规则进行分析
4. 生成标准告警事件
5. 存储处理后的数据

### 3.3 通知分发流程
1. 触发告警通知规则
2. 确定通知目标和渠道
3. 生成通知内容
4. 发送通知
5. 记录通知状态

## 4. 扩展性设计

### 4.1 插件化架构

#### 4.1.1 数据源适配器
- **核心接口设计**
```java
// 基础告警数据结构
public class AlertData {
    private String id;                     // 告警ID
    private String title;                  // 告警标题
    private String description;            // 告警描述
    private AlertSource source;            // 告警来源
    private String severity;               // 严重程度
    private LocalDateTime timestamp;       // 告警时间
    private List<String> tags;            // 标签列表
    private Map<String, Object> attributes;// 告警属性
    private Map<String, Object> rawData;   // 原始数据
}

// 告警来源信息
public class AlertSource {
    private String type;      // 来源类型
    private String name;      // 来源名称
    private String instance;  // 实例标识
}

// 适配器接口
public interface AlertAdapter {
    // 获取适配器类型
    String getType();
    
    // 初始化适配器
    void initialize(AdapterConfig config) throws AdapterInitException;
    
    // 解析告警数据
    AlertData parse(String rawData) throws AlertParseException;
    
    // 批量解析告警数据
    List<AlertData> parseBatch(List<String> rawDataList) throws AlertParseException;
}

// 适配器配置
public class AdapterConfig {
    private String name;        // 配置名称
    private Map<String, FieldMappingConfig> fieldMappings;  // 字段映射配置
    private Map<String, String> severityMapping;   // 告警级别映射
    private boolean enabled;    // 是否启用
}

// 适配器示例实现
@Component
public class WafAlertAdapter implements AlertAdapter {
    private AdapterConfig config;
    private JsonPathCompiler pathCompiler;
    
    @Override
    public String getType() {
        return "waf";
    }
    
    @Override
    public void initialize(AdapterConfig config) throws AdapterInitException {
        this.config = config;
        this.pathCompiler = new JsonPathCompiler();
        
        // 验证配置
        if (config == null || config.getFieldMappings() == null) {
            throw new AdapterInitException("Invalid adapter configuration");
        }
        
        // 初始化JsonPath编译器
        for (FieldMappingConfig mapping : config.getFieldMappings().values()) {
            if (mapping.getSourceField() != null) {
                pathCompiler.compile(mapping.getSourceField());
            }
        }
    }
    
    @Override
    public AlertData parse(String rawData) throws AlertParseException {
        try {
            // 解析数据
            JsonNode data = JsonUtils.parse(rawData);
            AlertData alert = new AlertData();
            
            // 应用字段映射
            for (Map.Entry<String, FieldMappingConfig> entry : config.getFieldMappings().entrySet()) {
                applyFieldMapping(alert, data, entry.getKey(), entry.getValue());
            }
            
            // 保存原始数据
            alert.setRawData(JsonUtils.toMap(data));
            
            return alert;
            
        } catch (Exception e) {
            throw new AlertParseException("Failed to parse alert data", rawData);
        }
    }
    
    @Override
    public List<AlertData> parseBatch(List<String> rawDataList) throws AlertParseException {
        List<AlertData> results = new ArrayList<>();
        for (String rawData : rawDataList) {
            results.add(parse(rawData));
        }
        return results;
    }
    
    private void applyFieldMapping(AlertData alert, JsonNode data, String fieldName, FieldMappingConfig mapping) {
        try {
            // 提取字段值
            Object value = extractField(data, mapping);
            // 应用转换
            Object transformed = transformValue(value, mapping);
            // 设置字段值
            setFieldValue(alert, fieldName, transformed);
        } catch (Exception e) {
            // 记录错误但继续处理
            log.error("Field mapping error: " + fieldName, e);
        }
    }
}

// 适配器管理器
@Service
public class AlertAdapterManager {
    private Map<String, AlertAdapter> adapters = new ConcurrentHashMap<>();
    private Map<String, AdapterConfig> configs = new ConcurrentHashMap<>();
    
    // 注册适配器
    public void registerAdapter(AlertAdapter adapter) {
        adapters.put(adapter.getType(), adapter);
    }
    
    // 加载适配器配置
    public void loadConfig(String type, AdapterConfig config) throws AdapterInitException {
        AlertAdapter adapter = adapters.get(type);
        if (adapter != null) {
            adapter.initialize(config);
            configs.put(type, config);
        }
    }
    
    // 获取适配器
    public AlertAdapter getAdapter(String type) {
        return adapters.get(type);
    }
}
```

### 4.2 配置化设计

#### 4.2.1 配置管理
- **配置存储**
```java
@Entity
@Table(name = "alert_adapter_config")
public class AdapterConfigEntity {
    @Id
    private String id;
    private String sourceType;    // 数据源类型
    private String name;          // 配置名称
    private String description;   // 配置描述
    private boolean enabled;      // 是否启用
    
    @Column(columnDefinition = "jsonb")
    private Map<String, FieldMappingConfig> fieldMappings;  // 字段映射配置
    
    @Column(columnDefinition = "jsonb")
    private Map<String, String> severityMapping;  // 告警级别映射
    
    @Column(columnDefinition = "jsonb")
    private Map<String, Object> extraConfig;      // 额外配置项
}

// 字段映射配置
public class FieldMappingConfig {
    private String sourceField;           // 源字段路径
    private String targetField;           // 目标字段
    private String type;                  // 字段类型
    private String defaultValue;          // 默认值
    private List<String> arrayFields;     // 数组字段配置
    private Map<String, String> enumMapping;  // 枚举值映射
    private String transformExpression;   // 转换表达式
    private boolean required;             // 是否必填
}

@Entity
@Table(name = "notify_channel_config")
public class NotifyChannelConfigEntity {
    @Id
    private String id;
    private String channelType;   // 通知渠道类型
    private String name;          // 配置名称
    private Map<String, String> params;  // 渠道参数
    private boolean enabled;      // 是否启用
}
```

- **配置示例**
```yaml
alert:
  adapters:
    waf:
      name: "长亭WAF"
      description: "长亭WAF告警适配器"
      field-mappings:
        # 基础字段映射
        alert_id:
          source-field: "$.data.id"
          target-field: "id"
          type: "string"
          required: true
        
        title:
          source-field: "$.data.alert_name"
          target-field: "title"
          type: "string"
          required: true
        
        # 复杂字段映射
        source:
          source-field: "$.data.source"
          target-field: "source"
          type: "object"
          field-mappings:
            type: 
              value: "WAF"
            name:
              source-field: "$.data.waf_instance"
            instance:
              source-field: "$.data.site_id"
        
        # 数组字段映射
        tags:
          source-field: "$.data.tags"
          target-field: "tags"
          type: "array"
          array-fields:
            - source-field: "name"
              target-field: "tag_name"
            - source-field: "value"
              target-field: "tag_value"
        
        # 枚举值映射
        severity:
          source-field: "$.data.risk_level"
          target-field: "severity"
          type: "enum"
          enum-mapping:
            high: "critical"
            medium: "warning"
            low: "info"
          required: true
        
        # 自定义转换
        timestamp:
          source-field: "$.data.timestamp"
          target-field: "timestamp"
          type: "datetime"
          transform-expression: "timestamp.toISOString()"
          required: true
        
        # 属性字段映射
        attributes:
          type: "object"
          field-mappings:
            ip:
              source-field: "$.data.src_ip"
            domain:
              source-field: "$.data.domain"
            protocol:
              source-field: "$.data.protocol"
            port:
              source-field: "$.data.dst_port"
              type: "integer"
            attack_type:
              source-field: "$.data.attack_type"
            risk_level:
              source-field: "$.data.risk_level"
            
        # 保留原始数据
        raw_data:
          source-field: "$"
          target-field: "raw_data"
          type: "json"
    
    tdp:
      name: "微步TDP"
      description: "微步威胁检测平台适配器"
      field-mappings:
        alert_id:
          source-field: "$.alert.alert_id"
          target-field: "id"
          type: "string"
          required: true
        
        title:
          source-field: "$.alert.title"
          target-field: "title"
          type: "string"
          required: true
        
        source:
          type: "object"
          field-mappings:
            type:
              value: "TDP"
            name:
              source-field: "$.alert.product_name"
            instance:
              source-field: "$.alert.instance_id"
        
        severity:
          source-field: "$.alert.severity"
          target-field: "severity"
          type: "enum"
          enum-mapping:
            严重: "critical"
            警告: "warning"
            提示: "info"
          required: true
```

#### 4.2.2 配置管理服务
```java
@Service
public class ConfigurationService {
    // 更新适配器配置
    public void updateAdapterConfig(AdapterConfigDTO config) {
        validateAdapterConfig(config);
        validateFieldMappings(config.getFieldMappings());
        saveConfig(config);
        refreshCache();
    }

    // 验证字段映射配置
    private void validateFieldMappings(Map<String, FieldMappingConfig> mappings) {
        for (Map.Entry<String, FieldMappingConfig> entry : mappings.entrySet()) {
            FieldMappingConfig mapping = entry.getValue();
            
            // 验证必填字段
            if (mapping.isRequired() && 
                (mapping.getSourceField() == null || mapping.getDefaultValue() == null)) {
                throw new ValidationException("Required field must have source or default value");
            }
            
            // 验证JsonPath表达式
            if (mapping.getSourceField() != null) {
                validateJsonPath(mapping.getSourceField());
            }
            
            // 验证转换表达式
            if (mapping.getTransformExpression() != null) {
                validateTransformExpression(mapping.getTransformExpression());
            }
            
            // 验证枚举映射
            if ("enum".equals(mapping.getType()) && mapping.getEnumMapping() == null) {
                throw new ValidationException("Enum type must have mapping values");
            }
        }
    }

    // 测试适配器配置
    public TestResult testAdapterConfig(String sourceType, String testData) {
        AdapterConfigEntity config = getAdapterConfig(sourceType);
        return testFieldMappings(config.getFieldMappings(), testData);
    }
    
    // 测试字段映射
    private TestResult testFieldMappings(Map<String, FieldMappingConfig> mappings, String testData) {
        TestResult result = new TestResult();
        
        try {
            // 解析测试数据
            JsonNode data = JsonUtils.parse(testData);
            
            // 测试每个字段映射
            for (Map.Entry<String, FieldMappingConfig> entry : mappings.entrySet()) {
                String fieldName = entry.getKey();
                FieldMappingConfig mapping = entry.getValue();
                
                try {
                    // 提取字段值
                    Object value = extractField(data, mapping);
                    // 应用转换
                    Object transformed = transformValue(value, mapping);
                    // 记录测试结果
                    result.addFieldResult(fieldName, true, transformed);
                } catch (Exception e) {
                    result.addFieldResult(fieldName, false, e.getMessage());
                }
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setError("Failed to parse test data: " + e.getMessage());
        }
        
        return result;
    }
}
```

### 4.3 实现建议

#### 4.3.1 数据源适配
1. **字段映射处理**
   - 使用JsonPath提取字段值
   - 支持复杂的数据结构转换
   - 灵活的默认值处理
   - 类型转换和验证

2. **配置验证**
   - JsonPath表达式验证
   - 必填字段检查
   - 数据类型验证
   - 转换表达式验证

3. **性能优化**
   - 缓存JsonPath编译结果
   - 批量数据处理优化
   - 并行处理能力

## 5. 后续演进

### 5.1 功能演进
- 告警知识库
- 智能分析能力
- 自动化处理