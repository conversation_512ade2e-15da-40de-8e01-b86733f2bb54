# 告警中心设计文档

## 1. 系统概述

告警中心是一个集中管理各类安全告警的平台，支持多种告警源输入和输出方式。系统能够从不同的安全设备和系统收集告警信息，统一处理后转发到指定的目标系统。

## 2. 系统架构

### 2.1 整体架构

```mermaid
graph TD
    A[告警源] -->|日志/Webhook| B[告警输入]
    B --> C[告警处理]
    C --> D[告警存储]
    D --> E[告警输出]
    E -->|基于标签过滤| F[FlashDuty]
    E -->|转发元数据| F
```

### 2.2 告警处理流程

```mermaid
flowchart TD
    A[开始] --> B{数据获取方式?}
    B -->|日志| C[识别日志类型]
    B -->|Webhook| D[根据Token识别告警源]
    C --> E[查找匹配的告警输入配置]
    D --> F[查找对应的告警输入配置]
    E --> G[解析数据]
    F --> G
    G --> H[转换为标准Alert对象]
    H --> I[存储告警]
    I --> J[查询关联的输出配置]
    J --> K{筛选方式?}
    K -->|基于标签过滤| L[检查是否匹配标签规则]
    K -->|转发元数据| M[直接转发原始数据]
    L -->|匹配| N[发送告警]
    L -->|不匹配| O[结束]
    M --> N
    N --> O
```

## 3. 数据模型设计

### 3.1 告警源类型枚举（AlertSourceTypeEnum）

定义了系统支持的告警源类型，包括网宿WAF、雷池WAF、蜜罐、牧云、GRFNA、阿里云等。每种告警源类型关联了数据获取方式和支持的日志类型。

### 3.2 数据获取方式枚举（DataCollectionTypeEnum）

定义了系统支持的数据获取方式，包括日志和Webhook。日志方式需要指定日志类型，Webhook方式需要提供Webhook URL。

### 3.3 日志类型枚举（LogTypeEnum）

定义了系统支持的日志类型，包括蜜罐访问日志、蜜罐攻击日志、牧云安全日志、牧云性能日志、GRFNA告警日志、GRFNA事件日志等。

### 3.4 数据筛选方式枚举（FilterTypeEnum）

定义了系统支持的数据筛选方式，包括基于标签过滤和转发元数据。

### 3.5 告警方式枚举（OutputTypeEnum）

定义了系统支持的告警方式，目前只有FlashDuty。

### 3.6 告警输入配置（AlertInputConfig）

告警输入配置定义了如何接收和解析告警数据。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | Long | 配置ID |
| name | String | 告警源名称 |
| sourceType | String | 告警源类型 |
| collectionType | String | 数据获取方式 |
| logType | String | 日志类型 |
| webhookUrl | String | Webhook URL |
| webhookToken | String | Webhook Token |
| adapterType | String | 适配器类型 |
| configJson | String | 配置JSON |
| status | String | 状态 |
| createAt | Long | 创建时间 |
| createBy | String | 创建人ID |
| createName | String | 创建人名称 |
| updateAt | Long | 更新时间 |
| updateBy | String | 更新人ID |
| updateName | String | 更新人名称 |
| isDel | Integer | 是否删除 |

### 3.7 告警输出配置（AlertOutputConfig）

告警输出配置定义了如何处理和转发告警数据。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | Long | 配置ID |
| name | String | 输出名称 |
| sourceId | Long | 关联告警源ID |
| sourceName | String | 关联告警源名称 |
| outputType | String | 告警方式 |
| webhookUrl | String | 输出Webhook URL |
| filterType | String | 数据筛选方式 |
| tagFilterRules | String | 标签过滤规则JSON |
| status | String | 状态 |
| createAt | Long | 创建时间 |
| createBy | String | 创建人ID |
| createName | String | 创建人名称 |
| updateAt | Long | 更新时间 |
| updateBy | String | 更新人ID |
| updateName | String | 更新人名称 |
| isDel | Integer | 是否删除 |

### 3.8 告警字段映射（AlertFieldMapping）

告警字段映射定义了如何将源数据字段映射到目标字段。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | Long | 映射ID |
| sourceId | Long | 告警源ID |
| sourceField | String | 源字段路径 |
| targetField | String | 目标字段 |
| fieldType | String | 字段类型 |
| defaultValue | String | 默认值 |
| transformExpression | String | 转换表达式 |
| required | String | 是否必填 |
| enumMappingJson | String | 枚举映射JSON |
| orderNum | Integer | 排序 |
| status | String | 状态 |
| createAt | Long | 创建时间 |
| createBy | String | 创建人ID |
| createName | String | 创建人名称 |
| updateAt | Long | 更新时间 |
| updateBy | String | 更新人ID |
| updateName | String | 更新人名称 |
| isDel | Integer | 是否删除 |

### 3.9 告警信息（Alert）

告警信息是系统处理的核心数据对象。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | Long | 告警ID |
| title | String | 告警标题 |
| description | String | 告警详细描述 |
| severity | String | 严重程度 |
| status | String | 状态 |
| sourceId | Long | 告警源ID |
| sourceName | String | 告警源名称 |
| sourceInstance | String | 告警源实例 |
| occurredAt | Date | 告警发生时间 |
| detectedAt | Date | 告警检测时间 |
| resolvedAt | Date | 告警解决时间 |
| resolvedBy | String | 解决人ID |
| resolvedByName | String | 解决人名称 |
| resolutionNote | String | 解决说明 |
| rawData | String | 原始数据 |
| createAt | Long | 创建时间 |
| createBy | String | 创建人ID |
| createName | String | 创建人名称 |
| updateAt | Long | 更新时间 |
| updateBy | String | 更新人ID |
| updateName | String | 更新人名称 |
| isDel | Integer | 是否删除 |

## 4. 核心服务设计

### 4.1 日志处理服务（LogProcessService）

日志处理服务负责处理日志方式的告警数据，包括识别日志类型、查找匹配的告警输入配置、解析日志数据等功能。

### 4.2 告警输出处理服务（AlertOutputService）

告警输出处理服务负责处理告警输出，包括查询与告警源关联的输出配置、根据筛选方式处理告警数据、发送告警等功能。

### 4.3 告警输入配置服务（IAlertInputConfigService）

告警输入配置服务提供了对告警输入配置的增删改查功能，以及生成Webhook Token和URL的功能。

### 4.4 告警输出配置服务（IAlertOutputConfigService）

告警输出配置服务提供了对告警输出配置的增删改查功能，以及根据告警源ID查询输出配置的功能。

## 5. 接口设计

### 5.1 告警API接口（AlertApiController）

告警API接口提供了接收Webhook告警数据的功能，是系统对外的主要接口。

```
POST /api/v1/alert/{token}
```

### 5.2 告警输入配置接口（AlertInputConfigController）

告警输入配置接口提供了对告警输入配置的管理功能。

```
GET /alert/input/list
GET /alert/input/export
GET /alert/input/{id}
POST /alert/input
PUT /alert/input
DELETE /alert/input/{ids}
GET /alert/input/token/generate
GET /alert/input/url/generate/{token}
```

### 5.3 告警输出配置接口（AlertOutputConfigController）

告警输出配置接口提供了对告警输出配置的管理功能。

```
GET /alert/output/list
GET /alert/output/export
GET /alert/output/{id}
POST /alert/output
PUT /alert/output
DELETE /alert/output/{ids}
GET /alert/output/source/{sourceId}
```

## 6. 数据库设计

### 6.1 告警输入配置表（alert_input_config）

```sql
create table alert_input_config (
  id                bigint(20)      not null auto_increment    comment '配置ID',
  name              varchar(100)    not null                   comment '告警源名称',
  source_type       varchar(50)     not null                   comment '告警源类型',
  collection_type   varchar(50)     not null                   comment '数据获取方式',
  log_type          varchar(50)                                comment '日志类型',
  webhook_url       varchar(255)                               comment 'Webhook URL',
  webhook_token     varchar(100)                               comment 'Webhook Token',
  adapter_type      varchar(50)     not null                   comment '适配器类型',
  config_json       text                                       comment '配置JSON',
  status            varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',
  create_at         bigint(20)      not null                   comment '创建时间',
  create_by         varchar(64)     not null                   comment '创建人ID',
  create_name       varchar(100)    not null                   comment '创建人名称',
  update_at         bigint(20)      not null                   comment '更新时间',
  update_by         varchar(64)     not null                   comment '更新人ID',
  update_name       varchar(100)    not null                   comment '更新人名称',
  is_del            tinyint(1)      default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警输入配置表';
```

### 6.2 告警输出配置表（alert_output_config）

```sql
create table alert_output_config (
  id                bigint(20)      not null auto_increment    comment '配置ID',
  name              varchar(100)    not null                   comment '输出名称',
  source_id         bigint(20)      not null                   comment '关联告警源ID',
  source_name       varchar(100)    not null                   comment '关联告警源名称',
  output_type       varchar(50)     not null                   comment '告警方式',
  webhook_url       varchar(255)                               comment '输出Webhook URL',
  filter_type       varchar(50)     not null                   comment '数据筛选方式',
  tag_filter_rules  text                                       comment '标签过滤规则JSON',
  status            varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',
  create_at         bigint(20)      not null                   comment '创建时间',
  create_by         varchar(64)     not null                   comment '创建人ID',
  create_name       varchar(100)    not null                   comment '创建人名称',
  update_at         bigint(20)      not null                   comment '更新时间',
  update_by         varchar(64)     not null                   comment '更新人ID',
  update_name       varchar(100)    not null                   comment '更新人名称',
  is_del            tinyint(1)      default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警输出配置表';
```

### 6.3 告警字段映射表（alert_field_mapping）

```sql
create table alert_field_mapping (
  id                    bigint(20)      not null auto_increment    comment '映射ID',
  source_id             bigint(20)      not null                   comment '告警源ID',
  source_field          varchar(255)    not null                   comment '源字段路径',
  target_field          varchar(100)    not null                   comment '目标字段',
  field_type            varchar(50)     not null                   comment '字段类型',
  default_value         varchar(255)                               comment '默认值',
  transform_expression  varchar(500)                               comment '转换表达式',
  required              varchar(10)     default 'false'            comment '是否必填(true/false)',
  enum_mapping_json     text                                       comment '枚举映射JSON',
  order_num             int(4)          default 0                  comment '排序',
  status                varchar(20)     default 'enabled'          comment '状态(enabled/disabled)',
  create_at             bigint(20)      not null                   comment '创建时间',
  create_by             varchar(64)     not null                   comment '创建人ID',
  create_name           varchar(100)    not null                   comment '创建人名称',
  update_at             bigint(20)      not null                   comment '更新时间',
  update_by             varchar(64)     not null                   comment '更新人ID',
  update_name           varchar(100)    not null                   comment '更新人名称',
  is_del                tinyint(1)      default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警字段映射表';
```

## 7. 扩展性设计

### 7.1 告警源类型扩展

系统支持通过添加新的告警源类型枚举值来扩展支持的告警源类型。

### 7.2 日志类型扩展

系统支持通过添加新的日志类型枚举值来扩展支持的日志类型。

### 7.3 告警方式扩展

系统支持通过添加新的告警方式枚举值和相应的处理逻辑来扩展支持的告警方式。

### 7.4 适配器扩展

系统支持通过实现AlertAdapter接口来扩展支持的适配器类型。

## 8. 安全性设计

### 8.1 Webhook Token

系统使用随机生成的Token来验证Webhook请求的合法性，确保只有授权的系统可以发送告警数据。

### 8.2 数据验证

系统对接收的数据进行验证，确保数据的完整性和有效性。

### 8.3 权限控制

系统使用Spring Security进行权限控制，确保只有授权的用户可以访问和管理告警配置。

## 9. 性能优化

### 9.1 数据库索引

系统在关键字段上创建索引，提高查询性能。

### 9.2 缓存机制

系统使用缓存机制，减少数据库访问，提高响应速度。

### 9.3 异步处理

系统使用异步处理机制，提高并发处理能力。

## 10. 部署要求

### 10.1 硬件要求

- CPU: 4核以上
- 内存: 8GB以上
- 磁盘: 100GB以上

### 10.2 软件要求

- 操作系统: CentOS 7.x或更高版本
- JDK: 1.8或更高版本
- MySQL: 5.7或更高版本
- Redis: 5.0或更高版本

### 10.3 网络要求

- 内网带宽: 100Mbps以上
- 外网带宽: 10Mbps以上
- 防火墙: 开放必要的端口
