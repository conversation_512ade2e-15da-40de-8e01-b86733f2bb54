# Alert 搜索功能实现建议

## 1. 搜索字段分析

### 当前数据结构
```sql
-- Alert 表主要字段
title VARCHAR(500)          -- 告警标题
source_type VARCHAR(32)     -- 告警源类型
severity VARCHAR(20)        -- 严重程度
status VARCHAR(20)          -- 状态
rule_name VARCHAR(64)       -- 规则名称
group_name VARCHAR(100)     -- 组名称
source_ip VARCHAR(50)       -- 源IP
target_ip VARCHAR(50)       -- 目标IP
tags JSON                   -- 标签（JSON格式）
occurred_at BIGINT(20)      -- 发生时间
```

### 新增搜索字段
1. **targetIp** - 直接字段查询，性能好
2. **serviceName** - 通过 tags.service 字段搜索服务名称
3. **tagSearch** - JSON 字段查询，需要特殊处理

## 2. 标签搜索实现方案

### 方案一：MySQL JSON 函数（推荐）
```java
// 标签搜索解析
public class TagSearchParser {

    public static String parseTagSearch(String tagSearch, String searchType) {
        if (StringUtils.isEmpty(tagSearch)) {
            return null;
        }

        // 解析 key:value 格式
        String[] parts = tagSearch.split(":", 2);
        if (parts.length != 2) {
            return null;
        }

        String key = parts[0].trim();
        String value = parts[1].trim();

        if ("exact".equals(searchType)) {
            return String.format("JSON_EXTRACT(tags, '$.%s') = '%s'", key, value);
        } else {
            return String.format("JSON_EXTRACT(tags, '$.%s') LIKE '%%%s%%'", key, value);
        }
    }
}
```

### 方案二：虚拟列索引（高性能）
```sql
-- 创建常用标签的虚拟列
ALTER TABLE alert ADD COLUMN environment_tag VARCHAR(50)
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.environment'))) VIRTUAL;
CREATE INDEX idx_environment_tag ON alert(environment_tag);

ALTER TABLE alert ADD COLUMN service_tag VARCHAR(100)
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.service'))) VIRTUAL;
CREATE INDEX idx_service_tag ON alert(service_tag);
```

## 3. MyBatis Mapper 实现

### AlertMapper.xml 更新
```xml
<select id="selectAlertList" parameterType="AlertQuery" resultMap="AlertResult">
    SELECT
        id, title, description, severity, status, alert_type, source_ip, target_ip, url,
        event_id, source_type, source_ident, source_name, rule_name, group_name,
        occurred_at, detected_at, resolved_at, resolved_by, resolved_by_name, resolution_note,
        duration_ms, tags, attributes, raw_data,
        create_at, create_by, create_name, update_at, update_by, update_name, is_del
    FROM alert
    <where>
        is_del = 0
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND source_type = #{sourceType}
        </if>
        <if test="severity != null and severity != ''">
            AND severity = #{severity}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="serviceName != null and serviceName != ''">
            AND JSON_EXTRACT(tags, '$.service') LIKE CONCAT('%', #{serviceName}, '%')
        </if>
        <if test="sourceIp != null and sourceIp != ''">
            AND source_ip = #{sourceIp}
        </if>
        <if test="targetIp != null and targetIp != ''">
            AND target_ip = #{targetIp}
        </if>
        <if test="tagSearchCondition != null and tagSearchCondition != ''">
            AND ${tagSearchCondition}
        </if>
        <if test="startTime != null and endTime != null">
            AND occurred_at BETWEEN #{startTime} AND #{endTime}
        </if>
    </where>
    ORDER BY occurred_at DESC
</select>
```

## 4. Service 层实现

### AlertService 更新
```java
@Service
public class AlertServiceImpl implements IAlertService {

    @Override
    public List<Alert> selectAlertList(Alert alert) {
        // 处理标签搜索
        if (StringUtils.isNotEmpty(alert.getTagSearch())) {
            String tagCondition = parseTagSearch(alert.getTagSearch(), alert.getTagSearchType());
            alert.setTagSearchCondition(tagCondition);
        }

        // 处理时间范围
        if (alert.getTimeRange() != null && alert.getTimeRange().length == 2) {
            alert.setStartTime(parseTime(alert.getTimeRange()[0]));
            alert.setEndTime(parseTime(alert.getTimeRange()[1]));
        }

        return alertMapper.selectAlertList(alert);
    }

    private String parseTagSearch(String tagSearch, String searchType) {
        if (StringUtils.isEmpty(tagSearch)) {
            return null;
        }

        // 支持多种格式
        if (tagSearch.contains(":")) {
            // key:value 格式
            String[] parts = tagSearch.split(":", 2);
            String key = parts[0].trim();
            String value = parts[1].trim();

            if ("exact".equals(searchType)) {
                return String.format("JSON_EXTRACT(tags, '$.%s') = '%s'", key, value);
            } else {
                return String.format("JSON_EXTRACT(tags, '$.%s') LIKE '%%%s%%'", key, value);
            }
        } else {
            // 全文搜索
            return String.format("tags LIKE '%%%s%%'", tagSearch);
        }
    }
}
```

## 5. 前端标签搜索用户体验

### 搜索提示
```javascript
// 常用标签提示
const commonTags = [
  'environment:production',
  'environment:staging',
  'environment:test',
  'service:web',
  'service:api',
  'service:database',
  'team:backend',
  'team:frontend',
  'team:devops'
]

// 搜索建议组件
<el-autocomplete
  v-model="queryParams.tagSearch"
  :fetch-suggestions="getTagSuggestions"
  placeholder="environment:prod 或 service:api"
  @select="handleTagSelect"
>
  <template slot-scope="{ item }">
    <div class="tag-suggestion">
      <span class="tag-key">{{ item.key }}</span>:
      <span class="tag-value">{{ item.value }}</span>
    </div>
  </template>
</el-autocomplete>
```

## 6. 性能优化建议

### 索引策略
```sql
-- 基础字段索引
CREATE INDEX idx_alert_search ON alert (source_type, severity, status, occurred_at DESC);
CREATE INDEX idx_alert_ip ON alert (source_ip, target_ip);
CREATE INDEX idx_alert_rule ON alert (rule_name, group_name);

-- JSON 虚拟列索引（推荐）
ALTER TABLE alert ADD COLUMN environment_tag VARCHAR(50)
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.environment'))) VIRTUAL;
CREATE INDEX idx_environment_tag ON alert(environment_tag);

ALTER TABLE alert ADD COLUMN service_tag VARCHAR(100)
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.service'))) VIRTUAL;
CREATE INDEX idx_service_tag ON alert(service_tag);
```

### 查询优化
1. **分页查询**：使用 LIMIT 和 OFFSET
2. **时间范围限制**：避免全表扫描
3. **索引覆盖**：尽量使用覆盖索引
4. **查询缓存**：对热点查询使用 Redis 缓存

## 7. 实现优先级

### 第一阶段（基础功能）
1. ✅ targetIp 字段查询
2. ✅ ruleOrGroup 组合查询
3. ✅ 基础标签搜索（key:value 格式）

### 第二阶段（性能优化）
1. 创建虚拟列索引
2. 实现查询缓存
3. 添加搜索建议

### 第三阶段（高级功能）
1. 复杂标签查询（多条件组合）
2. 全文搜索
3. 搜索历史记录

## 8. 测试用例

### 标签搜索测试
```javascript
// 测试用例
const testCases = [
  {
    input: 'environment:production',
    expected: "JSON_EXTRACT(tags, '$.environment') = 'production'"
  },
  {
    input: 'service:web',
    searchType: 'contains',
    expected: "JSON_EXTRACT(tags, '$.service') LIKE '%web%'"
  },
  {
    input: 'team:backend',
    searchType: 'exact',
    expected: "JSON_EXTRACT(tags, '$.team') = 'backend'"
  }
]
```

这个实现方案既保证了功能的完整性，又考虑了性能和用户体验。建议按照优先级分阶段实现。
