# Securio Plugin 模块

## 概述

`securio-plugin` 是 Securio 安全运营平台的插件集成模块，用于集成第三方组件和服务。目前已集成悟空 CMDB API，提供完整的主机和服务器信息查询功能。

## 功能特性

### 悟空 CMDB 集成

- **服务器基础信息查询**：根据 IP 地址获取服务器的基本配置信息
- **主机进程信息查询**：获取主机上运行的进程详细信息
- **主机详细信息查询**：获取主机的系统配置、环境变量、网络端口等详细信息
- **服务器关联信息查询**：获取服务器上部署的中间件、服务和应用信息
- **服务器在线状态检查**：检查服务器是否在线
- **服务器综合信息查询**：一次性获取服务器的所有相关信息

## 模块结构

```
securio-plugin/
├── src/main/java/com/wiwj/securio/plugin/
│   └── wukong/                          # 悟空 CMDB 集成
│       ├── api/                         # API 客户端
│       │   └── WukongCMDBAPI.java      # 悟空 CMDB API 客户端
│       ├── config/                      # 配置类
│       │   └── WukongCMDBConfig.java   # 悟空 CMDB 配置
│       ├── controller/                  # 控制器
│       │   └── WukongCMDBController.java # REST API 控制器
│       ├── dto/                         # 数据传输对象
│       │   ├── WukongBaseResponse.java  # 基础响应类
│       │   ├── ServerInfo.java          # 服务器信息
│       │   ├── ProcessInfo.java         # 进程信息
│       │   ├── HostDetailInfo.java      # 主机详细信息
│       │   ├── MiddlewareInfo.java      # 中间件信息
│       │   ├── ServiceInfo.java         # 服务信息
│       │   ├── ServerRelationsData.java # 服务器关联数据
│       │   └── WukongServerSummary.java # 服务器综合信息
│       └── service/                     # 服务层
│           ├── WukongCMDBService.java   # 服务接口
│           └── impl/
│               └── WukongCMDBServiceImpl.java # 服务实现
└── src/main/resources/
    └── application-wukong.yml           # 配置文件示例
```

## 配置说明

### 悟空 CMDB 配置

在 `application.yml` 或 `application-wukong.yml` 中添加以下配置：

```yaml
wukong:
  cmdb:
    # 悟空 CMDB API 基础 URL
    base-url: http://wukong-test.5i5j.com/prod-api
    # API 超时时间（毫秒）
    timeout: 30000
    # 连接超时时间（毫秒）
    connect-timeout: 10000
    # 读取超时时间（毫秒）
    read-timeout: 30000
    # 最大重试次数
    max-retries: 3
    # 是否启用 SSL 验证
    ssl-enabled: true
    # API 版本
    api-version: v1
    # 用户代理
    user-agent: Securio-Plugin/1.0.0
```

## API 接口

### REST API 端点

所有 API 端点都需要相应的权限：`plugin:wukong:cmdb:query`

#### 1. 获取服务器基础信息
```
GET /plugin/wukong/cmdb/server/info?ip={ip}
```

#### 2. 获取主机进程信息
```
GET /plugin/wukong/cmdb/host/process?ip={ip}
```

#### 3. 获取主机详细信息
```
GET /plugin/wukong/cmdb/host/detail?ip={ip}
```

#### 4. 获取服务器关联信息
```
GET /plugin/wukong/cmdb/server/relations?ip={ip}
```

```

## 使用示例

### Java 代码示例

```java
@Resource
private WukongCMDBService wukongCMDBService;

// 获取服务器基础信息
ServerInfo serverInfo = wukongCMDBService.getServerInfoByIp("***********");

// 获取主机进程信息
List<ProcessInfo> processInfoList = wukongCMDBService.getHostProcessInfo("***********");

// 获取服务器综合信息
WukongServerSummary summary = wukongCMDBService.getServerSummary("***********");

// 检查服务器是否在线
boolean isOnline = wukongCMDBService.isServerOnline("***********");
```

### HTTP 请求示例

```bash
# 获取服务器基础信息
curl -X GET "http://localhost:8080/plugin/wukong/cmdb/server/info?ip=***********" \
     -H "Authorization: Bearer {token}"

# 获取服务器综合信息
curl -X GET "http://localhost:8080/plugin/wukong/cmdb/server/summary?ip=***********" \
     -H "Authorization: Bearer {token}"
```

## 依赖说明

### Maven 依赖

```xml
<dependency>
    <groupId>com.wiwj</groupId>
    <artifactId>securio-plugin</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 核心依赖

- `securio-common`：通用工具类
- `securio-framework`：框架核心
- `spring-web`：HTTP 客户端支持
- `fastjson2`：JSON 处理
- `swagger`：API 文档

## 扩展开发

### 添加新的第三方集成

1. 在 `com.wiwj.securio.plugin` 下创建新的包
2. 按照悟空 CMDB 的结构创建相应的类：
   - `api/`：API 客户端
   - `config/`：配置类
   - `dto/`：数据传输对象
   - `service/`：服务层
   - `controller/`：控制器

### 最佳实践

1. **遵循 SOLID 原则**：保持代码的单一职责和开闭原则
2. **使用 DRY 原则**：避免重复代码
3. **遵循 OWASP 最佳实践**：确保安全性
4. **完善的日志记录**：记录关键操作和异常信息
5. **异常处理**：优雅处理各种异常情况
6. **配置外部化**：将配置项外部化，便于部署时调整

## 注意事项

1. 所有 API 调用都需要相应的权限验证
2. 建议在生产环境中配置适当的超时时间
3. 注意 IP 地址格式的验证
4. 对于大量数据查询，建议实现分页或限制返回数据量
5. 定期检查第三方 API 的可用性和版本更新

## 版本历史

- **v1.0.0**：初始版本，集成悟空 CMDB API 