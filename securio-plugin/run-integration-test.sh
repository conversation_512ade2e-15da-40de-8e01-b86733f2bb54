#!/bin/bash

# 悟空 CMDB API 集成测试运行脚本
# 
# 使用方法：
# 1. 直接运行所有测试（包括集成测试）：
#    ./run-integration-test.sh
# 
# 2. 只运行单元测试：
#    ./run-integration-test.sh unit
#
# 3. 只运行集成测试：
#    ./run-integration-test.sh integration

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Maven 是否可用
if ! command -v mvn &> /dev/null; then
    print_error "Maven 未安装或不在 PATH 中"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

print_info "当前工作目录: $(pwd)"

# 解析命令行参数
TEST_TYPE="${1:-all}"

case "$TEST_TYPE" in
    "unit")
        print_info "运行单元测试..."
        mvn test -Dtest="!*IntegrationTest"
        ;;
    "integration")
        print_info "运行集成测试..."
        print_warn "注意：集成测试需要网络连接和有效的 API 访问权限"
        
        # 设置环境变量启用集成测试
        export WUKONG_INTEGRATION_TEST=true
        
        # 运行集成测试
        mvn test -Dtest="*IntegrationTest"
        ;;
    "all")
        print_info "运行所有测试..."
        
        # 先运行单元测试
        print_info "1. 运行单元测试..."
        mvn test -Dtest="!*IntegrationTest"
        
        if [ $? -eq 0 ]; then
            print_info "单元测试通过 ✓"
            
            # 询问是否运行集成测试
            read -p "是否运行集成测试？(需要网络连接) [y/N]: " -n 1 -r
            echo
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                print_info "2. 运行集成测试..."
                export WUKONG_INTEGRATION_TEST=true
                mvn test -Dtest="*IntegrationTest"
                
                if [ $? -eq 0 ]; then
                    print_info "集成测试通过 ✓"
                else
                    print_error "集成测试失败 ✗"
                    exit 1
                fi
            else
                print_warn "跳过集成测试"
            fi
        else
            print_error "单元测试失败 ✗"
            exit 1
        fi
        ;;
    *)
        print_error "无效的测试类型: $TEST_TYPE"
        echo "使用方法: $0 [unit|integration|all]"
        exit 1
        ;;
esac

print_info "测试完成！" 