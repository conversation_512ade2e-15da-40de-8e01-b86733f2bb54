package com.wiwj.securio.plugin.wukong;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.plugin.wukong.api.WukongCMDBAPI;
import com.wiwj.securio.plugin.wukong.dto.WukongBaseResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 悟空 CMDB API 简单单元测试
 * 
 * 专门测试 Bearer Token 授权功能
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class WukongCMDBAPISimpleTest {
    
    @Mock
    private RestTemplate restTemplate;
    
    @InjectMocks
    private WukongCMDBAPI wukongCMDBAPI;
    
    private static final String TEST_TOKEN = "B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd";
    private static final String TEST_BASE_URL = "https://wukong.5i5j.com/prod-api";
    
    @BeforeEach
    public void setUp() {
        // 使用反射设置私有字段
        ReflectionTestUtils.setField(wukongCMDBAPI, "token", TEST_TOKEN);
        ReflectionTestUtils.setField(wukongCMDBAPI, "baseUrl", TEST_BASE_URL);
        ReflectionTestUtils.setField(wukongCMDBAPI, "timeout", 30000);
    }
    
    @Test
    public void testBearerTokenIsIncludedInRequest() {
        // 准备测试数据
        String testIp = "***********";
        String responseJson = "{\"msg\":\"操作成功\",\"code\":200,\"data\":{\"hostIp\":\"***********\",\"hostName\":\"test-server\"}}";
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);
        
        // 使用 ArgumentCaptor 捕获 HttpEntity 参数
        ArgumentCaptor<HttpEntity<String>> entityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), entityCaptor.capture(), eq(String.class)))
                .thenReturn(responseEntity);
        
        // 执行测试
        WukongBaseResponse<JSONObject> result = wukongCMDBAPI.getServerInfoByIp(testIp);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("操作成功", result.getMsg());
        
        // 验证 Authorization 头是否正确设置
        HttpEntity<String> capturedEntity = entityCaptor.getValue();
        HttpHeaders headers = capturedEntity.getHeaders();
        
        assertTrue(headers.containsKey("Authorization"), "请求头应该包含 Authorization");
        
        String authHeader = headers.getFirst("Authorization");
        assertNotNull(authHeader, "Authorization 头不能为空");
        assertTrue(authHeader.startsWith("Bearer "), "Authorization 头应该以 'Bearer ' 开头");
        assertTrue(authHeader.contains(TEST_TOKEN), "Authorization 头应该包含正确的 token");
        
        // 验证其他必要的请求头
        assertTrue(headers.containsKey("Content-Type"), "请求头应该包含 Content-Type");
        assertTrue(headers.containsKey("User-Agent"), "请求头应该包含 User-Agent");
        assertTrue(headers.containsKey("Accept"), "请求头应该包含 Accept");
        
        assertEquals("application/json", headers.getFirst("Content-Type"), "Content-Type 应该是 application/json");
        assertEquals("Securio-Plugin/1.0.0", headers.getFirst("User-Agent"), "User-Agent 应该是 Securio-Plugin/1.0.0");
        assertEquals("application/json", headers.getFirst("Accept"), "Accept 应该是 application/json");
    }
    
    @Test
    public void testGetTokenMethod() {
        // 测试 getToken 方法
        String token = wukongCMDBAPI.getToken();
        assertEquals(TEST_TOKEN, token, "getToken 方法应该返回正确的 token");
    }
    
    @Test
    public void testGetBaseUrlMethod() {
        // 测试 getBaseUrl 方法
        String baseUrl = wukongCMDBAPI.getBaseUrl();
        assertEquals(TEST_BASE_URL, baseUrl, "getBaseUrl 方法应该返回正确的 base URL");
    }
    
    @Test
    public void testGetTimeoutMethod() {
        // 测试 getTimeout 方法
        int timeout = wukongCMDBAPI.getTimeout();
        assertEquals(30000, timeout, "getTimeout 方法应该返回正确的超时时间");
    }
    
    @Test
    public void testIsSuccessMethod() {
        // 测试成功响应
        WukongBaseResponse<String> successResponse = new WukongBaseResponse<>();
        successResponse.setCode(200);
        assertTrue(wukongCMDBAPI.isSuccess(successResponse), "响应码 200 应该被认为是成功");
        
        // 测试失败响应
        WukongBaseResponse<String> failResponse = new WukongBaseResponse<>();
        failResponse.setCode(500);
        assertFalse(wukongCMDBAPI.isSuccess(failResponse), "响应码 500 应该被认为是失败");
        
        // 测试空响应
        assertFalse(wukongCMDBAPI.isSuccess(null), "null 响应应该被认为是失败");
        
        // 测试空 code
        WukongBaseResponse<String> nullCodeResponse = new WukongBaseResponse<>();
        assertFalse(wukongCMDBAPI.isSuccess(nullCodeResponse), "null code 应该被认为是失败");
    }
    
    @Test
    public void testInvalidIpParameter() {
        // 测试空 IP
        assertThrows(IllegalArgumentException.class, () -> {
            wukongCMDBAPI.getServerInfoByIp("");
        }, "空 IP 应该抛出 IllegalArgumentException");
        
        assertThrows(IllegalArgumentException.class, () -> {
            wukongCMDBAPI.getServerInfoByIp(null);
        }, "null IP 应该抛出 IllegalArgumentException");
    }
    
    @Test
    public void testUrlConstruction() {
        // 准备测试数据
        String testIp = "*************";
        String responseJson = "{\"msg\":\"操作成功\",\"code\":200,\"data\":{}}";
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);
        
        // 使用 ArgumentCaptor 捕获 URL 参数
        ArgumentCaptor<String> urlCaptor = ArgumentCaptor.forClass(String.class);
        
        when(restTemplate.exchange(urlCaptor.capture(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);
        
        // 执行测试
        wukongCMDBAPI.getServerInfoByIp(testIp);
        
        // 验证 URL 构造
        String capturedUrl = urlCaptor.getValue();
        assertTrue(capturedUrl.contains(TEST_BASE_URL), "URL 应该包含基础 URL");
        assertTrue(capturedUrl.contains("/openapi/cmdb/getServerInfoByIp"), "URL 应该包含正确的端点路径");
        assertTrue(capturedUrl.contains("ip=" + testIp), "URL 应该包含 IP 参数");
    }
    
    @Test
    public void testHttpErrorHandling() {
        // 测试 HTTP 错误响应
        String testIp = "***********";
        
        ResponseEntity<String> errorResponse = new ResponseEntity<>("Internal Server Error", HttpStatus.INTERNAL_SERVER_ERROR);
        
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(errorResponse);
        
        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            wukongCMDBAPI.getServerInfoByIp(testIp);
        }, "HTTP 错误应该抛出 RuntimeException");
        
        assertTrue(exception.getMessage().contains("API 调用失败"), "异常消息应该包含 'API 调用失败'");
        assertTrue(exception.getMessage().contains("500"), "异常消息应该包含 HTTP 状态码");
    }
} 