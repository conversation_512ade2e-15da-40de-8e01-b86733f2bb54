package com.wiwj.securio.plugin.wukong.dto;

import com.alibaba.fastjson2.JSONObject;
import java.util.HashMap;
import java.util.Map;

/**
 * 悟空服务器综合信息 DTO
 * 使用JSONObject存储原始数据，避免DTO类维护问题
 *
 * <AUTHOR>
 */
public class WukongServerSummary {
    
    /**
     * 原始数据存储
     */
    private Map<String, JSONObject> rawData;
    
    /**
     * 数据获取时间戳
     */
    private Long timestamp;
    
    /**
     * 数据获取状态
     */
    private String status;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    public WukongServerSummary() {
        this.rawData = new HashMap<>();
        this.timestamp = System.currentTimeMillis();
        this.status = "SUCCESS";
    }
    
    public WukongServerSummary(String errorMessage) {
        this.rawData = new HashMap<>();
        this.timestamp = System.currentTimeMillis();
        this.status = "ERROR";
        this.errorMessage = errorMessage;
    }
    
    public Map<String, JSONObject> getRawData() {
        return rawData;
    }
    
    public void setRawData(Map<String, JSONObject> rawData) {
        this.rawData = rawData;
    }
    
    public JSONObject getServerInfo() {
        return rawData.get("serverInfo");
    }
    
    public void setServerInfo(JSONObject serverInfo) {
        this.rawData.put("serverInfo", serverInfo);
    }
    
    public JSONObject getHostDetailInfo() {
        return rawData.get("hostDetailInfo");
    }
    
    public void setHostDetailInfo(JSONObject hostDetailInfo) {
        this.rawData.put("hostDetailInfo", hostDetailInfo);
    }
    
    public JSONObject getProcessInfo() {
        return rawData.get("processInfo");
    }
    
    public void setProcessInfo(JSONObject processInfo) {
        this.rawData.put("processInfo", processInfo);
    }
    
    public JSONObject getServerRelations() {
        return rawData.get("serverRelations");
    }
    
    public void setServerRelations(JSONObject serverRelations) {
        this.rawData.put("serverRelations", serverRelations);
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    /**
     * 检查是否有错误
     *
     * @return 是否有错误
     */
    public boolean hasError() {
        return "ERROR".equals(status);
    }
    
    /**
     * 检查服务器是否在线
     *
     * @return 是否在线
     */
    public boolean isServerOnline() {
        JSONObject serverInfo = getServerInfo();
        return serverInfo != null && "在线".equals(serverInfo.getString("host_online_status"));
    }
    
    /**
     * 获取进程数量
     *
     * @return 进程数量
     */
    public int getProcessCount() {
        JSONObject processInfo = getProcessInfo();
        if (processInfo != null && processInfo.containsKey("processes")) {
            Object processes = processInfo.get("processes");
            if (processes instanceof java.util.List) {
                return ((java.util.List<?>) processes).size();
            }
        }
        return 0;
    }
    
    /**
     * 获取中间件数量
     *
     * @return 中间件数量
     */
    public int getMiddlewareCount() {
        JSONObject serverRelations = getServerRelations();
        if (serverRelations != null && serverRelations.containsKey("midware")) {
            Object midware = serverRelations.get("midware");
            if (midware instanceof java.util.List) {
                return ((java.util.List<?>) midware).size();
            }
        }
        return 0;
    }
    
    /**
     * 获取服务数量
     *
     * @return 服务数量
     */
    public int getServiceCount() {
        JSONObject serverRelations = getServerRelations();
        if (serverRelations != null && serverRelations.containsKey("serviceList")) {
            Object serviceList = serverRelations.get("serviceList");
            if (serviceList instanceof java.util.List) {
                return ((java.util.List<?>) serviceList).size();
            }
        }
        return 0;
    }
    
    @Override
    public String toString() {
        return "WukongServerSummary{" +
                "rawData=" + (rawData != null ? rawData.keySet() : "null") +
                ", processCount=" + getProcessCount() +
                ", middlewareCount=" + getMiddlewareCount() +
                ", serviceCount=" + getServiceCount() +
                ", timestamp=" + timestamp +
                ", status='" + status + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
} 