package com.wiwj.securio.plugin.wukong.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 悟空 CMDB 配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "wukong.cmdb")
public class WukongCMDBConfig {
    
    /**
     * 悟空 CMDB API 基础 URL
     */
    private String baseUrl = "http://wukong-test.5i5j.com/prod-api";
    
    /**
     * API 超时时间（毫秒）
     */
    private int timeout = 30000;
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 10000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 3;
    
    /**
     * 是否启用 SSL 验证
     */
    private boolean sslEnabled = true;
    
    /**
     * API 版本
     */
    private String apiVersion = "v1";
    
    /**
     * 用户代理
     */
    private String userAgent = "Securio-Plugin/1.0.0";
    
    /**
     * API 访问令牌
     */
    private String token = "B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd";
    
    public WukongCMDBConfig() {
    }
    
    public String getBaseUrl() {
        return baseUrl;
    }
    
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    public int getTimeout() {
        return timeout;
    }
    
    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
    
    public int getConnectTimeout() {
        return connectTimeout;
    }
    
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    
    public int getReadTimeout() {
        return readTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public int getMaxRetries() {
        return maxRetries;
    }
    
    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }
    
    public boolean isSslEnabled() {
        return sslEnabled;
    }
    
    public void setSslEnabled(boolean sslEnabled) {
        this.sslEnabled = sslEnabled;
    }
    
    public String getApiVersion() {
        return apiVersion;
    }
    
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    @Override
    public String toString() {
        return "WukongCMDBConfig{" +
                "baseUrl='" + baseUrl + '\'' +
                ", timeout=" + timeout +
                ", connectTimeout=" + connectTimeout +
                ", readTimeout=" + readTimeout +
                ", maxRetries=" + maxRetries +
                ", sslEnabled=" + sslEnabled +
                ", apiVersion='" + apiVersion + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", token='" + (token != null ? "***" : "null") + '\'' +
                '}';
    }
} 