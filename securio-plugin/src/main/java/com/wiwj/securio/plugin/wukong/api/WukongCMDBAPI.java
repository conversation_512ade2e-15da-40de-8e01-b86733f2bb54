package com.wiwj.securio.plugin.wukong.api;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.securio.plugin.wukong.dto.WukongBaseResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;

/**
 * 悟空 CMDB API 客户端
 * 
 * 提供与悟空 CMDB 系统交互的 API 接口封装
 *
 * <AUTHOR>
 */
@Component
public class WukongCMDBAPI {
    
    private static final Logger logger = LoggerFactory.getLogger(WukongCMDBAPI.class);
    
    @Resource
    private RestTemplate restTemplate;
    
    /**
     * 悟空 CMDB API 基础 URL
     */
    @Value("${wukong.cmdb.base-url:http://wukong-test.5i5j.com/prod-api}")
    private String baseUrl;
    
    /**
     * API 超时时间（毫秒）
     */
    @Value("${wukong.cmdb.timeout:30000}")
    private int timeout;
    
    /**
     * API 访问令牌
     */
    @Value("${wukong.cmdb.token:B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd}")
    private String token;
    
    /**
     * 根据 IP 获取服务器基础信息
     *
     * @param ip 服务器 IP 地址
     * @return 服务器信息响应
     * @throws RuntimeException 当 API 调用失败时抛出异常
     */
    public WukongBaseResponse<JSONObject> getServerInfoByIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("开始调用悟空 CMDB API 获取服务器信息，IP: {}", ip);
        
        try {
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path("/openapi/cmdb/getServerInfoByIp")
                    .queryParam("ip", ip)
                    .toUriString();
            
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                
                JSONObject responseJson = JSON.parseObject(responseBody);
                WukongBaseResponse<JSONObject> result = new WukongBaseResponse<>();
                result.setCode(responseJson.getInteger("code"));
                result.setMsg(responseJson.getString("msg"));
                result.setData(responseJson.getJSONObject("data"));
                
                logger.info("成功获取服务器信息，IP: {}, 响应码: {}", ip, result.getCode());
                return result;
            } else {
                throw new RuntimeException("API 调用失败，HTTP 状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("调用悟空 CMDB API 获取服务器信息失败，IP: {}", ip, e);
            throw new RuntimeException("获取服务器信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据 IP 获取主机进程信息
     *
     * @param ip 主机 IP 地址
     * @return 主机进程信息响应
     * @throws RuntimeException 当 API 调用失败时抛出异常
     */
    public WukongBaseResponse<JSONObject> getHostProcessInfo(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("开始调用悟空 CMDB API 获取主机进程信息，IP: {}", ip);
        
        try {
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path("/openapi/cmdb/getHostProcessInfo")
                    .queryParam("ip", ip)
                    .toUriString();
            
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                
                JSONObject responseJson = JSON.parseObject(responseBody);
                WukongBaseResponse<JSONObject> result = new WukongBaseResponse<>();
                result.setCode(responseJson.getInteger("code"));
                result.setMsg(responseJson.getString("msg"));
                result.setData(responseJson.getJSONObject("data"));
                
                logger.info("成功获取主机进程信息，IP: {}, 响应码: {}", ip, result.getCode());
                return result;
            } else {
                throw new RuntimeException("API 调用失败，HTTP 状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("调用悟空 CMDB API 获取主机进程信息失败，IP: {}", ip, e);
            throw new RuntimeException("获取主机进程信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取主机详细信息
     *
     * @param ip 主机 IP 地址
     * @return 主机详细信息响应
     * @throws RuntimeException 当 API 调用失败时抛出异常
     */
    public WukongBaseResponse<JSONObject> getHostDetailInfo(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("开始调用悟空 CMDB API 获取主机详细信息，IP: {}", ip);
        
        try {
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path("/openapi/cmdb/getHostDetailInfo")
                    .queryParam("ip", ip)
                    .toUriString();
            
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            logger.info("[getHostDetailInfo]请求URL: {}", url);
            logger.info("[getHostDetailInfo]请求头: {}", headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                logger.info("[getHostDetailInfo]悟空 CMDB API 原始响应: {}", responseBody);
                
                JSONObject responseJson = JSON.parseObject(responseBody);
                logger.info("[getHostDetailInfo]解析后的JSON: {}", responseJson);
                logger.info("[getHostDetailInfo]data字段类型: {}", responseJson.get("data") != null ? responseJson.get("data").getClass().getSimpleName() : "null");
                logger.info("[getHostDetailInfo]data字段内容: {}", responseJson.get("data"));
                
                WukongBaseResponse<JSONObject> result = new WukongBaseResponse<>();
                result.setCode(responseJson.getInteger("code"));
                result.setMsg(responseJson.getString("msg"));
                
                // 更安全地处理data字段
                Object dataObj = responseJson.get("data");
                if (dataObj instanceof JSONObject) {
                    result.setData((JSONObject) dataObj);
                } else if (dataObj != null) {
                    // 如果data不是JSONObject，尝试转换
                    try {
                        JSONObject dataJson = JSON.parseObject(dataObj.toString());
                        result.setData(dataJson);
                    } catch (Exception e) {
                        logger.warn("无法将data字段转换为JSONObject，原始类型: {}, 内容: {}", 
                            dataObj.getClass().getSimpleName(), dataObj);
                        result.setData(null);
                    }
                } else {
                    result.setData(null);
                }
                
                logger.info("成功获取主机详细信息，IP: {}, 响应码: {}, 结果data: {}", ip, result.getCode(), result.getData());
                return result;
            } else {
                throw new RuntimeException("API 调用失败，HTTP 状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("调用悟空 CMDB API 获取主机详细信息失败，IP: {}", ip, e);
            throw new RuntimeException("获取主机详细信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取服务器关联信息
     *
     * @param ip 服务器 IP 地址
     * @return 服务器关联信息响应
     * @throws RuntimeException 当 API 调用失败时抛出异常
     */
    public WukongBaseResponse<JSONObject> getServerRelationsByIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("开始调用悟空 CMDB API 获取服务器关联信息，IP: {}", ip);
        
        try {
            String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .path("/openapi/cmdb/getServerRelationsByIp")
                    .queryParam("ip", ip)
                    .toUriString();
            
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                
                JSONObject responseJson = JSON.parseObject(responseBody);
                WukongBaseResponse<JSONObject> result = new WukongBaseResponse<>();
                result.setCode(responseJson.getInteger("code"));
                result.setMsg(responseJson.getString("msg"));
                result.setData(responseJson.getJSONObject("data"));
                
                logger.info("成功获取服务器关联信息，IP: {}, 响应码: {}", ip, result.getCode());
                return result;
            } else {
                throw new RuntimeException("API 调用失败，HTTP 状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("调用悟空 CMDB API 获取服务器关联信息失败，IP: {}", ip, e);
            throw new RuntimeException("获取服务器关联信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建 HTTP 请求头
     *
     * @return HTTP 请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", "Securio-Plugin/1.0.0");
        headers.set("Accept", "application/json");
        headers.set("Authorization", "Bearer " + token);
        return headers;
    }
    
    /**
     * 检查 API 响应是否成功
     *
     * @param response API 响应
     * @return 是否成功
     */
    public boolean isSuccess(WukongBaseResponse<?> response) {
        return response != null && response.getCode() != null && response.getCode() == 200;
    }
    
    /**
     * 获取配置的基础 URL
     *
     * @return 基础 URL
     */
    public String getBaseUrl() {
        return baseUrl;
    }
    
    /**
     * 获取配置的超时时间
     *
     * @return 超时时间（毫秒）
     */
    public int getTimeout() {
        return timeout;
    }
    
    /**
     * 获取配置的访问令牌
     *
     * @return 访问令牌
     */
    public String getToken() {
        return token;
    }
} 