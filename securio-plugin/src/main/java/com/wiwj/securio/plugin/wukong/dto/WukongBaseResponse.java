package com.wiwj.securio.plugin.wukong.dto;

/**
 * 悟空 CMDB API 基础响应类
 *
 * <AUTHOR>
 */
public class WukongBaseResponse<T> {
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应数据
     */
    private T data;
    
    public WukongBaseResponse() {
    }
    
    public WukongBaseResponse(String msg, Integer code, T data) {
        this.msg = msg;
        this.code = code;
        this.data = data;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    @Override
    public String toString() {
        return "WukongBaseResponse{" +
                "msg='" + msg + '\'' +
                ", code=" + code +
                ", data=" + data +
                '}';
    }
} 