package com.wiwj.securio.plugin.wukong.service;

import com.alibaba.fastjson2.JSONObject;
import com.wiwj.securio.plugin.wukong.dto.WukongServerSummary;

/**
 * 悟空 CMDB 服务接口
 *
 * <AUTHOR>
 */
public interface WukongCMDBService {
    
    /**
     * 根据 IP 获取服务器基础信息
     *
     * @param ip 服务器 IP 地址
     * @return 服务器信息JSON对象
     */
    JSONObject getServerInfoByIp(String ip);
    
    /**
     * 根据 IP 获取主机进程信息
     *
     * @param ip 主机 IP 地址
     * @return 主机进程信息JSON对象
     */
    JSONObject getHostProcessInfo(String ip);
    
    /**
     * 获取主机详细信息
     *
     * @param ip 主机 IP 地址
     * @return 主机详细信息JSON对象
     */
    JSONObject getHostDetailInfo(String ip);
    
    /**
     * 获取服务器关联信息
     *
     * @param ip 服务器 IP 地址
     * @return 服务器关联信息JSON对象
     */
    JSONObject getServerRelationsByIp(String ip);
    
    /**
     * 检查服务器是否在线
     *
     * @param ip 服务器 IP 地址
     * @return 是否在线
     */
    boolean isServerOnline(String ip);
    
    /**
     * 获取服务器的所有相关信息（综合信息）
     *
     * @param ip 服务器 IP 地址
     * @return 服务器综合信息
     */
    WukongServerSummary getServerSummary(String ip);
} 