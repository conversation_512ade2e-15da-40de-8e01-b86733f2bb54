package com.wiwj.securio.plugin.wukong.controller;

import com.alibaba.fastjson2.JSONObject;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.securio.plugin.wukong.dto.WukongServerSummary;
import com.wiwj.securio.plugin.wukong.service.WukongCMDBService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 悟空 CMDB 控制器
 *
 * <AUTHOR>
 */
@Api(tags = "悟空 CMDB 管理")
@RestController
@RequestMapping("/plugin/wukong/cmdb")
public class WukongCMDBController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(WukongCMDBController.class);
    
    @Resource
    private WukongCMDBService wukongCMDBService;
    
    /**
     * 根据 IP 获取服务器基础信息
     */
    @ApiOperation("根据 IP 获取服务器基础信息")
    @PreAuthorize("@ss.hasPermi('plugin:wukong:cmdb:query')")
    @GetMapping("/server/info")
    public AjaxResult getServerInfo(
            @ApiParam(value = "服务器 IP 地址", required = true)
            @RequestParam String ip) {
        
        if (StringUtils.isEmpty(ip)) {
            return AjaxResult.error("IP 地址不能为空");
        }
        
        try {
            JSONObject serverInfo = wukongCMDBService.getServerInfoByIp(ip);
            if (serverInfo != null) {
                return AjaxResult.success("获取服务器信息成功", serverInfo);
            } else {
                return AjaxResult.error("未找到对应的服务器信息");
            }
        } catch (Exception e) {
            logger.error("获取服务器信息失败，IP: {}", ip, e);
            return AjaxResult.error("获取服务器信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据 IP 获取主机进程信息
     */
    @ApiOperation("根据 IP 获取主机进程信息")
    @PreAuthorize("@ss.hasPermi('plugin:wukong:cmdb:query')")
    @GetMapping("/host/process")
    public AjaxResult getHostProcessInfo(
            @ApiParam(value = "主机 IP 地址", required = true)
            @RequestParam String ip) {
        
        if (StringUtils.isEmpty(ip)) {
            return AjaxResult.error("IP 地址不能为空");
        }
        
        try {
            JSONObject processInfo = wukongCMDBService.getHostProcessInfo(ip);
            if (processInfo != null) {
                return AjaxResult.success("获取主机进程信息成功", processInfo);
            } else {
                return AjaxResult.error("未找到对应的主机进程信息");
            }
        } catch (Exception e) {
            logger.error("获取主机进程信息失败，IP: {}", ip, e);
            return AjaxResult.error("获取主机进程信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取主机详细信息
     */
    @ApiOperation("获取主机详细信息")
    @PreAuthorize("@ss.hasPermi('plugin:wukong:cmdb:query')")
    @GetMapping("/host/detail")
    public AjaxResult getHostDetailInfo(
            @ApiParam(value = "主机 IP 地址", required = true)
            @RequestParam String ip) {
        
        if (StringUtils.isEmpty(ip)) {
            return AjaxResult.error("IP 地址不能为空");
        }
        
        try {
            JSONObject hostDetailInfo = wukongCMDBService.getHostDetailInfo(ip);
            if (hostDetailInfo != null) {
                return AjaxResult.success("获取主机详细信息成功", hostDetailInfo);
            } else {
                return AjaxResult.error("未找到对应的主机详细信息");
            }
        } catch (Exception e) {
            logger.error("获取主机详细信息失败，IP: {}", ip, e);
            return AjaxResult.error("获取主机详细信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务器关联信息
     */
    @ApiOperation("获取服务器关联信息")
    @PreAuthorize("@ss.hasPermi('plugin:wukong:cmdb:query')")
    @GetMapping("/server/relations")
    public AjaxResult getServerRelations(
            @ApiParam(value = "服务器 IP 地址", required = true)
            @RequestParam String ip) {
        
        if (StringUtils.isEmpty(ip)) {
            return AjaxResult.error("IP 地址不能为空");
        }
        
        try {
            JSONObject serverRelations = wukongCMDBService.getServerRelationsByIp(ip);
            if (serverRelations != null) {
                return AjaxResult.success("获取服务器关联信息成功", serverRelations);
            } else {
                return AjaxResult.error("未找到对应的服务器关联信息");
            }
        } catch (Exception e) {
            logger.error("获取服务器关联信息失败，IP: {}", ip, e);
            return AjaxResult.error("获取服务器关联信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查服务器是否在线
     */
    @ApiOperation("检查服务器是否在线")
    @PreAuthorize("@ss.hasPermi('plugin:wukong:cmdb:query')")
    @GetMapping("/server/online")
    public AjaxResult isServerOnline(
            @ApiParam(value = "服务器 IP 地址", required = true)
            @RequestParam String ip) {
        
        if (StringUtils.isEmpty(ip)) {
            return AjaxResult.error("IP 地址不能为空");
        }
        
        try {
            boolean isOnline = wukongCMDBService.isServerOnline(ip);
            return AjaxResult.success("检查服务器在线状态成功", isOnline);
        } catch (Exception e) {
            logger.error("检查服务器在线状态失败，IP: {}", ip, e);
            return AjaxResult.error("检查服务器在线状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务器综合信息
     */
    @ApiOperation("获取服务器综合信息")
    @PreAuthorize("@ss.hasPermi('plugin:wukong:cmdb:query')")
    @GetMapping("/server/summary")
    public AjaxResult getServerSummary(
            @ApiParam(value = "服务器 IP 地址", required = true)
            @RequestParam String ip) {
        
        if (StringUtils.isEmpty(ip)) {
            return AjaxResult.error("IP 地址不能为空");
        }
        
        try {
            WukongServerSummary summary = wukongCMDBService.getServerSummary(ip);
            if (summary.hasError()) {
                return AjaxResult.error("获取服务器综合信息失败: " + summary.getErrorMessage());
            } else {
                return AjaxResult.success("获取服务器综合信息成功", summary);
            }
        } catch (Exception e) {
            logger.error("获取服务器综合信息失败，IP: {}", ip, e);
            return AjaxResult.error("获取服务器综合信息失败: " + e.getMessage());
        }
    }
} 