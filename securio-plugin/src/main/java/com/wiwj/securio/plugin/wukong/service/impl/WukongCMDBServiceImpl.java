package com.wiwj.securio.plugin.wukong.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.securio.plugin.wukong.api.WukongCMDBAPI;
import com.wiwj.securio.plugin.wukong.dto.WukongBaseResponse;
import com.wiwj.securio.plugin.wukong.dto.WukongServerSummary;
import com.wiwj.securio.plugin.wukong.service.WukongCMDBService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 悟空 CMDB 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class WukongCMDBServiceImpl implements WukongCMDBService {
    
    private static final Logger logger = LoggerFactory.getLogger(WukongCMDBServiceImpl.class);
    
    @Resource
    private WukongCMDBAPI wukongCMDBAPI;
    
    @Override
    public JSONObject getServerInfoByIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("获取服务器基础信息，IP: {}", ip);
        
        try {
            WukongBaseResponse<JSONObject> response = wukongCMDBAPI.getServerInfoByIp(ip);
            
            if (wukongCMDBAPI.isSuccess(response)) {
                logger.info("成功获取服务器基础信息，IP: {},data: {}", ip, response.getData());
                return response.getData();
            } else {
                logger.warn("获取服务器基础信息失败，IP: {}, 响应码: {}, 消息: {}", 
                        ip, response.getCode(), response.getMsg());
                return null;
            }
        } catch (Exception e) {
            logger.error("获取服务器基础信息异常，IP: {}", ip, e);
            throw new RuntimeException("获取服务器基础信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public JSONObject getHostProcessInfo(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("获取主机进程信息，IP: {}", ip);
        
        try {
            WukongBaseResponse<JSONObject> response = wukongCMDBAPI.getHostProcessInfo(ip);
            
            if (wukongCMDBAPI.isSuccess(response)) {
                logger.info("成功获取主机进程信息，IP: {},data: {}", ip, response.getData());
                return response.getData();
            } else {
                logger.warn("获取主机进程信息失败，IP: {}, 响应码: {}, 消息: {}", 
                        ip, response.getCode(), response.getMsg());
                return null;
            }
        } catch (Exception e) {
            logger.error("获取主机进程信息异常，IP: {}", ip, e);
            throw new RuntimeException("获取主机进程信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public JSONObject getHostDetailInfo(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("获取主机详细信息，IP: {}", ip);
        
        try {
            WukongBaseResponse<JSONObject> response = wukongCMDBAPI.getHostDetailInfo(ip);
            
            if (wukongCMDBAPI.isSuccess(response)) {
                logger.info("成功获取主机详细信息，IP: {},data: {}", ip, response.getData());
                return response.getData();
            } else {
                logger.warn("获取主机详细信息失败，IP: {}, 响应码: {}, 消息: {}", 
                        ip, response.getCode(), response.getMsg());
                return null;
            }
        } catch (Exception e) {
            logger.error("获取主机详细信息异常，IP: {}", ip, e);
            throw new RuntimeException("获取主机详细信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public JSONObject getServerRelationsByIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("获取服务器关联信息，IP: {}", ip);
        
        try {
            WukongBaseResponse<JSONObject> response = wukongCMDBAPI.getServerRelationsByIp(ip);
            
            if (wukongCMDBAPI.isSuccess(response)) {
                logger.info("成功获取服务器关联信息，IP: {}，data: {}", ip, response.getData());
                return response.getData();
            } else {
                logger.warn("获取服务器关联信息失败，IP: {}, 响应码: {}, 消息: {}", 
                        ip, response.getCode(), response.getMsg());
                return null;
            }
        } catch (Exception e) {
            logger.error("获取服务器关联信息异常，IP: {}", ip, e);
            throw new RuntimeException("获取服务器关联信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isServerOnline(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }
        
        try {
            JSONObject serverInfo = getServerInfoByIp(ip);
            return serverInfo != null && "在线".equals(serverInfo.getString("host_online_status"));
        } catch (Exception e) {
            logger.error("检查服务器在线状态异常，IP: {}", ip, e);
            return false;
        }
    }
    
    @Override
    public WukongServerSummary getServerSummary(String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw new IllegalArgumentException("IP 地址不能为空");
        }
        
        logger.info("获取服务器综合信息，IP: {}", ip);
        
        WukongServerSummary summary = new WukongServerSummary();
        
        try {
            // 获取服务器基础信息
            try {
                JSONObject serverInfo = getServerInfoByIp(ip);
                // 注意：这里需要修改WukongServerSummary类来接受JSONObject
                // 暂时注释掉，因为WukongServerSummary还依赖具体的DTO类
                // summary.setServerInfo(serverInfo);
            } catch (Exception e) {
                logger.warn("获取服务器基础信息失败，IP: {}", ip, e);
            }
            
            // 获取主机详细信息
            try {
                JSONObject hostDetailInfo = getHostDetailInfo(ip);
                // summary.setHostDetailInfo(hostDetailInfo);
            } catch (Exception e) {
                logger.warn("获取主机详细信息失败，IP: {}", ip, e);
            }
            
            // 获取进程信息
            try {
                JSONObject processInfo = getHostProcessInfo(ip);
                // summary.setProcessInfoList(processInfo);
            } catch (Exception e) {
                logger.warn("获取主机进程信息失败，IP: {}", ip, e);
            }
            
            // 获取服务器关联信息
            try {
                JSONObject serverRelations = getServerRelationsByIp(ip);
                // summary.setServerRelations(serverRelations);
            } catch (Exception e) {
                logger.warn("获取服务器关联信息失败，IP: {}", ip, e);
            }
            
            logger.info("成功获取服务器综合信息，IP: {}, 在线状态: {}, 进程数: {}, 中间件数: {}, 服务数: {}", 
                    ip, summary.isServerOnline(), summary.getProcessCount(), 
                    summary.getMiddlewareCount(), summary.getServiceCount());
            
            return summary;
            
        } catch (Exception e) {
            logger.error("获取服务器综合信息异常，IP: {}", ip, e);
            return new WukongServerSummary("获取服务器综合信息失败: " + e.getMessage());
        }
    }
} 