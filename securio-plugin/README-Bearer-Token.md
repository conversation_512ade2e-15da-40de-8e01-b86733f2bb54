# 悟空 CMDB API Bearer Token 授权功能

## 概述

本文档描述了悟空 CMDB API 客户端的 Bearer Token 授权功能的实现和使用方法。

## 功能特性

### 1. Bearer Token 授权
- 所有 API 请求都会自动添加 `Authorization: Bearer <token>` 请求头
- Token 值可通过配置文件进行外部化配置
- 默认 Token 值：`B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd`

### 2. 配置方式

#### 通过 application.yml 配置
```yaml
wukong:
  cmdb:
    base-url: http://wukong-test.5i5j.com/prod-api
    token: B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd
    timeout: 30000
```

#### 通过 @Value 注解注入
```java
@Value("${wukong.cmdb.token:B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd}")
private String token;
```

### 3. HTTP 请求头设置

在 `WukongCMDBAPI` 类的 `createHeaders()` 方法中自动设置以下请求头：

```java
private HttpHeaders createHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("User-Agent", "Securio-Plugin/1.0.0");
    headers.set("Accept", "application/json");
    headers.set("Authorization", "Bearer " + token);  // Bearer Token 授权
    return headers;
}
```

## 测试验证

### 单元测试
创建了 `WukongCMDBAPISimpleTest` 类来验证 Bearer Token 功能：

1. **testBearerTokenIsIncludedInRequest()** - 验证请求头中包含正确的 Bearer Token
2. **testGetTokenMethod()** - 验证 getToken() 方法返回正确的 token 值
3. **testUrlConstruction()** - 验证 URL 构造正确
4. **testHttpErrorHandling()** - 验证 HTTP 错误处理

### 运行测试
```bash
# 运行单元测试
mvn test -Dtest="WukongCMDBAPISimpleTest"

# 运行所有测试
mvn test
```

## API 接口

### 支持的接口
所有以下接口都会自动添加 Bearer Token 授权：

1. `/openapi/cmdb/getServerInfoByIp` - 获取服务器基础信息
2. `/openapi/cmdb/getHostProcessInfo` - 获取主机进程信息
3. `/openapi/cmdb/getHostDetailInfo` - 获取主机详细信息
4. `/openapi/cmdb/getServerRelationsByIp` - 获取服务器关联信息

### 使用示例
```java
@Autowired
private WukongCMDBAPI wukongCMDBAPI;

// 获取服务器信息（自动添加 Bearer Token）
WukongBaseResponse<ServerInfo> response = wukongCMDBAPI.getServerInfoByIp("***********");
```

## 安全考虑

1. **Token 保护**：在日志和 toString() 方法中，Token 值会被掩码显示为 `***`
2. **配置外部化**：Token 可通过环境变量或配置文件进行配置，避免硬编码
3. **HTTPS 传输**：建议在生产环境中使用 HTTPS 协议传输 Token

## 配置类支持

`WukongCMDBConfig` 配置类支持完整的外部化配置：

```java
@Configuration
@ConfigurationProperties(prefix = "wukong.cmdb")
public class WukongCMDBConfig {
    private String token = "B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd";
    // ... 其他配置
}
```

## 测试结果

单元测试全部通过，验证了以下功能：
- ✅ Bearer Token 正确添加到请求头
- ✅ Token 值正确配置和获取
- ✅ URL 构造正确
- ✅ HTTP 错误处理正常
- ✅ 参数验证正常

## 总结

Bearer Token 授权功能已成功集成到悟空 CMDB API 客户端中，提供了：
- 自动化的 Token 授权
- 灵活的配置管理
- 完善的测试覆盖
- 安全的 Token 处理

该功能确保了与悟空 CMDB API 的安全通信，满足了生产环境的安全要求。 