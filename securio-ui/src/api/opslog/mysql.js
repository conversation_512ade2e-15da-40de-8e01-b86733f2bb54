import request from '@/utils/request'

/**
 * 获取 MySQL 登录失败统计信息
 * @param {String} type 统计类型（user/host/agent）
 * @param {String} timeRange 时间范围
 * @param {Number} limit 返回数量限制
 * @param {String} additionalQuery 额外查询条件
 * @param {String} startTime 开始时间（自定义时间范围）
 * @param {String} endTime 结束时间（自定义时间范围）
 * @returns {Promise} 请求Promise
 */
export function getMysqlFailedLoginStats(type, timeRange, limit, additionalQuery, startTime, endTime) {
  const params = {
    type: type,
    limit: limit || 10,
    additionalQuery: additionalQuery
  };

  // 如果提供了自定义时间范围，使用自定义时间
  if (startTime && endTime) {
    params.startTime = startTime;
    params.endTime = endTime;
  } else {
    params.timeRange = timeRange || '10d';
  }

  return request({
    url: '/opslog/mysql/failed-login-stats',
    method: 'get',
    params: params
  })
}

/**
 * 获取 MySQL 日志时间序列统计数据
 * @param {String} type 统计类型（all/failed_login）
 * @param {String} dimension 统计维度（agent/client/user/cmd）
 * @param {String} timeRange 时间范围
 * @param {String} step 时间步长
 * @param {String} additionalQuery 额外查询条件
 * @param {String} startTime 开始时间（自定义时间范围）
 * @param {String} endTime 结束时间（自定义时间范围）
 * @returns {Promise} 请求Promise
 */
export function getMysqlTimeSeriesStats(type, dimension, timeRange, step, additionalQuery, startTime, endTime) {
  const params = {
    type: type || 'all',
    dimension: dimension,
    step: step || '1h',
    additionalQuery: additionalQuery
  };

  // 如果提供了自定义时间范围，使用自定义时间
  if (startTime && endTime) {
    params.startTime = startTime;
    params.endTime = endTime;
  } else {
    params.timeRange = timeRange || '14d';
  }

  return request({
    url: '/opslog/mysql/time-series-stats',
    method: 'get',
    params: params
  })
}

/**
 * 获取 MySQL 数据流向统计数据（桑基图数据）
 * @param {String} timeRange 时间范围
 * @param {Number} dbLimit 数据库数量限制
 * @param {Number} clientLimit 客户端数量限制
 * @param {String} additionalQuery 额外查询条件
 * @param {String} startTime 开始时间（自定义时间范围）
 * @param {String} endTime 结束时间（自定义时间范围）
 * @returns {Promise} 请求Promise
 */
export function getMysqlDataFlowStats(timeRange, dbLimit, clientLimit, additionalQuery, startTime, endTime) {
  const params = {
    dbLimit: dbLimit || 10,
    clientLimit: clientLimit || 10,
    additionalQuery: additionalQuery
  };

  // 如果提供了自定义时间范围，使用自定义时间
  if (startTime && endTime) {
    params.startTime = startTime;
    params.endTime = endTime;
  } else {
    params.timeRange = timeRange || '1d';
  }

  return request({
    url: '/opslog/mysql/data-flow-stats',
    method: 'get',
    params: params
  })
}
