import request from '@/utils/request'

/**
 * 获取 MySQL 登录失败统计信息
 * @param {String} type 统计类型（user/host/agent）
 * @param {String} timeRange 时间范围
 * @param {Number} limit 返回数量限制
 * @param {String} additionalQuery 额外查询条件
 * @returns {Promise} 请求Promise
 */
export function getMysqlFailedLoginStats(type, timeRange, limit, additionalQuery) {
  return request({
    url: '/opslog/mysql/failed-login-stats',
    method: 'get',
    params: {
      type: type,
      timeRange: timeRange || '10d',
      limit: limit || 10,
      additionalQuery: additionalQuery
    }
  })
}

/**
 * 获取 MySQL 日志时间序列统计数据
 * @param {String} type 统计类型（all/failed_login）
 * @param {String} dimension 统计维度（agent/client/user/cmd）
 * @param {String} timeRange 时间范围
 * @param {String} step 时间步长
 * @param {String} additionalQuery 额外查询条件
 * @returns {Promise} 请求Promise
 */
export function getMysqlTimeSeriesStats(type, dimension, timeRange, step, additionalQuery) {
  return request({
    url: '/opslog/mysql/time-series-stats',
    method: 'get',
    params: {
      type: type || 'all',
      dimension: dimension,
      timeRange: timeRange || '14d',
      step: step || '1h',
      additionalQuery: additionalQuery
    }
  })
}

/**
 * 获取 MySQL 数据流向统计数据（桑基图数据）
 * @param {String} timeRange 时间范围
 * @param {Number} dbLimit 数据库数量限制
 * @param {Number} clientLimit 客户端数量限制
 * @param {String} additionalQuery 额外查询条件
 * @returns {Promise} 请求Promise
 */
export function getMysqlDataFlowStats(timeRange, dbLimit, clientLimit, additionalQuery) {
  return request({
    url: '/opslog/mysql/data-flow-stats',
    method: 'get',
    params: {
      timeRange: timeRange || '1d',
      dbLimit: dbLimit || 10,
      clientLimit: clientLimit || 10,
      additionalQuery: additionalQuery
    }
  })
}
