import request from '@/utils/request'

// 查询日志
export function queryLogs(query, start, end, limit, timeout, extraFilters, extraStreamFilters, instance) {
  console.log("queryLogs");
  console.log(query, start, end, limit, timeout, extraFilters, extraStreamFilters, instance);
  
  // 构建form-data格式的参数，只传递后端支持的参数
  const params = new URLSearchParams();
  params.append('query', query);
  if (limit) params.append('limit', limit);
  if (timeout) params.append('timeout', timeout);
  if (instance) params.append('instance', instance);
  
  return request({
    url: '/opslog/victoria/query',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
}

// 实时跟踪日志
export function tailLogs(query, startOffset, offset, refreshInterval, extraFilters, extraStreamFilters) {
  const params = { query };
  if (startOffset) params.startOffset = startOffset;
  if (offset) params.offset = offset;
  if (refreshInterval) params.refreshInterval = refreshInterval;

  return request({
    url: '/opslog/victoria/tail',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志命中统计
export function queryHits(query, start, end, step, offset, fields, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;
  if (step) params.step = step;
  if (offset) params.offset = offset;
  if (fields && fields.length > 0) params.fields = fields;

  return request({
    url: '/opslog/victoria/hits',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志字段频率统计
export function queryFacets(query, start, end, limit, maxValuesPerField, maxValueLen, keepConstFields, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;
  if (limit) params.limit = limit;
  if (maxValuesPerField) params.maxValuesPerField = maxValuesPerField;
  if (maxValueLen) params.maxValueLen = maxValueLen;
  if (keepConstFields) params.keepConstFields = keepConstFields;

  return request({
    url: '/opslog/victoria/facets',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 获取字段统计信息
export function getFacets(queryOrRequestData, timeRange, fields, limit, instance) {
  // 支持两种调用方式：
  // 1. getFacets(query, timeRange, fields, limit, instance) - 参数形式
  // 2. getFacets(requestData) - 对象形式
  
  let requestData;
  
  if (typeof queryOrRequestData === 'object' && queryOrRequestData !== null) {
    // 对象形式调用
    requestData = queryOrRequestData;
  } else {
    // 参数形式调用
    requestData = {
      query: queryOrRequestData,
      timeRange,
      fields,
      limit,
      instance
    };
  }
  
  return request({
    url: '/opslog/victoria/facets',
    method: 'post',
    data: requestData
  });
}

// 查询日志统计
export function queryStats(query, time, extraFilters, extraStreamFilters) {
  const params = { query };
  if (time) params.time = time;

  return request({
    url: '/opslog/victoria/stats',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志范围统计
export function queryStatsRange(query, start, end, step, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;
  if (step) params.step = step;

  return request({
    url: '/opslog/victoria/stats_range',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志流ID
export function queryStreamIds(query, start, end, limit, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;
  if (limit) params.limit = limit;

  return request({
    url: '/opslog/victoria/stream_ids',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志流
export function queryStreams(query, start, end, limit, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;
  if (limit) params.limit = limit;

  return request({
    url: '/opslog/victoria/streams',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志流字段名
export function queryStreamFieldNames(query, start, end, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;

  return request({
    url: '/opslog/victoria/stream_field_names',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志流字段值
export function queryStreamFieldValues(query, start, end, field, limit, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;
  if (field) params.field = field;
  if (limit) params.limit = limit;

  return request({
    url: '/opslog/victoria/stream_field_values',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志字段名
export function queryFieldNames(query, start, end, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;

  return request({
    url: '/opslog/victoria/field_names',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 查询日志字段值
export function queryFieldValues(query, start, end, field, limit, extraFilters, extraStreamFilters) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;
  if (field) params.field = field;
  if (limit) params.limit = limit;

  return request({
    url: '/opslog/victoria/field_values',
    method: 'post',
    params: params,
    data: {
      ...extraFilters,
      ...extraStreamFilters
    }
  });
}

// 按字段统计
export function statByField(stream, field, timeRange, limit) {
  const params = {};
  params.stream = stream;
  params.field = field;
  if (timeRange) params.timeRange = timeRange;
  if (limit) params.limit = limit;

  return request({
    url: '/opslog/victoria/statByField',
    method: 'get',
    params: params
  });
}

// 获取时间序列统计数据
export function getTimeSeriesStats(query, start, end, step) {
  const params = { query };
  if (start) params.start = start;
  if (end) params.end = end;
  if (step) params.step = step;

  return request({
    url: '/opslog/victoria/stats_query_range',
    method: 'post',
    params: params,
    data: {} // 添加空的 data 对象，避免 @RequestBody 参数解析错误
  });
}

// 获取指定 stream 指定字段去重后的统计数量
export function countUniqueValues(stream, field, timeRange, instance) {
  const params = {};
  params.stream = stream;
  params.field = field;
  if (timeRange) params.timeRange = timeRange;
  if (instance) params.instance = instance;

  return request({
    url: '/opslog/victoria/count_unique',
    method: 'get',
    params: params
  });
}

// 获取上行流量排名
export function getUploadTrafficRanking(timeRange, limit) {
  const query = `_time:${timeRange} and stream:"AUDITLOG_H3C_BEHAVIOR" and log_type:"statistic_traffic" |stats by (user_name) sum(up) sum_up | sort by (sum_up desc)`;
  
  const params = new URLSearchParams();
  params.append('query', query);
  if (limit) params.append('limit', limit);
  
  return request({
    url: '/opslog/victoria/query',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
}

// 获取下行流量排名
export function getDownloadTrafficRanking(timeRange, limit) {
  const query = `_time:${timeRange} and stream:"AUDITLOG_H3C_BEHAVIOR" and log_type:"statistic_traffic" |stats by (user_name) sum(down) sum_down | sort by (sum_down desc)`;
  
  const params = new URLSearchParams();
  params.append('query', query);
  if (limit) params.append('limit', limit);
  
  return request({
    url: '/opslog/victoria/query',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
}
