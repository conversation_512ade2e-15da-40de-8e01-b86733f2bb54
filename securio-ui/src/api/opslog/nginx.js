import request from '@/utils/request'

/**
 * 获取 Nginx 访问统计信息
 * @param {String} type 统计类型（ip/url/status）
 * @param {String} timeRange 时间范围
 * @param {Number} limit 返回数量限制
 * @returns {Promise} 请求Promise
 */
export function getNginxAccessStats(type, timeRange, limit) {
  return request({
    url: '/opslog/nginx/access-stats',
    method: 'get',
    params: {
      type: type,
      timeRange: timeRange || '10d',
      limit: limit || 10
    }
  })
}

/**
 * 获取 Nginx 热门客户端 IP
 * @param {String} timeRange 时间范围
 * @param {Number} limit 返回数量限制
 * @returns {Promise} 请求Promise
 */
export function getTopClients(timeRange, limit) {
  return getNginxAccessStats('ip', timeRange, limit)
}

/**
 * 获取 Nginx 热门请求路径
 * @param {String} timeRange 时间范围
 * @param {Number} limit 返回数量限制
 * @returns {Promise} 请求Promise
 */
export function getTopUrls(timeRange, limit) {
  return getNginxAccessStats('url', timeRange, limit)
}

/**
 * 获取 Nginx 错误统计信息
 * @param {String} type 统计类型（ip/error）
 * @param {String} timeRange 时间范围
 * @param {Number} limit 返回数量限制
 * @returns {Promise} 请求Promise
 */
export function getNginxErrorStats(type, timeRange, limit) {
  return request({
    url: '/opslog/nginx/error-stats',
    method: 'get',
    params: {
      type: type,
      timeRange: timeRange || '10d',
      limit: limit || 10
    }
  })
}


