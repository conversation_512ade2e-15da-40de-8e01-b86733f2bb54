import request from '@/utils/request'

/**
 * 获取网宿WAF攻击日志统计指标
 * @param {string} timeRange 时间范围
 * @returns {Promise} 请求Promise
 */
export function getWangsuAttackStats(timeRange) {
  return request({
    url: '/opslog/wangsu-attack/stats',
    method: 'get',
    params: {
      timeRange: timeRange
    }
  })
}

/**
 * 获取网宿WAF攻击日志图表数据
 * @param {string} timeRange 时间范围
 * @param {number} limit 限制返回的记录数
 * @returns {Promise} 请求Promise
 */
export function getWangsuAttackCharts(timeRange, limit = 1000) {
  return request({
    url: '/opslog/wangsu-attack/charts',
    method: 'get',
    params: {
      timeRange: timeRange,
      limit: limit
    }
  })
}

/**
 * 获取攻击趋势时间序列数据
 * @param {string} start 开始时间
 * @param {string} end 结束时间
 * @param {string} step 时间步长
 * @param {string} metric 统计指标类型
 * @param {string} attackType 攻击类型过滤
 * @returns {Promise} 请求Promise
 */
export function getAttackTimeSeries(start, end, step = '1h', metric = 'count', attackType = '') {
  return request({
    url: '/opslog/wangsu-attack/timeseries',
    method: 'get',
    params: {
      start: start,
      end: end,
      step: step,
      metric: metric,
      attackType: attackType
    }
  })
}

/**
 * 获取Top攻击IP排名
 * @param {string} timeRange 时间范围
 * @param {number} limit 限制返回的记录数
 * @returns {Promise} 请求Promise
 */
export function getTopAttackIps(timeRange = '1h', limit = 20) {
  return request({
    url: '/opslog/wangsu-attack/top-ips',
    method: 'get',
    params: {
      timeRange: timeRange,
      limit: limit
    }
  })
} 