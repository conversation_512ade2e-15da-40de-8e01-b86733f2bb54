import request from '@/utils/request'

/**
 * 获取网宿WAF访问日志统计指标
 * @param {string} timeRange 时间范围
 * @returns {Promise} 请求Promise
 */
export function getWangsuStats(timeRange) {
  return request({
    url: '/opslog/wangsu/stats',
    method: 'get',
    params: {
      timeRange: timeRange
    }
  })
}

/**
 * 获取网宿WAF访问日志图表数据
 * @param {string} timeRange 时间范围
 * @param {number} limit 限制返回的记录数
 * @returns {Promise} 请求Promise
 */
export function getWangsuCharts(timeRange, limit = 1000) {
  return request({
    url: '/opslog/wangsu/charts',
    method: 'get',
    params: {
      timeRange: timeRange,
      limit: limit
    }
  })
}

/**
 * 获取网宿WAF访问日志时间序列统计数据
 * @param {string} channel 渠道名称
 * @param {string} start 开始时间
 * @param {string} end 结束时间
 * @param {string} step 时间步长
 * @param {string} metric 统计指标类型
 * @returns {Promise} 请求Promise
 */
export function getWangsuTimeSeries(channel, start, end, step = '1h', metric = 'count') {
  return request({
    url: '/opslog/wangsu/timeseries',
    method: 'get',
    params: {
      channel: channel,
      start: start,
      end: end,
      step: step,
      metric: metric
    }
  })
}

/**
 * 获取网宿WAF访问日志渠道排名统计数据
 * @param {string} timeRange 时间范围
 * @param {number} limit 限制返回的记录数
 * @returns {Promise} 请求Promise
 */
export function getWangsuChannelRanking(timeRange = '1d', limit = 50) {
  return request({
    url: '/opslog/wangsu/channel-ranking',
    method: 'get',
    params: {
      timeRange: timeRange,
      limit: limit
    }
  })
} 