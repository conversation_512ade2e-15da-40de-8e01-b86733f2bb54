import request from '@/utils/request'

/**
 * 获取 MySQL 连接桑基图数据
 * @param {String} rule 连接规则（mysql_connection_established/mysql_connection_closed/mysql_client_request）
 * @param {String} databaseIp 数据库IP地址
 * @param {String} timeRange 时间范围
 * @param {Number} limit 返回数量限制
 * @param {String} startTime 开始时间
 * @param {String} endTime 结束时间
 * @returns {Promise} 请求Promise
 */
export function getMysqlSankeyData(rule, databaseIp, timeRange, limit, startTime, endTime) {
  return request({
    url: '/opslog/mysql-monitor/sankey-data',
    method: 'get',
    params: {
      rule: rule || 'mysql_connection_established',
      databaseIp: databaseIp,
      timeRange: timeRange || '1d',
      limit: limit || 10,
      startTime: startTime,
      endTime: endTime
    }
  })
}

/**
 * 获取 MySQL 连接时间序列数据
 * @param {String} rule 连接规则
 * @param {String} databaseIp 数据库IP地址
 * @param {String} groupBy 分组字段（src_ip/agent_hostip）
 * @param {String} timeRange 时间范围
 * @param {String} step 时间步长
 * @param {String} startTime 开始时间
 * @param {String} endTime 结束时间
 * @returns {Promise} 请求Promise
 */
export function getMysqlTimeSeriesData(rule, databaseIp, groupBy, timeRange, step, startTime, endTime) {
  return request({
    url: '/opslog/mysql-monitor/time-series-data',
    method: 'get',
    params: {
      rule: rule || 'mysql_connection_established',
      databaseIp: databaseIp,
      groupBy: groupBy || 'src_ip',
      timeRange: timeRange || '10d',
      step: step || '1h',
      startTime: startTime,
      endTime: endTime
    }
  })
}

/**
 * 获取 MySQL 连接统计概览
 * @param {String} rule 连接规则
 * @param {String} databaseIp 数据库IP地址  
 * @param {String} timeRange 时间范围
 * @param {String} startTime 开始时间
 * @param {String} endTime 结束时间
 * @returns {Promise} 请求Promise
 */
export function getMysqlOverviewStats(rule, databaseIp, timeRange, startTime, endTime) {
  return request({
    url: '/opslog/mysql-monitor/overview-stats',
    method: 'get',
    params: {
      rule: rule || 'mysql_connection_established',
      databaseIp: databaseIp,
      timeRange: timeRange || '1d',
      startTime: startTime,
      endTime: endTime
    }
  })
}

/**
 * 获取 MySQL 流量时间序列数据（入站和出站）
 * @param {String} databaseIp 数据库IP地址
 * @param {String} timeRange 时间范围
 * @param {String} step 时间步长
 * @param {String} startTime 开始时间
 * @param {String} endTime 结束时间
 * @returns {Promise} 请求Promise
 */
export function getMysqlTrafficData(databaseIp, timeRange, step, startTime, endTime) {
  return request({
    url: '/opslog/mysql-monitor/traffic-data',
    method: 'get',
    params: {
      databaseIp: databaseIp,
      timeRange: timeRange || '1d',
      step: step || '1h',
      startTime: startTime,
      endTime: endTime
    }
  })
} 