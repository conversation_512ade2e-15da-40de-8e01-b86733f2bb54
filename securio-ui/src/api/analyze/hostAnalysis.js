import request from '@/utils/request'

// 执行主机综合分析
export function analyzeHost(data) {
  return request({
    url: '/app/host-analysis/analyze',
    method: 'post',
    data
  })
}

// 启动主机综合分析（异步）
export function startAnalyzeHost(data) {
  return request({
    url: '/app/host-analysis/analyze/start',
    method: 'post',
    data
  })
}

// 获取任务进度历史
export function getProgressHistory(taskId) {
  return request({
    url: `/app/host-analysis/analyze/history/${taskId}`,
    method: 'get'
  })
}

// 获取主机基本信息
export function getHostBasicInfo(hostIp) {
  return request({
    url: `/app/host-analysis/basic-info/${hostIp}`,
    method: 'get'
  })
}

// 获取主机资产信息
export function getHostAssetInfo(hostIp) {
  return request({
    url: '/app/host-analysis/asset-info/' + hostIp,
    method: 'get'
  })
}

// 获取主机事件信息
export function getHostEventInfo(hostIp, startTime, endTime) {
  return request({
    url: '/app/host-analysis/event-info/' + hostIp,
    method: 'get',
    params: {
      startTime: startTime,
      endTime: endTime
    }
  })
}

// 获取主机风险信息
export function getHostRiskInfo(hostIp, startTime, endTime) {
  return request({
    url: '/app/host-analysis/risk-info/' + hostIp,
    method: 'get',
    params: {
      startTime: startTime,
      endTime: endTime
    }
  })
}

// 获取支持的分析器列表
export function getSupportedAnalyzers(hostIp) {
  return request({
    url: `/app/host-analysis/analyzers/${hostIp}`,
    method: 'get'
  })
} 