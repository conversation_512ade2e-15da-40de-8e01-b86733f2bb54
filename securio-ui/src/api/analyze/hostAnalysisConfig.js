import request from '@/utils/request'

/**
 * 获取主机分析配置
 */
export function getHostAnalysisConfig() {
  return request({
    url: '/app/hostAnalysis/config',
    method: 'get'
  })
}

/**
 * 更新主机分析配置
 */
export function updateHostAnalysisConfig(data) {
  return request({
    url: '/app/hostAnalysis/config',
    method: 'put',
    data: data
  })
}

/**
 * 更新主机基本信息数据源配置
 */
export function updateBasicInfoSource(source) {
  return request({
    url: '/app/hostAnalysis/config/basicInfoSource',
    method: 'put',
    params: { source }
  })
}



/**
 * 更新超时时间配置
 */
export function updateTimeout(timeout) {
  return request({
    url: '/app/hostAnalysis/config/timeout',
    method: 'put',
    params: { timeout }
  })
}

/**
 * 获取可用的数据源列表
 */
export function getDataSources() {
  return request({
    url: '/app/hostAnalysis/config/dataSources',
    method: 'get'
  })
} 