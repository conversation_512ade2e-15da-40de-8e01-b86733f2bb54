import request from '@/utils/request'

// 查询告警输出配置列表
export function listOutputConfig(query) {
  return request({
    url: '/alert/output/list',
    method: 'get',
    params: query
  })
}

// 查询告警输出配置详细
export function getOutputConfig(id) {
  return request({
    url: '/alert/output/' + id,
    method: 'get'
  })
}

// 新增告警输出配置
export function addOutputConfig(data) {
  return request({
    url: '/alert/output',
    method: 'post',
    data: data
  })
}

// 修改告警输出配置
export function updateOutputConfig(data) {
  return request({
    url: '/alert/output',
    method: 'put',
    data: data
  })
}

// 删除告警输出配置
export function delOutputConfig(id) {
  return request({
    url: '/alert/output/' + id,
    method: 'delete'
  })
}

// 根据告警源ID查询告警输出配置
export function listOutputConfigBySourceId(sourceId) {
  return request({
    url: '/alert/output/source/' + sourceId,
    method: 'get'
  })
}

// 测试告警输出配置
export function testOutputConfig(id, testData) {
  return request({
    url: '/alert/output/test/' + id,
    method: 'post',
    data: testData
  })
}
