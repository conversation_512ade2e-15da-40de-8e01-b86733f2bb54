import request from '@/utils/request'

// 查询告警列表
export function listAlerts(query) {
  return request({
    url: '/alert/list',
    method: 'get',
    params: query
  })
}

// 获取告警统计数据
export function getAlertStats() {
  return request({
    url: '/alert/stats',
    method: 'get'
  })
}

// 获取告警趋势数据
export function getAlertTrends(params) {
  return request({
    url: '/alert/trends',
    method: 'get',
    params: params
  })
}

// 获取告警分布数据
export function getAlertDistribution(params) {
  return request({
    url: '/alert/distribution',
    method: 'get',
    params: params
  })
}

// 获取告警详情
export function getAlert(id) {
  return request({
    url: '/alert/' + id,
    method: 'get'
  })
}

// 更新告警状态
export function updateAlertStatus(id, data) {
  return request({
    url: '/alert/status/' + id,
    method: 'put',
    data: data
  })
}

// 处理告警
export function processAlert(id, data) {
  return request({
    url: '/alert/process/' + id,
    method: 'put',
    data: data
  })
}

// 解决告警
export function resolveAlert(id, data) {
  return request({
    url: '/alert/resolve/' + id,
    method: 'put',
    data: data
  })
}

// 关闭告警
export function closeAlert(id, data) {
  return request({
    url: '/alert/close/' + id,
    method: 'put',
    data: data
  })
}

// 忽略告警
export function ignoreAlert(id, data) {
  return request({
    url: '/alert/ignore/' + id,
    method: 'put',
    data: data
  })
}

// 批量处理告警
export function batchProcessAlerts(data) {
  return request({
    url: '/alert/batch/process',
    method: 'put',
    data: data
  })
}
