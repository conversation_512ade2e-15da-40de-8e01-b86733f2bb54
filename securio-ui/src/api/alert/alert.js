import request from '@/utils/request'

// 查询告警列表
export function listAlert(query) {
  return request({
    url: '/alert/list',
    method: 'get',
    params: query
  })
}

// 查询告警详细
export function getAlert(id) {
  return request({
    url: '/alert/' + id,
    method: 'get'
  })
}

// 处理告警（完整版本，支持根因分析和FlashDuty）
export function processAlert(id, data) {
  return request({
    url: '/alert/resolve/' + id,
    method: 'put',
    data
  })
}

// 忽略告警（完整版本）
export function ignoreAlertComplete(id, data) {
  return request({
    url: '/alert/ignore/' + id,
    method: 'put',
    data
  })
}

// 批量处理告警
export function batchProcessAlerts(data) {
  return request({
    url: '/alert/batch-resolve',
    method: 'put',
    data
  })
}

// 删除告警
export function deleteAlert(id) {
  return request({
    url: '/alert/' + id,
    method: 'delete'
  })
}

// 导出告警
export function exportAlert(query) {
  return request({
    url: '/alert/export',
    method: 'get',
    params: query
  })
}

// 导出FlashDuty告警数据
export function exportFlashDutyAlerts(params) {
  return request({
    url: '/alert/export-flashduty',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}

// 导出Top20告警统计数据
export function exportTop20Alerts(params) {
  return request({
    url: '/alert/export-top20',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}

// 获取可用标签
export function getAvailableTags() {
  return request({
    url: '/alert/available-tags',
    method: 'get'
  })
}

// 生成测试数据
export function generateTestData(count = 10) {
  return request({
    url: '/alert/generate-test-data',
    method: 'post',
    params: { count }
  })
}

// 重新打开告警
export function reopenAlert(id) {
  return request({
    url: '/alert/reopen/' + id,
    method: 'put'
  })
}

// 获取30天内新增事件统计
export function getDailyStatistics(days = 30) {
  return request({
    url: '/alert/daily-statistics',
    method: 'get',
    params: { days }
  })
}

// 获取最近入侵事件列表
export function getRecentIntrusionEvents(limit = 10) {
  return request({
    url: '/alert/recent-intrusion-events',
    method: 'get',
    params: { limit }
  })
}

// 认领告警（批量处理）
export function claimAlert(alertIds) {
  const params = {
    alertIds: Array.isArray(alertIds) ? alertIds.join(',') : alertIds
  };
  return request({
    url: '/alert/acknowledge',
    method: 'put',
    params: params
  })
}

// 获取用户列表（用于下拉选择）
export function getUserList() {
  return request({
    url: '/alert/statistics/users',
    method: 'get'
  })
} 