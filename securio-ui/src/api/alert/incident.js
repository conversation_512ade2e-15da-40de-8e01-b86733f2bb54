import request from '@/utils/request'

// 查询故障列表
export function queryIncidentList(data) {
  return request({
    url: '/alert/incidents/list',
    method: 'post',
    data: data
  })
}

// 查询故障时间线
export function queryIncidentFeed(data) {
  return request({
    url: '/alert/incident-feed/query',
    method: 'post',
    data: data
  })
}

// 根据告警ID查询故障时间线（GET方式）
export function queryIncidentFeedByGet(incidentId, params) {
  return request({
    url: `/alert/incident-feed/query/${incidentId}`,
    method: 'get',
    params: params
  })
}

// 获取支持的操作类型列表
export function getActionTypes() {
  return request({
    url: '/alert/incident-feed/action-types',
    method: 'get'
  })
}

// 根据人员ID获取人员姓名（批量）
export function getPersonNames(personIds) {
  return request({
    url: '/alert/incident-feed/person-names',
    method: 'post',
    data: { personIds: personIds }
  })
}

// 删除故障
export function removeIncidents(data) {
  return request({
    url: '/alert/incident/remove',
    method: 'post',
    data: data
  })
}

// 批量删除故障（通过URL参数）
export function removeIncidentsByIds(incidentIds) {
  return request({
    url: `/alert/incident/remove/${incidentIds.join(',')}`,
    method: 'delete'
  })
} 