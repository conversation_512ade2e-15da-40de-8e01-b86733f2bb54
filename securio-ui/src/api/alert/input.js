import request from '@/utils/request'

// 查询告警输入配置列表
export function listInputConfig(query) {
  return request({
    url: '/alert/input/list',
    method: 'get',
    params: query
  })
}

// 查询告警输入配置详细
export function getInputConfig(id) {
  return request({
    url: '/alert/input/' + id,
    method: 'get'
  })
}

// 新增告警输入配置
export function addInputConfig(data) {
  return request({
    url: '/alert/input',
    method: 'post',
    data: data
  })
}

// 修改告警输入配置
export function updateInputConfig(data) {
  return request({
    url: '/alert/input',
    method: 'put',
    data: data
  })
}

// 删除告警输入配置
export function delInputConfig(id) {
  return request({
    url: '/alert/input/' + id,
    method: 'delete'
  })
}

// 生成推送URL
export function generateUrl() {
  return request({
    url: '/alert/input/url/generate',
    method: 'get'
  })
}

// 测试告警输入配置
export function testInputConfig(id) {
  return request({
    url: '/alert/input/test/' + id,
    method: 'get'
  })
}
