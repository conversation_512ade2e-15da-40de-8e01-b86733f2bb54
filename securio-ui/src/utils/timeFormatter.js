/**
 * 时间格式转换工具
 * 将中文时间格式转换为VictoriaLogs支持的英文格式
 */

/**
 * 转换时间格式为VictoriaLogs支持的格式
 * @param {string} timeRange - 原始时间范围字符串
 * @param {string} defaultValue - 默认值，当转换失败时使用
 * @returns {string} - 转换后的时间格式
 */
export function convertTimeRange(timeRange, defaultValue = '30m') {
  if (!timeRange || typeof timeRange !== 'string') {
    return defaultValue
  }

  // 处理中文时间格式，转换为VictoriaLogs支持的格式
  if (timeRange.includes('最近')) {
    const timeStr = timeRange.replace('最近 ', '').replace('最近', '')
    // 将中文时间单位转换为英文
    const converted = timeStr.replace('分钟', 'm')
                             .replace('小时', 'h')
                             .replace('天', 'd')
                             .replace('周', 'w')
                             .replace('月', 'M')
                             .replace('年', 'y')
    
    // 验证转换结果是否为有效格式
    if (/^\d+[mhdwMy]$/.test(converted)) {
      return converted
    }
  }
  
  // 如果已经是英文格式，验证并返回
  if (/^\d+[mhdwMy]$/.test(timeRange)) {
    return timeRange
  }
  
  // 处理特殊情况：纯数字+单位的格式（如："30分钟" -> "30m"）
  const timeMatch = timeRange.match(/(\d+)(分钟|小时|天|周|月|年)/)
  if (timeMatch) {
    const number = timeMatch[1]
    const unit = timeMatch[2]
    const unitMap = {
      '分钟': 'm',
      '小时': 'h',
      '天': 'd',
      '周': 'w',
      '月': 'M',
      '年': 'y'
    }
    return number + unitMap[unit]
  }
  
  // 如果无法转换，返回默认值
  console.warn(`无法转换时间格式: ${timeRange}，使用默认值: ${defaultValue}`)
  return defaultValue
}

/**
 * 验证时间格式是否有效
 * @param {string} timeRange - 时间范围字符串
 * @returns {boolean} - 是否为有效格式
 */
export function isValidTimeRange(timeRange) {
  return /^\d+[mhdwMy]$/.test(timeRange)
}

/**
 * 获取时间单位的中文描述
 * @param {string} timeRange - 时间范围字符串（如"30m"）
 * @returns {string} - 中文描述（如"30分钟"）
 */
export function getTimeRangeDescription(timeRange) {
  const match = timeRange.match(/^(\d+)([mhdwMy])$/)
  if (!match) {
    return timeRange
  }
  
  const number = match[1]
  const unit = match[2]
  const unitMap = {
    'm': '分钟',
    'h': '小时',
    'd': '天',
    'w': '周',
    'M': '月',
    'y': '年'
  }
  
  return number + unitMap[unit]
} 