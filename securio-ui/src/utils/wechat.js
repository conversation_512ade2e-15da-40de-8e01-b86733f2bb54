// 微信环境检测工具

/**
 * 检测是否在微信浏览器中
 */
export function isWeChatBrowser() {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

/**
 * 检测是否在微信小程序中
 */
export function isWeChatMiniProgram() {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('miniprogram')
}

/**
 * 检测是否在企业微信中
 */
export function isWeComBrowser() {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('wxwork')
}

/**
 * 获取微信环境类型
 */
export function getWeChatEnvironment() {
  if (isWeComBrowser()) {
    return 'wecom'
  } else if (isWeChatMiniProgram()) {
    return 'miniprogram'
  } else if (isWeChatBrowser()) {
    return 'wechat'
  }
  return 'other'
}

/**
 * 生成企业微信OAuth2授权URL
 */
export function generateWeComOAuth2Url(redirectPage) {
  const appId = process.env.VUE_APP_WECOM_APPID || 'ww5929e15785bee75a'
  const baseRedirectUri = process.env.VUE_APP_WECOM_REDIRECT_URI || 'https://wukong-openapi.5i5j.com/openapi/webcom/callback'
  const redirectUri = encodeURIComponent(baseRedirectUri + '?redirect_page=' + encodeURIComponent(redirectPage))
  const state = 'wecom_oauth2'
  
  return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=${state}#wechat_redirect`
}

/**
 * 检查是否需要微信OAuth2登录
 */
export function shouldUseWeComOAuth2() {
  const environment = getWeChatEnvironment()
  return environment === 'wecom' || environment === 'wechat'
} 