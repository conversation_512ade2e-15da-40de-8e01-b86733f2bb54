/**
 * 告警相关枚举常量
 * 与后端枚举类保持一致
 */

// 告警状态枚举
export const AlertStatusEnum = {
  TRIGGERED: { code: 'triggered', name: '触发', type: 'warning' },
  ACKNOWLEDGED: { code: 'acknowledged', name: '确认', type: 'primary' },
  RESOLVED: { code: 'resolved', name: '已解决', type: 'success' },
  SUPPRESSED: { code: 'suppressed', name: '沉默', type: 'info' }
}

// 告警严重程度枚举
export const AlertSeverityEnum = {
  CRITICAL: { code: 'critical', name: '严重', level: 1, type: 'danger', color: '#f56c6c' },
  HIGH: { code: 'high', name: '高危', level: 2, type: 'warning', color: '#e6a23c' },
  MEDIUM: { code: 'medium', name: '中危', level: 3, type: '', color: '#409eff' },
  LOW: { code: 'low', name: '低危', level: 4, type: 'success', color: '#67c23a' },
  INFO: { code: 'info', name: '信息', level: 5, type: 'info', color: '#909399' }
}

// 告警类型枚举
export const AlertTypeEnum = {
  SYSTEM_PERFORMANCE: { code: 'system_performance', name: '系统性能', type: 'warning' },
  NETWORK_MONITORING: { code: 'network_monitoring', name: '网络监控', type: 'warning' },
  INFRASTRUCTURE: { code: 'infrastructure', name: '基础设施', type: 'warning' },
  APPLICATION_PERFORMANCE: { code: 'application_performance', name: '应用性能', type: 'warning' },
  MICROSERVICE: { code: 'microservice', name: '微服务', type: 'primary' },
  API_MONITORING: { code: 'api_monitoring', name: 'API监控', type: 'primary' },
  BUSINESS: { code: 'business', name: '业务监控', type: 'success' },
  DATABASE_PERFORMANCE: { code: 'database_performance', name: '数据库性能', type: 'warning' },
  DATABASE_CONNECTION: { code: 'database_connection', name: '数据库连接', type: 'warning' },
  WEB_SECURITY: { code: 'web_security', name: 'Web安全', type: 'danger' },
  NETWORK_SECURITY: { code: 'network_security', name: '网络安全', type: 'danger' },
  HOST_SECURITY: { code: 'host_security', name: '主机安全', type: 'danger' },
  SECURITY: { code: 'security', name: '安全事件', type: 'danger' },
  KUBERNETES: { code: 'kubernetes', name: 'Kubernetes', type: 'primary' },
  CONTAINER_MONITORING: { code: 'container_monitoring', name: '容器监控', type: 'primary' },
  ERROR_LOG: { code: 'error_log', name: '错误日志', type: '' },
  EXCEPTION_MONITORING: { code: 'exception_monitoring', name: '异常监控', type: '' },
  BUILD: { code: 'build', name: '构建失败', type: 'success' },
  DEPLOYMENT: { code: 'deployment', name: '部署监控', type: 'success' },
  GENERIC_SYSTEM: { code: 'generic_system', name: '通用系统', type: '' },
  TEST_ALERT: { code: 'test_alert', name: '测试告警', type: 'info' },
  CUSTOM: { code: 'custom', name: '自定义', type: '' },
  UNKNOWN: { code: 'unknown', name: '未知类型', type: '' }
}

// 告警解决类型枚举
export const AlertResolutionTypeEnum = {
  AUTO_RESOLVED: { code: 'auto_resolved', name: '系统自动恢复' },
  MANUAL_RESOLVED: { code: 'manual_resolved', name: '人工解决' },
  SERVICE_RESTART: { code: 'service_restart', name: '服务重启' },
  CONFIG_CHANGE: { code: 'config_change', name: '配置调整' },
  NETWORK_FIXED: { code: 'network_fixed', name: '网络修复' },
  RESOURCE_ADJUSTED: { code: 'resource_adjusted', name: '资源调整' },
  FALSE_POSITIVE: { code: 'false_positive', name: '误报' }
}

// 告警源类型枚举
export const AlertSourceTypeEnum = {
  STANDARD: { code: 'standard', name: '标准告警事件' },
  GRAFANA: { code: 'grafana', name: 'Grafana' },
  ZABBIX: { code: 'zabbix', name: 'Zabbix' },
  N9E: { code: 'n9e', name: '夜莺监控' },
  HUAWEI_CES: { code: 'huawei_ces', name: '华为云CES' },
  SKYWALKING: { code: 'skywalking', name: 'SkyWalking' },
  WANGSU_WAF: { code: 'wangsu_waf', name: '网宿WAF' },
  SAFELINE_WAF: { code: 'safeline_waf', name: '雷池WAF' },
  HONEYPOT: { code: 'honeypot', name: '蜜罐' },
  MUYUN: { code: 'muyun', name: '牧云' },
  TDP: { code: 'tdp', name: '微步TDP' },
  ZEEK: { code: 'zeek', name: 'Zeek' },
  SURICATA: { code: 'suricata', name: 'Suricata' }
}

// 环境类型枚举
export const EnvironmentEnum = {
  PRODUCTION: { code: 'production', name: '生产环境', type: 'danger' },
  STAGING: { code: 'staging', name: '预发环境', type: 'warning' },
  TEST: { code: 'test', name: '测试环境', type: 'primary' },
  DEVELOPMENT: { code: 'development', name: '开发环境', type: 'success' }
}

/**
 * 根据代码获取枚举项
 */
export function getEnumByCode(enumObj, code) {
  for (const key in enumObj) {
    if (enumObj[key].code === code) {
      return enumObj[key]
    }
  }
  return null
}

/**
 * 获取枚举的所有选项（用于下拉框）
 */
export function getEnumOptions(enumObj) {
  return Object.values(enumObj).map(item => ({
    label: item.name,
    value: item.code
  }))
}

/**
 * 获取状态类型
 */
export function getStatusType(status) {
  const statusEnum = getEnumByCode(AlertStatusEnum, status)
  return statusEnum ? statusEnum.type : 'info'
}

/**
 * 获取状态文本
 */
export function getStatusText(status) {
  const statusEnum = getEnumByCode(AlertStatusEnum, status)
  return statusEnum ? statusEnum.name : status
}

/**
 * 获取严重程度类型
 */
export function getSeverityType(severity) {
  const severityEnum = getEnumByCode(AlertSeverityEnum, severity)
  return severityEnum ? severityEnum.type : 'info'
}

/**
 * 获取严重程度文本
 */
export function getSeverityText(severity) {
  const severityEnum = getEnumByCode(AlertSeverityEnum, severity)
  return severityEnum ? severityEnum.name : severity
}

/**
 * 获取告警类型类型
 */
export function getAlertTypeType(alertType) {
  const typeEnum = getEnumByCode(AlertTypeEnum, alertType)
  return typeEnum ? typeEnum.type : ''
}

/**
 * 获取告警类型文本
 */
export function getAlertTypeText(alertType) {
  const typeEnum = getEnumByCode(AlertTypeEnum, alertType)
  return typeEnum ? typeEnum.name : alertType
}

/**
 * 获取环境类型
 */
export function getEnvironmentType(environment) {
  const envEnum = getEnumByCode(EnvironmentEnum, environment)
  return envEnum ? envEnum.type : 'primary'
}

/**
 * 获取环境文本
 */
export function getEnvironmentText(environment) {
  const envEnum = getEnumByCode(EnvironmentEnum, environment)
  return envEnum ? envEnum.name : environment
}

/**
 * 获取告警源类型文本
 */
export function getSourceTypeText(sourceType) {
  const sourceEnum = getEnumByCode(AlertSourceTypeEnum, sourceType)
  return sourceEnum ? sourceEnum.name : sourceType
}
