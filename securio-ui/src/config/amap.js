/**
 * 高德地图API配置
 * 
 * 使用说明：
 * 1. 访问 https://console.amap.com/dev/key/app 注册高德开发者账号
 * 2. 创建应用，选择Web端(JS API)平台
 * 3. 获取Web服务API Key
 * 4. 创建安全密钥
 * 5. 将下面的配置替换为你的实际密钥
 */

export const AMAP_CONFIG = {
  // Web服务API Key（必填）
  key: process.env.VUE_APP_AMAP_KEY || '请在这里配置你的WebKey',
  
  // 安全密钥（必填）
  securityJsCode: process.env.VUE_APP_AMAP_SECURITY_CODE || '请在这里配置你的安全密钥',
  
  // API版本
  version: '2.0',
  
  // 默认插件列表
  plugins: [
    'AMap.Scale',      // 比例尺
    'AMap.ToolBar',    // 工具栏
    'AMap.InfoWindow'  // 信息窗体
  ],
  
  // 默认地图配置
  defaultMapOptions: {
    zoom: 10,
    mapStyle: 'normal',
    showIndoorMap: false,
    features: ['bg', 'road', 'building', 'point']
  }
}

/**
 * 验证配置是否有效
 */
export function validateAmapConfig() {
  const isValidKey = AMAP_CONFIG.key && !AMAP_CONFIG.key.includes('请在这里配置')
  const isValidSecurityCode = AMAP_CONFIG.securityJsCode && !AMAP_CONFIG.securityJsCode.includes('请在这里配置')
  
  return {
    isValid: isValidKey && isValidSecurityCode,
    errors: [
      !isValidKey ? '请配置有效的高德地图Web服务API Key' : null,
      !isValidSecurityCode ? '请配置有效的高德地图安全密钥' : null
    ].filter(Boolean)
  }
} 