<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="告警源名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入告警源名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="告警源类型" prop="sourceType">
        <el-select v-model="queryParams.sourceType" placeholder="请选择告警源类型" clearable size="small">
          <el-option v-for="dict in dict.type.alert_source_type" :key="dict.key" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格区 -->
    <el-table v-loading="loading" :data="inputConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="配置ID" align="center" prop="id" />
      <el-table-column label="告警源名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="告警源类型" align="center" prop="sourceType">
        <template slot-scope="scope">
        <dict-tag :options="dict.type.alert_source_type" :value="scope.row.sourceType"/>
        </template>
      </el-table-column>
      <el-table-column label="数据获取方式" align="center" prop="collectionType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.alert_collect_type" :value="scope.row.collectionType"/>
        </template>

      </el-table-column>
      <el-table-column label="告警源的域名" align="center" prop="domain" :show-overflow-tooltip="true" />

      <el-table-column label="推送URL" align="center" prop="pushUrl" >
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="viewWebhookUrl(scope.row)" v-if="scope.row.pushUrl">查看</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="enabled" inactive-value="disabled" @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-s-operation" @click="goToOutput(scope.row)">输出配置</el-button>
          <el-button size="mini" type="text" icon="el-icon-video-play" @click="handleTest(scope.row)">测试</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改告警输入配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 表单内容 -->
        <el-form-item label="告警源名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入告警源名称" />
        </el-form-item>
        <el-form-item label="告警源类型" prop="sourceType">
          <el-select v-model="form.sourceType" placeholder="请选择告警源类型" @change="handleSourceTypeChange" style="width: 100%">
            <el-option v-for="dict in sourceTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据获取方式" prop="collectionType">
          <el-select v-model="form.collectionType" placeholder="请选择数据获取方式" @change="handleCollectionTypeChange" style="width: 100%">
            <el-option v-for="dict in dict.type.alert_collect_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="推送URL" prop="pushUrl" v-if="form.collectionType === 'push'">
          <el-input v-model="form.pushUrl" placeholder="点击生成按钮获取推送URL" readonly>
            <el-button slot="append" type="primary" @click="generatePushUrl">生成</el-button>
            <el-button slot="append" type="success" @click="copyPushUrl" v-if="form.pushUrl">复制</el-button>
          </el-input>
          <span class="help-block">该URL用于接收外部系统的Webhook请求，生成后不可修改</span>
        </el-form-item>
        <el-form-item label="告警源域名" prop="domain">
          <el-input v-model="form.domain" placeholder="请输入告警源域名" />
          <span class="help-block">告警源的域名，例如：example.com</span>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="enabled">启用</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 查看抽屉 -->
    <el-drawer
      :title="'告警输入配置详情'"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="50%"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="配置ID">{{ form.id }}</el-descriptions-item>
        <el-descriptions-item label="告警源名称">{{ form.name }}</el-descriptions-item>
        <el-descriptions-item label="告警源类型">
          <dict-tag :options="dict.type.alert_source_type" :value="form.sourceType" />
        </el-descriptions-item>
        <el-descriptions-item label="数据获取方式">
          <dict-tag :options="dict.type.alert_collect_type" :value="form.collectionType" />
        </el-descriptions-item>
        <el-descriptions-item label="告警源的域名">{{ form.domain }}</el-descriptions-item>
        <el-descriptions-item label="推送URL" v-if="form.pushUrl">
          <el-button type="text" @click="copyPushUrl">
            {{ form.pushUrl }} <i class="el-icon-document-copy"></i>
          </el-button>
        </el-descriptions-item>
        <el-descriptions-item label="日志类型" v-if="form.logType">
          <dict-tag :options="logTypeOptions" :value="form.logType" />
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="form.status === 'enabled' ? 'success' : 'info'">
            {{ form.status === 'enabled' ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(form.createAt) }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ form.createName }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(form.updateAt) }}</el-descriptions-item>
        <el-descriptions-item label="更新人">{{ form.updateName }}</el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script>
import { listInputConfig, getInputConfig, delInputConfig, addInputConfig, updateInputConfig, generateUrl, testInputConfig } from "@/api/alert/input";
export default {
  name: "AlertInputConfig",
  dicts: ['alert_source_type','alert_collect_type'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 告警输入配置表格数据
      inputConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前步骤
      active: 0,
      // 是否显示抽屉
      drawerVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        sourceType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "告警源名称不能为空", trigger: "blur" }
        ],
        sourceType: [
          { required: true, message: "告警源类型不能为空", trigger: "change" }
        ],
        collectionType: [
          { required: true, message: "数据获取方式不能为空", trigger: "change" }
        ],
        logType: [
          { required: true, message: "日志类型不能为空", trigger: "change" }
        ],
        pushUrl: [
          { required: true, message: "推送URL不能为空", trigger: "change" }
        ],
        domain: [
          { required: true, message: "告警源的域名不能为空", trigger: "blur" }
        ]
      },
      // 告警源类型选项
      sourceTypeOptions: [],
      // 状态选项
      statusOptions: [
        { value: "enabled", label: "启用" },
        { value: "disabled", label: "停用" }
      ],
      // 数据获取方式选项
      collectionTypeOptions: [
        { value: "push", label: "推送方式" },
        { value: "pull", label: "拉取方式" }
      ],
      // 日志类型选项
      logTypeOptions: [
        { value: "honeypot_access", label: "蜜罐访问日志" },
        { value: "honeypot_attack", label: "蜜罐攻击日志" },
        { value: "muyun_security", label: "牧云安全日志" },
        { value: "muyun_performance", label: "牧云性能日志" },
        { value: "grfna_alert", label: "GRFNA告警日志" },
        { value: "grfna_event", label: "GRFNA事件日志" }
      ],
      // 适配器类型选项
      adapterTypeOptions: [
        { value: "waf", label: "WAF适配器" },
        { value: "honeypot", label: "蜜罐适配器" },
        { value: "muyun", label: "牧云适配器" },
        { value: "grfna", label: "GRFNA适配器" },
        { value: "aliyun", label: "阿里云适配器" },
        { value: "custom", label: "自定义适配器" }
      ]
    };
  },
  computed: {
    // 是否显示推送URL列
    showWebhookColumn() {
      return true; // 始终显示，也可以根据条件决定是否显示
    }
  },
  created() {
    this.getList();
    this.getDicts("alert_source_type").then(response => {
      this.sourceTypeOptions = response.data.map(item => {
        return { value: item.dictValue, label: item.dictLabel };
      });
    });
  },
  methods: {
    /** 查询告警输入配置列表 */
    getList() {
      this.loading = true;
      listInputConfig(this.queryParams).then(response => {
        this.inputConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        this.$message.error('获取数据失败，请检查后端服务是否正常运行');
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        sourceType: null,
        collectionType: null,
        logType: null,
        pushUrl: null,
        domain: null,
        adapterType: null,
        status: "enabled"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加告警输入配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInputConfig(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改告警输入配置";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.form = JSON.parse(JSON.stringify(row));
      this.drawerVisible = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInputConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInputConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除告警输入配置编号为"' + ids + '"的数据项？').then(function() {
        return delInputConfig(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 生成推送URL */
    generatePushUrl() {
      generateUrl().then(response => {
        this.form.pushUrl = response.data.url;
        this.$message.success('生成推送URL成功');
      }).catch(error => {
        console.error('生成推送URL失败:', error);
        this.$message.error('生成推送URL失败，请重试');
      });
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "enabled" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.name + '"吗？').then(function() {
        return updateInputConfig(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "enabled" ? "disabled" : "enabled";
      });
    },
    /** 告警源类型变更 */
    handleSourceTypeChange(value) {
      // 根据告警源类型设置数据获取方式
      // const sourceType = this.sourceTypeOptions.find(item => item.value === value);
      // if (sourceType) {
      //   if (value === "honeypot" || value === "muyun" || value === "grfna") {
      //     this.form.collectionType = "log";
      //   } else {
      //     this.form.collectionType = "webhook";
      //   }
      // }
    },
    /** 数据获取方式变更 */
    handleCollectionTypeChange(value) {
      // 清空相关字段
      if (value === "log") {
        this.form.webhookUrl = null;
        this.form.webhookToken = null;
      } else {
        this.form.logType = null;
      }
    },
    /** 复制推送URL */
    copyPushUrl() {
      const input = document.createElement('input');
      input.setAttribute('readonly', 'readonly');
      input.setAttribute('value', this.form.pushUrl);
      document.body.appendChild(input);
      input.select();
      if (document.execCommand('copy')) {
        document.execCommand('copy');
        this.$message.success('复制成功');
      }
      document.body.removeChild(input);
    },

    /** 查看推送URL */
    viewWebhookUrl(row) {
      if (row.pushUrl) {
        this.$alert(row.pushUrl, '推送URL', {
          confirmButtonText: '复制',
          callback: action => {
            if (action === 'confirm') {
              const input = document.createElement('input');
              input.setAttribute('readonly', 'readonly');
              input.setAttribute('value', row.pushUrl);
              document.body.appendChild(input);
              input.select();
              if (document.execCommand('copy')) {
                document.execCommand('copy');
                this.$message.success('复制成功');
              }
              document.body.removeChild(input);
            }
          }
        });
      }
    },
    /** 转到输出配置页面 */
    goToOutput(row) {
      this.$router.push({ path: "/alert/output", query: { sourceId: row.id, sourceName: row.name } });
    },

    /** 测试按钮操作 */
    handleTest(row) {
      this.$modal.confirm('是否确认测试告警输入配置"' + row.name + '"？').then(function() {
        return testInputConfig(row.id);
      }).then(() => {
        this.$modal.msgSuccess("测试成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}
.help-block {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 5px;
  display: block;
}
</style>
