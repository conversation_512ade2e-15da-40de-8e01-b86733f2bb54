<template>
  <div class="app-container">
    <!-- 统计概览卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card total">
          <div class="card-content">
            <div class="card-icon">
              <i class="el-icon-warning-outline"></i>
            </div>
            <div class="card-info">
              <div class="card-value">{{ statistics.total || 0 }}</div>
              <div class="card-label">总告警数</div>
              <div class="card-trend" :class="statistics.totalTrend > 0 ? 'trend-up' : 'trend-down'">
                <i :class="statistics.totalTrend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                {{ Math.abs(statistics.totalTrend || 0) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card critical">
          <div class="card-content">
            <div class="card-icon">
              <i class="el-icon-close"></i>
            </div>
            <div class="card-info">
              <div class="card-value">{{ statistics.critical || 0 }}</div>
              <div class="card-label">严重告警</div>
              <div class="card-trend" :class="statistics.criticalTrend > 0 ? 'trend-up' : 'trend-down'">
                <i :class="statistics.criticalTrend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                {{ Math.abs(statistics.criticalTrend || 0) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card pending">
          <div class="card-content">
            <div class="card-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="card-info">
              <div class="card-value">{{ statistics.pending || 0 }}</div>
              <div class="card-label">待处理</div>
              <div class="card-trend" :class="statistics.pendingTrend > 0 ? 'trend-up' : 'trend-down'">
                <i :class="statistics.pendingTrend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                {{ Math.abs(statistics.pendingTrend || 0) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card resolved">
          <div class="card-content">
            <div class="card-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="card-info">
              <div class="card-value">{{ statistics.resolved || 0 }}</div>
              <div class="card-label">已解决</div>
              <div class="card-trend" :class="statistics.resolvedTrend > 0 ? 'trend-up' : 'trend-down'">
                <i :class="statistics.resolvedTrend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                {{ Math.abs(statistics.resolvedTrend || 0) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第一行图表：趋势图 + 严重程度分布 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="16">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-data-line"></i> 告警趋势分析
            </span>
            <el-radio-group v-model="trendTimeRange" size="small" @change="onTrendTimeRangeChange">
              <el-radio-button label="day">24小时</el-radio-button>
              <el-radio-button label="week">7天</el-radio-button>
              <el-radio-button label="month">30天</el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-container">
            <div id="trendChart" style="width: 100%; height: 260px;"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-pie-chart"></i> 严重程度分布
            </span>
          </div>
          <div class="chart-container">
            <div id="severityChart" style="width: 100%; height: 260px;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第二行图表：告警源分布（隐藏告警类型分布） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-monitor"></i> 告警源分布
            </span>
          </div>
          <div class="chart-container">
            <div id="sourceTypeChart" style="width: 100%; height: 260px;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第三行：TOP排行榜 -->
    <el-row :gutter="20" class="top-section">
      <el-col :span="12">
        <el-card shadow="hover" class="top-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-trophy"></i> 告警源TOP10
            </span>
          </div>
          <div class="top-list">
            <div v-for="(item, index) in topSources" :key="index" class="top-item">
              <div class="rank-badge" :class="`rank-${Math.min(index + 1, 3)}`">
                {{ index + 1 }}
              </div>
              <div class="item-content">
                <div class="item-name">{{ item.sourceName || item.sourceIp || '未知' }}</div>
                <div class="item-meta">
                  <el-tag size="mini" :type="getSourceTypeColor(item.sourceType)">
                    {{ getSourceTypeName(item.sourceType) }}
                  </el-tag>
                  <span class="item-count">{{ item.count }}次</span>
                </div>
              </div>
              <div class="item-progress">
                <el-progress 
                  :percentage="getPercentage(item.count, topSources[0] && topSources[0].count)"
                  :show-text="false"
                  :color="getProgressColor(index)"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="top-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-monitor"></i> 告警对象TOP10
            </span>
          </div>
          <div class="top-list">
            <div v-for="(item, index) in topTargetIps" :key="index" class="top-item">
              <div class="rank-badge" :class="`rank-${Math.min(index + 1, 3)}`">
                {{ index + 1 }}
              </div>
              <div class="item-content">
                <div class="item-name">{{ item.dstIp || '未知对象' }}</div>
                <div class="item-meta">
                  <el-tag v-if="item.criticalCount > 0" size="mini" type="danger">
                    严重 {{ item.criticalCount }}
                  </el-tag>
                  <span class="item-count">{{ item.count }}次</span>
                </div>
              </div>
              <div class="item-progress">
                <el-progress 
                  :percentage="getPercentage(item.count, topTargetIps[0] && topTargetIps[0].count)"
                  :show-text="false"
                  :color="getProgressColor(index)"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 一周效率榜 -->
    <el-row :gutter="20" class="efficiency-ranking-section">
      <el-col :span="24">
        <el-card shadow="hover" class="efficiency-ranking-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-trophy"></i> 一周效率榜
            </span>
            <div class="card-actions">
              <el-button @click="getPersonnelStats" size="small" icon="el-icon-refresh">刷新</el-button>
            </div>
          </div>
          <div class="efficiency-ranking">
            <div v-if="personnelStats.length === 0" class="empty-state">
              <i class="el-icon-document"></i>
              <p>暂无数据</p>
            </div>
            <div v-else class="ranking-grid">
                             <div 
                 v-for="(person, index) in personnelStats.slice(0, 8)" 
                 :key="index" 
                 class="ranking-card"
                 :class="`rank-position-${Math.min(index + 1, 3)}`"
               >
                                 <div class="rank-header">
                   <div class="rank-number" :class="`rank-${Math.min(index + 1, 3)}`">
                     <i v-if="index === 0" class="el-icon-trophy"></i>
                     <span v-else>{{ index + 1 }}</span>
                   </div>
                   <div class="person-info">
                     <div class="person-name">{{ person.createName }}</div>
                     <div class="person-badge" v-if="index < 3">
                       <span v-if="index === 0">🏆 效率王者</span>
                       <span v-else-if="index === 1">🥈 优秀表现</span>
                       <span v-else-if="index === 2">🥉 积极贡献</span>
                     </div>
                   </div>
                 </div>
                                 <div class="stats-grid">
                   <div class="stat-item">
                     <div class="stat-icon">📊</div>
                     <div class="stat-content">
                       <div class="stat-label">解决数量</div>
                       <div class="stat-value primary">{{ person.resolvedCount }}个</div>
                       <div class="stat-progress">
                         <div class="progress-bar" :style="{ width: Math.min(person.resolvedCount / Math.max(...personnelStats.map(p => p.resolvedCount)) * 100, 100) + '%' }"></div>
                       </div>
                     </div>
                   </div>
                   <div class="stat-item">
                     <div class="stat-icon">⚡</div>
                     <div class="stat-content">
                       <div class="stat-label">平均耗时</div>
                       <div class="stat-value secondary">{{ formatTime(person.avgResolutionTime) }}</div>
                       <div class="efficiency-level">
                         <span v-if="person.avgResolutionTime <= 30" class="level excellent">极速</span>
                         <span v-else-if="person.avgResolutionTime <= 60" class="level good">快速</span>
                         <span v-else-if="person.avgResolutionTime <= 120" class="level normal">正常</span>
                         <span v-else class="level slow">待优化</span>
                       </div>
                     </div>
                   </div>
                 </div>
                <div class="efficiency-rating">
                  <el-rate
                    v-model="person.efficiencyRating"
                    disabled
                    :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                  />
                </div>
                                 <div class="additional-info">
                   <div class="info-row">
                     <div class="info-item">
                       <span class="info-icon">🚀</span>
                       <span class="info-text">
                         <span class="info-label">最快记录</span>
                         <span class="info-value">{{ formatTime(person.minResolutionTime) }}</span>
                       </span>
                     </div>
                     <div class="info-item">
                       <span class="info-icon">📅</span>
                       <span class="info-text">
                         <span class="info-label">最近解决</span>
                         <span class="info-value">{{ formatDateTime(person.lastResolvedTime, 'short') }}</span>
                       </span>
                     </div>
                   </div>
                 </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 处理效率统计 -->
    <el-row :gutter="20" class="efficiency-section">
      <el-col :span="24">
        <el-card shadow="hover" class="efficiency-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-timer"></i> 处理效率统计
            </span>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="efficiency-item">
                <div class="efficiency-icon avg-time">
                  <i class="el-icon-time"></i>
                </div>
                <div class="efficiency-content">
                  <div class="efficiency-value">{{ formatTime(resolutionStats.avgResolutionTime) }}</div>
                  <div class="efficiency-label">平均处理时间</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="efficiency-item">
                <div class="efficiency-icon resolution-rate">
                  <i class="el-icon-circle-check"></i>
                </div>
                <div class="efficiency-content">
                  <div class="efficiency-value">{{ resolutionStats.resolutionRate }}%</div>
                  <div class="efficiency-label">解决率</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="efficiency-item">
                <div class="efficiency-icon today-resolved">
                  <i class="el-icon-finished"></i>
                </div>
                <div class="efficiency-content">
                  <div class="efficiency-value">{{ resolutionStats.todayResolved }}</div>
                  <div class="efficiency-label">今日已处理</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="efficiency-item">
                <div class="efficiency-icon active-alerts">
                  <i class="el-icon-warning"></i>
                </div>
                <div class="efficiency-content">
                  <div class="efficiency-value">{{ resolutionStats.activeAlerts }}</div>
                  <div class="efficiency-label">活跃告警</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 回到顶部按钮 -->
    <transition name="back-to-top">
      <div 
        v-show="showBackToTop" 
        class="back-to-top-btn" 
        @click="scrollToTop"
        title="回到顶部"
      >
        <div class="btn-content">
          <i class="el-icon-top"></i>
          <span class="btn-text">TOP</span>
        </div>
        <div class="btn-progress">
          <svg class="progress-ring" width="60" height="60">
            <circle
              class="progress-ring-circle"
              stroke="currentColor"
              stroke-width="3"
              fill="transparent"
              r="27"
              cx="30"
              cy="30"
              :style="{ strokeDashoffset: progressOffset }"
            />
          </svg>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import request from '@/utils/request'
import * as echarts from 'echarts'
import { getSourceTypeText, getSeverityText } from '@/utils/alert-enums'

export default {
  name: "AlertStatistics",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 统计数据
      statistics: {
        total: 0,
        totalTrend: 0,
        critical: 0,
        criticalTrend: 0,
        pending: 0,
        pendingTrend: 0,
        resolved: 0,
        resolvedTrend: 0
      },
      // 趋势图数据
      trendTimeRange: 'week',
      trendData: [],
      // 分布数据
      severityDistribution: [],
      alertTypeDistribution: [],
      sourceTypeDistribution: [],
      // TOP排行榜数据
      topSources: [],
      topTargetIps: [],
      // 人员处理统计数据
      personnelStats: [],
      // 处理效率统计
      resolutionStats: {
        avgResolutionTime: 0,
        resolutionRate: 0,
        todayResolved: 0,
        activeAlerts: 0
      },
      // 图表实例
      charts: {
        trend: null,
        severity: null,
        sourceType: null
      },
      // 回到顶部按钮相关
      showBackToTop: false,
      progressOffset: 0
    }
  },
  mounted() {
    this.initPage()
    this.bindScrollEvent()
  },
  beforeDestroy() {
    this.unbindScrollEvent()
    this.destroyCharts()
  },
  methods: {
    // 初始化页面
    async initPage() {
      this.loading = true
      try {
        await Promise.all([
          this.getStatistics(),
          this.getTrendData(),
          this.getDistributionData(),
          this.getTopData(),
          this.getResolutionStats(),
          this.getPersonnelStats()
        ])
      } catch (error) {
        console.error('初始化页面失败:', error)
        this.$message.error('加载统计数据失败')
      } finally {
        this.loading = false
      }
    },

    // 获取基础统计数据
    async getStatistics() {
      try {
        const response = await request({
          url: '/alert/statistics',
          method: 'get'
        })
        if (response.code === 200) {
          this.statistics = {
            total: response.data.total || 0,
            totalTrend: response.data.totalTrend || 0,
            critical: response.data.critical || 0,
            criticalTrend: response.data.criticalTrend || 0,
            pending: response.data.pending || 0,
            pendingTrend: response.data.pendingTrend || 0,
            resolved: response.data.resolved || 0,
            resolvedTrend: response.data.resolvedTrend || 0
          }
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败')
      }
    },

    // 获取趋势数据
    async getTrendData() {
      try {
        const days = this.trendTimeRange === 'day' ? 1 : 
                    this.trendTimeRange === 'week' ? 7 : 30
        const response = await request({
          url: '/alert/trend',
          method: 'get',
          params: { days }
        })
        if (response.code === 200) {
          // 处理趋势数据，确保字段名称正确
          this.trendData = (response.data || []).map(item => ({
            date: item.date,
            total: item.count || item.total || 0,
            critical: item.critical_count || item.critical || 0,
            warning: item.high_count || item.medium_count || item.warning || 0,
            info: item.low_count || item.info || 0
          })).sort((a, b) => {
            return new Date(a.date) - new Date(b.date)
          })
          console.log('处理后的趋势数据:', this.trendData)
          this.renderTrendChart()
        }
      } catch (error) {
        console.error('获取趋势数据失败:', error)
      }
    },

    // 获取分布数据
    async getDistributionData() {
      try {
        // 获取严重程度分布
        const severityResponse = await request({
          url: '/alert/distribution',
          method: 'get',
          params: { type: 'severity' }
        })
        if (severityResponse.code === 200) {
          console.log('严重程度分布数据:', severityResponse.data)
          // 处理严重程度分布数据，确保字段名称正确
          this.severityDistribution = (severityResponse.data || []).map(item => ({
            severity: item.name || item.severity,
            count: item.value || item.count || 0
          }))
          this.renderSeverityChart()
        }

        // 获取告警类型分布（保留数据获取逻辑，但不在页面显示）
        const alertTypeResponse = await request({
          url: '/alert/distribution',
          method: 'get',
          params: { type: 'alerttype' }
        })
        console.log('告警类型分布响应:', alertTypeResponse)
        if (alertTypeResponse.code === 200) {
          console.log('告警类型分布数据:', alertTypeResponse.data)
          this.alertTypeDistribution = alertTypeResponse.data || []
          // 注释掉renderAlertTypeChart()，不显示告警类型分布图表
          // this.renderAlertTypeChart()
        } else {
          console.error('告警类型分布API调用失败:', alertTypeResponse)
        }

        // 获取告警源分布
        const sourceTypeResponse = await request({
          url: '/alert/distribution',
          method: 'get',
          params: { type: 'sourcetype' }
        })
        if (sourceTypeResponse.code === 200) {
          console.log('告警源分布原始数据:', sourceTypeResponse.data)
          this.sourceTypeDistribution = (sourceTypeResponse.data || []).map(item => ({
            name: item.name || item.sourceType || item.key, // 兼容不同的字段名
            value: item.count || item.value,
            sourceType: item.name || item.sourceType || item.key // 保留原始值用于枚举映射
          }))
          console.log('告警源分布处理后数据:', this.sourceTypeDistribution)
          this.renderSourceTypeChart()
        }
      } catch (error) {
        console.error('获取分布数据失败:', error)
      }
    },

    // 获取TOP数据
    async getTopData() {
      try {
        // 获取告警源TOP10
        const sourcesResponse = await request({
          url: '/alert/top-sources',
          method: 'get',
          params: { limit: 10 }
        })
        if (sourcesResponse.code === 200) {
          this.topSources = sourcesResponse.data || []
        }

        // 获取告警对象TOP10（原目标IP TOP10）
        const targetIpsResponse = await request({
          url: '/alert/top-targets',
          method: 'get',
          params: { limit: 10 }
        })
        if (targetIpsResponse.code === 200) {
          this.topTargetIps = targetIpsResponse.data || []
        }
      } catch (error) {
        console.error('获取TOP数据失败:', error)
      }
    },

    // 获取处理效率统计
    async getResolutionStats() {
      try {
        const response = await request({
          url: '/alert/resolution-stats',
          method: 'get',
          params: { days: 30 }
        })
        if (response.code === 200) {
          this.resolutionStats = response.data || {}
        }
      } catch (error) {
        console.error('获取处理效率统计失败:', error)
      }
    },

    // 获取人员处理统计
    async getPersonnelStats() {
      try {
        const response = await request({
          url: '/alert/personnel-resolution-stats',
          method: 'get'
        })
        if (response.code === 200) {
          let stats = response.data || []
          // 添加排名和效率评级
          stats = stats.map((item, index) => {
            return {
              ...item,
              rank: index + 1,
              efficiencyRating: this.calculateEfficiencyRating(item)
            }
          })
          this.personnelStats = stats
        }
      } catch (error) {
        console.error('获取人员处理统计失败:', error)
        this.$message.error('获取人员处理统计失败')
      }
    },

    // 计算效率评级
    calculateEfficiencyRating(item) {
      // 基于解决数量和平均解决时间计算评级（1-5星）
      const count = item.resolvedCount || 0
      const avgTime = item.avgResolutionTime || 0
      
      let score = 3 // 基础分3星
      
      // 根据解决数量调整分数
      if (count >= 20) score += 1
      else if (count >= 10) score += 0.5
      else if (count < 5) score -= 0.5
      
      // 根据平均解决时间调整分数（假设60分钟是标准时间）
      if (avgTime > 0) {
        if (avgTime <= 30) score += 1
        else if (avgTime <= 60) score += 0.5
        else if (avgTime > 120) score -= 0.5
        if (avgTime > 240) score -= 1
      }
      
      // 确保分数在1-5之间
      return Math.max(1, Math.min(5, Math.round(score)))
    },

    // 时间范围变化处理
    onTrendTimeRangeChange() {
      this.getTrendData()
    },

    // 渲染趋势图表
    renderTrendChart() {
      this.$nextTick(() => {
        const chartDom = document.getElementById('trendChart')
        if (!chartDom) return
        
        if (this.charts.trend) {
          this.charts.trend.dispose()
        }
        
        this.charts.trend = echarts.init(chartDom)
        
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          legend: {
            data: ['总数', '严重', '警告', '信息']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.trendData.map(item => item.date)
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '总数',
              type: 'line',
              data: this.trendData.map(item => item.total),
              smooth: true,
              lineStyle: { color: '#409EFF' },
              areaStyle: { opacity: 0.3, color: '#409EFF' }
            },
            {
              name: '严重',
              type: 'line',
              data: this.trendData.map(item => item.critical),
              smooth: true,
              lineStyle: { color: '#F56C6C' }
            },
            {
              name: '警告',
              type: 'line',
              data: this.trendData.map(item => item.warning),
              smooth: true,
              lineStyle: { color: '#E6A23C' }
            },
            {
              name: '信息',
              type: 'line',
              data: this.trendData.map(item => item.info),
              smooth: true,
              lineStyle: { color: '#67C23A' }
            }
          ]
        }
        
        this.charts.trend.setOption(option)
      })
    },

    // 渲染严重程度分布图表
    renderSeverityChart() {
      this.$nextTick(() => {
        const chartDom = document.getElementById('severityChart')
        if (!chartDom) return
        
        if (this.charts.severity) {
          this.charts.severity.dispose()
        }
        
        this.charts.severity = echarts.init(chartDom)
        
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [
            {
              name: '严重程度',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: false,
              data: this.severityDistribution.map(item => ({
                value: item.count,
                name: getSeverityText(item.severity),
                itemStyle: {
                  color: this.getSeverityColor(item.severity)
                }
              })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
        
        this.charts.severity.setOption(option)
      })
    },

    // 渲染告警源分布图表（柱状图）
    renderSourceTypeChart() {
      this.$nextTick(() => {
        const chartDom = document.getElementById('sourceTypeChart')
        if (!chartDom) return
        
        if (this.charts.sourceType) {
          this.charts.sourceType.dispose()
        }
        
        this.charts.sourceType = echarts.init(chartDom)
        
        const sourceData = this.sourceTypeDistribution.map(item => ({
          value: item.value || item.count || 0,
          name: getSourceTypeText(item.sourceType || item.name)
        })).sort((a, b) => b.value - a.value) // 按数量降序排列
        
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function(params) {
              const item = params[0]
              return `${item.name}<br/>告警数量: ${item.value}`
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '10%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: sourceData.map(item => item.name),
            axisLabel: {
              rotate: 45,
              fontSize: 12
            }
          },
          yAxis: {
            type: 'value',
            name: '告警数量',
            nameTextStyle: {
              fontSize: 12
            }
          },
          series: [
            {
              name: '告警数量',
              type: 'bar',
              data: sourceData.map((item, index) => ({
                value: item.value,
                itemStyle: {
                  color: this.getSourceBarColor(index)
                }
              })),
              barWidth: '60%',
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
        
        this.charts.sourceType.setOption(option)
      })
    },

    // 获取严重程度颜色
    getSeverityColor(severity) {
      const colorMap = {
        'critical': '#FF4757',    // 鲜红色 - 严重
        'high': '#FF6B35',        // 橙红色 - 高
        'medium': '#FFA502',      // 橙色 - 中
        'warning': '#FFA502',     // 橙色 - 警告（与medium相同）
        'low': '#2ED573',         // 绿色 - 低
        'info': '#5352ED',        // 蓝色 - 信息
        'unknown': '#747D8C'      // 灰色 - 未知
      }
      return colorMap[severity] || colorMap['unknown']
    },

    // 获取告警源柱状图颜色
    getSourceBarColor(index) {
      const colors = [
        '#5470C6', '#91CC75', '#FAC858', '#EE6666', 
        '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', 
        '#EA7CCC', '#FF9F7F', '#FFDB5C', '#FF7F50'
      ]
      return colors[index % colors.length]
    },

    // 获取源类型颜色
    getSourceTypeColor(sourceType) {
      const colorMap = {
        'network': 'primary',
        'security': 'danger',
        'system': 'warning',
        'application': 'info'
      }
      return colorMap[sourceType] || 'info'
    },

    // 获取源类型名称
    getSourceTypeName(sourceType) {
      return getSourceTypeText(sourceType)
    },

    // 计算百分比
    getPercentage(value, maxValue) {
      if (!maxValue) return 0
      return Math.round((value / maxValue) * 100)
    },

    // 获取进度条颜色
    getProgressColor(index) {
      const colors = [
        '#f56c6c', '#e6a23c', '#5cb87a',
        '#409eff', '#909399', '#c0c4cc'
      ]
      return colors[Math.min(index, colors.length - 1)]
    },

    // 格式化时间（分钟）
    formatTime(minutes) {
      if (!minutes || minutes === 0) return '0分钟'
      
      // 四舍五入到最近的整数
      const roundedMinutes = Math.round(minutes)
      
      if (roundedMinutes < 60) {
        return `${roundedMinutes}分钟`
      } else if (roundedMinutes < 1440) { // 小于24小时
        const hours = Math.floor(roundedMinutes / 60)
        const remainingMinutes = roundedMinutes % 60
        return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
      } else { // 大于等于24小时，显示天数
        const days = Math.floor(roundedMinutes / 1440)
        const hours = Math.floor((roundedMinutes % 1440) / 60)
        const remainingMinutes = roundedMinutes % 60
        
        let result = `${days}天`
        if (hours > 0) result += `${hours}小时`
        if (remainingMinutes > 0) result += `${remainingMinutes}分钟`
        
        return result
      }
    },

    // 格式化日期时间
    formatDateTime(dateTime, format = 'full') {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      
      if (format === 'short') {
        // 短格式：只显示月-日 时:分
        return date.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } else {
        // 完整格式
        return date.toLocaleString('zh-CN')
      }
    },

    // 绑定滚动事件
    bindScrollEvent() {
      window.addEventListener('scroll', this.handleScroll)
    },

    // 解绑滚动事件
    unbindScrollEvent() {
      window.removeEventListener('scroll', this.handleScroll)
    },

    // 处理滚动事件
    handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const windowHeight = window.innerHeight
      const documentHeight = document.documentElement.scrollHeight
      
      // 显示/隐藏回到顶部按钮
      this.showBackToTop = scrollTop > 300
      
      // 计算滚动进度
      const scrollProgress = scrollTop / (documentHeight - windowHeight)
      const circumference = 2 * Math.PI * 27 // 圆的周长
      this.progressOffset = circumference - (scrollProgress * circumference)
    },

    // 回到顶部
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },

    // 销毁图表
    destroyCharts() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
      }

      .card-content {
        display: flex;
        align-items: center;
        padding: 24px 20px;
        position: relative;

        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
          backdrop-filter: blur(10px);

          i {
            font-size: 24px;
            color: white;
          }
        }

        .card-info {
          flex: 1;

          .card-value {
            font-size: 28px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 6px;
            color: #303133;
          }

          .card-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
            margin-bottom: 8px;
          }

          .card-trend {
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;

            i {
              margin-right: 4px;
              font-size: 14px;
            }

            &.trend-up {
              color: #f56c6c;
            }

            &.trend-down {
              color: #67c23a;
            }
          }
        }
      }

      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        .card-value, .card-label { color: white; }
      }

      &.critical {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        .card-value, .card-label { color: white; }
      }

      &.pending {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        .card-value, .card-label { color: white; }
      }

      &.resolved {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        .card-value, .card-label { color: white; }
      }
    }
  }

  .chart-row {
    margin-bottom: 20px;

    .chart-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }
      }

      .chart-container {
        padding: 20px;
        min-height: 300px;
      }
    }
  }

  .top-section {
    margin-bottom: 20px;

    .top-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }
      }

      .top-list {
        padding: 16px;
        max-height: 400px;
        overflow-y: auto;

        .top-item {
          display: flex;
          align-items: center;
          padding: 12px 8px;
          border-radius: 8px;
          margin-bottom: 8px;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f5f7fa;
          }

          .rank-badge {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: white;
            margin-right: 12px;

            &.rank-1 {
              background: linear-gradient(135deg, #ffd700, #ffed4e);
            }

            &.rank-2 {
              background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
            }

            &.rank-3 {
              background: linear-gradient(135deg, #cd7f32, #daa520);
            }
          }

          .item-content {
            flex: 1;
            margin-right: 12px;

            .item-name {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin-bottom: 4px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .item-meta {
              display: flex;
              align-items: center;
              gap: 8px;

              .item-count {
                font-size: 12px;
                color: #909399;
              }
            }
          }

          .item-progress {
            width: 100px;
          }
        }
      }
    }
  }

  .efficiency-ranking-section {
    margin-bottom: 20px;

    .efficiency-ranking-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }
      }

      .card-actions {
        display: flex;
        gap: 8px;
      }

      .efficiency-ranking {
        padding: 20px;

        .empty-state {
          text-align: center;
          padding: 60px 20px;
          color: #909399;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
          }
        }

        .ranking-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 20px;
          
          @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: 16px;
          }
          
          @media (min-width: 769px) and (max-width: 1200px) {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          }
          
          @media (min-width: 1201px) {
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            max-width: 1400px; // 限制最大宽度，避免卡片过多时页面过宽
          }
        }

        .ranking-card {
          background: #fff;
          border-radius: 12px;
          padding: 20px;
          box-shadow: 0 2px 12px rgba(0,0,0,0.1);
          transition: all 0.3s ease;
          border: 1px solid #ebeef5;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
          }

          &.rank-position-1 {
            background: #fff;
            border: 2px solid #1890ff;
            box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
            
            .rank-number {
              background: #1890ff !important;
              color: #fff;
            }
            
            .person-name {
              color: #1890ff;
              font-weight: 600;
            }
          }

          &.rank-position-2, &.rank-position-3 {
            background: #fff;
            border: 2px solid #40a9ff;
            box-shadow: 0 2px 8px rgba(64, 169, 255, 0.1);
            
            .rank-number {
              background: #40a9ff !important;
              color: #fff;
            }
            
            .person-name {
              color: #40a9ff;
              font-weight: 600;
            }
          }

          .rank-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .rank-number {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              font-size: 16px;
              margin-right: 12px;
              color: #fff;

                                           &.rank-1 {
                background: #1890ff;
              }

              &.rank-2 {
                background: #40a9ff;
              }

              &.rank-3 {
                background: #40a9ff;
              }

              &:not(.rank-1):not(.rank-2):not(.rank-3) {
                background: #409EFF;
              }
            }

            .person-name {
              font-size: 18px;
              font-weight: bold;
              flex: 1;
            }
          }

          .person-info {
            flex: 1;
            
            .person-name {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 4px;
            }
            
            .person-badge {
              font-size: 12px;
              color: #666;
            }
          }

          .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;

            .stat-item {
              display: flex;
              align-items: flex-start;
              gap: 8px;
              
              .stat-icon {
                font-size: 20px;
                margin-top: 2px;
              }
              
              .stat-content {
                flex: 1;
                
                .stat-label {
                  font-size: 12px;
                  color: #909399;
                  margin-bottom: 4px;
                }

                .stat-value {
                  font-size: 18px;
                  font-weight: bold;
                  margin-bottom: 6px;

                  &.primary {
                    color: #1890ff;
                  }

                  &.secondary {
                    color: #52c41a;
                  }
                }
                
                .stat-progress {
                  width: 100%;
                  height: 4px;
                  background: #f0f0f0;
                  border-radius: 2px;
                  overflow: hidden;
                  
                  .progress-bar {
                    height: 100%;
                    background: #1890ff;
                    transition: width 0.3s ease;
                  }
                }
                
                .efficiency-level {
                  .level {
                    display: inline-block;
                    padding: 2px 6px;
                    border-radius: 8px;
                    font-size: 10px;
                    font-weight: 500;
                    
                    &.excellent {
                      background: #f6ffed;
                      color: #52c41a;
                      border: 1px solid #b7eb8f;
                    }
                    
                    &.good {
                      background: #e6f7ff;
                      color: #1890ff;
                      border: 1px solid #91d5ff;
                    }
                    
                    &.normal {
                      background: #fffbe6;
                      color: #faad14;
                      border: 1px solid #ffe58f;
                    }
                    
                    &.slow {
                      background: #fff2e8;
                      color: #fa8c16;
                      border: 1px solid #ffbb96;
                    }
                  }
                }
              }
            }
          }

          &.rank-position-1 .stat-label,
          &.rank-position-2 .stat-label,
          &.rank-position-3 .stat-label {
            color: rgba(255,255,255,0.8);
          }

          &.rank-position-1 .stat-value,
          &.rank-position-2 .stat-value,
          &.rank-position-3 .stat-value {
            color: #fff;
          }

          .efficiency-rating {
            text-align: center;
            margin-bottom: 16px;
          }

          .additional-info {
            .info-row {
              display: flex;
              gap: 12px;
              
              .info-item {
                flex: 1;
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 8px;
                background: #fafafa;
                border-radius: 6px;
                font-size: 12px;

                .info-icon {
                  font-size: 14px;
                }

                .info-text {
                  display: flex;
                  flex-direction: column;
                  
                  .info-label {
                    color: #909399;
                    line-height: 1.2;
                  }

                  .info-value {
                    font-weight: 500;
                    color: #303133;
                    line-height: 1.2;
                    margin-top: 2px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .efficiency-section {
    .efficiency-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }
      }

      .efficiency-item {
        display: flex;
        align-items: center;
        padding: 24px;
        text-align: center;

        .efficiency-icon {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 16px;

          i {
            font-size: 24px;
            color: white;
          }

          &.avg-time {
            background: linear-gradient(135deg, #667eea, #764ba2);
          }

          &.resolution-rate {
            background: linear-gradient(135deg, #f093fb, #f5576c);
          }

          &.today-resolved {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
          }

          &.active-alerts {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
          }
        }

        .efficiency-content {
          flex: 1;
          margin-left: 16px;

          .efficiency-value {
            font-size: 24px;
            font-weight: 700;
            color: #303133;
            margin-bottom: 4px;
          }

          .efficiency-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
}

// 回到顶部按钮样式
.back-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  user-select: none;

  &:hover {
    background: linear-gradient(135deg, #66b1ff, #409eff);
    box-shadow: 0 6px 25px rgba(64, 158, 255, 0.4);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 15px rgba(64, 158, 255, 0.3);
  }

  .btn-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    i {
      font-size: 20px;
      color: white;
      margin-bottom: 2px;
      transition: transform 0.3s ease;
    }

    .btn-text {
      font-size: 10px;
      color: white;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  &:hover .btn-content i {
    transform: translateY(-2px);
  }

  .btn-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
    pointer-events: none;

    .progress-ring {
      width: 100%;
      height: 100%;
    }

    .progress-ring-circle {
      stroke: rgba(255, 255, 255, 0.3);
      stroke-dasharray: 169.65; // 2 * π * 27
      stroke-dashoffset: 169.65;
      transition: stroke-dashoffset 0.3s ease;
      stroke-linecap: round;
    }
  }
}

// 过渡动画
.back-to-top-enter-active,
.back-to-top-leave-active {
  transition: all 0.3s ease;
}

.back-to-top-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

.back-to-top-leave-to {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}
</style> 