<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
        <el-form-item label="输出名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入输出名称" clearable size="small" @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="告警源类型" prop="alertSourceType">
          <el-select v-model="queryParams.alertSourceType" placeholder="请选择告警源类型" clearable size="small">
            <el-option
              v-for="option in sourceTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="输出方式" prop="outputType">
          <el-select v-model="queryParams.outputType" placeholder="请选择输出方式" clearable size="small">
            <el-option label="转发原始数据" value="forward_raw" />
            <el-option label="发送标准告警" value="send_standard_alert" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格区 -->
    <el-card class="table-card" shadow="never">
      <el-table v-loading="loading" :data="outputConfigList" @selection-change="handleSelectionChange" stripe>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="输出名称" align="center" prop="name" :show-overflow-tooltip="true" min-width="120" />
        <el-table-column label="告警源类型" align="center" prop="alertSourceType" min-width="100">
          <template slot-scope="scope">
            <el-tag size="small" :type="getSourceTypeColor(scope.row.alertSourceType)">{{ getSourceTypeName(scope.row.alertSourceType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="告警源实例" align="center" prop="alertSourceName" :show-overflow-tooltip="true" min-width="120" />
        <el-table-column label="告警规则" align="center" prop="alertRuleName" :show-overflow-tooltip="true" min-width="120" />
        <el-table-column label="告警组" align="center" prop="groupName" :show-overflow-tooltip="true" min-width="100" />
        <el-table-column label="输出方式" align="center" prop="outputType" min-width="120">
          <template slot-scope="scope">
            <el-tag size="small" :type="scope.row.outputType === 'forward_raw' ? 'warning' : 'success'">
              {{ scope.row.outputType === 'forward_raw' ? '转发原始数据' : '发送标准告警' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="输出目标" align="center" prop="outputTarget" min-width="100">
          <template slot-scope="scope">
            <el-tag size="small" type="primary">{{ scope.row.outputTarget || 'FlashDuty' }}</el-tag>
          </template>
        </el-table-column>
<!--        <el-table-column label="输入推送URL" align="center" prop="inputPushUrl" min-width="180">-->
<!--          <template slot-scope="scope">-->
<!--            <el-input v-model="scope.row.inputPushUrl" readonly size="mini" />-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-value="enabled" inactive-value="disabled" @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createAt" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createAt) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" style="color: #f56c6c;">删除</el-button>
            <!-- <el-button size="mini" type="text" icon="el-icon-s-promotion" @click="handleTest(scope.row)" style="color: #67c23a;">测试</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改告警输出配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body class="config-dialog">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" class="config-form">
        <!-- 基本信息 -->
        <el-card class="form-section" shadow="never">
          <div slot="header" class="section-header">
            <i class="el-icon-info"></i>
            <span>基本信息</span>
          </div>
          <el-form-item prop="alertSourceType">
            <span slot="label">
              告警源类型
              <el-tooltip content="匹配指定类型的告警源，只有来自该类型的告警才会被输出" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-select v-model="form.alertSourceType" placeholder="请选择告警源类型" style="width: 100%">
              <el-option
                  v-for="option in sourceTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="name">
            <span slot="label">
              输出名称
              <el-tooltip content="输出配置的名称，建议使用有意义的名称" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-input v-model="form.name" placeholder="请输入输出名称，用于标识该输出配置" />
          </el-form-item>
          </el-form-item>
          <el-form-item prop="outputTarget">
            <span slot="label">
              输出目标
              <el-tooltip content="告警输出的目标系统，目前只支持 FlashDuty" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-select v-model="form.outputTarget" placeholder="请选择输出目标" style="width: 100%">
              <el-option label="FlashDuty" value="flashduty" />
            </el-select>
          </el-form-item>
          <el-form-item prop="outputType">
            <span slot="label">
              输出方式
              <el-tooltip placement="top">
                <div slot="content">
                  <strong>转发原始数据：</strong>直接转发告警的原始数据，不做任何处理<br/>
                  <strong>发送标准告警：</strong>将告警数据转换为标准格式后发送到目标系统
                </div>
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-select v-model="form.outputType" placeholder="请选择输出方式" style="width: 100%">
              <el-option label="转发原始数据" value="forward_raw">
                <span>转发原始数据</span>
                <span style="float: right; color: #8492a6; font-size: 13px;">直接转发告警原始数据</span>
              </el-option>
              <el-option label="发送标准告警" value="send_standard_alert">
                <span>发送标准告警</span>
                <span style="float: right; color: #8492a6; font-size: 13px;">转换为标准格式后发送</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="outputWebhookUrl">
            <span slot="label">
              Webhook URL
              <el-tooltip content="目标系统的 Webhook 接收地址，告警将发送到该地址" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-input v-model="form.outputWebhookUrl" placeholder="请输入目标系统的 Webhook URL" />
          </el-form-item>
          <el-form-item prop="inputPushUrl">
            <span slot="label">
              输入推送URL
              <el-tooltip content="本系统接收推送的URL，供外部系统推送数据用" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-input v-model="form.inputPushUrl" placeholder="请输入输入推送URL（可选）" readonly>
              <el-button slot="append" type="primary" @click="generateInputPushUrl" :disabled="!form.alertSourceType">生成</el-button>
              <el-button slot="append" type="success" @click="copyInputPushUrl" v-if="form.inputPushUrl">复制</el-button>
            </el-input>
          </el-form-item>
          
          <!-- 告警对象字段配置，仅在输入推送URL不为空时显示 -->
          <el-form-item prop="alertObjectKey" v-if="form.inputPushUrl">
            <span slot="label">
              告警对象字段
              <el-tooltip placement="top">
                <div slot="content">
                  <strong>告警对象字段说明：</strong><br/>
                  • 配置需要提取的告警对象字段的key<br/>
                  • 这些字段将从告警数据中提取并作为URL参数<br/>
                  • 当字段变化时，会自动拼接到输入推送URL后面<br/>
                  • 例如：添加host字段后，URL会变成 .../alerts?token=xxx&host={host}
                </div>
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <div class="object-keys-container">
              <!-- 已添加的字段显示 -->
              <div v-if="objectKeys.length > 0" class="object-keys-display">
                <el-tag v-for="(key, index) in objectKeys" :key="index" closable @close="removeObjectKey(index)" class="object-key-tag">
                  {{ key }}
                </el-tag>
              </div>
              
              <!-- 输入框+按钮区域 -->
              <div v-if="showObjectKeyInput" class="object-key-flex-row">
                <el-input v-model="newObjectKey" placeholder="请输入字段key，例如：host,ip,service" size="small" />
                <div class="button-group">
                  <el-button size="small" type="success" @click="addObjectKey" icon="el-icon-check">添加</el-button>
                  <el-button size="small" type="danger" @click="cancelObjectKey" icon="el-icon-close">取消</el-button>
                </div>
              </div>
              
              <!-- 添加按钮 -->
              <el-button v-if="!showObjectKeyInput" size="small" type="primary" plain @click="showObjectKeyInput = true" icon="el-icon-plus">
                添加对象字段
              </el-button>
            </div>
          </el-form-item>
        </el-card>

        <!-- 匹配条件 -->
        <el-card class="form-section" shadow="never">
          <div slot="header" class="section-header">
            <i class="el-icon-s-operation"></i>
            <span>匹配条件</span>
            <el-tooltip placement="top">
              <div slot="content">
                <strong>匹配规则说明：</strong><br/>
                • 只有同时满足所有已配置条件的告警才会被输出<br/>
                • 空白字段将被忽略，不参与匹配<br/>
                • 所有字段均为可选<br/>
                • 建议至少配置一个匹配条件以避免过度匹配
              </div>
              <i class="el-icon-question" style="margin-left: 5px; color: #909399;"></i>
            </el-tooltip>
          </div>
          <el-form-item>
            <span slot="label">
              告警源实例标识
              <el-tooltip content="告警源实例的唯一标识，用于区分不同的告警源实例（可选）" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-input v-model="form.alertSourceIdent" placeholder="请输入告警源实例的唯一标识（可选）" />
          </el-form-item>
          <el-form-item>
            <span slot="label">
              告警源实例名称
              <el-tooltip content="告警源实例的显示名称，便于识别和管理（可选）" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-input v-model="form.alertSourceName" placeholder="请输入告警源实例的名称（可选）" />
          </el-form-item>
          <el-form-item>
            <span slot="label">
              告警规则名称
              <el-tooltip content="匹配指定规则名称的告警，用于精确匹配特定的告警规则（可选）" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-input v-model="form.alertRuleName" placeholder="请输入告警规则名称（可选）" />
          </el-form-item>
          <el-form-item>
            <span slot="label">
              告警组名称
              <el-tooltip content="匹配指定组名称的告警，用于按组进行告警路由（可选）" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-input v-model="form.groupName" placeholder="请输入告警组名称（可选）" />
          </el-form-item>
        </el-card>

        <!-- 高级过滤 -->
        <el-card class="form-section" shadow="never">
          <div slot="header" class="section-header">
            <i class="el-icon-s-tools"></i>
            <span>高级过滤</span>
            <el-tooltip content="基于告警标签进行更精细的过滤，可选配置" placement="top">
              <i class="el-icon-question" style="margin-left: 5px; color: #909399;"></i>
            </el-tooltip>
          </div>
          <el-form-item prop="alertRuleFilter">
            <span slot="label">
              标签过滤规则
              <el-tooltip placement="top">
                <div slot="content">
                  <strong>标签过滤说明：</strong><br/>
                  • 只有同时满足所有标签条件的告警才会被输出<br/>
                  • 支持对告警的基本字段进行过滤：severity、status、alertType、sourceType、sourceIdent、groupName<br/>
                  • 也支持对告警的自定义标签进行过滤<br/>
                  • 如果不配置任何过滤条件，则匹配所有满足基本条件的告警
                </div>
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <div class="tag-filter-container">
              <el-button size="small" type="primary" plain @click="showTagEditor = !showTagEditor" icon="el-icon-edit">
                {{ showTagEditor ? '隐藏编辑器' : '显示编辑器' }}
              </el-button>
              <el-button size="small" type="success" plain @click="addTagFilter" icon="el-icon-plus" v-if="!showTagEditor">添加标签过滤</el-button>
              <el-button size="small" type="warning" plain @click="clearTagFilters" icon="el-icon-delete" v-if="tagFilters.length > 0">清空所有</el-button>
            </div>

            <!-- 简化的标签过滤编辑 -->
            <div v-if="!showTagEditor && tagFilters.length === 0" class="empty-filter-tip">
              <i class="el-icon-info"></i>
              <span>未配置标签过滤，将匹配所有满足基本条件的告警</span>
            </div>

            <div v-if="!showTagEditor && tagFilters.length > 0" class="tag-filters-display">
              <el-tag v-for="(filter, index) in tagFilters" :key="index" closable @close="removeTagFilter(index)" class="filter-tag">
                {{ filter.key }} = {{ filter.value }}
              </el-tag>
            </div>

            <div v-if="!showTagEditor" class="tag-filter-form" v-for="(filter, index) in editingFilters" :key="'edit-' + index">
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-input v-model="filter.key" placeholder="标签名" size="small" />
                </el-col>
                <el-col :span="10">
                  <el-input v-model="filter.value" placeholder="标签值" size="small" />
                </el-col>
                <el-col :span="4">
                  <el-button size="small" type="success" @click="confirmTagFilter(index)" icon="el-icon-check"></el-button>
                  <el-button size="small" type="danger" @click="cancelTagFilter(index)" icon="el-icon-close"></el-button>
                </el-col>
              </el-row>
            </div>

            <!-- JSON 编辑器 -->
            <el-input v-if="showTagEditor" v-model="form.alertRuleFilter" type="textarea" :rows="6"
                     placeholder="请输入JSON格式的标签过滤规则，例如：{&quot;severity&quot;: &quot;critical&quot;, &quot;env&quot;: &quot;prod&quot;}"
                     @blur="validateJsonFilter" />
          </el-form-item>
        </el-card>

        <!-- 状态配置 -->
        <el-card class="form-section" shadow="never">
          <div slot="header" class="section-header">
            <i class="el-icon-switch-button"></i>
            <span>状态配置</span>
          </div>
          <el-form-item prop="status">
            <span slot="label">
              状态
              <el-tooltip content="启用后该输出配置才会生效，禁用后将不会输出任何告警" placement="top">
                <i class="el-icon-question" style="margin-left: 4px; color: #909399; cursor: help;"></i>
              </el-tooltip>
            </span>
            <el-radio-group v-model="form.status">
              <el-radio label="enabled">启用</el-radio>
              <el-radio label="disabled">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 查看抽屉 -->
    <el-drawer
      :title="'告警输出配置详情'"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%"
      class="detail-drawer"
    >
      <div class="drawer-content">
        <!-- 基本信息 -->
        <el-card class="detail-section" shadow="never">
          <div slot="header" class="section-header">
            <i class="el-icon-info"></i>
            <span>基本信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="配置ID">{{ viewForm.id }}</el-descriptions-item>
            <el-descriptions-item label="输出名称">{{ viewForm.name }}</el-descriptions-item>
            <el-descriptions-item label="告警源类型">
              <el-tag :type="getSourceTypeColor(viewForm.alertSourceType)">{{ getSourceTypeName(viewForm.alertSourceType) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="输出目标">
              <el-tag type="primary">{{ viewForm.outputTarget || 'FlashDuty' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="输出方式">
              <el-tag :type="viewForm.outputType === 'forward_raw' ? 'warning' : 'success'">
                {{ viewForm.outputType === 'forward_raw' ? '转发原始数据' : '发送标准告警' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="Webhook URL" :span="2" v-if="viewForm.outputWebhookUrl">
              <div class="webhook-url">
                <el-input :value="viewForm.outputWebhookUrl" readonly>
                  <el-button slot="append" icon="el-icon-document-copy" @click="copyWebhookUrl">复制</el-button>
                </el-input>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="输入推送URL" :span="2" v-if="viewForm.inputPushUrl">
              <div class="webhook-url">
                <el-input :value="viewForm.inputPushUrl" readonly>
                  <el-button slot="append" icon="el-icon-document-copy" @click="copyViewInputPushUrl">复制</el-button>
                </el-input>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="告警对象字段" v-if="viewForm.alertObjectKey">
              <div class="object-keys">
                <el-tag v-for="key in parseObjectKeys(viewForm.alertObjectKey)" :key="key"
                        style="margin-right: 8px; margin-bottom: 8px;" type="success">
                  {{ key }}
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="viewForm.status === 'enabled' ? 'success' : 'danger'">
                {{ viewForm.status === 'enabled' ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 匹配条件 -->
        <el-card class="detail-section" shadow="never">
          <div slot="header" class="section-header">
            <i class="el-icon-s-operation"></i>
            <span>匹配条件</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="告警源实例标识">{{ viewForm.alertSourceIdent || '-' }}</el-descriptions-item>
            <el-descriptions-item label="告警源实例名称">{{ viewForm.alertSourceName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="告警规则名称">{{ viewForm.alertRuleName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="告警组名称" :span="2">{{ viewForm.groupName || '-' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 高级过滤 -->
        <el-card class="detail-section" shadow="never" v-if="viewForm.alertRuleFilter">
          <div slot="header" class="section-header">
            <i class="el-icon-s-tools"></i>
            <span>高级过滤</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="标签过滤规则">
              <div class="filter-rules">
                <el-tag v-for="(value, key) in parseFilterRules(viewForm.alertRuleFilter)" :key="key"
                       style="margin-right: 8px; margin-bottom: 8px;" type="info">
                  {{ key }}: {{ value }}
                </el-tag>
                <div v-if="!parseFilterRules(viewForm.alertRuleFilter) || Object.keys(parseFilterRules(viewForm.alertRuleFilter)).length === 0"
                     class="no-filter">
                  <i class="el-icon-info"></i>
                  <span>未配置标签过滤</span>
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 时间信息 -->
        <el-card class="detail-section" shadow="never">
          <div slot="header" class="section-header">
            <i class="el-icon-time"></i>
            <span>时间信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createAt) }}</el-descriptions-item>
            <el-descriptions-item label="创建人">{{ viewForm.createName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateAt) }}</el-descriptions-item>
            <el-descriptions-item label="更新人">{{ viewForm.updateName || '-' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 操作按钮 -->
        <div class="drawer-actions">
          <el-button type="primary" @click="handleUpdate(viewForm)" icon="el-icon-edit">编辑配置</el-button>
          <!-- <el-button type="success" @click="handleTest(viewForm)" icon="el-icon-s-promotion">测试连接</el-button> -->
          <el-button @click="drawerVisible = false">关闭</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 测试对话框 -->
    <el-dialog title="测试告警输出" :visible.sync="testDialogVisible" width="800px" append-to-body class="test-dialog">
      <div class="test-content">
        <el-alert
          title="测试说明"
          type="info"
          :closable="false"
          show-icon>
          <div slot="default">
            系统将根据当前配置生成一条测试告警数据，您可以编辑后发送进行测试。测试数据会标注为测试告警，不会影响正常的告警处理。
          </div>
        </el-alert>

        <el-form ref="testForm" :model="testAlert" label-width="120px" class="test-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="告警标题">
                <el-input v-model="testAlert.title" placeholder="请输入告警标题" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="严重程度">
                <el-select v-model="testAlert.severity" placeholder="请选择严重程度" style="width: 100%">
                  <el-option label="Critical" value="critical" />
                  <el-option label="Warning" value="warning" />
                  <el-option label="Info" value="info" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="告警类型">
                <el-input v-model="testAlert.alertType" placeholder="请输入告警类型" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态">
                <el-select v-model="testAlert.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="New" value="new" />
                  <el-option label="Firing" value="firing" />
                  <el-option label="Resolved" value="resolved" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="告警源类型">
                <el-input v-model="testAlert.sourceType" placeholder="告警源类型" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="告警源标识">
                <el-input v-model="testAlert.sourceIdent" placeholder="告警源标识" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="规则名称">
                <el-input v-model="testAlert.ruleName" placeholder="规则名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组名称">
                <el-input v-model="testAlert.groupName" placeholder="组名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="描述">
            <el-input v-model="testAlert.description" type="textarea" :rows="3" placeholder="请输入告警描述" />
          </el-form-item>

          <el-form-item label="标签">
            <div class="test-tags-container">
              <el-tag v-for="(tag, index) in testTags" :key="index" closable @close="removeTestTag(index)" class="test-tag">
                {{ tag.key }}: {{ tag.value }}
              </el-tag>
              <el-button size="small" type="primary" plain @click="addTestTag" icon="el-icon-plus">添加标签</el-button>
            </div>

            <div v-if="editingTestTag" class="test-tag-form">
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-input v-model="newTestTag.key" placeholder="标签名" size="small" />
                </el-col>
                <el-col :span="8">
                  <el-input v-model="newTestTag.value" placeholder="标签值" size="small" />
                </el-col>
                <el-col :span="8">
                  <el-button size="small" type="success" @click="confirmTestTag" icon="el-icon-check"></el-button>
                  <el-button size="small" type="danger" @click="cancelTestTag" icon="el-icon-close"></el-button>
                </el-col>
              </el-row>
            </div>
          </el-form-item>

          <el-form-item label="原始数据">
            <el-input v-model="testAlert.rawData" type="textarea" :rows="4" placeholder="原始告警数据（JSON格式）" />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="testDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sendTestAlert" :loading="testSending">发送测试</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOutputConfig, getOutputConfig, delOutputConfig, addOutputConfig, updateOutputConfig, testOutputConfig, listOutputConfigBySourceId } from "@/api/alert/output";
import {
  AlertSourceTypeEnum, AlertSeverityEnum, AlertStatusEnum,
  getSourceTypeText,
  getEnumOptions
} from '@/utils/alert-enums'

export default {
  name: "AlertOutputConfig",
  dicts: ['alert_output_type','alert_output_filter'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 告警输出配置表格数据
      outputConfigList: [],
      // 告警源类型选项
      sourceTypeOptions: getEnumOptions(AlertSourceTypeEnum),
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示抽屉
      drawerVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        alertSourceType: null,
        outputType: null,
        status: null
      },
      // 表单参数
      form: {
        id: null,
        name: null,
        alertSourceType: null,
        alertSourceIdent: null,
        alertSourceName: null,
        alertRuleName: null,
        groupName: null,
        alertRuleFilter: null,
        alertObjectKey: null,
        outputTarget: "flashduty",
        outputType: "send_standard_alert",
        outputWebhookUrl: null,
        inputPushUrl: null,
        status: "enabled"
      },
      // 查看表单参数
      viewForm: {},
      // 标签过滤器
      tagFilters: [],
      editingFilters: [],
      showTagEditor: false,
      submitLoading: false,
      // 测试相关数据
      testDialogVisible: false,
      testSending: false,
      testAlert: {},
      testTags: [],
      editingTestTag: false,
      newTestTag: { key: '', value: '' },
      currentTestConfig: null,
      // 表单校验
      rules: {
        name: [
          { required: true, message: "输出名称不能为空", trigger: "blur" }
        ],
        outputTarget: [
          { required: true, message: "输出目标不能为空", trigger: "change" }
        ],
        outputType: [
          { required: true, message: "输出方式不能为空", trigger: "change" }
        ],
        outputWebhookUrl: [
          { required: true, message: "Webhook URL不能为空", trigger: "blur" },
          { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
        ],
        alertSourceType: [
          { required: true, message: "告警源类型不能为空", trigger: "change" }
        ]
        // alertSourceIdent、alertSourceName、alertRuleName、groupName 都不做必选条件
      },
      // 告警方式选项
      outputTypeOptions: [
        { value: "flash_duty", label: "FlashDuty" }
      ],
      // 数据筛选方式选项
      filterTypeOptions: [
        { value: "tag_filter", label: "基于标签过滤" },
        { value: "forward_raw", label: "转发元数据" }
      ],
      // 状态选项
      statusOptions: [
        { value: "enabled", label: "启用" },
        { value: "disabled", label: "禁用" }
      ],
      // 告警对象字段相关
      objectKeys: [],
      showObjectKeyInput: false,
      newObjectKey: '',
      // 枚举选项
      severityOptions: getEnumOptions(AlertSeverityEnum),
      statusOptions: getEnumOptions(AlertStatusEnum),
      // 用户列表（用于下拉选择）
      userList: [],
    };
  },
  created() {
    this.getList();
  },
  watch: {
    // 监听对象字段变化，动态更新输入推送URL
    objectKeys: {
      handler(newKeys) {
        this.updateInputPushUrlWithObjectKeys();
      },
      deep: true
    }
  },
  methods: {
    /** 查询告警输出配置列表 */
    getList() {
      this.loading = true;
      // 如果有指定的源ID，则查询该源的输出配置
      if (this.queryParams.sourceId) {
        listOutputConfigBySourceId(this.queryParams.sourceId).then(response => {
          this.outputConfigList = response.data;
          this.total = this.outputConfigList.length;
          this.loading = false;
        }).catch(error => {
          this.$message.error('获取数据失败，请检查后端服务是否正常运行');
          this.loading = false;
        });
      } else {
        // 否则查询所有输出配置
        listOutputConfig(this.queryParams).then(response => {
          this.outputConfigList = response.rows;
          this.total = response.total;
          this.loading = false;
        }).catch(error => {

          this.$message.error('获取数据失败，请检查后端服务是否正常运行');
          this.loading = false;
        });
      }
    },
    /** 获取告警源选项 */
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        alertSourceType: null,
        alertSourceIdent: null,
        alertSourceName: null,
        alertRuleName: null,
        groupName: null,
        alertRuleFilter: null,
        alertObjectKey: null,
        outputTarget: "flashduty",
        outputType: "send_standard_alert",
        outputWebhookUrl: null,
        inputPushUrl: null,
        status: "enabled"
      };
      this.tagFilters = [];
      this.editingFilters = [];
      this.showTagEditor = false;
      // 清空对象字段相关数据
      this.objectKeys = [];
      this.showObjectKeyInput = false;
      this.newObjectKey = '';
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加告警输出配置";
    },

    // 生成输入推送URL
    generateInputPushUrl() {
      const loghubBaseUrl = 'https://securio-loghub.5i5j.com/api';
      const wukongBaseUrl = 'https://wukong-openapi.5i5j.com/api';
      const token = '9K8mN4pQ7xR2vB3wE6yH1jL5sA9dF0gT';
      const randomIdent = Math.random().toString(36).substring(2, 15);
      
      let url = '';
      switch (this.form.alertSourceType) {
        case 'safeline_waf':
          url = `${loghubBaseUrl}/safeline/waf/alerts?token=${token}&ident=${randomIdent}`;
          break;
        case 'honeypot':
          url = `${loghubBaseUrl}/hfish/alerts?token=${token}&ident=${randomIdent}`;
          break;
        case 'standard':
          url = `${loghubBaseUrl}/alerts/standard?token=${token}&ident=${randomIdent}`;
          break;
        case 'muyun':
          url = `${loghubBaseUrl}/muyun/alerts?token=${token}&ident=${randomIdent}`;
          break;
        case 'skywalking':
          url = `${loghubBaseUrl}/skywalking/alerts?token=${token}&ident=${randomIdent}`;
          break;
        case 'zabbix':
          url = `${loghubBaseUrl}/zabbix/alerts?token=${token}&ident=${randomIdent}`;
          break;
        case 'grafana':
          url = `${loghubBaseUrl}/grafana/alerts?token=${token}&ident=${randomIdent}`;
          break;
        case 'n9e':
          url = `${loghubBaseUrl}/n9e/alerts?token=${token}&ident=${randomIdent}`;
          break;
        case 'huawei_ces':
          url = `${wukongBaseUrl}/huawei/ces/alerts?token=${token}&ident=${randomIdent}`;
          break;
        case 'wangsu_waf':
          url = `${wukongBaseUrl}/wangsu/waf/alerts?token=${token}&ident=${randomIdent}`;
          break;
        default:
          this.$message.warning('该告警源类型不支持输入推送URL');
          return;
      }
      
      this.form.inputPushUrl = url;
      this.$message.success('输入推送URL生成成功');
      // 初始化对象字段
      this.initObjectKeys();
    },

    // 复制输入推送URL
    copyInputPushUrl() {
      if (this.form.inputPushUrl) {
        const input = document.createElement('input');
        input.setAttribute('readonly', 'readonly');
        input.setAttribute('value', this.form.inputPushUrl);
        document.body.appendChild(input);
        input.select();
        if (document.execCommand('copy')) {
          this.$message.success('复制成功');
        }
        document.body.removeChild(input);
      }
    },

    // 添加标签过滤
    addTagFilter() {
      this.editingFilters.push({ key: '', value: '' });
    },

    // 确认添加标签过滤
    confirmTagFilter(index) {
      const filter = this.editingFilters[index];
      if (filter.key && filter.value) {
        this.tagFilters.push({ key: filter.key, value: filter.value });
        this.editingFilters.splice(index, 1);
        this.updateFilterJson();
      } else {
        this.$message.warning('请输入标签名和标签值');
      }
    },

    // 取消添加标签过滤
    cancelTagFilter(index) {
      this.editingFilters.splice(index, 1);
    },

    // 移除标签过滤
    removeTagFilter(index) {
      this.tagFilters.splice(index, 1);
      this.updateFilterJson();
    },

    // 清空所有标签过滤
    clearTagFilters() {
      this.tagFilters = [];
      this.editingFilters = [];
      this.form.alertRuleFilter = '';
    },

    // 更新过滤JSON
    updateFilterJson() {
      if (this.tagFilters.length > 0) {
        const filterObj = {};
        this.tagFilters.forEach(filter => {
          filterObj[filter.key] = filter.value;
        });
        this.form.alertRuleFilter = JSON.stringify(filterObj);
      } else {
        this.form.alertRuleFilter = '';
      }
    },

    // 验证JSON过滤
    validateJsonFilter() {
      if (this.form.alertRuleFilter) {
        try {
          const parsed = JSON.parse(this.form.alertRuleFilter);
          this.tagFilters = Object.keys(parsed).map(key => ({ key, value: parsed[key] }));
        } catch (e) {
          this.$message.error('无效的JSON格式');
        }
      }
    },

    // 获取告警源类型名称
    getSourceTypeName(type) {
      return getSourceTypeText(type);
    },

    // 获取告警源类型颜色
    getSourceTypeColor(type) {
      const colorMap = {
        'muyun': 'primary',
        'wangsu': 'success',
        'n9e': 'warning',
        'grafana': 'info'
      };
      return colorMap[type] || 'default';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getOutputConfig(id).then(response => {
        this.form = response.data;
        // 解析标签过滤
        if (this.form.alertRuleFilter) {
          try {
            const parsed = JSON.parse(this.form.alertRuleFilter);
            this.tagFilters = Object.keys(parsed).map(key => ({ key, value: parsed[key] }));
          } catch (e) {
            this.tagFilters = [];
          }
        }
        // 初始化对象字段
        this.initObjectKeys();
        this.open = true;
        this.title = "修改告警输出配置";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      const id = row.id;
      getOutputConfig(id).then(response => {
        this.viewForm = response.data;
        this.drawerVisible = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitLoading = true;

          // 更新过滤JSON
          this.updateFilterJson();

          // 更新对象字段
          this.updateFormObjectKey();

          // 设置时间戳
          const now = Date.now();
          if (this.form.id != null) {
            this.form.updateAt = now;
            updateOutputConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.submitLoading = false;
            });
          } else {
            this.form.createAt = now;
            this.form.updateAt = now;
            addOutputConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.submitLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除告警输出配置编号为"' + ids + '"的数据项？').then(function() {
        return delOutputConfig(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 测试按钮操作 */
    handleTest(row) {
      this.$modal.confirm('是否确认测试告警输出配置"' + row.name + '"？').then(function() {
        return testOutputConfig(row.id);
      }).then(() => {
        this.$modal.msgSuccess("测试成功");
      }).catch(() => {});
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "enabled" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.name + '"吗？').then(function() {
        return updateOutputConfig(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "enabled" ? "disabled" : "enabled";
      });
    },
    /** 查看Webhook URL */
    viewWebhookUrl(row) {
      if (row.webhookUrl) {
        this.$alert(row.webhookUrl, 'Webhook URL', {
          confirmButtonText: '复制',
          callback: action => {
            if (action === 'confirm') {
              const input = document.createElement('input');
              input.setAttribute('readonly', 'readonly');
              input.setAttribute('value', row.webhookUrl);
              document.body.appendChild(input);
              input.select();
              if (document.execCommand('copy')) {
                document.execCommand('copy');
                this.$message.success('复制成功');
              }
              document.body.removeChild(input);
            }
          }
        });
      }
    },

    /** 数据筛选方式变更 */
    handleFilterTypeChange(value) {
      if (value === "forward_raw") {
        this.filterConditions = [];
        this.form.tagFilterRules = null;
      }
    },

    /** 解析过滤规则 */
    parseFilterRules(rulesJson) {
      try {
        return JSON.parse(rulesJson);
      } catch (e) {
        return {};
      }
    },

    /** 解析对象字段key */
    parseObjectKeys(objectKeysStr) {
      if (!objectKeysStr) {
        return [];
      }
      return objectKeysStr.split(',').map(key => key.trim()).filter(key => key.length > 0);
    },

    /** 复制Webhook URL */
    copyWebhookUrl() {
      if (this.viewForm.outputWebhookUrl) {
        const input = document.createElement('input');
        input.setAttribute('readonly', 'readonly');
        input.setAttribute('value', this.viewForm.outputWebhookUrl);
        document.body.appendChild(input);
        input.select();
        if (document.execCommand('copy')) {
          this.$message.success('复制成功');
        }
        document.body.removeChild(input);
      }
    },
    /** 复制输入推送URL */
    copyViewInputPushUrl() {
      if (this.viewForm.inputPushUrl) {
        const input = document.createElement('input');
        input.setAttribute('readonly', 'readonly');
        input.setAttribute('value', this.viewForm.inputPushUrl);
        document.body.appendChild(input);
        input.select();
        if (document.execCommand('copy')) {
          this.$message.success('复制成功');
        }
        document.body.removeChild(input);
      }
    },
    /** 添加过滤条件 */
    addFilterCondition() {
      this.filterConditions.push({
        field: "",
        operator: "eq",
        value: ""
      });
    },
    /** 移除过滤条件 */
    removeFilterCondition(index) {
      this.filterConditions.splice(index, 1);
    },
    /** 测试按钮操作 */
    handleTest(row) {
      this.currentTestConfig = row;
      this.generateTestAlert(row);
      this.testDialogVisible = true;
    },

    // 生成测试告警数据
    generateTestAlert(config) {
      const now = Date.now();
      const testId = 'test_' + now;

      // 生成测试数据
      this.testAlert = {
        title: `[测试] ${config.name} - 测试告警`,
        description: `这是一条由系统生成的测试告警数据，用于验证输出配置是否正常工作。生成时间：${new Date(now).toLocaleString()}`,
        severity: 'warning',
        status: 'firing',
        alertType: 'test',
        sourceType: config.alertSourceType || 'test',
        sourceIdent: config.alertSourceIdent || 'test-instance',
        ruleName: config.alertRuleName || 'test-rule',
        groupName: config.groupName || 'test-group',
        eventId: testId,
        detectedAt: now,
        occurredAt: now,
        createAt: now,
        updateAt: now
      };

      // 生成测试标签
      this.testTags = [
        { key: 'test', value: 'true' },
        { key: 'config_id', value: config.id.toString() },
        { key: 'config_name', value: config.name },
        { key: 'generated_at', value: new Date(now).toISOString() }
      ];

      // 生成原始数据
      const rawData = {
        alert_id: testId,
        title: this.testAlert.title,
        description: this.testAlert.description,
        severity: this.testAlert.severity,
        status: this.testAlert.status,
        source: {
          type: this.testAlert.sourceType,
          ident: this.testAlert.sourceIdent,
          name: config.alertSourceName || 'test-source'
        },
        rule: {
          name: this.testAlert.ruleName,
          group: this.testAlert.groupName
        },
        labels: {
          test: 'true',
          config_id: config.id.toString(),
          config_name: config.name,
          generated_at: new Date(now).toISOString()
        },
        annotations: {
          description: this.testAlert.description,
          summary: this.testAlert.title
        },
        timestamp: now
      };

      this.testAlert.rawData = JSON.stringify(rawData, null, 2);
    },

    // 添加测试标签
    addTestTag() {
      this.editingTestTag = true;
      this.newTestTag = { key: '', value: '' };
    },

    // 确认添加测试标签
    confirmTestTag() {
      if (this.newTestTag.key && this.newTestTag.value) {
        this.testTags.push({ key: this.newTestTag.key, value: this.newTestTag.value });
        this.editingTestTag = false;
        this.newTestTag = { key: '', value: '' };
      } else {
        this.$message.warning('请输入标签名和标签值');
      }
    },

    // 取消添加测试标签
    cancelTestTag() {
      this.editingTestTag = false;
      this.newTestTag = { key: '', value: '' };
    },

    // 移除测试标签
    removeTestTag(index) {
      this.testTags.splice(index, 1);
    },

    // 发送测试告警
    sendTestAlert() {
      this.testSending = true;

      // 更新标签数据
      if (this.testTags.length > 0) {
        const tagsObj = {};
        this.testTags.forEach(tag => {
          tagsObj[tag.key] = tag.value;
        });
        this.testAlert.tags = JSON.stringify(tagsObj);
      }

      // 发送测试请求
      testOutputConfig(this.currentTestConfig.id, this.testAlert).then(response => {
        this.$modal.msgSuccess('测试告警发送成功！');
        this.testDialogVisible = false;
      }).catch(error => {
        this.$modal.msgError('测试告警发送失败：' + (error.msg || error.message || '未知错误'));
      }).finally(() => {
        this.testSending = false;
      });
    },

    // 添加对象字段
    addObjectKey() {
      if (this.newObjectKey.trim() !== '') {
        this.objectKeys.push(this.newObjectKey.trim());
        this.newObjectKey = '';
        this.showObjectKeyInput = false;
        this.updateFormObjectKey();
        this.updateInputPushUrlWithObjectKeys();
      } else {
        this.$message.warning('请输入有效的字段key');
      }
    },

    // 移除对象字段
    removeObjectKey(index) {
      this.objectKeys.splice(index, 1);
      this.updateFormObjectKey();
      this.updateInputPushUrlWithObjectKeys();
    },

    // 显示对象字段输入
    showObjectKeyInput() {
      this.showObjectKeyInput = true;
    },

    // 取消对象字段输入
    cancelObjectKey() {
      this.showObjectKeyInput = false;
      this.newObjectKey = '';
    },

    // 动态更新输入推送URL
    updateInputPushUrlWithObjectKeys() {
      if (!this.form.inputPushUrl) return;

      // 先移除已有的 alert_object_key 参数
      let url = this.form.inputPushUrl.replace(/([&?])alert_object_key=[^&]*/g, "");

      // 去除末尾多余的 & 或 ?
      url = url.replace(/[&?]$/, "");

      // 拼接新的 alert_object_key
      if (this.objectKeys.length > 0) {
        const param = `alert_object_key=${this.objectKeys.join(",")}`;
        url += (url.includes("?") ? "&" : "?") + param;
      }

      this.form.inputPushUrl = url;
    },

    // 初始化对象字段
    initObjectKeys() {
      if (this.form.alertObjectKey) {
        this.objectKeys = this.parseObjectKeys(this.form.alertObjectKey);
      } else {
        this.objectKeys = [];
      }
    },

    // 更新表单中的alertObjectKey字段
    updateFormObjectKey() {
      this.form.alertObjectKey = this.objectKeys.join(',');
    }
  }
};
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.config-dialog .el-dialog__body {
  padding: 10px 20px;
}

.config-form .form-section {
  margin-bottom: 20px;
}

.config-form .form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.section-header i {
  margin-right: 8px;
  color: #409eff;
}

/* 已改为 tooltip 形式，不再需要 field-tip 样式 */

.tag-filter-container {
  margin-bottom: 10px;
}

.tag-filter-container .el-button {
  margin-right: 10px;
}

.empty-filter-tip {
  padding: 20px;
  text-align: center;
  color: #909399;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin: 10px 0;
}

.empty-filter-tip i {
  margin-right: 5px;
}

.tag-filters-display {
  margin: 10px 0;
}

.filter-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-filter-form {
  margin-bottom: 10px;
}

.detail-drawer .drawer-content {
  padding: 20px;
}

.detail-drawer .detail-section {
  margin-bottom: 20px;
}

.detail-drawer .detail-section:last-child {
  margin-bottom: 0;
}

.detail-drawer .section-header {
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.detail-drawer .section-header i {
  margin-right: 8px;
  color: #409eff;
}

.webhook-url {
  width: 100%;
}

.filter-rules {
  min-height: 40px;
}

.no-filter {
  color: #909399;
  font-style: italic;
}

.no-filter i {
  margin-right: 5px;
}

.drawer-actions {
  margin-top: 30px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.drawer-actions .el-button {
  margin: 0 10px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}

/* 对话框样式优化 */
.config-dialog .el-dialog {
  border-radius: 8px;
}

.config-dialog .el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  border-radius: 8px 8px 0 0;
}

.config-dialog .el-dialog__title {
  font-weight: 600;
  color: #303133;
}

.config-dialog .el-card__header {
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

/* 抽屉样式优化 */
.detail-drawer .el-drawer__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 0;
  padding: 20px;
}

.detail-drawer .el-drawer__title {
  font-weight: 600;
  color: #303133;
}

.detail-drawer .el-drawer__body {
  padding: 0;
}

/* 测试对话框样式 */
.test-dialog .el-dialog {
  border-radius: 8px;
}

.test-dialog .el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  border-radius: 8px 8px 0 0;
}

.test-dialog .el-dialog__title {
  font-weight: 600;
  color: #303133;
}

.test-content {
  padding: 0;
}

.test-content .el-alert {
  margin-bottom: 20px;
}

.test-form {
  margin-top: 20px;
}

.test-tags-container {
  margin-bottom: 10px;
}

.test-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.test-tag-form {
  margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-dialog {
    width: 95% !important;
  }

  .detail-drawer {
    width: 90% !important;
  }

  .test-dialog {
    width: 95% !important;
  }

  .config-form .el-form-item__label {
    width: 100px !important;
  }

  .test-form .el-form-item__label {
    width: 100px !important;
  }
}

/* 对象字段样式 */
.object-keys-container {
  margin-top: 10px;
}

.object-keys-display {
  margin-bottom: 15px;
}

.object-key-tag {
  margin-right: 8px;
  margin-bottom: 8px;
  background-color: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.object-key-input {
  margin-bottom: 15px;
}

.object-key-input .el-row {
  margin-bottom: 10px;
}

.object-key-input .el-col {
  display: flex;
  align-items: center;
}

.object-key-input .el-col:first-child {
  padding-right: 15px;
}

.object-key-input .el-col:last-child {
  padding-left: 15px;
}

.object-key-input .el-button {
  margin-left: 8px;
}

.object-key-input .el-button:first-child {
  margin-left: 0;
}

.button-group {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

.object-key-flex-row {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 480px;
  margin-bottom: 15px;
}
</style>

<style scoped>
.filter-condition {
  margin-bottom: 10px;
}
</style>
