<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" class="search-form">
      <el-form-item label="告警来源" prop="source">
        <el-select v-model="queryParams.source" placeholder="请选择告警来源" clearable>
          <el-option
            v-for="dict in sourceOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="告警级别" prop="severity">
        <el-select v-model="queryParams.severity" placeholder="请选择告警级别" clearable>
          <el-option
            v-for="dict in severityOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="告警状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择告警状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围" prop="timeRange">
        <el-date-picker
          v-model="queryParams.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>总告警数</span>
            <el-tag :type="statistics.totalTrend > 0 ? 'danger' : 'success'">
              {{ statistics.totalTrend > 0 ? '↑' : '↓' }} {{ Math.abs(statistics.totalTrend) }}%
            </el-tag>
          </div>
          <div class="card-value">{{ statistics.totalAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>新增告警</span>
            <el-tag :type="statistics.newTrend > 0 ? 'danger' : 'success'">
              {{ statistics.newTrend > 0 ? '↑' : '↓' }} {{ Math.abs(statistics.newTrend) }}%
            </el-tag>
          </div>
          <div class="card-value">{{ statistics.newAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>已处理告警</span>
            <el-tag :type="statistics.resolvedTrend > 0 ? 'success' : 'danger'">
              {{ statistics.resolvedTrend > 0 ? '↑' : '↓' }} {{ Math.abs(statistics.resolvedTrend) }}%
            </el-tag>
          </div>
          <div class="card-value">{{ statistics.resolvedAlerts }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>平均处理时间</span>
            <el-tag :type="statistics.avgResolutionTimeTrend < 0 ? 'success' : 'danger'">
              {{ statistics.avgResolutionTimeTrend < 0 ? '↓' : '↑' }} {{ Math.abs(statistics.avgResolutionTimeTrend) }}%
            </el-tag>
          </div>
          <div class="card-value">{{ statistics.avgResolutionTime }}分钟</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-area">
      <el-col :span="16">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>告警趋势</span>
            <el-radio-group v-model="trendTimeRange" size="small">
              <el-radio-button label="day">24小时</el-radio-button>
              <el-radio-button label="week">7天</el-radio-button>
              <el-radio-button label="month">30天</el-radio-button>
            </el-radio-group>
          </div>
          <trend-chart :chart-data="trendData" :options="trendOptions" />
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>告警分布</span>
          </div>
          <pie-chart :chart-data="distributionData" :options="distributionOptions" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 告警列表 -->
    <el-card shadow="hover" class="alert-list">
      <div slot="header" class="card-header">
        <span>告警列表</span>
        <div class="right-buttons">
          <el-button type="primary" icon="el-icon-refresh" @click="getList">刷新</el-button>
          <el-button type="success" icon="el-icon-download" @click="handleExport">导出</el-button>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :data="alertList"
        style="width: 100%"
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left" inline class="alert-detail">
              <el-form-item label="告警描述">
                <span>{{ props.row.description }}</span>
              </el-form-item>
              <el-form-item label="告警详情">
                <span>{{ props.row.details }}</span>
              </el-form-item>
              <el-form-item label="处理记录" v-if="props.row.status !== 'pending'">
                <span>{{ props.row.resolutionNote }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="告警来源" prop="source" />
        <el-table-column label="告警级别" prop="severity">
          <template slot-scope="scope">
            <el-tag :type="getSeverityType(scope.row.severity)">
              {{ scope.row.severity }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="告警状态" prop="status">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="告警时间" prop="alertTime" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              type="text"
              icon="el-icon-check"
              v-if="scope.row.status === 'pending'"
              @click="handleResolve(scope.row)"
            >处理</el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 查看告警详情对话框 -->
    <el-dialog
      title="告警详情"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="告警来源">{{ currentAlert.source }}</el-descriptions-item>
        <el-descriptions-item label="告警级别">
          <el-tag :type="getSeverityType(currentAlert.severity)">
            {{ currentAlert.severity }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警状态">
          <el-tag :type="getStatusType(currentAlert.status)">
            {{ currentAlert.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警时间">{{ currentAlert.alertTime }}</el-descriptions-item>
        <el-descriptions-item label="告警描述" :span="2">{{ currentAlert.description }}</el-descriptions-item>
        <el-descriptions-item label="告警详情" :span="2">{{ currentAlert.details }}</el-descriptions-item>
        <el-descriptions-item label="处理记录" :span="2" v-if="currentAlert.status !== 'pending'">
          {{ currentAlert.resolutionNote }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 处理告警对话框 -->
    <el-dialog
      title="处理告警"
      :visible.sync="resolveDialogVisible"
      width="40%"
    >
      <el-form :model="resolveForm" label-width="100px">
        <el-form-item label="处理方式">
          <el-radio-group v-model="resolveForm.action">
            <el-radio label="resolve">解决</el-radio>
            <el-radio label="ignore">忽略</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理说明">
          <el-input
            type="textarea"
            v-model="resolveForm.note"
            :rows="4"
            placeholder="请输入处理说明"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resolveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitResolve">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Message, MessageBox } from 'element-ui'
import TrendChart from '@/components/Charts/TrendChart.vue'
import PieChart from '@/components/Charts/PieChart.vue'
import Pagination from '@/components/Pagination'
// 暂时注释掉 API 导入
// import {
//   listAlert,
//   getAlert,
//   resolveAlert,
//   ignoreAlert,
//   deleteAlert,
//   exportAlert,
//   getAlertStatistics,
//   getAlertTrend,
//   getAlertDistribution
// } from '@/api/alert/alert'

export default {
  name: "AlertCenter",
  components: {
    TrendChart,
    PieChart,
    Pagination
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        source: undefined,
        severity: undefined,
        status: undefined,
        timeRange: []
      },
      // 数据列表
      alertList: [],
      loading: false,
      total: 0,
      showSearch: true,
      // 统计数据
      statistics: {
        totalAlerts: 0,
        totalTrend: 0,
        newAlerts: 0,
        newTrend: 0,
        resolvedAlerts: 0,
        resolvedTrend: 0,
        avgResolutionTime: 0,
        avgResolutionTimeTrend: 0
      },
      // 趋势图数据
      trendTimeRange: 'day',
      trendData: {
        labels: [],
        datasets: []
      },
      trendOptions: {
        responsive: true,
        maintainAspectRatio: false
      },
      // 分布图数据
      distributionData: {
        labels: [],
        datasets: [{
          data: [],
          backgroundColor: []
        }]
      },
      distributionOptions: {
        responsive: true,
        maintainAspectRatio: false
      },
      // 对话框控制
      dialogVisible: false,
      resolveDialogVisible: false,
      currentAlert: {},
      resolveForm: {
        action: 'resolve',
        note: ''
      },
      // 选项数据
      sourceOptions: [
        { value: 'system', label: '系统' },
        { value: 'security', label: '安全' },
        { value: 'network', label: '网络' }
      ],
      severityOptions: [
        { value: 'critical', label: '严重' },
        { value: 'warning', label: '警告' },
        { value: 'info', label: '信息' }
      ],
      statusOptions: [
        { value: 'pending', label: '待处理' },
        { value: 'resolved', label: '已解决' },
        { value: 'ignored', label: '已忽略' }
      ]
    };
  },
  methods: {
    // Mock 数据生成函数
    generateMockAlerts(count = 10) {
      const alerts = []
      const sources = ['system', 'security', 'network']
      const severities = ['critical', 'warning', 'info']
      const statuses = ['pending', 'resolved', 'ignored']

      for (let i = 0; i < count; i++) {
        const alertTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
        alerts.push({
          id: i + 1,
          source: sources[Math.floor(Math.random() * sources.length)],
          severity: severities[Math.floor(Math.random() * severities.length)],
          status: statuses[Math.floor(Math.random() * statuses.length)],
          alertTime: alertTime.toLocaleString(),
          description: `告警描述 ${i + 1}`,
          details: `告警详细信息 ${i + 1}`,
          resolutionNote: Math.random() > 0.5 ? `处理说明 ${i + 1}` : ''
        })
      }
      return alerts
    },

    generateMockStatistics() {
      return {
        totalAlerts: Math.floor(Math.random() * 1000),
        totalTrend: Math.floor(Math.random() * 20) - 10,
        newAlerts: Math.floor(Math.random() * 100),
        newTrend: Math.floor(Math.random() * 20) - 10,
        resolvedAlerts: Math.floor(Math.random() * 800),
        resolvedTrend: Math.floor(Math.random() * 20) - 10,
        avgResolutionTime: Math.floor(Math.random() * 60),
        avgResolutionTimeTrend: Math.floor(Math.random() * 20) - 10
      }
    },

    generateMockTrendData(timeRange) {
      const now = new Date()
      const labels = []
      const data = []
      let points = 24
      let interval = 3600000 // 1小时

      if (timeRange === 'week') {
        points = 7
        interval = 24 * 3600000 // 1天
      } else if (timeRange === 'month') {
        points = 30
        interval = 24 * 3600000 // 1天
      }

      for (let i = points - 1; i >= 0; i--) {
        const time = new Date(now.getTime() - i * interval)
        labels.push(timeRange === 'day' ? time.getHours() + ':00' : time.toLocaleDateString())
        data.push(Math.floor(Math.random() * 50))
      }

      return {
        labels,
        datasets: [{
          label: '告警数量',
          data,
          borderColor: '#409EFF',
          backgroundColor: 'rgba(64, 158, 255, 0.1)',
          fill: true
        }]
      }
    },

    generateMockDistributionData() {
      return {
        labels: ['严重', '警告', '信息'],
        datasets: [{
          data: [
            Math.floor(Math.random() * 100),
            Math.floor(Math.random() * 200),
            Math.floor(Math.random() * 300)
          ],
          backgroundColor: ['#F56C6C', '#E6A23C', '#909399']
        }]
      }
    },

    // 消息提示
    showMessage(type, message) {
      Message({
        type,
        message
      })
    },

    // 确认框
    showConfirm(message, title = '提示') {
      return MessageBox.confirm(message, title, {
        type: 'warning'
      })
    },

    // 获取告警列表
    async getList() {
      this.loading = true
      try {
        // 模拟 API 延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        const mockData = this.generateMockAlerts(20)
        this.alertList = mockData
        this.total = mockData.length
      } catch (error) {
        console.error('获取告警列表失败:', error)
        this.showMessage('error', '获取告警列表失败')
      }
      this.loading = false
    },

    // 获取统计数据
    async getStatistics() {
      try {
        // 模拟 API 延迟
        await new Promise(resolve => setTimeout(resolve, 300))
        Object.assign(this.statistics, this.generateMockStatistics())
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.showMessage('error', '获取统计数据失败')
      }
    },

    // 获取趋势数据
    async getTrend() {
      try {
        // 模拟 API 延迟
        await new Promise(resolve => setTimeout(resolve, 300))
        this.trendData = this.generateMockTrendData(this.trendTimeRange)
      } catch (error) {
        console.error('获取趋势数据失败:', error)
        this.showMessage('error', '获取趋势数据失败')
      }
    },

    // 获取分布数据
    async getDistribution() {
      try {
        // 模拟 API 延迟
        await new Promise(resolve => setTimeout(resolve, 300))
        this.distributionData = this.generateMockDistributionData()
      } catch (error) {
        console.error('获取分布数据失败:', error)
        this.showMessage('error', '获取分布数据失败')
      }
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
      this.getStatistics()
      this.getTrend()
      this.getDistribution()
    },

    // 重置查询
    resetQuery() {
      this.queryParams.source = undefined
      this.queryParams.severity = undefined
      this.queryParams.status = undefined
      this.queryParams.timeRange = []
      this.handleQuery()
    },

    // 查看告警详情
    handleView(row) {
      this.currentAlert = row
      this.dialogVisible = true
    },

    // 处理告警
    handleResolve(row) {
      this.currentAlert = row
      this.resolveForm.action = 'resolve'
      this.resolveForm.note = ''
      this.resolveDialogVisible = true
    },

    // 提交处理
    async submitResolve() {
      try {
        // 模拟 API 延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        this.showMessage('success', '处理成功')
        this.resolveDialogVisible = false
        this.getList()
        this.getStatistics()
      } catch (error) {
        console.error('处理告警失败:', error)
        this.showMessage('error', '处理告警失败')
      }
    },

    // 删除告警
    handleDelete(row) {
      this.showConfirm('确认删除该告警吗？', '警告').then(async () => {
        try {
          // 模拟 API 延迟
          await new Promise(resolve => setTimeout(resolve, 500))
          this.showMessage('success', '删除成功')
          this.getList()
          this.getStatistics()
        } catch (error) {
          console.error('删除告警失败:', error)
          this.showMessage('error', '删除告警失败')
        }
      })
    },

    // 导出告警
    handleExport() {
      this.showMessage('success', '导出成功')
    },

    // 获取告警级别样式
    getSeverityType(severity) {
      const map = {
        critical: 'danger',
        warning: 'warning',
        info: 'info'
      }
      return map[severity] || 'info'
    },

    // 获取告警状态样式
    getStatusType(status) {
      const map = {
        pending: 'warning',
        resolved: 'success',
        ignored: 'info'
      }
      return map[status] || 'info'
    }
  },
  watch: {
    // 监听趋势时间范围变化
    trendTimeRange() {
      this.getTrend()
    }
  },
  mounted() {
    // 页面加载时获取数据
    this.getList()
    this.getStatistics()
    this.getTrend()
    this.getDistribution()
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .search-form {
    margin-bottom: 20px;
  }

  .statistics-cards {
    margin-bottom: 20px;

    .el-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .card-value {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        margin-top: 10px;
      }
    }
  }

  .chart-area {
    margin-bottom: 20px;

    .el-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  .alert-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .right-buttons {
        .el-button {
          margin-left: 10px;
        }
      }
    }

    .alert-detail {
      padding: 20px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>