<template>
  <div class="app-container alert-list-page">
    <!-- 视角切换Tab -->
    <el-card class="view-tab-card" shadow="never">
      <el-tabs v-model="activeView" @tab-click="handleViewChange" class="view-tabs">
        <el-tab-pane label="全局视角" name="global">
          <template slot="label">
            <i class="el-icon-view"></i>
            全局视角
          </template>
        </el-tab-pane>
        <el-tab-pane label="运维告警" name="ops">
          <template slot="label">
            <i class="el-icon-s-operation"></i>
            运维告警
          </template>
        </el-tab-pane>
        <el-tab-pane label="安全事件" name="security">
          <template slot="label">
            <i class="el-icon-warning"></i>
            安全事件
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      
      <!-- 基础搜索 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" class="search-form" @keyup.enter.native="handleQuery">
        <el-form-item label="告警源类型" prop="sourceTypes">
          <el-select v-model="queryParams.sourceTypes" placeholder="请选择告警源类型" clearable multiple collapse-tags style="width: 180px" @change="handleSourceTypeChange">
            <el-option
              v-for="option in filteredSourceTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="告警标题" prop="title">
          <el-input 
            v-model="queryParams.title" 
            placeholder="请输入告警标题" 
            clearable 
            style="width: 250px"
          />
        </el-form-item>
      
        <el-form-item label="严重程度" prop="severities">
          <el-select v-model="queryParams.severities" placeholder="请选择严重程度" clearable multiple collapse-tags style="width: 160px">
            <el-option
              v-for="option in severityOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="告警状态" prop="statuses">
          <el-select v-model="queryParams.statuses" placeholder="请选择告警状态" clearable multiple collapse-tags style="width: 160px">
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="告警对象" prop="alertObject">
          <el-input
            v-model="queryParams.alertObject"
            placeholder="输入key:value格式进行精确匹配，如：host:server1 或 service:web-api"
            clearable
            style="width: 300px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="时间范围" prop="timeRange">
          <el-date-picker
            v-model="queryParams.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :picker-options="pickerOptions"
            style="width: 350px"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button type="text" @click="toggleAdvancedSearch">
            {{ showAdvancedSearch ? '收起高级搜索' : '高级搜索' }}
            <i :class="showAdvancedSearch ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 高级搜索 -->
      <el-form :model="queryParams" ref="advancedForm" :inline="true" v-show="showAdvancedSearch" class="advanced-search-form" @keyup.enter.native="handleQuery">
        <el-form-item label="告警ID" prop="id">
          <el-input v-model="queryParams.id" placeholder="请输入告警ID" clearable style="width: 150px" />
        </el-form-item>
        <el-form-item label="告警源事件ID" prop="eventId">
          <el-input v-model="queryParams.eventId" placeholder="请输入告警源事件ID" clearable style="width: 180px" />
        </el-form-item>
        <el-form-item label="服务名称" prop="serviceName">
          <el-input v-model="queryParams.serviceName" placeholder="请输入服务名称" clearable style="width: 180px">
            <template slot="prepend">
              <el-tooltip content="通过tags.service字段搜索" placement="top">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="告警主机IP" prop="dstIp">
          <el-input v-model="queryParams.dstIp" placeholder="请输入告警主机IP地址" clearable style="width: 160px" />
        </el-form-item>
        <!-- 攻击主机IP相关字段只在安全视角显示 -->
        <el-form-item label="攻击主机IP" prop="srcIp" v-if="activeView !== 'ops'">
          <el-input v-model="queryParams.srcIp" placeholder="请输入攻击主机IP地址" clearable style="width: 160px" />
        </el-form-item>
        <el-form-item label="分配人" prop="assignedUserId">
          <el-select 
            v-model="queryParams.assignedUserId" 
            placeholder="请选择分配人" 
            clearable 
            filterable
            style="width: 180px"
          >
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.userName + (user.teamName ? ' (' + user.teamName + ')' : '')"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="认领人" prop="ackedBy">
          <el-select 
            v-model="queryParams.ackedBy" 
            placeholder="请选择认领人" 
            clearable 
            filterable
            style="width: 180px"
          >
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.userName + (user.teamName ? ' (' + user.teamName + ')' : '')"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理人" prop="resolvedBy">
          <el-select 
            v-model="queryParams.resolvedBy" 
            placeholder="请选择处理人" 
            clearable 
            filterable
            style="width: 180px"
          >
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.userName + (user.teamName ? ' (' + user.teamName + ')' : '')"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签搜索" prop="tagSearch" class="tag-search-item">
          <div class="tag-search-container">
            <div class="tag-search-input-wrapper">
              <el-input
                v-model="queryParams.tagSearch"
                placeholder="输入标签搜索，支持 AND/OR 逻辑，如：environment:production AND service:api" 
                clearable
                style="width: 450px"
                @keyup.enter="handleSearch"
                @focus="handleTagSearchFocus"
              >
                <template slot="prepend">
                  <el-select v-model="tagSearchType" style="width: 90px">
                    <el-option label="包含" value="contains" />
                    <el-option label="精确" value="exact" />
                  </el-select>
                </template>
                <template slot="append">
                  <el-tooltip content="保存当前标签到常用标签列表" placement="top" effect="light">
                    <el-button @click="saveCurrentTag" :disabled="!queryParams.tagSearch">
                      <i class="el-icon-plus"></i>
                    </el-button>
                  </el-tooltip>
                </template>
              </el-input>
            </div>
            
            <!-- 标签建议下拉框 -->
            <div v-show="showTagSuggestions && (savedTags.length > 0 || availableTags.length > 0)" 
                 class="tag-suggestions"
                 @mousedown.prevent
                 @mouseenter="cancelHideTagSuggestions"
                 @mouseleave="scheduleHideTagSuggestions">
              <div class="suggestion-section" v-if="savedTags.length > 0">
                <div class="section-title">
                  <span class="title-text">💖 我的常用标签</span>
                  <span class="tag-count">({{ savedTags.length }}/100)</span>
                </div>
                <div class="tag-list">
                  <el-tag 
                    v-for="(tag, index) in savedTags" 
                    :key="'saved-' + index"
                    size="small"
                    class="tag-item saved-tag"
                    @click="selectTag(tag)"
                    closable
                    @close="removeSavedTag(index)"
                    type="primary"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
              
              <div class="suggestion-section" v-if="availableTags.length > 0">
                <div class="section-title">
                  <span class="title-text">🏷️ 系统标签</span>
                </div>
                <div class="tag-list">
                  <el-tag 
                    v-for="(tag, index) in availableTags" 
                    :key="'available-' + index"
                    size="small"
                    type="info"
                    class="tag-item system-tag"
                    @click="selectTag(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
              
              <!-- 逻辑操作符标签 -->
              <div class="suggestion-section">
                <div class="section-title">
                  <span class="title-text">🔧 逻辑操作符</span>
                </div>
                <div class="tag-list">
                  <el-tag 
                    v-for="operator in ['AND', 'OR']" 
                    :key="'operator-' + operator"
                    size="small"
                    type="warning"
                    class="tag-item operator-tag"
                    @click="selectTag(operator)"
                  >
                    {{ operator }}
                  </el-tag>
                </div>
              </div>
              
              <!-- 常用搜索模板 -->
              <div class="suggestion-section">
                <div class="section-title">
                  <span class="title-text">📋 搜索模板</span>
                </div>
                <div class="template-list">
                  <div 
                    v-for="template in searchTemplates" 
                    :key="template.value"
                    class="template-item"
                    @click="selectTag(template.value)"
                  >
                    <span class="template-label">{{ template.label }}</span>
                    <span class="template-desc">{{ template.desc }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="toolbar-card" shadow="never">
      <el-row :gutter="10" class="toolbar-row">
        <el-col :span="12">
          <el-button-group>
            <el-button type="primary" icon="el-icon-refresh" @click="handleRefresh">刷新</el-button>
            <el-button type="success" icon="el-icon-check" @click="handleBatchResolve" :disabled="!hasSelection">批量处理</el-button>
            <!-- <el-button type="info" icon="el-icon-download" @click="handleExport">导出</el-button> -->
            <!-- <el-button type="warning" icon="el-icon-document" @click="handleFlashDutyExport">导出告警数据</el-button> -->
          </el-button-group>
        </el-col>
        <el-col :span="12" class="toolbar-right">
          <el-tooltip content="自动刷新" placement="top">
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              @change="toggleAutoRefresh"
            ></el-switch>
          </el-tooltip>
          <!-- <el-divider direction="vertical"></el-divider>
          <el-tooltip content="显示/隐藏搜索" placement="top">
            <el-button icon="el-icon-search" circle @click="showSearch = !showSearch"></el-button>
          </el-tooltip> -->
          <!-- <el-tooltip content="列表设置" placement="top">
            <el-button icon="el-icon-setting" circle @click="handleColumnSetting"></el-button>
          </el-tooltip> -->
        </el-col>
      </el-row>
    </el-card>

    <!-- 告警列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="alertList"
        @selection-change="handleSelectionChange"
        class="alert-table"
        stripe
        border
        ref="alertTable"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="告警标题" prop="title" :show-overflow-tooltip="true" min-width="250">
          <template slot-scope="scope">
            <div class="alert-title">
              <el-link type="primary" @click="handleView(scope.row)" class="title-link">
                {{ scope.row.title }}
              </el-link>
              <div class="alert-meta">
                <el-tag v-if="scope.row.alertType" size="mini" type="info">{{ scope.row.alertType }}</el-tag>
                <span class="event-id" v-if="scope.row.eventId">#{{ scope.row.eventId }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="严重程度" prop="severity" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getSeverityType(scope.row.severity)" size="small">
              {{ getSeverityText(scope.row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="告警源" prop="sourceType" width="120" align="center">
          <template slot-scope="scope">
            <div class="source-info">
              <el-tag :type="getSourceTypeColor(scope.row.sourceType)" size="small">
                {{ getSourceTypeName(scope.row.sourceType) }}
              </el-tag>
              <div class="source-detail" v-if="scope.row.sourceName">
                {{ scope.row.sourceName }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="告警对象" prop="alertObject" width="150" align="center">
          <template slot-scope="scope">
            <div class="alert-object-display">
              <template v-if="scope.row.alertObject && scope.row.alertObject.trim()">
                <div v-if="isValidJson(scope.row.alertObject)" class="alert-object-tags">
                  <el-tooltip
                    v-for="(value, key) in parseAlertObject(scope.row.alertObject)"
                    :key="key"
                    :content="`${key}:${value}`"
                    effect="dark"
                    placement="top"
                  >
                    <el-tag
                      size="mini"
                      type="primary"
                      class="alert-object-tag"
                    >
                      {{ key }}:{{ value }}
                    </el-tag>
                  </el-tooltip>
                </div>
                <div v-else class="alert-object-raw">
                  <el-tag size="mini" type="info">{{ scope.row.alertObject }}</el-tag>
                </div>
              </template>
              <span v-else class="no-alert-object">-</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="服务/组件" prop="serviceName" width="150" align="center">
          <template slot-scope="scope">
            <div class="service-info">
              <div v-if="getServiceFromTags(scope.row.tags)" class="service-name">
                <el-tag size="mini" type="primary">{{ getServiceFromTags(scope.row.tags) }}</el-tag>
              </div>
              <div v-if="scope.row.ruleName" class="rule-name">{{ scope.row.ruleName }}</div>
              <div v-if="scope.row.groupName" class="group-name">{{ scope.row.groupName }}</div>
              <div v-if="!getServiceFromTags(scope.row.tags) && !scope.row.ruleName && !scope.row.groupName" class="no-service">-</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="环境/位置" prop="environment" width="140" align="center">
          <template slot-scope="scope">
            <div class="location-info">
              <!-- 优先显示tags中的环境信息 -->
              <div v-if="getTargetLocationInfo(scope.row)" class="location-tags">
                <el-tag v-if="getTargetLocationInfo(scope.row).environment" 
                        size="mini" 
                        :type="getEnvironmentColor(getTargetLocationInfo(scope.row).environment)">
                  {{ getTargetLocationInfo(scope.row).environment }}
                </el-tag>
                <div v-if="getTargetLocationInfo(scope.row).datacenter" class="datacenter-info">
                  <i class="el-icon-office-building"></i>
                  {{ getTargetLocationInfo(scope.row).datacenter }}
                </div>
                <div v-if="getTargetLocationInfo(scope.row).region" class="region-info">
                  <i class="el-icon-location"></i>
                  {{ getTargetLocationInfo(scope.row).region }}
                </div>
              </div>
              <!-- 基于IP推断环境，但办公区优先显示tags环境 -->
              <div v-else-if="getEnvironmentByIP(scope.row.dstIp)" class="ip-based-env">
                <el-tag size="mini" :type="getIPBasedEnvironmentColor(scope.row)">
                  {{ getIPBasedEnvironmentText(scope.row) }}
                </el-tag>
                <div v-if="getEnvironmentByIP(scope.row.dstIp).region" class="region-info">
                  <i class="el-icon-location"></i>
                  {{ getEnvironmentByIP(scope.row.dstIp).region }}
                </div>
              </div>
              <!-- 无环境信息时显示目标IP -->
              <div v-else-if="scope.row.dstIp" class="target-ip-info">
                <el-tag size="mini" type="info">{{ scope.row.dstIp }}</el-tag>
              </div>
              <span v-else class="no-location">-</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="告警主机" prop="dstIp" width="160" align="center">
          <template slot-scope="scope">
            <div class="ip-info">
              <div v-if="scope.row.dstIp" class="ip-address">
                <el-tag size="small" type="success">
                  {{ scope.row.dstIp }}{{ scope.row.dstPort ? ':' + scope.row.dstPort : '' }}
                </el-tag>
              </div>
              <span v-else class="no-ip">-</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="攻击主机" prop="srcIp" width="160" align="center" v-if="activeView !== 'ops'">
          <template slot-scope="scope">
            <div class="ip-info">
              <div v-if="scope.row.srcIp" class="ip-address">
                <el-tag size="small" type="danger">
                  {{ scope.row.srcIp }}{{ scope.row.srcPort ? ':' + scope.row.srcPort : '' }}
                </el-tag>
              </div>
              <span v-else class="no-ip">-</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="标签" prop="tags" width="200" align="center">
          <template slot-scope="scope">
            <div class="tags-display">
              <template v-if="getTagsForDisplay(scope.row.tags).length > 0">
                <!-- 显示前3个标签 -->
                <el-tag
                  v-for="(tag, index) in getTagsForDisplay(scope.row.tags).slice(0, 3)"
                  :key="index"
                  size="mini"
                  :type="getTagTypeByKey(tag.key)"
                  class="tag-item"
                >
                  {{ tag.key }}:{{ tag.value }}
                </el-tag>
                <!-- 如果有更多标签，显示更多按钮 -->
                <el-popover
                  v-if="getTagsForDisplay(scope.row.tags).length > 3"
                  placement="top"
                  width="300"
                  trigger="hover"
                >
                  <div class="all-tags">
                    <el-tag
                      v-for="(tag, index) in getTagsForDisplay(scope.row.tags)"
                      :key="index"
                      size="mini"
                      :type="getTagTypeByKey(tag.key)"
                      class="tag-item"
                    >
                      {{ tag.key }}:{{ tag.value }}
                    </el-tag>
                  </div>
                  <el-tag slot="reference" size="mini" type="info" class="more-tags">
                    +{{ getTagsForDisplay(scope.row.tags).length - 3 }}
                  </el-tag>
                </el-popover>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="发生时间" prop="occurredAt" width="160" align="center">
          <template slot-scope="scope">
            <div class="time-info">
              <div class="occurred-time">{{ parseTime(scope.row.occurredAt) }}</div>
              <div class="time-ago">{{ getTimeAgo(scope.row.occurredAt) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="认领时间" prop="ackedAt" width="160" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.ackedAt" class="time-info">
              <div class="occurred-time">{{ parseTime(scope.row.ackedAt) }}</div>
              <div class="time-ago">{{ getTimeAgo(scope.row.ackedAt) }}</div>
            </div>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column label="解决时间" prop="resolvedAt" width="160" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.resolvedAt" class="time-info">
              <div class="occurred-time">{{ parseTime(scope.row.resolvedAt) }}</div>
              <div class="time-ago">{{ getTimeAgo(scope.row.resolvedAt) }}</div>
            </div>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column label="持续时间" width="120" align="center">
          <template slot-scope="scope">
            {{ getDurationText(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="人员流转" prop="personnel" width="180" align="center">
          <template slot-scope="scope">
            <div class="personnel-flow">
              <!-- 分配人信息 -->
              <div v-if="scope.row.assignedUserNames" class="personnel-item assignee">
                <div class="personnel-avatar">
                  <i class="el-icon-user-solid"></i>
                </div>
                <div class="personnel-content">
                  <el-tooltip :content="scope.row.assignedUserNames" placement="top" :disabled="!isTextOverflow(scope.row.assignedUserNames)">
                    <div class="personnel-name">{{ scope.row.assignedUserNames }}</div>
                  </el-tooltip>
                  <div class="personnel-role">分配</div>
                </div>
              </div>
              
              <!-- 认领人信息 -->
              <div v-if="scope.row.ackedByName" class="personnel-item claimer">
                <div class="personnel-avatar">
                  <i class="el-icon-user"></i>
                </div>
                <div class="personnel-content">
                  <el-tooltip :content="scope.row.ackedByName" placement="top" :disabled="!isTextOverflow(scope.row.ackedByName)">
                    <div class="personnel-name">{{ scope.row.ackedByName }}</div>
                  </el-tooltip>
                  <div class="personnel-role">认领</div>
                </div>
              </div>
              
              <!-- 处理人信息 -->
              <div v-if="scope.row.resolvedByName" class="personnel-item resolver">
                <div class="personnel-avatar">
                  <i class="el-icon-check"></i>
                </div>
                <div class="personnel-content">
                  <el-tooltip :content="scope.row.resolvedByName" placement="top" :disabled="!isTextOverflow(scope.row.resolvedByName)">
                    <div class="personnel-name">{{ scope.row.resolvedByName }}</div>
                  </el-tooltip>
                  <div class="personnel-role">处理</div>
                </div>
              </div>
              
              <!-- 无人员信息时显示 -->
              <div v-if="!scope.row.assignedUserNames && !scope.row.ackedByName && !scope.row.resolvedByName" class="personnel-item empty">
                <div class="personnel-avatar">
                  <i class="el-icon-user"></i>
                </div>
                <div class="personnel-content">
                  <div class="personnel-name">待分配</div>
                  <div class="personnel-role">未处理</div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
            <el-button size="mini" type="text" icon="el-icon-time" @click="handleTimeline(scope.row)" title="查看故障时间线">时间线</el-button>
            <el-button 
              v-if="!scope.row.ackedByName && !isResolved(scope.row.status)" 
              size="mini" 
              type="text" 
              icon="el-icon-user" 
              @click="handleClaim(scope.row)"
              style="color: #e6a23c;"
            >
              认领
            </el-button>
            <el-button v-if="!isResolved(scope.row.status)" size="mini" type="text" icon="el-icon-check" @click="handleResolve(scope.row)" style="color: #67c23a;">处理</el-button>
<!--            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" style="color: #f56c6c;">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    </el-card>


    <!-- 告警详情抽屉 -->
    <el-drawer
      title="告警详情"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%"
      :before-close="handleDrawerClose"
      :destroy-on-close="true"
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
    >
      <div class="alert-detail-drawer" v-if="currentAlert.id">
        <!-- 告警基本信息 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">基本信息</span>
            <el-tag :type="getStatusType(currentAlert.status)" size="small">
              {{ getStatusText(currentAlert.status) }}
            </el-tag>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="告警标题">
              <span class="alert-title-text">{{ currentAlert.title }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="事件ID">
              <el-tag size="mini" type="info" v-if="currentAlert.eventId">#{{ currentAlert.eventId }}</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="严重程度">
              <el-tag :type="getSeverityType(currentAlert.severity)" size="small">
                {{ getSeverityText(currentAlert.severity) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="告警分类">
              <el-tag :type="currentAlert.alertCategory === 'security' ? 'danger' : 'warning'" size="small">
                {{ currentAlert.alertCategory === 'security' ? '安全告警' : '运维告警' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="告警类型">
              <el-tag size="mini" type="info" v-if="currentAlert.alertType">{{ currentAlert.alertType }}</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="告警对象" v-if="currentAlert.alertObject && currentAlert.alertObject.trim()">
              <div class="alert-object-display">
                <template v-if="isValidJson(currentAlert.alertObject)">
                  <div class="alert-object-tags">
                    <el-tooltip
                      v-for="(value, key) in parseAlertObject(currentAlert.alertObject)"
                      :key="key"
                      :content="`${key}: ${value}`"
                      effect="dark"
                      placement="top"
                    >
                      <el-tag
                        size="mini"
                        type="primary"
                        class="alert-object-tag"
                        style="margin: 2px 4px 2px 0;"
                      >
                        {{ key }}: {{ value }}
                      </el-tag>
                    </el-tooltip>
                  </div>
                </template>
                <template v-else>
                  <el-tag size="mini" type="info">{{ currentAlert.alertObject }}</el-tag>
                </template>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              <div class="alert-description">
                {{ currentAlert.description || '-' }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 告警来源信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <span class="card-title">来源信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="告警源">
              <div>
                <el-tag :type="getSourceTypeColor(currentAlert.sourceType)" size="small">
                  {{ getSourceTypeName(currentAlert.sourceType) }}
                </el-tag>
                <el-tag v-if="currentAlert.sourceSubType" size="mini" type="info" style="margin-left: 5px;">
                  {{ currentAlert.sourceSubType }}
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="源实例">
              {{ currentAlert.sourceName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="源标识">
              {{ currentAlert.sourceIdent || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="输入标识">
              {{ currentAlert.inputIdent || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="规则名称">
              {{ currentAlert.ruleName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="组名称">
              {{ currentAlert.groupName || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 网络信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;" v-if="currentAlert.srcIp || currentAlert.dstIp">
          <div slot="header" class="card-header">
            <span class="card-title">网络信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="源IP地址">
              <el-tag size="mini" type="warning" v-if="currentAlert.srcIp">{{ currentAlert.srcIp }}</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="源端口">
              <el-tag size="mini" type="info" v-if="currentAlert.srcPort">{{ currentAlert.srcPort }}</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="目标IP地址">
              <el-tag size="mini" type="danger" v-if="currentAlert.dstIp">{{ currentAlert.dstIp }}</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="目标端口">
              <el-tag size="mini" type="info" v-if="currentAlert.dstPort">{{ currentAlert.dstPort }}</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="URL" :span="2" v-if="currentAlert.url">
              <el-link type="primary" :href="currentAlert.url" target="_blank">{{ currentAlert.url }}</el-link>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 时间信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <span class="card-title">时间信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="发生时间">
              <span class="time-text">{{ formatDateTime(currentAlert.occurredAt) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="检测时间">
              <span class="time-text">{{ formatDateTime(currentAlert.detectedAt) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="认领时间" v-if="currentAlert.ackedAt">
              <span class="time-text">{{ formatDateTime(currentAlert.ackedAt) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="解决时间" v-if="currentAlert.resolvedAt">
              <span class="time-text">{{ formatDateTime(currentAlert.resolvedAt) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="持续时间" :span="2">
              <el-tag :type="getDurationTagType(currentAlert.durationMs || calculateDuration(currentAlert))" size="small">
                {{ formatDuration(currentAlert.durationMs || calculateDuration(currentAlert)) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 人员信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;" v-if="currentAlert.assignedUserNames || currentAlert.ackedByName || currentAlert.resolvedByName">
          <div slot="header" class="card-header">
            <span class="card-title">人员信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="分配人员" v-if="currentAlert.assignedUserNames">
              <el-tag size="small" type="warning">{{ currentAlert.assignedUserNames }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="认领人员" v-if="currentAlert.ackedByName">
              <el-tag size="small" type="primary">{{ currentAlert.ackedByName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="处理人员" v-if="currentAlert.resolvedByName">
              <el-tag size="small" type="success">{{ currentAlert.resolvedByName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="协作空间" v-if="currentAlert.channelName">
              <el-tag size="small" type="info">{{ currentAlert.channelName }}</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 处理信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;" v-if="currentAlert.resolutionNote || currentAlert.rootCause">
          <div slot="header" class="card-header">
            <span class="card-title">处理信息</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="处理说明" v-if="currentAlert.resolutionNote">
              <div class="resolution-text">{{ currentAlert.resolutionNote }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="根因分析" v-if="currentAlert.rootCause">
              <div class="root-cause-text">{{ currentAlert.rootCause }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 标签信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 20px;" v-if="currentAlert.tags && currentAlert.tags !== '{}'">
          <div slot="header" class="card-header">
            <span class="card-title">标签信息</span>
          </div>
          <div class="tags-container">
            <el-tag
              v-for="(value, key) in parseTags(currentAlert.tags)"
              :key="key"
              size="small"
              style="margin: 2px 4px 2px 0;"
            >
              {{ key }}: {{ value }}
            </el-tag>
          </div>
        </el-card>

        <!-- 告警元数据(原始数据) -->
        <el-card class="detail-card json-card" shadow="hover" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-document"></i> 原始数据
            </span>
            <div class="header-controls">
              <el-input
                v-if="currentAlert.rawData"
                placeholder="搜索字段..."
                v-model="jsonSearchQuery"
                prefix-icon="el-icon-search"
                clearable
                size="mini"
                style="width: 200px; margin-right: 10px;"
                @input="highlightJsonSearch"
              ></el-input>
              <el-dropdown size="mini" split-button type="primary" @command="handleJsonViewCommand">
                视图选项
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="expand">{{ rawDataExpanded ? '收起' : '展开全部' }}</el-dropdown-item>
                  <el-dropdown-item command="format">重新格式化</el-dropdown-item>
                  <el-dropdown-item command="tree" :disabled="!supportsTreeView">树形视图</el-dropdown-item>
                  <el-dropdown-item command="plain" :disabled="!supportsTreeView">普通视图</el-dropdown-item>
                  <el-dropdown-item divided command="copy">复制到剪贴板</el-dropdown-item>
                  <el-dropdown-item command="download">下载JSON文件</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div v-if="currentAlert.rawData" class="json-container">
            <div class="json-controls">
              <el-slider
                v-model="jsonFontSize"
                :min="10"
                :max="18"
                :step="1"
                :format-tooltip="value => `${value}px`"
                @change="updateJsonStyles"
              ></el-slider>
              <div class="search-results" v-if="jsonSearchResults.length > 0">
                发现 {{ jsonSearchResults.length }} 处匹配
                <el-button-group class="search-navigation">
                  <el-button
                    size="mini"
                    icon="el-icon-arrow-up"
                    :disabled="jsonSearchIndex <= 0"
                    @click="navigateJsonSearch('prev')"
                  ></el-button>
                  <el-button
                    size="mini"
                    icon="el-icon-arrow-down"
                    :disabled="jsonSearchIndex >= jsonSearchResults.length - 1"
                    @click="navigateJsonSearch('next')"
                  ></el-button>
                </el-button-group>
                <span class="search-index" v-if="jsonSearchResults.length > 0">
                  {{ jsonSearchIndex + 1 }}/{{ jsonSearchResults.length }}
                </span>
              </div>
            </div>
            <div class="json-wrapper" :style="{ 'max-height': rawDataExpanded ? 'none' : '400px' }">
              <pre
                ref="jsonViewer"
                class="raw-data-content"
                :class="{
                  'expanded': rawDataExpanded,
                  'syntax-highlight': enableSyntaxHighlight,
                  'dark-theme': jsonDarkTheme
                }"
                :style="{ 'font-size': `${jsonFontSize}px` }"
                v-html="highlightedJson"
              ></pre>
            </div>
            <div class="json-footer">
              <el-switch
                v-model="enableSyntaxHighlight"
                active-text="语法高亮"
                inactive-text="普通文本"
                @change="updateJsonDisplay"
              ></el-switch>
              <el-switch
                v-model="jsonDarkTheme"
                active-text="暗色主题"
                inactive-text="亮色主题"
                @change="updateJsonDisplay"
              ></el-switch>
              <el-tooltip content="在新窗口中查看" placement="top">
                <el-button
                  size="mini"
                  icon="el-icon-full-screen"
                  circle
                  @click="openJsonInNewWindow"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
          <div v-else class="empty-data">
            <i class="el-icon-document"></i>
            <p>暂无原始数据</p>
          </div>
        </el-card>
      </div>

      <!-- 抽屉底部操作按钮 -->
      <div class="drawer-footer">
        <el-button @click="drawerVisible = false">关闭</el-button>
        <el-button 
          v-if="!currentAlert.ackedByName && !isResolved(currentAlert.status)" 
          type="warning" 
          @click="handleClaim(currentAlert)"
        >
          <i class="el-icon-user"></i> 认领告警
        </el-button>
        <el-button v-if="!isResolved(currentAlert.status)" type="primary" @click="handleResolve(currentAlert)">处理告警</el-button>
        <!-- <el-button type="danger" @click="handleDelete(currentAlert)">删除告警</el-button> -->
      </div>
    </el-drawer>

    <!-- 查看告警详情对话框 -->
    <el-dialog
      title="告警详情"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="告警来源">{{ currentAlert.source }}</el-descriptions-item>
        <el-descriptions-item label="告警级别">
          <el-tag :type="getSeverityType(currentAlert.severity)">
            {{ currentAlert.severity }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警状态">
          <el-tag :type="getStatusType(currentAlert.status)">
            {{ currentAlert.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警时间">{{ currentAlert.alertTime }}</el-descriptions-item>
        <el-descriptions-item label="告警描述" :span="2">{{ currentAlert.description }}</el-descriptions-item>
        <el-descriptions-item label="告警详情" :span="2">{{ currentAlert.details }}</el-descriptions-item>
        <el-descriptions-item label="处理记录" :span="2" v-if="currentAlert.status !== 'pending'">
          {{ currentAlert.resolutionNote }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 处理告警对话框 -->
    <el-dialog
      title="处理告警"
      :visible.sync="resolveDialogVisible"
      width="50%"
      :close-on-click-modal="false"
    >
      <div class="resolve-alert-container">
        <!-- 告警信息摘要 -->
        <div class="alert-summary">
          <div class="alert-title">
            <span class="label">告警:</span>
            <span class="value">{{ currentAlert.title }}</span>
            <el-tag size="mini" :type="getSeverityType(currentAlert.severity)">
              {{ getSeverityText(currentAlert.severity) }}
            </el-tag>
          </div>
          <div class="alert-meta">
            <div class="meta-item">
              <span class="label">来源:</span>
              <el-tag size="mini" :type="getSourceTypeColor(currentAlert.sourceType)">
                {{ getSourceTypeName(currentAlert.sourceType) }}
              </el-tag>
            </div>
            <div class="meta-item">
              <span class="label">ID:</span>
              <span class="value">{{ currentAlert.id }}</span>
            </div>
            <div class="meta-item">
              <span class="label">时间:</span>
              <span class="value">{{ parseTime(currentAlert.occurredAt) }}</span>
            </div>
          </div>
        </div>

        <el-divider content-position="center">处理方式</el-divider>

        <el-form :model="resolveForm" label-width="100px">
          <el-form-item label="选择操作">
            <div class="action-buttons">
              <el-button 
                :type="resolveForm.action === 'resolve' ? 'primary' : 'default'"
                :class="{ 'is-active': resolveForm.action === 'resolve' }"
                @click="resolveForm.action = 'resolve'"
                class="action-btn"
              >
                <i class="el-icon-check"></i> 已解决
              </el-button>
              <el-button 
                :type="resolveForm.action === 'ignore' ? 'primary' : 'default'"
                :class="{ 'is-active': resolveForm.action === 'ignore' }"
                @click="resolveForm.action = 'ignore'"
                class="action-btn"
              >
                <i class="el-icon-minus-circle"></i> 静默忽略
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="处理说明">
            <el-input
              type="textarea"
              v-model="resolveForm.resolutionNote"
              :rows="4"
              placeholder="请输入处理说明"
            />
          </el-form-item>
          <el-form-item label="根因分析" v-if="resolveForm.action === 'resolve'">
            <el-input
              type="textarea"
              v-model="resolveForm.rootCause"
              :rows="3"
              placeholder="请输入根因分析（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="resolveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitResolve" :loading="resolveSubmitting">
          {{ getResolveActionText() }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量处理对话框 -->
    <el-dialog
      title="批量处理告警"
      :visible.sync="batchResolveDialogVisible"
      width="45%"
      :close-on-click-modal="false"
    >
      <div class="batch-resolve-container">
        <div class="batch-alert-summary">
          <p><strong>待处理告警数量：</strong>{{ batchProcessRows.length }} 条</p>
          <p v-if="batchSkippedCount > 0" class="skipped-info">
            <i class="el-icon-info"></i> 
            已跳过 {{ batchSkippedCount }} 条已处理的告警
          </p>
        </div>

        <el-divider content-position="center">处理方式</el-divider>

        <el-form :model="batchResolveForm" label-width="100px">
          <el-form-item label="处理方式">
            <div class="action-buttons">
              <el-button 
                :type="batchResolveForm.action === 'resolve' ? 'primary' : 'default'"
                :class="{ 'is-active': batchResolveForm.action === 'resolve' }"
                @click="batchResolveForm.action = 'resolve'"
                class="action-btn"
              >
                <i class="el-icon-check"></i> 批量解决
              </el-button>
              <el-button 
                :type="batchResolveForm.action === 'ignore' ? 'primary' : 'default'"
                :class="{ 'is-active': batchResolveForm.action === 'ignore' }"
                @click="batchResolveForm.action = 'ignore'"
                class="action-btn"
              >
                <i class="el-icon-minus-circle"></i> 批量静默忽略
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="处理说明">
            <el-input
              type="textarea"
              v-model="batchResolveForm.resolutionNote"
              :rows="3"
              placeholder="请输入批量处理说明（可选）"
            />
          </el-form-item>
          <el-form-item label="根因分析" v-if="batchResolveForm.action === 'resolve'">
            <el-input
              type="textarea"
              v-model="batchResolveForm.rootCause"
              :rows="3"
              placeholder="请输入根因分析（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="batchResolveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeBatchProcess" :loading="batchProcessing">
          {{ getBatchActionText() }}
        </el-button>
      </div>
    </el-dialog>

    <!-- FlashDuty导出对话框 -->
    <el-dialog
      title="导出FlashDuty告警数据"
      :visible.sync="exportDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      custom-class="export-dialog"
    >
      <div class="export-content">
        <!-- 导出类型选择 -->
        <div class="form-group" style="margin-bottom: 15px;">
          <label class="form-label" >
            <i class="el-icon-menu" style="margin-bottom: 15px;"></i>
            导出类型
          </label>
          <el-radio-group v-model="exportForm.exportType" class="radio-group">
            <el-radio label="all" class="radio-button">
              <div class="radio-content">
                <i class="el-icon-document-copy"></i>
                <span>告警值班数据</span>
              </div>
            </el-radio>
            <el-radio label="top20" class="radio-button">
              <div class="radio-content">
                <i class="el-icon-pie-chart"></i>
                <span>Top20告警数据</span>
              </div>
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 时间范围选择 -->
        <div class="form-group">
          <label class="form-label">
            <i class="el-icon-time" style="margin-bottom: 15px;"></i>
            时间范围
          </label>
          <el-date-picker
            v-model="exportForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
            :picker-options="exportPickerOptions"
            size="medium"
          />
          <div class="tips" style="margin-top: 15px;">
            <div class="tip-item">
              <i class="el-icon-info"></i>
              <span>默认近7天（昨天23:59截止）</span>
            </div>
            <div class="tip-item">
              <i class="el-icon-warning"></i>
              <span>大量数据导出需要时间</span>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">
          取 消
        </el-button>
        <el-button 
          type="primary" 
          @click="executeExport" 
          :loading="exporting"
          :disabled="!exportForm.timeRange || exportForm.timeRange.length !== 2"
        >
          <i class="el-icon-download"></i>
          {{ exporting ? '正在导出...' : '开始导出' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 故障时间线抽屉 -->
    <el-drawer
      title="故障时间线"
      :visible.sync="timelineVisible"
      direction="rtl"
      size="60%"
      :before-close="handleTimelineClose"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <IncidentTimeline 
        :event-id="timelineEventId"
        :fd-incident-id="timelineFdIncidentId"
        :alert-id="timelineAlertId"
        :alert-occurred-at="timelineAlertOccurredAt"
      />
    </el-drawer>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import RightToolbar from '@/components/RightToolbar'
import IncidentTimeline from '@/components/IncidentTimeline'
import { 
  listAlert, 
  getAlert, 
  processAlert, 
  ignoreAlertComplete, 
  batchProcessAlerts, 
  getAvailableTags, 
  generateTestData,
  exportAlert,
  exportFlashDutyAlerts,
  exportTop20Alerts,
  deleteAlert,
  reopenAlert,
  claimAlert,
  getUserList
} from '@/api/alert/alert'
import {
  AlertStatusEnum,
  AlertSeverityEnum,
  AlertTypeEnum,
  AlertSourceTypeEnum,
  EnvironmentEnum,
  getStatusType,
  getStatusText,
  getSeverityType,
  getSeverityText,
  getAlertTypeType,
  getAlertTypeText,
  getEnvironmentType,
  getSourceTypeText,
  getEnumOptions
} from '@/utils/alert-enums'

export default {
  name: "AlertList",
  components: {
    Pagination,
    RightToolbar,
    IncidentTimeline
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 显示高级搜索
      showAdvancedSearch: false,
      // 当前视角
      activeView: 'global', // global | ops | security
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        id: null,
        eventId: null,
        sourceTypes: [], // 告警源类型（支持多选）
        severities: [], // 严重程度（支持多选）
        statuses: [], // 告警状态（支持多选）
        serviceName: null,
        alertCategory: null,
        dstIp: null,
        srcIp: null,
        tagSearch: null,
        alertObject: null, // 告警对象搜索
        assignedUserId: null, // 分配人ID
        ackedBy: null, // 认领人ID
        resolvedBy: null, // 处理人ID
        timeRange: this.getDefaultTimeRange(), // 默认近一周
        ackedTimeRange: null, // 认领时间范围
        resolvedTimeRange: null // 解决时间范围
      },
      // 标签搜索类型
      tagSearchType: 'contains',
      // 标签搜索模式
      tagSearchMode: 'simple', // simple | advanced
      // 高级选择器状态
      showAdvancedSelector: false,
      activeTagTab: 'environment',
      selectedAdvancedTags: [],
      // 标签示例
      tagExamples: [
        { label: 'environment:production', value: 'environment:production' },
        { label: 'environment:staging', value: 'environment:staging' },
        { label: 'service:user-api', value: 'service:user-api' },
        { label: 'service:order-service', value: 'service:order-service' },
        { label: 'team:backend', value: 'team:backend' },
        { label: 'team:frontend', value: 'team:frontend' },
        { label: 'severity:critical', value: 'severity:critical' },
        { label: 'monitor_system:grafana', value: 'monitor_system:grafana' }
      ],
      // 搜索模板
      searchTemplates: [
        { 
          label: '生产环境告警', 
          value: 'environment:production', 
          desc: '查找生产环境的所有告警' 
        },
        { 
          label: '严重告警', 
          value: 'severity:critical OR severity:high', 
          desc: '查找严重或高危级别的告警' 
        },
        { 
          label: '后端服务告警', 
          value: 'team:backend AND environment:production', 
          desc: '查找生产环境中后端团队的告警' 
        },
        { 
          label: '用户服务问题', 
          value: 'service:user-api OR service:user-service', 
          desc: '查找用户相关服务的告警' 
        },
        { 
          label: '监控系统告警', 
          value: 'monitor_system:grafana OR monitor_system:prometheus', 
          desc: '查找来自监控系统的告警' 
        }
      ],
      // 枚举选项
      severityOptions: getEnumOptions(AlertSeverityEnum),
      statusOptions: getEnumOptions(AlertStatusEnum),
      sourceTypeOptions: getEnumOptions(AlertSourceTypeEnum),
      // 用户列表（用于下拉选择）
      userList: [],
      // 动态标签分类（从后端获取）
      environmentTags: [],
      serviceTags: [],
      teamTags: [],
      monitorTags: [],
      // 标签加载状态
      tagsLoading: false,
      // 数据列表
      alertList: [],
      // 选中的行
      selectedRows: [],
      // 自动刷新
      autoRefresh: false,
      refreshTimer: null,
      // 对话框控制
      dialogVisible: false,
      drawerVisible: false,
      resolveDialogVisible: false,
      currentAlert: {},
      resolveForm: {
        action: 'resolve',
        resolutionNote: '',
        rootCause: ''
        // flashDutyIncidentId现在直接从列表行数据获取，不需要在表单中存储
      },
      rawDataExpanded: false,
      jsonSearchQuery: '',
      jsonSearchResults: [],
      jsonSearchIndex: 0,
      enableSyntaxHighlight: true,
      jsonDarkTheme: false,
      jsonFontSize: 14,
      highlightedJson: '', // 将highlightedJson从computed属性改为data属性
      // 日期选择器选项
      pickerOptions: {
        shortcuts: [
          {
            text: '最近1小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '今天',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().toDateString());
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const end = new Date(new Date().toDateString());
              const start = new Date();
              start.setTime(end.getTime() - 3600 * 1000 * 24);
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近3天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      selectedTemplate: '',
      resolveSubmitting: false,
      batchResolveDialogVisible: false,
      batchProcessRows: [],
      batchSkippedCount: 0,
      batchResolveForm: {
        action: 'resolve',
        resolutionNote: '',
        rootCause: ''
        // flashDutyIncidentId现在直接从列表行数据获取，不需要在表单中存储
      },
      batchProcessing: false,
      showTagSuggestions: false,
      savedTags: [],
      availableTags: [],
      hideTagSuggestionsTimer: null, // 添加定时器变量
      logicalOperators: ['AND', 'OR', 'NOT'],
      // FlashDuty导出相关
      exportDialogVisible: false,
      exporting: false,
      exportForm: {
        exportType: 'all',
        timeRange: []
      },
      // 时间线相关
      timelineVisible: false,
      timelineEventId: '',
      timelineFdIncidentId: '',
      timelineAlertOccurredAt: '',
      timelineAlertId: '',
      // 导出时间选择器选项
      exportPickerOptions: {
        shortcuts: [
          {
            text: '昨天全天',
            onClick(picker) {
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              const start = new Date(yesterday);
              start.setHours(0, 0, 0, 0);
              const end = new Date(yesterday);
              end.setHours(23, 59, 59, 999);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近3天',
            onClick(picker) {
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              const end = new Date(yesterday);
              end.setHours(23, 59, 59, 999);
              const start = new Date(yesterday);
              start.setDate(start.getDate() - 2);
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一周(默认)',
            onClick(picker) {
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              const end = new Date(yesterday);
              end.setHours(23, 59, 59, 999);
              const start = new Date(yesterday);
              start.setDate(start.getDate() - 6);
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近两周',
            onClick(picker) {
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              const end = new Date(yesterday);
              end.setHours(23, 59, 59, 999);
              const start = new Date(yesterday);
              start.setDate(start.getDate() - 13);
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              const end = new Date(yesterday);
              end.setHours(23, 59, 59, 999);
              const start = new Date(yesterday);
              start.setDate(start.getDate() - 29);
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '今天实时',
            onClick(picker) {
              const today = new Date();
              const start = new Date(today);
              start.setHours(0, 0, 0, 0);
              const end = new Date(); // 当前时间
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      }
    };
  },
  computed: {
    // 是否有选中的行
    hasSelection() {
      return this.selectedRows.length > 0;
    },
    // 高级模式显示值
    advancedDisplayValue() {
      if (this.selectedAdvancedTags.length === 0) {
        return ''
      } else if (this.selectedAdvancedTags.length === 1) {
        return this.selectedAdvancedTags[0]
      } else {
        return `${this.selectedAdvancedTags.length} 个标签已选择`
      }
    },
    supportsTreeView() {
      return this.currentAlert.rawData && typeof this.currentAlert.rawData === 'object';
    },
    // 是否为开发环境
    isDevelopment() {
      return process.env.NODE_ENV === 'development';
    },
    // 根据当前TAB过滤告警源类型选项
    filteredSourceTypeOptions() {
      const opsSourceTypes = ['grafana', 'zabbix', 'n9e', 'huawei_ces', 'skywalking', 'standard'];
      const securitySourceTypes = ['wangsu_waf', 'safeline_waf', 'honeypot', 'muyun', 'tdp', 'zeek', 'suricata'];
      
      switch (this.activeView) {
        case 'ops':
          return this.sourceTypeOptions.filter(option => opsSourceTypes.includes(option.value));
        case 'security':
          return this.sourceTypeOptions.filter(option => securitySourceTypes.includes(option.value));
        case 'global':
        default:
          return this.sourceTypeOptions;
      }
    }
  },
  watch: {
    // 监听当前告警变化，更新JSON显示
    'currentAlert.rawData': {
      handler(newVal) {
        if (newVal) {
          this.updateJsonDisplay();
        }
      },
      immediate: true
    },
    
    // 监听抽屉显示状态，管理遮罩层
    drawerVisible: {
      handler(newVal, oldVal) {
        if (!newVal && oldVal) {
          // 抽屉关闭时，延迟清理遮罩层
          setTimeout(() => {
            this.removeOverlayMask();
          }, 300); // 等待动画完成
        }
      },
      immediate: false
    }
  },
  mounted() {
    // 从URL query参数获取参数并设置到查询条件中
    this.initQueryParamsFromUrl();
    
    // 初始化保存的标签
    this.loadSavedTags();
    
    this.getList();
    this.loadAvailableTags();

    // 从URL query参数获取flashDutyIncidentId
    this.initFlashDutyIncidentId();

    // 初始化JSON显示
    this.$nextTick(() => {
      if (this.currentAlert && this.currentAlert.rawData) {
        this.updateJsonDisplay();
      }
    });
    
    // 添加全局点击事件监听，用于点击空白处关闭标签下拉框
    document.addEventListener('click', this.handleGlobalClick);
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    // 清理标签建议下拉框的定时器
    if (this.hideTagSuggestionsTimer) {
      clearTimeout(this.hideTagSuggestionsTimer);
    }
    // 清理全局点击事件监听
    document.removeEventListener('click', this.handleGlobalClick);
    
    // 确保关闭所有对话框和抽屉
    this.drawerVisible = false;
    this.dialogVisible = false;
    this.resolveDialogVisible = false;
    this.batchResolveDialogVisible = false;
  },
  methods: {
    // 使用导入的枚举函数
    getStatusType,
    getStatusText,
    getSeverityType,
    getSeverityText,
    getAlertTypeType,
    getAlertTypeText,
    getEnvironmentType,
    getSourceTypeText,

    // 处理视角切换
    handleViewChange(tab) {
      // 根据视角设置告警分类过滤条件
      switch (tab.name) {
        case 'ops':
          this.queryParams.alertCategory = 'ops';
          break;
        case 'security':
          this.queryParams.alertCategory = 'security';
          break;
        case 'global':
        default:
          // 全局视角不设置分类过滤，显示所有告警
          delete this.queryParams.alertCategory;
          break;
      }
      
      // 切换视角时清空告警源类型选择，让用户重新选择
      this.queryParams.sourceTypes = [];
      
      // 重置页码并重新查询
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 切换高级搜索显示状态
    toggleAdvancedSearch() {
      this.showAdvancedSearch = !this.showAdvancedSearch;
    },

    // 获取告警列表
    getList() {
      this.loading = true;
      // 构建查询参数
      const params = { ...this.queryParams };
      // 处理时间范围参数
      if (params.timeRange && params.timeRange.length === 2) {
        params.startTime = new Date(params.timeRange[0]).getTime();
        params.endTime = new Date(params.timeRange[1]).getTime();
        delete params.timeRange;
      }
      // 处理标签搜索参数
      if (params.tagSearch && params.tagSearch.trim()) {
        params.tagSearchType = this.tagSearchType;
      }
      listAlert(params).then(response => {
        this.alertList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 获取用户列表
    getUserList() {
      getUserList().then(response => {
        if (response.code === 200) {
          this.userList = response.data;
        }
      });
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: null,
        id: null,
        eventId: null,
        sourceTypes: [], // 告警源类型（支持多选）
        severities: [], // 严重程度（支持多选）
        statuses: [], // 告警状态（支持多选）
        serviceName: null,
        alertCategory: null,
        dstIp: null,
        srcIp: null,
        tagSearch: null,
        alertObject: null, // 告警对象搜索
        assignedUserId: null, // 分配人ID
        ackedBy: null, // 认领人ID
        resolvedBy: null, // 处理人ID
        timeRange: this.getDefaultTimeRange(), // 重置时也设置默认时间范围
        ackedTimeRange: null, // 认领时间范围
        resolvedTimeRange: null // 解决时间范围
      };
      this.tagSearchType = 'contains';
      this.handleQuery();
    },

    // 刷新
    handleRefresh() {
      this.getList();
    },

    // 切换自动刷新
    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.refreshTimer = setInterval(() => {
          this.getList();
        }, 30000); // 30秒刷新一次
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer);
          this.refreshTimer = null;
        }
      }
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 批量处理告警
    handleBatchResolve() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要处理的告警');
        return;
      }

      // 检查是否有已解决的告警
      const unresolvedRows = this.selectedRows.filter(row => !this.isResolved(row.status));
      const resolvedRows = this.selectedRows.filter(row => this.isResolved(row.status));

      if (unresolvedRows.length === 0) {
        this.$message.warning('所选告警都已处理完成，无需重复处理');
        return;
      }

      // 检查选中的告警是否有FlashDuty故障ID
      const hasFlashDutyId = unresolvedRows.some(row => row.fdIncidentId);
      if (hasFlashDutyId) {
        console.log('批量处理告警，部分告警有FlashDuty故障ID:', 
          unresolvedRows.filter(row => row.fdIncidentId).map(row => ({id: row.id, fdIncidentId: row.fdIncidentId})));
      }

      // 设置批量处理数据
      this.batchProcessRows = unresolvedRows;
      this.batchSkippedCount = resolvedRows.length;
      this.batchResolveForm = {
        action: 'resolve',
        resolutionNote: '',
        rootCause: ''
      };
      this.batchResolveDialogVisible = true;
    },

    // 执行批量处理
    executeBatchProcess() {
      this.batchProcessing = true;
      const action = this.batchResolveForm.action;
      const note = this.batchResolveForm.resolutionNote;
      const actionText = action === 'resolve' ? '解决' : '静默忽略';
      
      // 提取告警ID列表，拼接成逗号分隔的字符串
      const ids = this.batchProcessRows.map(row => row.id).join(',');
      
      console.log('批量处理告警ID字符串:', ids);
      
      const data = {
        ids: ids,  // 后端期望逗号分隔的字符串
        action: action,
        resolutionNote: note || `批量${actionText}`,
        rootCause: this.batchResolveForm.rootCause,
        flashDutyIncidentId: ''  // 批量处理时使用空字符串
      };
      
      batchProcessAlerts(data).then(response => {
        if (response.code === 200) {
          const result = response.data;
          let message = `批量处理完成！\n`;
          message += `总计: ${result.totalCount} 条\n`;
          message += `成功: ${result.successCount} 条\n`;
          message += `失败: ${result.failedCount} 条`;
          
          if (result.alreadyResolvedCount > 0) {
            message += `\n已处理: ${result.alreadyResolvedCount} 条（跳过）`;
          }
          
          this.$message.success(message);
          
          // 关闭对话框
          this.batchResolveDialogVisible = false;
          
          // 刷新列表
          this.getList();
          
          // 清空选择
          this.selectedRows = [];
          if (this.$refs.alertTable) {
            this.$refs.alertTable.clearSelection();
          }
        } else {
          this.$message.error(response.msg || `批量${actionText}失败`);
        }
        this.batchProcessing = false;
      }).catch(error => {
        console.error('批量处理失败:', error);
        this.$message.error('批量处理失败');
        this.batchProcessing = false;
      });
    },

    // 查看告警详情
    handleView(row) {
      this.currentAlert = { ...row };
      this.drawerVisible = true;
    },

    // 关闭抽屉
    handleDrawerClose() {
      this.drawerVisible = false;
      this.currentAlert = {};
    },

    // 处理告警
    handleResolve(row) {
      this.currentAlert = row;
      this.resolveForm.action = 'resolve';
      this.resolveForm.resolutionNote = '';
      // flashDutyIncidentId现在直接从currentAlert.fdIncidentId获取，不需要单独设置
      this.resolveDialogVisible = true;
    },

    // 重新打开告警
    handleReopen(row) {
      this.$confirm('是否确认重新打开该告警?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        reopenAlert(row.id).then(response => {
          if (response.code === 200) {
            this.$message.success('告警已重新打开');
            this.getList();
          } else {
            this.$message.error(response.msg || '重开告警失败');
          }
        }).catch(error => {
          console.error('重开告警失败:', error);
          this.$message.error('重开告警失败');
        });
      });
    },

    // 更多操作
    handleMoreAction(command, row) {
      switch (command) {
        case 'assign':
          this.handleAssign(row);
          break;
        case 'note':
          this.handleAddNote(row);
          break;
        case 'silence':
          this.handleSilence(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },

    // 分配告警
    handleAssign(row) {
      this.$message.info('分配功能待实现');
    },

    // 添加备注
    handleAddNote(row) {
      this.$message.info('添加备注功能待实现');
    },

    // 静默告警
    handleSilence(row) {
      this.$message.info('静默功能待实现');
    },

    // 提交处理
    submitResolve() {
      const data = {
        resolutionNote: this.resolveForm.resolutionNote,
        rootCause: this.resolveForm.rootCause,
        flashDutyIncidentId: this.currentAlert.fdIncidentId || ''
      };
      
      // 添加日志，显示使用的flashDutyIncidentId
      console.log('处理告警，使用FlashDuty故障ID:', data.flashDutyIncidentId);
      console.log('当前告警信息:', this.currentAlert);
      
      let apiCall;
      if (this.resolveForm.action === 'ignore') {
        apiCall = ignoreAlertComplete(this.currentAlert.id, data);
      } else {
        apiCall = processAlert(this.currentAlert.id, data);
      }
      
      apiCall.then(response => {
        if (response.code === 200) {
          this.$message.success('处理成功');
          this.resolveDialogVisible = false;
          this.getList();
        } else {
          this.$message.error(response.msg || '处理失败');
        }
      }).catch(error => {
        console.error('处理告警失败:', error);
        this.$message.error('处理告警失败');
      });
    },

    // 删除告警
    handleDelete(row) {
      this.$confirm('确认删除该告警吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAlert(row.id).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功');
            this.getList();
          } else {
            this.$message.error(response.msg || '删除失败');
          }
        }).catch(error => {
          console.error('删除告警失败:', error);
          this.$message.error('删除告警失败');
        });
      });
    },

    // 导出告警
    handleExport() {
      exportAlert(this.queryParams).then(response => {
        const blob = new Blob([response.data]);
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '告警列表.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$message.success('导出成功');
      }).catch(error => {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      });
    },

    // 打开FlashDuty导出抽屉
    handleFlashDutyExport() {
      // 设置默认时间范围：结束时间是昨天23:59:59，开始时间是往前推7天的00:00:00
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1); // 昨天
      
      // 结束时间：昨天 23:59:59
      const endTime = new Date(yesterday);
      endTime.setHours(23, 59, 59, 999);
      
      // 开始时间：往前推7天的 00:00:00
      const startTime = new Date(yesterday);
      startTime.setDate(startTime.getDate() - 6); // 往前推6天（加上昨天共7天）
      startTime.setHours(0, 0, 0, 0);
      
      this.exportForm.timeRange = [
        this.formatDateTime(startTime),
        this.formatDateTime(endTime)
      ];
      
      this.exportDialogVisible = true;
    },

    // 执行导出
    executeExport() {
      let startTime = null;
      let endTime = null;
      
      // 处理时间范围
      if (this.exportForm.timeRange && this.exportForm.timeRange.length === 2) {
        startTime = Math.floor(new Date(this.exportForm.timeRange[0]).getTime() / 1000);
        endTime = Math.floor(new Date(this.exportForm.timeRange[1]).getTime() / 1000);
      }
      
      this.exporting = true;
      
      // 构建请求参数
      const params = {};
      if (startTime) {
        params.startTime = startTime;
      }
      if (endTime) {
        params.endTime = endTime;
      }
      
      // 根据导出类型选择API和文件名
      const isTop20 = this.exportForm.exportType === 'top20';
      const exportAPI = isTop20 ? exportTop20Alerts : exportFlashDutyAlerts;
      const exportTypeText = isTop20 ? 'Top20告警统计' : 'FlashDuty告警数据';
      
      // 调用相应的导出API
      exportAPI(params).then(response => {
        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        
        // 设置下载文件名
        const timeStr = this.exportForm.timeRange && this.exportForm.timeRange.length === 2 
          ? `_${this.formatDateForFileName(this.exportForm.timeRange[0])}_${this.formatDateForFileName(this.exportForm.timeRange[1])}`
          : '_近一周';
        link.download = `${exportTypeText}${timeStr}.xlsx`;
        
        // 触发下载
        link.click();
        window.URL.revokeObjectURL(link.href);
        
        this.exporting = false;
        this.exportDialogVisible = false;
        this.$message.success(`${exportTypeText}导出成功！`);
      }).catch(error => {
        console.error(`导出${exportTypeText}失败:`, error);
        this.exporting = false;
        
        if (error.response && error.response.status === 403) {
          this.$message.error('没有导出权限，请联系管理员');
        } else {
          this.$message.error(`导出${exportTypeText}失败，请稍后重试`);
        }
      });
    },

    // 格式化日期时间
    formatDateTime(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const seconds = String(d.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 格式化日期用于文件名
    formatDateForFileName(dateStr) {
      const d = new Date(dateStr);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      
      return `${year}${month}${day}`;
    },

    // 生成测试告警数据
    generateTestAlert() {
      const sources = ['system', 'security', 'network'];
      const severities = ['critical', 'warning', 'info'];
      const source = sources[Math.floor(Math.random() * sources.length)];
      const severity = severities[Math.floor(Math.random() * severities.length)];

      return {
        source,
        severity,
        status: 'pending',
        alertTime: new Date().toLocaleString(),
        description: `测试告警 - ${source} - ${severity}`,
        details: `这是一个测试告警，用于验证告警中心的功能。\n来源：${source}\n级别：${severity}\n时间：${new Date().toLocaleString()}`,
        resolutionNote: ''
      };
    },

    // 处理测试告警
    handleTest() {
      const testAlert = this.generateTestAlert();
      request({
        url: '/system/alert/test',
        method: 'post',
        data: testAlert
      }).then(response => {
        if (response.code === 200) {
          this.$message.success('测试告警发送成功');
          this.getList();
        } else {
          this.$message.error(response.msg || '测试告警发送失败');
        }
      }).catch(error => {
        console.error('发送测试告警失败:', error);
        this.$message.error('发送测试告警失败');
      });
    },

    // 消息提示
    showMessage(type, message) {
      this.$message({
        type,
        message
      });
    },

    // 确认框
    showConfirm(message, title = '提示') {
      return this.$confirm(message, title, {
        type: 'warning'
      });
    },

    // 获取告警源类型名称（使用枚举）
    getSourceTypeName(sourceType) {
      return getSourceTypeText(sourceType);
    },

    // 获取告警源类型颜色
    getSourceTypeColor(sourceType) {
      const map = {
        muyun: 'primary',
        wangsu: 'success',
        n9e: 'warning',
        grafana: 'info',
        prometheus: 'danger',
        zabbix: 'primary'
      };
      return map[sourceType] || 'default';
    },

    // 从标签中获取环境
    getEnvironmentFromTags(tags) {
      try {
        if (!tags) return null;
        const tagsObj = typeof tags === 'string' ? JSON.parse(tags) : tags;
        return tagsObj.environment || null;
      } catch (e) {
        return null;
      }
    },

    // 从标签中获取服务名称
    getServiceFromTags(tags) {
      try {
        if (!tags) return null;
        const tagsObj = typeof tags === 'string' ? JSON.parse(tags) : tags;
        return tagsObj.service || null;
      } catch (e) {
        return null;
      }
    },

    // 获取环境颜色（使用枚举）
    getEnvironmentColor(environment) {
      if (!environment) return 'info';
      
      const env = environment.toLowerCase();
      
      // 生产环境
      if (env === 'prod' || env === 'production') {
        return 'danger';
      }
      
      // 准生产环境
      if (env === 'uat' || env === 'pre-production' || env === 'preprod') {
        return 'warning';
      }
      
      // 测试环境
      if (env === 'test' || env === 'testing' || env.startsWith('sit')) {
        return 'warning';
      }
      
      // 开发环境
      if (env === 'dev' || env.startsWith('dev') || env === 'development') {
        return 'success';
      }
      
      // 办公区环境
      if (env === 'office') {
        return 'info';
      }
      
      // 先尝试精确匹配
      let envType = getEnvironmentType(environment);
      if (envType !== 'primary') {
        return envType;
      }

      // 如果没有精确匹配，尝试模糊匹配
      if (env?.includes('prod')) return 'danger';
      if (env?.includes('stag')) return 'warning';
      if (env?.includes('test')) return 'warning';
      if (env?.includes('dev')) return 'success';

      return 'info';
    },

    // 计算持续时间
    calculateDuration(row) {
      if (!row.occurredAt) return 0;
      const endTime = row.resolvedAt || Date.now();
      return endTime - row.occurredAt;
    },

    // 格式化持续时间
    formatDuration(durationMs) {
      if (!durationMs || durationMs <= 0) return '-';

      const seconds = Math.floor(durationMs / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);

      if (days > 0) {
        return `${days}天${hours % 24}小时`;
      } else if (hours > 0) {
        return `${hours}小时${minutes % 60}分钟`;
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`;
      } else {
        return `${seconds}秒`;
      }
    },

    // 获取持续时间样式
    getDurationClass(durationMs) {
      if (!durationMs) return 'duration-normal';

      const hours = durationMs / (1000 * 60 * 60);
      if (hours > 24) return 'duration-critical';
      if (hours > 4) return 'duration-warning';
      return 'duration-normal';
    },

    // 获取时间前显示
    getTimeAgo(timestamp) {
      if (!timestamp) return '';

      const now = Date.now();
      const diff = now - timestamp;
      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);

      if (days > 0) {
        return `${days}天前`;
      } else if (hours > 0) {
        return `${hours}小时前`;
      } else if (minutes > 0) {
        return `${minutes}分钟前`;
      } else {
        return '刚刚';
      }
    },

    // 检查是否已解决
    isResolved(status) {
      return status === 'resolved' || status === 'suppressed';
    },

    // 获取用户头像
    getUserAvatar(userId) {
      // 这里应该从用户服务获取实际的头像URL
      // 暂时返回空字符串，使用默认头像
      return '';
    },

    // 列表设置
    handleColumnSetting() {
      this.$message.info('列表设置功能待实现');
    },

    // 生成测试数据
    generateTestData() {
      this.$prompt('请输入要生成的测试数据数量', '生成测试数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入有效数字',
        inputValue: '10'
      }).then(({ value: count }) => {
        if (!count) return;

        generateTestData(parseInt(count)).then(response => {
          if (response.code === 200) {
            this.$message.success(`成功生成 ${response.data.success} 条测试数据`);
            this.getList();
          } else {
            this.$message.error(response.msg || '生成测试数据失败');
          }
        }).catch(error => {
          console.error('生成测试数据失败:', error);
          this.$message.error('生成测试数据失败');
        });
      }).catch(error => {
        if (error !== 'cancel') {
          console.error('生成测试数据失败:', error);
          this.$message.error('生成测试数据失败');
        }
      });
    },

    // 选择标签示例
    selectTagExample(tagExample) {
      // 如果当前是简单搜索模式
      if (this.tagSearchMode === 'simple') {
        // 如果已有内容，添加 AND 连接符
        if (this.queryParams.tagSearch && this.queryParams.tagSearch.trim()) {
          this.queryParams.tagSearch += ' AND ' + tagExample;
        } else {
          this.queryParams.tagSearch = tagExample;
        }
      }
      this.$message.success(`已添加标签条件: ${tagExample}`);
    },

    // 切换高级标签选择
    toggleAdvancedTag(tagValue) {
      const index = this.selectedAdvancedTags.indexOf(tagValue);
      if (index > -1) {
        this.selectedAdvancedTags.splice(index, 1);
      } else {
        this.selectedAdvancedTags.push(tagValue);
      }
    },

    // 移除高级标签
    removeAdvancedTag(tagValue) {
      const index = this.selectedAdvancedTags.indexOf(tagValue);
      if (index > -1) {
        this.selectedAdvancedTags.splice(index, 1);
      }
    },

    // 清空所有标签
    clearAllTags() {
      this.selectedAdvancedTags = [];
      this.queryParams.tagSearch = '';
    },

    // 确认高级标签选择
    confirmAdvancedSelection() {
      if (this.selectedAdvancedTags.length > 0) {
        // 将选中的标签转换为搜索字符串
        this.queryParams.tagSearch = this.selectedAdvancedTags.join(' AND ');
      } else {
        this.queryParams.tagSearch = '';
      }
      this.showAdvancedSelector = false;
      this.$message.success('标签选择已更新');
    },

    // 标签搜索变化处理
    handleTagChange(value, searchType) {
      this.queryParams.tagSearch = value;
      this.tagSearchType = searchType;
    },

    // 获取标签对象
    getTagsObject(tags) {
      try {
        return typeof tags === 'string' ? JSON.parse(tags) : tags;
      } catch (e) {
        return null;
      }
    },

    // 获取属性对象
    getAttributesObject(attributes) {
      try {
        return typeof attributes === 'string' ? JSON.parse(attributes) : attributes;
      } catch (e) {
        return null;
      }
    },

    // 根据标签键获取类型
    getTagTypeByKey(key) {
      const typeMap = {
        environment: 'danger',
        service: 'primary',
        team: 'success',
        monitor_system: 'info',
        datacenter: 'warning',
        cluster: 'info',
        severity: 'danger'
      };
      return typeMap[key] || '';
    },

    // 获取用于显示的标签数组
    getTagsForDisplay(tags) {
      try {
        if (!tags) return [];
        const tagsObj = typeof tags === 'string' ? JSON.parse(tags) : tags;

        // 转换为 key-value 数组，排除一些不需要显示的系统字段
        const excludeKeys = ['raw_data', 'original_data', 'internal_id'];

        return Object.entries(tagsObj)
          .filter(([key, value]) => {
            return value !== null &&
                   value !== undefined &&
                   value !== '' &&
                   !excludeKeys.includes(key);
          })
          .map(([key, value]) => ({
            key: key,
            value: String(value).length > 15 ? String(value).substring(0, 15) + '...' : String(value)
          }))
          .sort((a, b) => {
            // 优先显示重要的标签
            const priority = ['environment', 'service', 'team', 'severity'];
            const aIndex = priority.indexOf(a.key);
            const bIndex = priority.indexOf(b.key);

            if (aIndex !== -1 && bIndex !== -1) {
              return aIndex - bIndex;
            } else if (aIndex !== -1) {
              return -1;
            } else if (bIndex !== -1) {
              return 1;
            } else {
              return a.key.localeCompare(b.key);
            }
          });
      } catch (e) {
        return [];
      }
    },

    // 格式化原始数据
    formatRawData(rawData) {
      try {
        const data = typeof rawData === 'string' ? JSON.parse(rawData) : rawData;
        return JSON.stringify(data, null, 2);
      } catch (e) {
        return rawData;
      }
    },

    // 复制原始数据
    copyRawData() {
      if (!this.currentAlert.rawData) return;

      const textArea = document.createElement('textarea');
      textArea.value = typeof this.currentAlert.rawData === 'object'
        ? JSON.stringify(this.currentAlert.rawData, null, 2)
        : this.currentAlert.rawData;

      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      this.$message.success('已复制到剪贴板');
    },

    // 加载可用标签
    loadAvailableTags() {
      this.tagsLoading = true;
      getAvailableTags().then(response => {
        if (response.code === 200) {
          const tags = response.data || {};
          this.environmentTags = tags.environment || [];
          this.serviceTags = tags.service || [];
          this.teamTags = tags.team || [];
          this.monitorTags = tags.monitor_system || [];
          
          // 整合所有标签到availableTags数组中
          this.availableTags = [];
          
          // 添加环境标签
          if (tags.environment && tags.environment.length > 0) {
            tags.environment.forEach(env => {
              this.availableTags.push(`environment:${env}`);
            });
          }
          
          // 添加服务标签
          if (tags.service && tags.service.length > 0) {
            tags.service.forEach(service => {
              this.availableTags.push(`service:${service}`);
            });
          }
          
          // 添加团队标签
          if (tags.team && tags.team.length > 0) {
            tags.team.forEach(team => {
              this.availableTags.push(`team:${team}`);
            });
          }
          
          // 添加监控系统标签
          if (tags.monitor_system && tags.monitor_system.length > 0) {
            tags.monitor_system.forEach(monitor => {
              this.availableTags.push(`monitor_system:${monitor}`);
            });
          }
          
          // 如果没有从后端获取到标签，添加一些示例标签
          if (this.availableTags.length === 0) {
            this.availableTags = [
              'environment:production',
              'environment:staging', 
              'environment:development',
              'service:user-api',
              'service:order-service',
              'team:backend',
              'team:frontend',
              'monitor_system:grafana',
              'monitor_system:prometheus'
            ];
          }
        }
        this.tagsLoading = false;
      }).catch(error => {
        console.error('加载可用标签失败:', error);
        // 设置默认的示例标签
        this.availableTags = [
          'environment:production',
          'environment:staging', 
          'environment:development',
          'service:user-api',
          'service:order-service',
          'team:backend',
          'team:frontend',
          'monitor_system:grafana',
          'monitor_system:prometheus'
        ];
        this.tagsLoading = false;
      });
    },

    // 格式化JSON数据
    formatJson(jsonStr) {
      let parsed;
      try {
        // 如果已经是对象，直接格式化
        if (typeof jsonStr === 'object') {
          parsed = jsonStr;
        } else {
          // 尝试解析JSON字符串
          parsed = JSON.parse(jsonStr);
        }
        return JSON.stringify(parsed, null, 2);
      } catch (e) {
        console.error('JSON格式化失败:', e);
        return jsonStr; // 如果解析失败，返回原始字符串
      }
    },

    // 复制原始数据
    copyRawData() {
      if (!this.currentAlert.rawData) return;

      const textArea = document.createElement('textarea');
      textArea.value = typeof this.currentAlert.rawData === 'object'
        ? JSON.stringify(this.currentAlert.rawData, null, 2)
        : this.currentAlert.rawData;

      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      this.$message.success('已复制到剪贴板');
    },

    // 切换原始数据展开状态
    toggleRawDataExpand() {
      this.rawDataExpanded = !this.rawDataExpanded;
    },

    handleJsonViewCommand(command) {
      switch (command) {
        case 'expand':
          this.rawDataExpanded = true;
          break;
        case 'format':
          this.formatJson(this.currentAlert.rawData);
          break;
        case 'tree':
          // Implement tree view functionality
          break;
        case 'plain':
          // Implement plain view functionality
          break;
        case 'copy':
          this.copyRawData();
          break;
        case 'download':
          this.downloadJson();
          break;
      }
    },

    downloadJson() {
      const blob = new Blob([JSON.stringify(this.currentAlert.rawData, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'alert_data.json';
      a.click();
      window.URL.revokeObjectURL(url);
    },

    updateJsonStyles() {
      this.highlightJsonSearch();
    },

    highlightJsonSearch() {
      if (!this.currentAlert.rawData) return;
      const searchText = this.jsonSearchQuery.trim().toLowerCase();

      // 如果搜索文本为空，重置为普通语法高亮
      if (!searchText) {
        this.jsonSearchResults = [];
        this.jsonSearchIndex = -1;
        this.updateJsonDisplay();
        return;
      }

      // 获取格式化的原始JSON文本（没有HTML标签）
      const formattedJson = typeof this.currentAlert.rawData === 'object'
        ? JSON.stringify(this.currentAlert.rawData, null, 2)
        : String(this.currentAlert.rawData);

      // 查找所有匹配项
      const regex = new RegExp(this.escapeRegExp(searchText), 'gi');
      this.jsonSearchResults = [];
      let match;

      // 收集所有匹配项的位置
      while ((match = regex.exec(formattedJson)) !== null) {
        this.jsonSearchResults.push({
          index: match.index,
          length: match[0].length,
          text: match[0]
        });
      }

      // 如果没有找到匹配项，恢复普通显示
      if (this.jsonSearchResults.length === 0) {
        this.updateJsonDisplay();
        return;
      }

      // 设置当前匹配索引
      this.jsonSearchIndex = 0;

      // 全新的高亮实现方式
      // 将匹配项位置按照从后往前排序，这样替换时不会影响后续位置
      this.jsonSearchResults.sort((a, b) => b.index - a.index);

      // 对原始文本进行语法高亮（不包含搜索高亮）
      let syntaxHighlightedText = this.getSyntaxHighlightedJson(this.currentAlert.rawData);

      // 将HTML编码转换为安全的替代符号，以防替换时破坏HTML结构
      let workingText = syntaxHighlightedText;

      // 直接替换HTML字符串中的纯文本部分
      // 为确保安全，我们需要按字符位置操作并检查是否在HTML标签内

      // 简化方法：重新生成HTML，直接把需要高亮的文本包在高亮标签里
      let htmlParts = [];
      let lastIndex = 0;
      const lines = formattedJson.split('\n');
      let lineOffsets = [0];

      // 计算每行的起始位置
      for (let i = 0; i < lines.length - 1; i++) {
        lineOffsets.push(lineOffsets[i] + lines[i].length + 1); // +1 for newline
      }

      // 为每个匹配添加高亮标记
      for (let i = 0; i < formattedJson.length; i++) {
        // 检查当前位置是否是一个匹配的开始
        const matchAtCurrentPos = this.jsonSearchResults.find(
          match => match.index === i
        );

        if (matchAtCurrentPos) {
          // 在开始处添加高亮标签
          htmlParts.push('<span class="highlight">');

          // 添加匹配的文本
          htmlParts.push(this.escapeHtml(formattedJson.substr(i, matchAtCurrentPos.length)));

          // 在结束处添加结束标签
          htmlParts.push('</span>');

          // 跳过已处理的匹配文本
          i += matchAtCurrentPos.length - 1;
        } else {
          // 添加普通字符
          htmlParts.push(this.escapeHtml(formattedJson[i]));
        }
      }

      // 将生成的HTML转换为带有语法高亮的HTML
      let highlightedText = htmlParts.join('');

      // 应用语法高亮
      if (this.enableSyntaxHighlight) {
        // 将普通的转义字符替换为带有语法高亮的HTML
        highlightedText = highlightedText
          .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, (match) => {
            let cls = 'json-number';
            if (/^"/.test(match)) {
              if (/:$/.test(match)) {
                cls = 'json-key';
              } else {
                cls = 'json-string';
              }
            } else if (/true|false/.test(match)) {
              cls = 'json-boolean';
            } else if (/null/.test(match)) {
              cls = 'json-null';
            }
            // 保留已有的highlight标签
            return match.replace(/([^<]*)(<span class="highlight">)(.*?)(<\/span>)([^>]*)/g,
              (all, before, startTag, content, endTag, after) => {
                if (before) before = `<span class="${cls}">${before}</span>`;
                if (after) after = `<span class="${cls}">${after}</span>`;
                return `${before || ''}${startTag}${content}${endTag}${after || ''}`;
              });
          });
      }

      // 设置最终的高亮HTML
      this.highlightedJson = highlightedText.replace(/\n/g, '<br>');

      // 滚动到第一个匹配项
      this.$nextTick(() => {
        this.scrollToHighlight();
      });
    },

    // 转义正则表达式特殊字符
    escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    },

    openJsonInNewWindow() {
      const win = window.open('', '_blank');
      win.document.write(`<pre>${this.formatJson(this.currentAlert.rawData)}</pre>`);
    },

    // 获取语法高亮的JSON
    getSyntaxHighlightedJson(json) {
      if (!json) return '';

      if (typeof json === 'string') {
        try {
          json = JSON.parse(json);
        } catch (e) {
          return this.escapeHtml(json);
        }
      }

      const formattedJson = JSON.stringify(json, null, 2);

      if (!this.enableSyntaxHighlight) {
        return this.escapeHtml(formattedJson);
      }

      // 基本语法高亮
      return formattedJson
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, (match) => {
          let cls = 'json-number';
          if (/^"/.test(match)) {
            if (/:$/.test(match)) {
              cls = 'json-key';
            } else {
              cls = 'json-string';
            }
          } else if (/true|false/.test(match)) {
            cls = 'json-boolean';
          } else if (/null/.test(match)) {
            cls = 'json-null';
          }
          return `<span class="${cls}">${match}</span>`;
        });
    },

    // 转义HTML特殊字符
    escapeHtml(unsafe) {
      return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
    },

    // 更新JSON显示
    updateJsonDisplay() {
      if (!this.currentAlert.rawData) {
        this.highlightedJson = '';
        return;
      }

      this.highlightedJson = this.getSyntaxHighlightedJson(this.currentAlert.rawData);

      // 如果有搜索关键字，应用搜索高亮
      if (this.jsonSearchQuery.trim()) {
        this.highlightJsonSearch();
      }
    },

    // 导航JSON搜索结果
    navigateJsonSearch(direction) {
      if (this.jsonSearchResults.length === 0) return;

      if (direction === 'next') {
        this.jsonSearchIndex = (this.jsonSearchIndex + 1) % this.jsonSearchResults.length;
      } else {
        this.jsonSearchIndex = this.jsonSearchIndex <= 0 ?
          this.jsonSearchResults.length - 1 : this.jsonSearchIndex - 1;
      }

      this.scrollToHighlight();
    },

    // 滚动到当前高亮位置
    scrollToHighlight() {
      this.$nextTick(() => {
        const highlights = this.$refs.jsonViewer?.querySelectorAll('.highlight');
        if (highlights && highlights.length > this.jsonSearchIndex) {
          highlights[this.jsonSearchIndex].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      });
    },

    // 应用处理模板
    applyTemplate(templateId) {
      const templates = {
        fixed_service: '已对相关服务进行修复，问题已解决。服务恢复正常运行。',
        restart_service: '通过重启服务解决问题，服务已恢复正常。',
        config_change: '通过调整配置参数解决问题，已验证服务正常运行。',
        network_fixed: '网络连接问题已修复，连接恢复正常。',
        resource_issue: '临时资源不足导致，已增加资源配额，问题已解决。',
        custom: ''
      };

      if (templateId !== 'custom') {
        this.resolveForm.resolutionNote = templates[templateId] || '';
      }
    },

    // 获取处理动作文本
    getResolveActionText() {
      const actionMap = {
        'resolve': '确认解决',
        'ignore': '确认静默忽略'
      };
      return actionMap[this.resolveForm.action] || '确认';
    },

    // 获取解决类型样式
    getResolutionTypeColor(type) {
      const map = {
        auto_resolved: 'success',
        manual_resolved: 'primary',
        service_restart: 'warning',
        config_change: 'info',
        network_fixed: 'warning',
        resource_adjusted: 'info',
        false_positive: 'danger'
      };
      return map[type] || '';
    },

    // 获取解决类型文本
    getResolutionTypeText(type) {
      const map = {
        auto_resolved: '系统自动恢复',
        manual_resolved: '人工解决',
        service_restart: '服务重启',
        config_change: '配置调整',
        network_fixed: '网络修复',
        resource_adjusted: '资源调整',
        false_positive: '误报'
      };
      return map[type] || type;
    },

    // 处理告警源类型变化
    handleSourceTypeChange(value) {
      // 告警源类型变化时的处理逻辑，可以根据需要添加
      console.log('告警源类型变化:', value);
    },

    // 获取批量处理动作文本
    getBatchActionText() {
      const actionMap = {
        'resolve': '确认批量解决',
        'ignore': '确认批量静默忽略'
      };
      return actionMap[this.batchResolveForm.action] || '确认';
    },

    // 从URL query参数初始化FlashDuty事件ID（已优化为直接使用列表行数据）
    initFlashDutyIncidentId() {
      try {
        const flashDutyIncidentId = this.$route.query.flashDutyIncidentId;
        if (flashDutyIncidentId) {
          console.log('从URL获取到FlashDuty事件ID:', flashDutyIncidentId);
          
          // 可选：显示提示信息
          this.$message.info(`已从FlashDuty跳转，事件ID: ${flashDutyIncidentId}`);
          
          // 注意：现在flashDutyIncidentId直接从列表行数据获取，不再需要预填充表单
          // 如果需要根据flashDutyIncidentId查找对应告警，可以在这里添加逻辑
        }
      } catch (error) {
        console.warn('获取URL参数时发生错误:', error);
      }
    },

    // 批量处理提交
    submitBatchResolve() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要处理的告警');
        return;
      }

      this.batchProcessing = true;
      
      // 提取告警ID列表，拼接成逗号分隔的字符串
      const ids = this.selectedRows.map(row => row.id).join(',');
      
      console.log('批量处理告警ID字符串:', ids);
      
      const data = {
        ids: ids,  // 后端期望逗号分隔的字符串
        resolutionNote: this.batchResolveForm.resolutionNote,
        rootCause: this.batchResolveForm.rootCause,
        action: this.batchResolveForm.action,
        flashDutyIncidentId: ''  // 批量处理时使用空字符串
      };

      batchProcessAlerts(data).then(response => {
        if (response.code === 200) {
          this.$message.success(`批量${this.batchResolveForm.action === 'resolve' ? '处理' : '忽略'}成功`);
          this.batchResolveDialogVisible = false;
          this.getList();
          this.selectedRows = [];
          this.$refs.multipleTable.clearSelection();
        } else {
          this.$message.error(response.msg || '批量处理失败');
        }
      }).catch(error => {
        console.error('批量处理失败:', error);
        this.$message.error('批量处理失败');
      }).finally(() => {
        this.batchProcessing = false;
      });
    },

    // 从URL query参数初始化查询参数
    initQueryParamsFromUrl() {
      try {
        const query = this.$route.query;
        
        // 获取alert_id并赋值给queryParams.id
        if (query.alert_id) {
          this.queryParams.id = query.alert_id;
          console.log('从URL获取到告警ID:', query.alert_id);
        }
        
        // 获取event_id并赋值给queryParams.eventId
        if (query.event_id) {
          this.queryParams.eventId = query.event_id;
          console.log('从URL获取到事件ID:', query.event_id);
        }
        
        // 如果需要，也可以处理其他query参数
        if (query.title) {
          this.queryParams.title = query.title;
        }
        
        if (query.severity) {
          this.queryParams.severity = query.severity;
        }
        
        if (query.status) {
          this.queryParams.status = query.status;
        }
        
        if (query.sourceType) {
          this.queryParams.sourceType = query.sourceType;
        }
        
      } catch (error) {
        console.warn('获取URL参数时发生错误:', error);
      }
    },

    // 从localStorage加载保存的标签
    loadSavedTags() {
      try {
        const saved = localStorage.getItem('alertTagSearch_savedTags');
        if (saved) {
          this.savedTags = JSON.parse(saved);
        }
      } catch (error) {
        console.warn('加载保存的标签时发生错误:', error);
        this.savedTags = [];
      }
    },

    // 保存标签到localStorage
    saveTags() {
      try {
        localStorage.setItem('alertTagSearch_savedTags', JSON.stringify(this.savedTags));
      } catch (error) {
        console.warn('保存标签到localStorage时发生错误:', error);
      }
    },

    saveCurrentTag() {
      const currentTag = this.queryParams.tagSearch ? this.queryParams.tagSearch.trim() : '';
      if (!currentTag) {
        this.$message.warning('请输入标签内容');
        return;
      }

      // 检查是否已存在
      if (this.savedTags.includes(currentTag)) {
        this.$message.info('该标签已存在');
        return;
      }

      // 检查数量限制
      if (this.savedTags.length >= 100) {
        this.$message.warning('最多只能保存100个标签，请先删除一些标签');
        return;
      }

      // 添加到开头
      this.savedTags.unshift(currentTag);
      
      // 保存到localStorage
      this.saveTags();
      
      this.$message.success('标签已保存');
      this.queryParams.tagSearch = '';
      this.showTagSuggestions = false;
    },

    removeSavedTag(index) {
      if (index >= 0 && index < this.savedTags.length) {
        const removedTag = this.savedTags[index];
        this.savedTags.splice(index, 1);
        this.saveTags();
        this.$message.success(`已删除标签: ${removedTag}`);
        // 删除标签后保持下拉框显示
        this.$nextTick(() => {
          this.showTagSuggestions = true;
        });
      }
    },

    selectTag(tag) {
      // 获取当前搜索内容
      const currentValue = this.queryParams.tagSearch ? this.queryParams.tagSearch.trim() : '';
      
      // 如果当前搜索框为空，直接设置标签
      if (!currentValue) {
        this.queryParams.tagSearch = tag;
      } else {
        // 如果当前搜索框有内容，使用空格连接（让后台处理逻辑）
        this.queryParams.tagSearch = currentValue + ' ' + tag;
      }
      
      // 阻止下拉框关闭，取消任何待执行的隐藏定时器
      clearTimeout(this.hideTagSuggestionsTimer);
      
      // 保持下拉框显示，将焦点重新设置到输入框
      this.$nextTick(() => {
        const inputElement = this.$el.querySelector('.tag-search-input-wrapper .el-input__inner');
        if (inputElement) {
          inputElement.focus();
          // 将光标移到最后
          inputElement.setSelectionRange(inputElement.value.length, inputElement.value.length);
        }
      });
    },

    handleSearch() {
      this.getList();
    },

    hideTagSuggestions() {
      // 清除之前的定时器
      clearTimeout(this.hideTagSuggestionsTimer);
      // 设置新的定时器
      this.hideTagSuggestionsTimer = setTimeout(() => {
        this.showTagSuggestions = false;
      }, 300);
    },

    // 显示标签建议时动态计算位置
    showTagSuggestionsWithPosition() {
      this.showTagSuggestions = true;
      this.$nextTick(() => {
        const inputWrapper = this.$el.querySelector('.tag-search-input-wrapper');
        const suggestions = this.$el.querySelector('.tag-suggestions');
        
        if (inputWrapper && suggestions) {
          const inputRect = inputWrapper.getBoundingClientRect();
          const viewportHeight = window.innerHeight;
          const suggestionsHeight = suggestions.offsetHeight || 300; // 预估高度
          
          // 计算是否有足够空间在下方显示
          const spaceBelow = viewportHeight - inputRect.bottom;
          const spaceAbove = inputRect.top;
          
          if (spaceBelow >= suggestionsHeight) {
            // 在下方显示
            suggestions.style.position = 'fixed';
            suggestions.style.top = `${inputRect.bottom + 8}px`;
            suggestions.style.left = `${inputRect.left}px`;
            suggestions.style.width = `${inputRect.width}px`;
            suggestions.style.transform = 'none';
          } else if (spaceAbove >= suggestionsHeight) {
            // 在上方显示
            suggestions.style.position = 'fixed';
            suggestions.style.top = `${inputRect.top - suggestionsHeight - 8}px`;
            suggestions.style.left = `${inputRect.left}px`;
            suggestions.style.width = `${inputRect.width}px`;
            suggestions.style.transform = 'none';
          } else {
            // 空间不足时，优先在下方显示，但限制高度
            suggestions.style.position = 'fixed';
            suggestions.style.top = `${inputRect.bottom + 8}px`;
            suggestions.style.left = `${inputRect.left}px`;
            suggestions.style.width = `${inputRect.width}px`;
            suggestions.style.maxHeight = `${Math.max(spaceBelow - 20, 200)}px`;
            suggestions.style.transform = 'none';
          }
        }
      });
    },

    // 处理标签搜索框获得焦点
    handleTagSearchFocus() {
      this.showTagSuggestionsWithPosition();
    },

    cancelHideTagSuggestions() {
      clearTimeout(this.hideTagSuggestionsTimer);
    },

    scheduleHideTagSuggestions() {
      this.hideTagSuggestionsTimer = setTimeout(() => {
        this.showTagSuggestions = false;
      }, 300); // 从200ms增加到300ms
    },

    handleGlobalClick(event) {
      // 检查点击是否在标签搜索容器外部
      const tagSearchContainer = this.$el.querySelector('.tag-search-container');
      if (tagSearchContainer && !tagSearchContainer.contains(event.target)) {
        this.showTagSuggestions = false;
        // 清除之前的定时器
        clearTimeout(this.hideTagSuggestionsTimer);
      }
    },

    // 强制移除遮罩层
    removeOverlayMask() {
      // 移除：让Element UI自动管理遮罩层
    },

    // 确保遮罩层正确显示
    ensureOverlayMask() {
      // 移除：让Element UI自动管理遮罩层
    },

    // 获取目标主机的位置信息（新增）
    getTargetLocationInfo(row) {
      try {
        if (!row.tags) return null;
        const tagsObj = typeof row.tags === 'string' ? JSON.parse(row.tags) : row.tags;
        
        // 提取与目标主机相关的位置信息
        const locationInfo = {
          environment: tagsObj.target_env || tagsObj.environment || tagsObj.env,
          datacenter: tagsObj.target_dc || tagsObj.datacenter || tagsObj.dc,
          region: tagsObj.target_region || tagsObj.region,
          zone: tagsObj.target_zone || tagsObj.zone || tagsObj.az,
          cluster: tagsObj.target_cluster || tagsObj.cluster
        };
        
        // 如果所有字段都为空，返回null
        const hasValidInfo = Object.values(locationInfo).some(value => value != null && value !== '');
        return hasValidInfo ? locationInfo : null;
      } catch (e) {
        return null;
      }
    },

    // 基于IP推断环境（新增）
    getEnvironmentByIP(dstIp) {
      if (!dstIp || typeof dstIp !== 'string') return null;
      
      // IP段到环境的映射规则 - 根据实际网络规划调整
      const envMappings = [
        // 生产环境
        { subnet: '10.20.', env: 'production', region: '秦淮机房', color: 'danger' },
        { subnet: '10.15.', env: 'production', region: '华为云', color: 'danger' },
        
        // 测试环境
        { subnet: '10.2.', env: 'testing', region: '秦淮测试机房', color: 'warning' },
        
        // 办公区环境（根据具体env标签决定颜色）
        { subnet: '10.1.', env: 'office', region: '办公区', color: 'info' },
        
        // 其他常见网段的默认映射
        { subnet: '172.16.', env: 'internal', region: '内网环境', color: 'info' },
        { subnet: '192.168.', env: 'local', region: '本地网络', color: 'success' }
      ];
      
      for (const mapping of envMappings) {
        if (dstIp.startsWith(mapping.subnet)) {
          return {
            env: mapping.env,
            region: mapping.region,
            color: mapping.color
          };
        }
      }
      
      // 默认返回未知环境
      return {
        env: 'unknown',
        region: 'Unknown-Network',
        color: 'info'
      };
    },

    // 获取IP基于环境的文本
    getIPBasedEnvironmentText(row) {
      if (!row || !row.tags) {
        // 如果没有tags，基于IP段推断
        const ipEnv = this.getEnvironmentByIP(row?.dstIp);
        if (ipEnv && ipEnv.env !== 'unknown') {
          const envMap = {
            'production': '生产环境',
            'testing': '测试环境',
            'office': '办公区',
            'internal': '内网环境',
            'local': '本地网络'
          };
          return envMap[ipEnv.env] || '未知环境';
        }
        return '未知环境';
      }

      const env = this.getEnvironmentFromTags(row.tags);
      if (!env) {
        // 如果tags中没有环境信息，基于IP段推断
        const ipEnv = this.getEnvironmentByIP(row.dstIp);
        if (ipEnv && ipEnv.env !== 'unknown') {
          const envMap = {
            'production': '生产环境',
            'testing': '测试环境',
            'office': '办公区',
            'internal': '内网环境',
            'local': '本地网络'
          };
          return envMap[ipEnv.env] || '未知环境';
        }
        return '未知环境';
      }

      // 处理tags中的环境信息
      const envLower = env.toLowerCase();
      if (envLower === 'office') {
        return '办公区';
      } else if (envLower === 'prod' || envLower === 'production') {
        return '生产环境';
      } else if (envLower === 'uat' || envLower === 'pre-production' || envLower === 'preprod') {
        return '准生产环境';
      } else if (envLower === 'test' || envLower === 'testing' || envLower.startsWith('sit')) {
        return '测试环境';
      } else if (envLower === 'dev' || envLower.startsWith('dev') || envLower === 'development') {
        return '开发环境';
      } else {
        return '未知环境';
      }
    },

    // 获取IP基于环境的颜色
    getIPBasedEnvironmentColor(row) {
      if (!row || !row.tags) {
        // 如果没有tags，基于IP段推断
        const ipEnv = this.getEnvironmentByIP(row?.dstIp);
        return ipEnv ? ipEnv.color : 'info';
      }

      const env = this.getEnvironmentFromTags(row.tags);
      if (!env) {
        // 如果tags中没有环境信息，基于IP段推断
        const ipEnv = this.getEnvironmentByIP(row.dstIp);
        return ipEnv ? ipEnv.color : 'info';
      }

      // 处理tags中的环境信息
      const envLower = env.toLowerCase();
      if (envLower === 'office') {
        return 'info';
      } else if (envLower === 'prod' || envLower === 'production') {
        return 'danger';
      } else if (envLower === 'uat' || envLower === 'pre-production' || envLower === 'preprod') {
        return 'warning';
      } else if (envLower === 'test' || envLower === 'testing' || envLower.startsWith('sit')) {
        return 'warning';
      } else if (envLower === 'dev' || envLower.startsWith('dev') || envLower === 'development') {
        return 'success';
      } else {
        return 'info';
      }
    },

    handleTimeline(row) {
      // 设置所有ID信息，让它们都显示
      this.timelineAlertId = row.id; // 告警ID
      this.timelineEventId = row.eventId || ''; // EventID
      this.timelineFdIncidentId = row.fdIncidentId || ''; // FlashDuty故障ID
      this.timelineAlertOccurredAt = row.occurredAt;
      
      // 检查是否有可用的ID进行查询
      if (row.fdIncidentId) {
        // 优先使用FlashDuty故障ID查询时间线
        console.log('使用FlashDuty故障ID查询时间线:', row.fdIncidentId);
        this.timelineVisible = true;
      } else if (row.eventId) {
        // 如果没有FlashDuty故障ID，使用eventId查询时间线
        console.log('使用EventID查询时间线:', row.eventId);
        this.timelineVisible = true;
      } else {
        this.$message.warning('该告警没有FlashDuty故障ID或EventID，无法查看时间线');
        return;
      }
    },

    getDefaultTimeRange() {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 7);
      start.setHours(0, 0, 0, 0);
      
      // 格式化为 yyyy-MM-dd HH:mm:ss 格式
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };
      
      return [formatDate(start), formatDate(end)];
    },

    getAssignedUserNames(assignedUserIds) {
      // 如果assignedUserIds为空，返回空数组
      if (!assignedUserIds || assignedUserIds.trim() === '') {
        return [];
      }
      
      // 解析逗号分隔的用户ID列表
      const userIds = assignedUserIds.split(',').map(id => id.trim()).filter(id => id);
      
      // 这里应该从用户服务获取实际的用户名
      // 暂时返回模拟数据，实际项目中应该调用用户服务API
      const userMap = {
        '1': '张三',
        '2': '李四', 
        '3': '王五',
        '4': '赵六',
        '5': '钱七'
      };
      
      return userIds.map(id => userMap[id] || `用户${id}`);
    },

    getAssignedUserIds(assignedUserIds) {
      // 如果assignedUserIds为空，返回空数组
      if (!assignedUserIds || assignedUserIds.trim() === '') {
        return [];
      }
      
      // 解析逗号分隔的用户ID列表
      return assignedUserIds.split(',').map(id => id.trim()).filter(id => id);
    },

    // 格式化时间
    formatTime(ts) {
      if (!ts) return '';
      const d = new Date(ts);
      const pad = n => n < 10 ? '0' + n : n;
      return `${d.getFullYear()}-${pad(d.getMonth()+1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
    },

    // 获取持续时间标签类型
    getDurationTagType(durationMs) {
      if (!durationMs || durationMs <= 0) return 'info';
      
      const hours = Math.floor(durationMs / (1000 * 60 * 60));
      
      if (hours > 24) return 'danger';
      if (hours > 4) return 'warning';
      if (hours > 1) return 'primary';
      return 'success';
    },

    // 解析标签JSON
    parseTags(tagsStr) {
      try {
        if (!tagsStr || tagsStr === '{}') return {};
        return typeof tagsStr === 'string' ? JSON.parse(tagsStr) : tagsStr;
      } catch (e) {
        console.error('解析标签失败:', e);
        return {};
      }
    },

    // 检查文本是否溢出
    isTextOverflow(text) {
      if (!text) return false;
      return text.length > 8; // 如果名字超过8个字符就显示tooltip
    },

    // 认领告警
    handleClaim(alert) {
      this.$confirm('确定要认领这个告警吗？', '认领告警', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        claimAlert(alert.id).then(response => {
          if (response.code === 200) {
            this.$message.success('告警认领成功');
            // 刷新列表
            this.getList();
            // 如果当前告警在详情抽屉中，刷新详情
            if (this.currentAlert.id === alert.id) {
              this.handleView(alert);
            }
          } else {
            this.$message.error(response.msg || '认领告警失败');
          }
        }).catch(error => {
          console.error('认领告警失败:', error);
          this.$message.error('认领告警失败');
        });
      }).catch(() => {
        // 用户取消
      });
    },

    // 检查JSON是否有效
    isValidJson(jsonStr) {
      try {
        JSON.parse(jsonStr);
        return true;
      } catch (e) {
        return false;
      }
    },

    // 解析告警对象
    parseAlertObject(alertObject) {
      try {
        return JSON.parse(alertObject);
      } catch (e) {
        console.error('解析告警对象失败:', e);
        return {};
      }
    },

    getDurationText(row) {
      const start = new Date(row.occurredAt || row.alertTime)
      const end = row.resolvedAt ? new Date(row.resolvedAt) : new Date()
      const diff = Math.max(0, end - start)
      const hours = Math.floor(diff / 3600000)
      const minutes = Math.floor((diff % 3600000) / 60000)
      const seconds = Math.floor((diff % 60000) / 1000)
      if (hours > 0) return `${hours}小时${minutes}分`
      if (minutes > 0) return `${minutes}分${seconds}秒`
      return `${seconds}秒`
    },

    // 关闭时间线抽屉时清空数据
    handleTimelineClose() {
      this.timelineVisible = false;
      this.timelineEventId = '';
      this.timelineFdIncidentId = '';
      this.timelineAlertId = '';
      this.timelineAlertOccurredAt = '';
      // 如果有时间线数据变量，也清空
      if (this.timelineData) this.timelineData = [];
      if (this.timelineList) this.timelineList = [];
    },
  },
  created() {
    this.getUserList();
    this.loadSavedTags();
    this.loadAvailableTags();
    this.initQueryParamsFromUrl();
    this.getList();
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  // 视角切换Tab样式
  .view-tab-card {
    margin-bottom: 20px;
    border-radius: 8px;
    
    .view-tabs {
      .el-tabs__header {
        margin-bottom: 0;
      }
      
      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }
      
      .el-tabs__item {
        font-size: 16px;
        font-weight: 500;
        padding: 0 30px;
        height: 50px;
        line-height: 50px;
        transition: all 0.3s;
        
        i {
          margin-right: 8px;
          font-size: 18px;
        }
        
        &.is-active {
          color: #409eff;
          background-color: #f0f9ff;
          border-radius: 8px 8px 0 0;
        }
        
        &:hover {
          color: #409eff;
        }
      }
      
      .el-tabs__active-bar {
        background-color: #409eff;
        height: 3px;
      }
    }
  }
}

// 统计卡片样式
.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);
    }

    .stat-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24px 20px;
      position: relative;

      .stat-info {
        flex: 1;

        .stat-number {
          font-size: 32px;
          font-weight: 700;
          line-height: 1;
          margin-bottom: 8px;
          color: #303133;

          &.critical {
            color: #f56c6c;
          }

          &.pending {
            color: #e6a23c;
          }

          &.resolved {
            color: #67c23a;
          }
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          font-weight: 500;
        }
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.8;

        i {
          font-size: 20px;
          color: white;
        }

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.critical {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.pending {
          background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        &.resolved {
          background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
      }
    }

    // 移除Element UI默认的body padding
    :deep(.el-card__body) {
      padding: 0;
    }
  }
}

// 搜索卡片样式
.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  position: relative;
  z-index: 10;
  overflow: visible !important; // 确保内容可以溢出显示

  .search-form {
    overflow: visible !important; // 确保表单内容可以溢出显示
    
    .el-form-item {
      margin-bottom: 15px;
      overflow: visible !important; // 确保表单项可以溢出显示
      
      &.tag-search-item {
        margin-bottom: 25px; // 从50px进一步减少到25px，让搜索栏更紧凑
        position: relative;
        z-index: 10000; // 进一步提高z-index确保不被遮挡
        overflow: visible !important; // 确保标签搜索项可以溢出显示
      }
    }
  }
  
  // 确保卡片内容可以溢出
  :deep(.el-card__body) {
    overflow: visible !important;
  }
}

// 工具栏样式
.toolbar-card {
  margin-bottom: 20px;
  border-radius: 8px;
  position: relative;
  z-index: 1; // 设置较低的z-index
  margin-top: 5px; // 从10px进一步减少到5px，让搜索栏距离更近

  .toolbar-row {
    display: flex;
    align-items: center;

    .toolbar-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .el-switch {
        margin-right: 15px;
      }
    }
  }
}

// 表格卡片样式
.table-card {
  border-radius: 8px;
  position: relative;
  z-index: 1; // 设置较低的z-index
  margin-top: 10px; // 从15px减少到10px，让表格距离工具栏更近

  .alert-table {
    .el-table__header {
      background-color: #fafafa;

      th {
        background-color: #fafafa !important;
        color: #606266;
        font-weight: 600;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }

    // 标签显示样式
    .tags-display {
      .tag-item {
        margin: 2px 3px 2px 0;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }

      .more-tags {
        margin: 2px 0;
        cursor: pointer;

        &:hover {
          transform: scale(1.05);
        }
      }

      .no-tags {
        color: #c0c4cc;
        font-size: 12px;
        font-style: italic;
      }
    }

    // 告警标题样式
    .alert-title {
      .title-link {
        font-weight: 500;
        font-size: 14px;

        &:hover {
          color: #409eff;
        }
      }

      .alert-meta {
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 8px;

        .event-id {
          font-size: 12px;
          color: #909399;
          font-family: 'Courier New', monospace;
        }
      }
    }

    // 告警源信息样式
    .source-info {
      .source-detail {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    // 服务信息样式
    .service-info {
      .service-name {
        margin-bottom: 3px;
      }

      .rule-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 2px;
        font-size: 12px;
      }

      .group-name {
        font-size: 11px;
        color: #909399;
      }

      .no-service {
        color: #c0c4cc;
      }
    }

    // 位置信息样式
    .location-info {
      .source-ident {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
        font-family: 'Courier New', monospace;
      }
      
      .location-tags, .ip-based-env {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 3px;
        
        .datacenter-info, .region-info {
          font-size: 11px;
          color: #909399;
          display: flex;
          align-items: center;
          gap: 2px;
          
          i {
            font-size: 10px;
          }
        }
        
        .datacenter-info {
          color: #f56c6c;
        }
        
        .region-info {
          color: #409eff;
        }
      }
      
      .target-ip-info {
        text-align: center;
      }
      
      .no-location {
        color: #c0c4cc;
        font-size: 12px;
      }
    }

    // 时间信息样式
    .time-info {
      .occurred-time {
        font-size: 13px;
        color: #303133;
      }

      .time-ago {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }

    // 持续时间样式
    .duration-info {
      .duration-normal {
        color: #67c23a;
      }

      .duration-warning {
        color: #e6a23c;
      }

      .duration-critical {
        color: #f56c6c;
        font-weight: bold;
      }
    }

    // 处理人信息样式
    .resolver-info {
      display: flex;
      align-items: center;

      .resolver-name {
        margin-left: 8px;
        font-size: 12px;
        color: #606266;
      }
    }

    .no-resolver {
      color: #c0c4cc;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-cards {
    .el-col {
      margin-bottom: 15px;
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .search-form {
    .el-form-item {
      display: block;
      margin-right: 0;
    }
  }

  .toolbar-row {
    flex-direction: column;
    gap: 15px;

    .el-col {
      width: 100%;
    }
  }

  .alert-table {
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

// 标签搜索样式
.tag-search-header {
  margin-bottom: 10px;

  .search-mode-toggle {
    .el-button {
      padding: 5px 15px;
      font-size: 12px;
    }
  }
}

.simple-search {
  margin-top: 8px;
}

.advanced-search {
  margin-top: 8px;
}

// 标签帮助样式
.tag-help {
  h4 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 14px;
  }

  h5 {
    margin: 10px 0 5px 0;
    color: #606266;
    font-size: 12px;
  }

  p {
    margin: 5px 0;
    color: #909399;
    font-size: 12px;
    line-height: 1.4;
  }

  code {
    background: #f5f7fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
    color: #e6a23c;
  }

  .multi-tag-info {
    margin: 15px 0;
    padding: 10px;
    background: #f0f9ff;
    border-radius: 4px;
    border-left: 3px solid #409eff;
  }

  .tag-examples {
    margin: 10px 0;

    .tag-categories {
      .tag-category {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .category-name {
          font-size: 12px;
          color: #606266;
          margin-right: 8px;
          min-width: 50px;
        }

        .example-tag {
          margin: 2px 4px 2px 0;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  .search-modes {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #ebeef5;
  }
}

// 高级选择器样式
.advanced-selector {
  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h4 {
      margin: 0;
      color: #303133;
      font-size: 14px;
    }
  }

  .selector-body {
    .tag-options {
      max-height: 200px;
      overflow-y: auto;

      .tag-option {
        display: flex;
        align-items: center;
        padding: 8px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        margin-bottom: 5px;

        &:hover {
          background-color: #f5f7fa;
        }

        &.active {
          background-color: #ecf5ff;
          border: 1px solid #b3d8ff;
        }

        .tag-description {
          margin-left: 10px;
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .selector-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;

    .selected-tags {
      margin-bottom: 10px;
      min-height: 24px;

      .label {
        font-size: 12px;
        color: #606266;
        margin-right: 8px;
      }

      .el-tag {
        margin-right: 5px;
        margin-bottom: 5px;
      }
    }

    .actions {
      text-align: right;

      .el-button {
        margin-left: 8px;
      }
    }
  }
}

// 抽屉样式
.alert-detail-drawer {
  padding: 0 20px 80px 20px;

  .detail-card {
    margin-bottom: 20px;
    border-radius: 8px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .alert-title-text {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .alert-description {
      line-height: 1.6;
      color: #606266;
    }

    .resolver-info {
      display: flex;
      align-items: center;

      .resolver-name {
        margin-left: 8px;
        color: #606266;
      }
    }

    .resolution-note {
      line-height: 1.6;
      color: #606266;
      background: #f5f7fa;
      padding: 10px;
      border-radius: 4px;
    }

    .tags-content {
      .tag-item {
        display: inline-block;
        margin: 5px 10px 5px 0;

        .tag-key {
          font-weight: 500;
          color: #606266;
          margin-right: 5px;
        }
      }

      .no-tags {
        color: #c0c4cc;
        font-style: italic;
      }
    }

    .attributes-content {
      .attribute-value {
        font-family: 'Courier New', monospace;
        background: #f5f7fa;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
      }

      .no-attributes {
        color: #c0c4cc;
        font-style: italic;
      }
    }

    .raw-data-content {
      .raw-data-pre {
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 15px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #606266;
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  z-index: 10;
}

// 标签弹窗样式
.all-tags {
  .tag-item {
    margin: 3px 5px 3px 0;
    display: inline-block;
  }
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

.raw-data-content {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
  font-family: 'SFMono-Regular', Monaco, Menlo, Consolas, 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;

  &.expanded {
    max-height: none;
  }

  &.syntax-highlight {
    background-color: #f8f8f8;

    .json-key {
      color: #881391;
    }

    .json-string {
      color: #1a1aa6;
    }

    .json-number {
      color: #1c00cf;
    }

    .json-boolean {
      color: #0d904f;
    }

    .json-null {
      color: #808080;
    }

    &.dark-theme {
      background-color: #1e1e1e;
      color: #d4d4d4;

      .json-key {
        color: #9cdcfe;
      }

      .json-string {
        color: #ce9178;
      }

      .json-number {
        color: #b5cea8;
      }

      .json-boolean {
        color: #569cd6;
      }

      .json-null {
        color: #569cd6;
      }
    }
  }
}

::v-deep .highlight {
  background-color: #ffff00 !important;
  color: #000000 !important;
  border-radius: 2px !important;
  padding: 1px 2px !important;
  margin: 0 -2px !important;
  border: 1px solid #ffa600 !important;
  font-weight: bold !important;
  display: inline-block !important;
  box-shadow: 0 0 2px rgba(255, 166, 0, 0.5) !important;
}

.dark-theme >>> .highlight {
  background-color: #ff9800 !important;
  color: #000000 !important;
  border: 1px solid #ffd740 !important;
}

.empty-data {
  color: #909399;
  text-align: center;
  padding: 40px 20px;
  font-size: 14px;
  background-color: #f8f8f8;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  i {
    font-size: 32px;
    margin-bottom: 10px;
    color: #c0c4cc;
  }

  p {
    margin: 0;
  }
}

.json-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-controls {
      display: flex;
      align-items: center;
    }
  }

  .json-container {
    .json-controls {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 8px;
      background-color: #f2f6fc;
      border-radius: 4px;

      .el-slider {
        width: 150px;
      }

      .search-results {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 12px;
        color: #606266;

        .search-navigation {
          margin: 0 5px;
        }

        .search-index {
          background-color: #ecf5ff;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 12px;
          color: #409eff;
        }
      }
    }

    .json-wrapper {
      overflow-y: auto;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        border-color: #c6e2ff;
        box-shadow: 0 0 10px rgba(0, 149, 255, 0.1);
      }
    }

    .json-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #ebeef5;
    }
  }
}


// 添加告警处理对话框样式
.resolve-alert-container {
  .alert-summary {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;

    .alert-title {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .label {
        font-weight: bold;
        margin-right: 10px;
        color: #606266;
      }

      .value {
        font-size: 16px;
        font-weight: 500;
        margin-right: 10px;
        flex: 1;
      }
    }

    .alert-meta {
      display: flex;
      flex-wrap: wrap;

      .meta-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 5px;

        .label {
          color: #909399;
          margin-right: 5px;
        }

        .value {
          color: #606266;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    width: 100%;
    
    .action-btn {
      flex: 1;
      margin: 0;
      border-radius: 0;
      
      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
      
      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-left: 0;
      }
      
      &:not(:last-child) {
        border-right: 0;
      }
      
      &.is-active {
        z-index: 1;
        border-color: #409eff;
      }
    }
  }

  .resolve-options {
    display: flex;
    width: 100%;

    .el-radio-button {
      flex: 1;

      &:not(:first-child) {
        margin-left: -1px;
        
        .el-radio-button__inner {
          border-left: 1px solid #dcdfe6 !important;
        }
      }

      .el-radio-button__inner {
        width: 100%;
        text-align: center;
        border: 1px solid #dcdfe6 !important;
      }

      &.is-active .el-radio-button__inner {
        border-color: #409eff !important;
        border-left: 1px solid #409eff !important;
      }

      // 特别针对第二个按钮的左边框
      &:nth-child(2) .el-radio-button__inner {
        border-left: 1px solid #dcdfe6 !important;
      }
      
      &:nth-child(2).is-active .el-radio-button__inner {
        border-left: 1px solid #409eff !important;
      }
    }
  }
}

.batch-resolve-container {
  .batch-alert-summary {
    margin-bottom: 15px;
    font-size: 14px;
    color: #606266;

    .skipped-info {
      margin-top: 5px;
      font-size: 12px;
      color: #909399;
    }
  }

  .action-buttons {
    display: flex;
    width: 100%;
    
    .action-btn {
      flex: 1;
      margin: 0;
      border-radius: 0;
      
      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
      
      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-left: 0;
      }
      
      &:not(:last-child) {
        border-right: 0;
      }
      
      &.is-active {
        z-index: 1;
        border-color: #409eff;
      }
    }
  }
}

.tag-search-container {
  position: relative;
  overflow: visible !important; // 确保容器内容可以溢出显示

  .tag-search-input-wrapper {
    position: relative;
    z-index: 1;
    overflow: visible !important; // 确保输入框包装器可以溢出显示
  }

  .tag-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
    border: 1px solid #e1e8f0;
    border-radius: 8px;
    box-shadow: 0 12px 40px rgba(64, 158, 255, 0.2);
    z-index: 999999; // 设置超高的z-index确保在最上层
    max-height: 450px; // 增加最大高度
    overflow-y: auto;
    margin-top: 8px;
    backdrop-filter: blur(10px);
    
    // 确保下拉框不受父容器限制
    transform: translateZ(0); // 创建新的层叠上下文
    will-change: transform; // 优化渲染性能

    .suggestion-section {
      padding: 16px;
      border-bottom: 1px solid #f0f4f8;

      &:last-child {
        border-bottom: none;
      }

      &:first-child {
        border-radius: 8px 8px 0 0;
      }

      &:last-child {
        border-radius: 0 0 8px 8px;
      }

      .section-title {
        font-weight: 600;
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title-text {
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .tag-count {
          font-weight: 500;
          color: #7c8db5;
          font-size: 12px;
          background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
          padding: 4px 8px;
          border-radius: 12px;
          border: 1px solid #bbdefb;
        }
      }

      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .tag-item {
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border-radius: 6px;
          font-weight: 500;
          border: 1px solid transparent;

          &.saved-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

            &:hover {
              transform: translateY(-3px) scale(1.05);
              box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
              background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }
          }

          &.system-tag {
            background: linear-gradient(135deg, #91d5ff 0%, #bae7ff 100%);
            border-color: #91d5ff;
            color: #0050b3;

            &:hover {
              transform: translateY(-3px) scale(1.05);
              box-shadow: 0 6px 20px rgba(145, 213, 255, 0.5);
              background: linear-gradient(135deg, #69c0ff 0%, #91d5ff 100%);
            }
          }

          &.operator-tag {
            background: linear-gradient(135deg, #ffa726 0%, #ffb74d 100%);
            border-color: #ffa726;
            color: #e65100;
            font-weight: 600;
            font-family: 'Courier New', monospace;

            &:hover {
              transform: translateY(-3px) scale(1.05);
              box-shadow: 0 6px 20px rgba(255, 167, 38, 0.5);
              background: linear-gradient(135deg, #ff9800 0%, #ffa726 100%);
            }
          }
        }
      }
    }

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
      border-radius: 3px;

      &:hover {
        background: linear-gradient(135deg, #337ecc 0%, #4da6ff 100%);
      }
    }
  }
  
  // 搜索模板样式
  .template-list {
    .template-item {
      display: flex;
      flex-direction: column;
      padding: 12px;
      margin-bottom: 8px;
      background: linear-gradient(135deg, #f8fafe 0%, #ffffff 100%);
      border: 1px solid #e1e8f0;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
        border-color: #409eff;
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
      }
      
      .template-label {
        font-weight: 600;
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 4px;
      }
      
      .template-desc {
        font-size: 12px;
        color: #7c8db5;
        line-height: 1.4;
      }
    }
  }
}

.tag-help-content {
  max-width: 360px;
  
  h4 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e8f4fd;
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  p {
    margin: 8px 0;
    color: #5a6c7d;
    font-size: 14px;
    line-height: 1.6;
    padding-left: 4px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
  }
}

// 帮助图标样式
.help-icon {
  margin-left: 8px;
  color: #409eff;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  
  &:hover {
    color: #66b1ff;
    transform: scale(1.1);
  }
}

/* IP信息显示样式 */
.ip-info {
  .ip-address {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .el-tag {
      white-space: nowrap;
    }
  }
  
  .no-ip {
    color: #c0c4cc;
    font-size: 12px;
  }
}

.export-dialog .radio-group .el-radio .el-radio__label {
  display: flex !important;
  align-items: center !important;
  flex: 1 !important;
  margin-left: 0 !important;
}

/* 高级搜索表单样式 */
.advanced-search-form {
  margin-top: 15px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.advanced-search-form .el-form-item {
  margin-bottom: 15px;
}

.advanced-search-form .el-form-item__label {
  font-weight: 500;
  color: #495057;
}

/* 基础搜索表单样式 */
.search-form {
  margin-bottom: 10px;
}

.search-form .el-form-item {
  margin-bottom: 15px;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: #303133;
}

/* 人员信息样式 */
.personnel-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.personnel-info .claimer-info,
.personnel-info .assignee-info,
.personnel-info .resolver-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  width: 100%;
  min-height: 32px;
}

.personnel-info .claimer-info {
  background-color: #f0f9ff;
  border-color: #bae6fd;
}

.personnel-info .assignee-info {
  background-color: #fff7ed;
  border-color: #fed7aa;
}

.personnel-info .resolver-info {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
}

.personnel-avatar {
  flex-shrink: 0;
  font-size: 12px;
  font-weight: 500;
}

.personnel-name {
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
  flex: 1;
  max-width: 100px;
}

.personnel-role {
  font-size: 9px;
  color: #6c757d;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
  flex-shrink: 0;
  padding: 1px 4px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 2px;
}

.personnel-time {
  font-size: 10px;
  color: #868e96;
  margin-top: 2px;
  font-style: italic;
}

.personnel-time .time-text {
  font-size: 11px;
  color: #909399;
  cursor: help;
}

.assignee-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px 0;
}

.assignee-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 4px;
  margin-bottom: 4px;
}

.no-personnel {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
  padding: 8px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px dashed #d9d9d9;
}

.no-personnel i {
  font-size: 14px;
}

/* 人员流转样式 */
.personnel-flow {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  padding: 4px 0;
  justify-content: center;
}

.personnel-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 3px 8px;
  border-radius: 4px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  min-height: 24px;
  width: 100%;
  max-width: 160px;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;
}

.personnel-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.personnel-item.assignee {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-color: #ffc107;
}

.personnel-item.claimer {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border-color: #17a2b8;
}

.personnel-item.resolver {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border-color: #28a745;
}

.personnel-item.empty {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-color: #6c757d;
  opacity: 0.7;
}

.personnel-avatar {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 7px;
  color: #fff;
  font-weight: 500;
}

.personnel-item.assignee .personnel-avatar {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
}

.personnel-item.claimer .personnel-avatar {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.personnel-item.resolver .personnel-avatar {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.personnel-item.empty .personnel-avatar {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.personnel-content {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.personnel-name {
  font-size: 11px;
  font-weight: 500;
  color: #2c3e50;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
  flex: 1;
}

.personnel-role {
  font-size: 9px;
  color: #6c757d;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
  flex-shrink: 0;
  padding: 1px 4px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 2px;
}

.personnel-time {
  font-size: 10px;
  color: #868e96;
  margin-top: 2px;
  font-style: italic;
}

.personnel-time .time-text {
  font-size: 11px;
  color: #909399;
  cursor: help;
}

.assignee-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px 0;
}

.assignee-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 4px;
  margin-bottom: 4px;
}

.no-personnel {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
  padding: 8px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px dashed #d9d9d9;
}

.no-personnel i {
  font-size: 14px;
}

/* 人员流转样式 */
.personnel-flow {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  padding: 4px 0;
  justify-content: center;
}

.personnel-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 3px 8px;
  border-radius: 4px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  min-height: 24px;
  width: 100%;
  max-width: 160px;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;
}

.personnel-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.personnel-item.assignee {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-color: #ffc107;
}

.personnel-item.claimer {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border-color: #17a2b8;
}

.personnel-item.resolver {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border-color: #28a745;
}

.personnel-item.empty {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-color: #6c757d;
  opacity: 0.7;
}

.personnel-avatar {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 7px;
  color: #fff;
  font-weight: 500;
}

.personnel-item.assignee .personnel-avatar {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
}

.personnel-item.claimer .personnel-avatar {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.personnel-item.resolver .personnel-avatar {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.personnel-item.empty .personnel-avatar {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.personnel-content {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.personnel-name {
  font-size: 11px;
  font-weight: 500;
  color: #2c3e50;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
  flex: 1;
}

.personnel-role {
  font-size: 9px;
  color: #6c757d;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
  flex-shrink: 0;
  padding: 1px 4px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 2px;
}

.personnel-time {
  font-size: 10px;
  color: #868e96;
  margin-top: 2px;
  font-style: italic;
}

/* 无数据样式 */
.no-data {
  color: #c0c4cc;
  font-style: italic;
}

/* 告警列表页面专用的highlight样式（包括抽屉组件） */
.alert-list-page .highlight,
.alert-list-page .el-drawer .highlight,
.alert-list-page .el-drawer__body .highlight {
  background-color: #ffff00 !important;
  color: #000000 !important;
  border-radius: 2px !important;
  padding: 1px 2px !important;
  margin: 0 -2px !important;
  border: 1px solid #ffa600 !important;
  font-weight: bold !important;
  box-shadow: 0 0 2px rgba(255, 166, 0, 0.5) !important;
}

.alert-list-page .dark-theme .highlight,
.alert-list-page .el-drawer .dark-theme .highlight,
.alert-list-page .el-drawer__body .dark-theme .highlight {
  background-color: #ff9800 !important;
  color: #000000 !important;
  border: 1px solid #ffd740 !important;
}

/* 标签搜索样式 - 仅针对告警列表页 */
.alert-list-page .tag-search-container {
  overflow: visible !important;
}

.alert-list-page .tag-suggestions {
  position: fixed !important;
  transform: none !important;
  will-change: auto !important;
}

/* 标签搜索详细样式 */
.tag-search-header {
  margin-bottom: 10px;
}

.tag-search-header .search-mode-toggle .el-button {
  padding: 5px 15px;
  font-size: 12px;
}

.simple-search {
  margin-top: 8px;
}

.advanced-search {
  margin-top: 8px;
}

/* 高级选择器样式 */
.advanced-selector .selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.advanced-selector .selector-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
}

/* 标签弹窗样式 */
.all-tags .tag-item {
  margin: 3px 5px 3px 0;
  display: inline-block;
}

/* 导出对话框radio样式强制覆盖 */
.export-dialog .radio-group {
  display: flex !important;
  gap: 20px !important;
}

.export-dialog .radio-group .el-radio {
  display: flex !important;
  align-items: center !important;
  flex: 1 !important;
  margin-right: 0 !important;
  white-space: normal !important;
  margin-bottom: 0 !important;
}

.export-dialog .radio-group .el-radio .el-radio__input {
  margin-right: 8px !important;
  flex-shrink: 0 !important;
}

.export-dialog .radio-group .el-radio .el-radio__label {
  display: flex !important;
  align-items: center !important;
  flex: 1 !important;
  margin-left: 0 !important;
}

.alert-object-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  min-height: 20px;
  justify-content: center;
}

.alert-object-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.alert-object-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f4f8fb;
  color: #2d8cf0;
  border: 1px solid #d0e2f5;
  border-radius: 12px;
  padding: 2px 10px;
  margin: 2px 4px 2px 0;
  font-size: 13px;
  line-height: 20px;
  min-height: 24px;
  transition: box-shadow 0.2s, background 0.2s, border-color 0.2s;
  cursor: pointer;
}

.alert-object-tag:hover {
  box-shadow: 0 2px 8px rgba(45,140,240,0.08);
  background: #eaf3fb;
  border-color: #b3d8fb;
}

.alert-object-raw {
  background-color: #e0e0e0;
  border-radius: 4px;
  padding: 2px 5px;
  font-size: 12px;
  color: #666;
  text-align: center;
}

.no-alert-object {
  color: #909399;
  font-style: italic;
  text-align: center;
  width: 100%;
}

/* 告警详情抽屉中的告警对象样式 */
.alert-detail-drawer .alert-object-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: flex-start;
}

.alert-detail-drawer .alert-object-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  width: 100%;
}

.alert-detail-drawer .alert-object-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  border: 1px solid #90caf9;
  transition: all 0.2s ease;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alert-detail-drawer .alert-object-tag:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
  color: #1565c0;
  border-color: #64b5f6;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
}

.alert-detail-drawer .alert-object-raw {
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  color: #616161;
  border: 1px solid #bdbdbd;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  word-break: break-all;
  max-width: 100%;
}

</style>


