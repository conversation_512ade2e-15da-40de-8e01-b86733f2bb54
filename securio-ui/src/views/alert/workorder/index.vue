<template>
  <div class="app-container workorder-statistics-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>告警工单统计</h2>
    </div>

    <!-- 时间范围选择 -->
    <el-card class="time-filter-card" shadow="never">
      <el-form :model="queryParams" :inline="true" class="time-filter-form">
        <el-form-item label="时间范围">
          <el-select v-model="queryParams.timeRangeType" @change="handleTimeRangeChange" style="width: 150px">
            <el-option label="近1小时" value="1h" />
            <el-option label="近24小时" value="24h" />
            <el-option label="近一周" value="7d" />
            <el-option label="近一月" value="30d" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="queryParams.timeRangeType === 'custom'">
          <el-date-picker
            v-model="queryParams.customTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 运维部整体统计 -->
    <el-card class="department-stats-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="card-title">运维部整体统计</span>
      </div>
      <div class="stats-overview">
        <div class="stats-item">
          <div class="stats-number">{{ departmentStats.totalAlerts || 0 }}</div>
          <div class="stats-label">故障总量</div>
        </div>
        <div class="stats-item critical">
          <div class="stats-number">{{ departmentStats.criticalAlerts || 0 }}</div>
          <div class="stats-label">严重告警</div>
        </div>
        <div class="stats-item pending">
          <div class="stats-number">{{ departmentStats.pendingAlerts || 0 }}</div>
          <div class="stats-label">待处理</div>
        </div>
        <div class="stats-item resolved">
          <div class="stats-number">{{ departmentStats.resolvedAlerts || 0 }}</div>
          <div class="stats-label">已解决</div>
        </div>
        <div class="stats-item security">
          <div class="stats-number">{{ departmentStats.securityAlerts || 0 }}</div>
          <div class="stats-label">安全类告警</div>
        </div>
        <div class="stats-item ops">
          <div class="stats-number">{{ departmentStats.opsAlerts || 0 }}</div>
          <div class="stats-label">运维类告警</div>
        </div>
      </div>
      <div class="stats-metrics">
        <div class="metric-item">
          <div class="metric-label">部门MTTA</div>
          <div class="metric-value">{{ formatTime(departmentStats.mtta) }}</div>
        </div>
        <div class="metric-item">
          <div class="metric-label">部门MTTR</div>
          <div class="metric-value">{{ formatTime(departmentStats.mttr) }}</div>
        </div>
      </div>
    </el-card>

    <!-- 团队统计 -->
    <div v-for="team in departmentStats.teams" :key="team.teamId" class="team-section">
      <el-card class="team-stats-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">{{ team.teamName }} ({{ team.users ? team.users.length : 0 }}人)</span>
        </div>
        
        <!-- 团队统计概览 -->
        <div class="team-overview">
          <div class="stats-item">
            <div class="stats-number">{{ team.totalAlerts || 0 }}</div>
            <div class="stats-label">故障总量</div>
          </div>
          <div class="stats-item critical">
            <div class="stats-number">{{ team.criticalAlerts || 0 }}</div>
            <div class="stats-label">严重告警</div>
          </div>
          <div class="stats-item pending">
            <div class="stats-number">{{ team.pendingAlerts || 0 }}</div>
            <div class="stats-label">待处理</div>
          </div>
          <div class="stats-item resolved">
            <div class="stats-number">{{ team.resolvedAlerts || 0 }}</div>
            <div class="stats-label">已解决</div>
          </div>
        </div>
        <div class="stats-metrics">
          <div class="metric-item">
            <div class="metric-label">团队MTTA</div>
            <div class="metric-value">{{ formatTime(team.mtta) }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">团队MTTR</div>
            <div class="metric-value">{{ formatTime(team.mttr) }}</div>
          </div>
        </div>

        <!-- 人员统计 -->
        <div class="users-grid">
          <div 
            v-for="user in team.users" 
            :key="user.userId" 
            class="user-card"
            :class="getPerformanceClass(user.performanceLevel)"
            @click="handleUserClick(user)"
          >
            <div class="user-header">
              <span class="user-name">{{ user.displayName }}</span>
            </div>
            <div class="user-metrics">
              <div class="metric-row">
                <span class="metric-label">MTTA:</span>
                <span class="metric-value">{{ formatTime(user.mtta) }}</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">MTTR:</span>
                <span class="metric-value">{{ formatTime(user.mttr) }}</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">故障:</span>
                <span class="metric-value">{{ user.totalAlerts || 0 }}</span>
              </div>
              <div class="metric-row">
                <span class="metric-label critical">严重:</span>
                <span class="metric-value critical">{{ user.criticalAlerts || 0 }}</span>
              </div>
              <div class="metric-row">
                <span class="metric-label pending">待处理:</span>
                <span class="metric-value pending">{{ user.pendingAlerts || 0 }}</span>
              </div>
              <div class="metric-row">
                <span class="metric-label resolved">已解决:</span>
                <span class="metric-value resolved">{{ user.resolvedAlerts || 0 }}</span>
              </div>
            </div>
            <div class="user-actions">
              <el-button size="mini" type="text" @click.stop="handleUserDetails(user)">详情</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 人员告警详情抽屉 -->
    <el-drawer
      title="人员告警详情"
      :visible.sync="userDetailVisible"
      direction="rtl"
      size="60%"
      :before-close="handleDrawerClose"
    >
      <div v-if="currentUser" class="user-detail-drawer">
        <!-- 人员统计信息 -->
        <el-card class="user-stats-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">{{ currentUser.userName }} - 统计信息</span>
          </div>
          <div class="stats-overview">
            <div class="stats-item">
              <div class="stats-number">{{ currentUser.totalAlerts || 0 }}</div>
              <div class="stats-label">故障总量</div>
            </div>
            <div class="stats-item critical">
              <div class="stats-number">{{ currentUser.criticalAlerts || 0 }}</div>
              <div class="stats-label">严重告警</div>
            </div>
            <div class="stats-item pending">
              <div class="stats-number">{{ currentUser.pendingAlerts || 0 }}</div>
              <div class="stats-label">待处理</div>
            </div>
            <div class="stats-item resolved">
              <div class="stats-number">{{ currentUser.resolvedAlerts || 0 }}</div>
              <div class="stats-label">已解决</div>
            </div>
          </div>
          <div class="stats-metrics">
            <div class="metric-item">
              <div class="metric-label">个人MTTA</div>
              <div class="metric-value">{{ formatTime(currentUser.mtta) }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">个人MTTR</div>
              <div class="metric-value">{{ formatTime(currentUser.mttr) }}</div>
            </div>
          </div>
        </el-card>

        <!-- 告警列表 -->
        <el-card class="alert-list-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <span class="card-title">告警列表</span>
          </div>
          <el-table
            v-loading="alertListLoading"
            :data="userAlertList"
            style="width: 100%"
            size="small"
          >
            <el-table-column prop="title" label="告警标题" min-width="200" show-overflow-tooltip />
            <el-table-column prop="severity" label="严重程度" width="100" align="center">
              <template slot-scope="scope">
                <el-tag :type="getSeverityType(scope.row.severity)" size="mini">
                  {{ getSeverityText(scope.row.severity) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="mini">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="occurredAt" label="发生时间" width="160" align="center">
              <template slot-scope="scope">
                {{ parseTime(scope.row.occurredAt) }}
              </template>
            </el-table-column>
            <el-table-column prop="ackedAt" label="认领时间" width="160" align="center">
              <template slot-scope="scope">
                {{ scope.row.ackedAt ? parseTime(scope.row.ackedAt) : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="resolvedAt" label="解决时间" width="160" align="center">
              <template slot-scope="scope">
                {{ scope.row.resolvedAt ? parseTime(scope.row.resolvedAt) : '-' }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getDepartmentStatistics, getUserAlertDetails } from '@/api/alert/statistics'
import { parseTime } from '@/utils/securio'

export default {
  name: 'WorkorderStatistics',
  data() {
    return {
      // 查询参数
      queryParams: {
        timeRangeType: '7d',
        customTimeRange: null,
        timeRange: null
      },
      // 部门统计数据
      departmentStats: {
        totalAlerts: 0,
        criticalAlerts: 0,
        pendingAlerts: 0,
        resolvedAlerts: 0,
        securityAlerts: 0,
        opsAlerts: 0,
        mtta: 0,
        mttr: 0,
        teams: []
      },
      // 人员详情相关
      userDetailVisible: false,
      currentUser: null,
      userAlertList: [],
      alertListLoading: false
    }
  },
  created() {
    this.initTimeRange()
    this.getStatistics()
  },
  methods: {
    // 初始化时间范围
    initTimeRange() {
      const now = new Date()
      let startTime
      
      switch (this.queryParams.timeRangeType) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000)
          break
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      }
      
      this.queryParams.timeRange = [startTime.getTime(), now.getTime()]
    },

    // 时间范围变化处理
    handleTimeRangeChange() {
      if (this.queryParams.timeRangeType === 'custom') {
        this.queryParams.timeRange = null
      } else {
        this.initTimeRange()
      }
    },

    // 查询统计数据
    async getStatistics() {
      try {
        const response = await getDepartmentStatistics(this.queryParams)
        if (response.code === 200) {
          this.departmentStats = response.data
        } else {
          this.$message.error(response.msg || '获取统计数据失败')
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败')
      }
    },

    // 查询按钮
    handleQuery() {
      if (this.queryParams.timeRangeType === 'custom' && this.queryParams.customTimeRange) {
        this.queryParams.timeRange = [
          new Date(this.queryParams.customTimeRange[0]).getTime(),
          new Date(this.queryParams.customTimeRange[1]).getTime()
        ]
      }
      this.getStatistics()
    },

    // 重置按钮
    handleReset() {
      this.queryParams.timeRangeType = '7d'
      this.queryParams.customTimeRange = null
      this.initTimeRange()
      this.getStatistics()
    },

    // 人员点击处理
    handleUserClick(user) {
      this.currentUser = user
      this.userDetailVisible = true
      this.getUserAlertDetails(user.userId)
    },

    // 人员详情按钮
    handleUserDetails(user) {
      this.currentUser = user
      this.userDetailVisible = true
      this.getUserAlertDetails(user.userId)
    },

    // 获取人员告警明细
    async getUserAlertDetails(userId) {
      this.alertListLoading = true
      try {
        const response = await getUserAlertDetails(userId, this.queryParams)
        if (response.code === 200) {
          this.userAlertList = response.data
        } else {
          this.$message.error(response.msg || '获取告警明细失败')
        }
      } catch (error) {
        console.error('获取告警明细失败:', error)
        this.$message.error('获取告警明细失败')
      } finally {
        this.alertListLoading = false
      }
    },

    // 关闭抽屉
    handleDrawerClose() {
      this.userDetailVisible = false
      this.currentUser = null
      this.userAlertList = []
    },

    // 格式化时间显示
    formatTime(hours) {
      if (!hours || hours === 0) {
        return '0小时'
      }
      if (hours < 1) {
        return `${Math.round(hours * 60)}分钟`
      }
      return `${hours.toFixed(1)}小时`
    },

    // 获取性能等级样式类
    getPerformanceClass(level) {
      if (!level) return ''
      // 兼容字符串和枚举类型
      const val = typeof level === 'string' ? level : (level && level.name)
      switch (val) {
        case 'GOLD':
          return 'performance-gold'
        case 'SILVER':
          return 'performance-silver'
        case 'BRONZE':
          return 'performance-bronze'
        case 'WARNING':
          return 'performance-warning'
        case 'CRITICAL':
          return 'performance-critical'
        case 'NORMAL':
          return 'performance-normal'
        default:
          return ''
      }
    },

    // 解析时间
    parseTime(time) {
      return parseTime(time)
    },

    // 获取严重程度类型
    getSeverityType(severity) {
      const typeMap = {
        'critical': 'danger',
        'high': 'warning',
        'medium': 'info',
        'low': 'success',
        'info': ''
      }
      return typeMap[severity] || ''
    },

    // 获取严重程度文本
    getSeverityText(severity) {
      const textMap = {
        'critical': '严重',
        'high': '高危',
        'medium': '中危',
        'low': '低危',
        'info': '信息'
      }
      return textMap[severity] || severity
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'triggered': 'danger',
        'acknowledged': 'warning',
        'resolved': 'success',
        'suppressed': 'info'
      }
      return typeMap[status] || ''
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'triggered': '触发',
        'acknowledged': '确认',
        'resolved': '已解决',
        'suppressed': '沉默'
      }
      return textMap[status] || status
    }
  }
}
</script>

<style scoped>
.workorder-statistics-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.time-filter-card {
  margin-bottom: 20px;
}

.time-filter-form {
  display: flex;
  align-items: center;
}

.department-stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stats-overview {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stats-item {
  flex: 1;
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.stats-item.critical {
  background: #fff5f5;
  border-color: #fed7d7;
}

.stats-item.pending {
  background: #fffbf0;
  border-color: #feebc8;
}

.stats-item.resolved {
  background: #f0fff4;
  border-color: #c6f6d5;
}

.stats-item.security {
  background: #f0f8ff;
  border-color: #87ceeb;
}

.stats-item.ops {
  background: #f5f5dc;
  border-color: #daa520;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #606266;
}

.stats-metrics {
  display: flex;
  gap: 40px;
}

.metric-item {
  flex: 1;
  text-align: center;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.team-section {
  margin-bottom: 20px;
}

.team-stats-card {
  margin-bottom: 20px;
}

.team-overview {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.team-overview .stats-item {
  padding: 15px;
}

.team-overview .stats-number {
  font-size: 24px;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.user-card {
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-header {
  margin-bottom: 10px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.user-metrics {
  margin-bottom: 10px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.metric-row .metric-label {
  font-size: 12px;
  color: #606266;
}

.metric-row .metric-value {
  font-size: 12px;
  font-weight: 600;
  color: #303133;
}

.user-actions {
  text-align: center;
}

/* 性能等级样式 */
.performance-gold {
  background: linear-gradient(135deg, #fffbe6 0%, #ffe066 100%);
  border: 2px solid #ffe066;
  box-shadow: 0 2px 8px rgba(255, 224, 102, 0.18);
}

.performance-silver {
  background: linear-gradient(135deg, #f5f7fa 0%, #cfd8dc 100%);
  border: 2px solid #b0bec5;
  box-shadow: 0 2px 8px rgba(176, 190, 197, 0.18);
}

.performance-bronze {
  background: linear-gradient(135deg, #fbeee6 0%, #e0b07a 100%);
  border: 2px solid #e0b07a;
  box-shadow: 0 2px 8px rgba(224, 176, 122, 0.18);
}

.performance-warning {
  background: linear-gradient(135deg, #fff3e0 0%, #ffb74d 100%);
  border: 2px solid #ffb74d;
  box-shadow: 0 2px 8px rgba(255, 183, 77, 0.18);
}

.performance-critical {
  background: linear-gradient(135deg, #ffeaea 0%, #ff8a80 100%);
  border: 2px solid #ff8a80;
  box-shadow: 0 2px 8px rgba(255, 138, 128, 0.18);
  color: #fff;
}

.performance-critical .user-name,
.performance-critical .metric-row .metric-value {
  color: #fff;
}

.performance-normal {
  background: linear-gradient(135deg, #f4f8fb 0%, #e3e9f1 100%);
  border: 2px solid #d3dae6;
  box-shadow: 0 2px 8px rgba(211, 218, 230, 0.12);
}

.user-detail-drawer {
  padding: 20px;
}

.user-stats-card {
  margin-bottom: 20px;
}

.alert-list-card {
  margin-top: 20px;
}
</style> 