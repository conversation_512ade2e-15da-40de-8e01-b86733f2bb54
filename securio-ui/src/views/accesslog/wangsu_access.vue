<template>
  <div class="app-container">
    <div class="custom-tabs-container">
      <div class="custom-tabs">
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'overview' }"
          @click="activeTab = 'overview'"
        >
          网宿 WAF 日志概览
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'query' }"
          @click="activeTab = 'query'"
        >
          网宿 WAF 日志探查
        </div>
      </div>
    </div>

    <div class="tab-content">
      <wangsu-stat-overview v-if="activeTab === 'overview'"></wangsu-stat-overview>
      <log-query-component
        v-else-if="activeTab === 'query'"
        title="网宿 WAF 访问日志"
        :settings="logSettings"
        stream="SECURIO_WANGSU_WAF_ACCESS"
        instance="vmlog2"
        defaultTimeRange="5m"
      />
    </div>
  </div>
</template>

<script>
import WangsuStatOverview from '@/views/accesslog/components/wangsu_stat_overview'
import LogQueryComponent from '@/components/log_query_component'

export default {
  name: 'WangsuAccess',
  components: {
    WangsuStatOverview,
    LogQueryComponent
  },
  data() {
    return {
      loading: false,
      activeTab: 'overview',
      logSettings: {
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          { prop: 'host', label: '客户端IP', tag: true },
          { prop: 'channel', label: '频道' },
          { prop: 'clientprovince', label: '客户端省份' },
          { prop: 'clientcountry', label: '客户端国家' },
          { prop: 'clientisp', label: '客户端运营商' },
          { prop: 'cdnip', label: '服务端IP' },
          { prop: 'servercountry', label: '服务端国家' },
          { prop: 'serverprovince', label: '服务端省份' },
          { prop: 'serverisp', label: '服务端运营商' },
          { prop: 'code', label: '响应状态码' },
          { prop: 'url', label: '请求URL', showOverflowTooltip: true },
          { prop: 'referer', label: '访问来源', showOverflowTooltip: true },
          { prop: 'ua', label: '客户端UA信息', showOverflowTooltip: true, className: 'log-message' },
          { prop: 'browser', label: '浏览器类型' },
          { prop: 'os', label: '操作系统类型' },
          { prop: 'device', label: '设备类型' },
          { prop: 'scheme', label: '请求scheme' },
          { prop: 'protocol', label: '请求协议' },
          { prop: 'method', label: '请求方法' },
          { prop: 'reqid', label: '请求ID' },
          { prop: 'logsample', label: '采样率' },
          { prop: 'size', label: '流量大小' },
          { prop: 'bsize', label: '响应body大小' },
          { prop: 'upsize', label: '请求大小包含头' },
          { prop: 'msec_dltime', label: '响应时间' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  methods: {
  }
}
</script>

<style scoped>
.app-container {
  padding: 0;
}

.custom-tabs-container {
  padding: 10px 0px 0px 10px;
}

.custom-tabs {
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden;
}

.custom-tab-item {
  padding: 8px 10px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
  position: relative;
}

.custom-tab-item.active {
  color: #E6A23C;
  font-weight: 500;
}

.custom-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #E6A23C;
}

.tab-content {
  margin-top: 20px;
}
</style>
    