<template>
  <div class="app-container">
    <div class="custom-tabs-container">
      <div class="custom-tabs">
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'overview' }"
          @click="activeTab = 'overview'"
        >
          Nginx 日志概览
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'query' }"
          @click="activeTab = 'query'"
        >
          Nginx 日志探查
        </div>
      </div>
    </div>

    <div class="tab-content">
      <nginx-stat-overview v-if="activeTab === 'overview'"></nginx-stat-overview>
      <log-query-component
        v-else-if="activeTab === 'query'"
        title="Nginx 访问日志"
        :settings="logSettings"
        stream="NGINX_ACCESS"
        instance="vmlog2"
        defaultTimeRange="5m"
      />
    </div>
  </div>
</template>

<script>
import NginxStatOverview from '@/views/accesslog/components/nginx_stat_overview'
import LogQueryComponent from '@/components/log_query_component'

export default {
  name: 'NginxAccess',
  components: {
    NginxStatOverview,
    LogQueryComponent
  },
  data() {
    return {
      loading: false,
      activeTab: 'overview',
      logSettings: {
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          { prop: 'message.remote_addr', label: '客户端 IP', tag: true },
          { prop: 'message.http_host', label: '主机名' },
          { prop: 'message.request_method', label: '请求方法' },
          { prop: 'message.request_uri', label: '请求路径' },
          { prop: 'message.status', label: '状态码' },
          { prop: 'message.body_bytes_sent', label: '响应大小' },
          { prop: 'message.request_time', label: '请求时间' },
          { prop: 'message.upstream_ip', label: '上游服务器' },
          { prop: 'message.http_user_agent', label: '用户代理', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  methods: {
  }
}
</script>

<style scoped>
.app-container {
  padding: 0;
}

.custom-tabs-container {
  padding: 10px 0px 0px 10px;
}

.custom-tabs {
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden;
}

.custom-tab-item {
  padding: 8px 10px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
  position: relative;
}

.custom-tab-item.active {
  color: #E6A23C;
  font-weight: 500;
}

.custom-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #E6A23C;
}

.tab-content {
  margin-top: 20px;
}
</style>