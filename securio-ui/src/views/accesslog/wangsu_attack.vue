<template>
    <div class="app-container">
    <div class="custom-tabs-container">
      <div class="custom-tabs">
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'overview' }"
          @click="activeTab = 'overview'"
        >
          网宿 WAF 攻击概览
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'query' }"
          @click="activeTab = 'query'"
        >
          网宿 WAF 攻击探查
        </div>
      </div>
    </div>

    <div class="tab-content">
      <wangsu-attack-overview v-if="activeTab === 'overview'"></wangsu-attack-overview>
      <log-query-component
        v-else-if="activeTab === 'query'"
        title="网宿 WAF 攻击日志"
        :settings="logSettings"
        stream="SECURIO_WANGSU_WAF_ATTACK"
        instance="vmlog2"
        defaultTimeRange="5m"
      />
    </div>
    </div>
  </template>
    
  <script>
import WangsuAttackOverview from '@/views/accesslog/components/wangsu_attack_overview'
import LogQueryComponent from '@/components/log_query_component'
  
  export default {
      name: 'WangsuAttack',
      components: {
    WangsuAttackOverview,
    LogQueryComponent
      },
      data() {
          return {
      loading: false,
      activeTab: 'overview',
      logSettings: {
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          { prop: 'ip', label: '攻击IP', tag: true },
          { prop: 'host', label: '被攻击域名' },
          { prop: 'attack_type', label: '攻击类型', width: '150' },
          { prop: 'act', label: '处理动作', width: '100', formatter: this.formatAction },
          { prop: 'custom_rule_name', label: '规则名称', showOverflowTooltip: true },
          { prop: 'final_rule_id', label: '规则ID', width: '100' },
          { prop: 'ip_province_cn', label: '攻击省份', width: '100' },
          { prop: 'ip_city_cn', label: '攻击城市', width: '100' },
          { prop: 'ip_country_cn', label: '攻击国家', width: '100' },
          { prop: 'url', label: '攻击URL', showOverflowTooltip: true },
          { prop: 'mode', label: '请求方法', width: '100' },
          { prop: 'version', label: 'HTTP版本', width: '100' },
          { prop: 'status_code', label: '响应状态', width: '100' },
          { prop: 'user_agent', label: 'User Agent', showOverflowTooltip: true, className: 'log-message' },
          { prop: 'referer', label: '来源页面', showOverflowTooltip: true },
          { prop: 'content', label: '攻击详情', showOverflowTooltip: true, className: 'log-message' },
          { prop: 'custom_msg', label: '自定义信息', showOverflowTooltip: true },
          { prop: 'client_id', label: '客户端ID', width: '150' },
          { prop: 'uuid', label: 'UUID', width: '200' },
          { prop: 'ws_request_id', label: '请求ID', width: '200' },
          { prop: 'attack_time', label: '攻击时间戳', width: '150' },
          { prop: 'collect_time', label: '收集时间戳', width: '150' },
          { prop: 'event_time', label: '事件时间戳', width: '150' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
          }
      },
      methods: {
    formatAction(row, column, cellValue) {
      const actionMap = {
        '1': '阻断',
        '2': '告警',
        '3': '放行'
      }
      return actionMap[cellValue] || cellValue
    }
      }
  }
  </script>

<style scoped>
.app-container {
  padding: 0;
}

.custom-tabs-container {
  padding: 10px 0px 0px 10px;
}

.custom-tabs {
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden;
}

.custom-tab-item {
  padding: 8px 10px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
  position: relative;
}

.custom-tab-item.active {
  color: #E6A23C;
  font-weight: 500;
}

.custom-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #E6A23C;
}

.tab-content {
  margin-top: 20px;
}
</style>
    