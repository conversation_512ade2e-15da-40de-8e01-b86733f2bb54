<template>
  <div class="audit-overview wangsu-stat-overview" v-loading="loading" element-loading-text="加载中...">
    <!-- 全局控制面板 -->
    <div class="global-controls-panel">
      <div class="global-title">时间范围控制</div>
      <div class="global-controls">
        <el-select v-model="globalTimeRange" placeholder="选择时间范围" size="small" @change="handleGlobalTimeRangeChange">
          <el-option label="最近 5 分钟" value="5m"></el-option>
          <el-option label="最近 30 分钟" value="30m"></el-option>
          <el-option label="最近 1 小时" value="1h"></el-option>
          <el-option label="最近 6 小时" value="6h"></el-option>
          <el-option label="最近 12 小时" value="12h"></el-option>
          <el-option label="最近 24 小时" value="1d"></el-option>
          <el-option label="最近 3 天" value="3d"></el-option>
          <el-option label="最近 7 天" value="7d"></el-option>
        </el-select>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text=""
          @change="handleAutoRefreshChange">
        </el-switch>
        <el-button type="primary" icon="el-icon-refresh" size="small" @click="refreshAllData">刷新数据</el-button>
      </div>
    </div>

    <!-- 统计数据卡片 -->
    <h3 class="section-title">数据总览</h3>
    <div class="stat-cards-container">
      <div class="stat-card">
        <div class="stat-circle" style="background-color: rgba(24, 144, 255, 0.15);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #1890ff;">{{ formatNumber(totalRequests) }}</div>
          <div class="stat-title">总请求数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-circle" style="background-color: rgba(82, 196, 26, 0.15);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #52c41a;">{{ formatNumber(uniqueVisitors) }}</div>
          <div class="stat-title">独立访客</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-circle" style="background-color: rgba(82, 196, 26, 0.15);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #52c41a;">{{ successRate }}%</div>
          <div class="stat-title">成功率</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-circle" style="background-color: rgba(24, 144, 255, 0.15);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #1890ff;">{{ formatBytes(totalTraffic) }}</div>
          <div class="stat-title">服务端到客户端流量</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-circle" style="background-color: rgba(245, 34, 45, 0.15);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #f5222d;">{{ formatBytes(totalUpTraffic) }}</div>
          <div class="stat-title">客户端请求大小</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <h3 class="section-title">请求统计</h3>
    
    <!-- 时间序列折线图 -->
    <div class="timeseries-section">
      <h4 class="subsection-title">渠道时间序列分析</h4>
      <div class="timeseries-controls">
        <div class="control-group">
          <label>渠道名称：</label>
          <el-input 
            v-model="timeseriesForm.channel" 
            placeholder="请输入渠道名称，如：appapi.5i5j.com"
            style="width: 300px;"
            @change="onTimeseriesFormChange"
          ></el-input>
        </div>
        <div class="control-group">
          <label>时间范围：</label>
          <el-date-picker
            v-model="timeseriesForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-ddTHH:mm:ssZ"
            style="width: 350px;"
            @change="onTimeseriesFormChange"
          ></el-date-picker>
        </div>
        <div class="control-group">
          <label>时间步长：</label>
          <el-select v-model="timeseriesForm.step" style="width: 120px;" @change="onTimeseriesFormChange">
            <el-option label="5分钟" value="5m"></el-option>
            <el-option label="15分钟" value="15m"></el-option>
            <el-option label="30分钟" value="30m"></el-option>
            <el-option label="1小时" value="1h"></el-option>
            <el-option label="3小时" value="3h"></el-option>
            <el-option label="6小时" value="6h"></el-option>
            <el-option label="12小时" value="12h"></el-option>
            <el-option label="1天" value="1d"></el-option>
          </el-select>
        </div>
        <div class="control-group">
          <label>统计指标：</label>
          <el-select v-model="timeseriesForm.metric" style="width: 150px;" @change="onTimeseriesFormChange">
            <el-option label="请求数" value="count"></el-option>
            <el-option label="响应流量" value="size"></el-option>
            <el-option label="请求流量" value="upsize"></el-option>
            <el-option label="独立访客" value="unique"></el-option>
          </el-select>
        </div>
        <el-button type="primary" @click="loadTimeSeries" :loading="timeseriesLoading">查询</el-button>
      </div>
      <div class="timeseries-chart">
        <div id="timeseriesChart" style="width: 100%; height: 400px;" v-loading="timeseriesLoading"></div>
      </div>
    </div>

    <!-- 渠道排名表格 -->
    <div class="channel-ranking-section">
      <h4 class="subsection-title">渠道访问量排名（Top 50）</h4>
      <div class="ranking-controls">
        <div class="control-group">
          <label>时间范围：</label>
          <el-select v-model="rankingForm.timeRange" style="width: 150px;" @change="loadChannelRanking">
            <el-option label="最近1小时" value="1h"></el-option>
            <el-option label="最近6小时" value="6h"></el-option>
            <el-option label="最近12小时" value="12h"></el-option>
            <el-option label="最近1天" value="1d"></el-option>
            <el-option label="最近3天" value="3d"></el-option>
            <el-option label="最近7天" value="7d"></el-option>
          </el-select>
        </div>
        <el-button type="primary" @click="loadChannelRanking" :loading="rankingLoading">刷新排名</el-button>
      </div>
      <div class="ranking-table">
        <el-table
          :data="channelRankings"
          v-loading="rankingLoading"
          stripe
          border
          size="small"
          max-height="500"
          style="width: 100%"
        >
          <el-table-column
            prop="rank"
            label="排名"
            width="80"
            align="center"
            :render-header="renderRankHeader"
          >
            <template slot-scope="scope">
              <span class="rank-badge" :class="getRankClass(scope.row.rank)">
                {{ scope.row.rank }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="channel"
            label="渠道名称"
            min-width="300"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-link type="primary" @click="selectChannelForTimeSeries(scope.row.channel)">
                {{ scope.row.channel }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            prop="count"
            label="访问次数"
            width="150"
            align="right"
            sortable
          >
            <template slot-scope="scope">
              <span class="count-number">{{ formatNumber(scope.row.count) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="占比"
            width="120"
            align="right"
          >
            <template slot-scope="scope">
              <span class="percentage">{{ getPercentage(scope.row.count) }}%</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-button 
                type="text" 
                size="mini" 
                @click="selectChannelForTimeSeries(scope.row.channel)"
                title="分析该渠道的时间序列"
              >
                分析
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="charts-container">
      <div class="chart-item">
        <div id="statusChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div id="methodChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <div class="charts-container">
      <div class="chart-item">
        <div id="browserChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div id="osChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <div class="charts-container">
      <div class="chart-item">
        <div id="provinceChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div id="deviceChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <div class="charts-container">
      <div class="chart-item">
        <div id="channelChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div id="ispChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <div class="charts-container">
      <div class="chart-item">
        <div id="protocolChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div id="schemeChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getWangsuStats, getWangsuCharts, getWangsuTimeSeries, getWangsuChannelRanking } from '@/api/opslog/wangsu'

export default {
  name: 'WangsuStatOverview',
  data() {
    return {
      loading: false,
      totalRequests: 0,
      uniqueVisitors: 0,
      successRate: 0,
      totalTraffic: 0,
      totalUpTraffic: 0,
      chartInstances: {},
      globalTimeRange: '1h', // 全局时间范围：默认最近 1 小时
      autoRefresh: false, // 自动刷新开关
      refreshTimer: null, // 自动刷新定时器
      timeseriesForm: {
        channel: '',
        timeRange: [],
        step: '1h',
        metric: 'count'
      },
      timeseriesLoading: false,
      channelRankings: [],
      rankingForm: {
        timeRange: '1d'
      },
      rankingLoading: false,
      timeseriesChangeTimer: null // 防抖定时器
    }
  },
  mounted() {
    this.loadStatistics()
    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', this.resizeCharts)
    
    // 初始化时间序列表单的默认时间范围（最近24小时）
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    this.timeseriesForm.timeRange = [
      yesterday.toISOString().slice(0, 19) + 'Z',
      now.toISOString().slice(0, 19) + 'Z'
    ]
    this.timeseriesForm.channel = 'appapi.5i5j.com' // 设置默认渠道
    
    // 加载渠道排名数据
    this.loadChannelRanking()
    
    // 自动加载默认渠道的时间序列数据
    this.$nextTick(() => {
      this.loadTimeSeries()
    })
  },
  beforeDestroy() {
    // 清理图表实例
    Object.values(this.chartInstances).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
    
    // 清理自动刷新定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
    
    // 清理防抖定时器
    if (this.timeseriesChangeTimer) {
      clearTimeout(this.timeseriesChangeTimer)
    }
    
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.resizeCharts)
  },
  methods: {
    async loadStatistics() {
      this.loading = true
      try {
        // 并行加载统计数据和图表数据
        const [statsResponse, chartsResponse] = await Promise.all([
          // 1. 加载统计指标数据
          this.loadStatsData(),
          // 2. 加载图表数据
          this.loadChartsData()
        ])

        // 处理统计数据
        if (statsResponse) {
          this.processStatsData(statsResponse)
        }

        // 处理图表数据
        if (chartsResponse && chartsResponse.facets) {
          this.renderCharts(chartsResponse.facets)
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        this.$message.error('加载统计数据失败')
      } finally {
        this.loading = false
      }
    },

    // 加载统计指标数据
    async loadStatsData() {
      const response = await getWangsuStats(this.globalTimeRange)
      if (response.code === 200) {
        return response.data
      }
      throw new Error(response.msg || '获取统计数据失败')
    },

    // 加载图表数据
    async loadChartsData() {
      const response = await getWangsuCharts(this.globalTimeRange, 1000)
      if (response.code === 200) {
        return response.data
      }
      throw new Error(response.msg || '获取图表数据失败')
    },

    // 处理统计数据
    processStatsData(statsData) {
      this.totalRequests = statsData.totalRequests || 0
      this.uniqueVisitors = statsData.uniqueRequests || 0
      this.successRate = statsData.successRate || 0
      this.totalTraffic = statsData.totalSize || 0
      
      // 添加上行流量数据（如果需要在页面显示）
      this.totalUpTraffic = statsData.totalUpSize || 0
    },

    renderCharts(facets) {
      const facetMap = {}
      facets.forEach(facet => {
        facetMap[facet.field_name] = facet.values
      })

      this.$nextTick(() => {
        // 状态码分布饼图
        if (facetMap.code) {
          this.renderPieChart('statusChart', '状态码分布', facetMap.code)
        }

        // 请求方法分布饼图
        if (facetMap.method) {
          this.renderPieChart('methodChart', '请求方法分布', facetMap.method)
        }

        // 浏览器类型分布柱状图
        if (facetMap.browser) {
          this.renderBarChart('browserChart', '浏览器类型', facetMap.browser)
        }

        // 操作系统分布柱状图
        if (facetMap.os) {
          this.renderBarChart('osChart', '操作系统', facetMap.os)
        }

        // 客户端省份分布柱状图
        if (facetMap.clientprovince) {
          this.renderBarChart('provinceChart', '客户端省份', facetMap.clientprovince)
        }

        // 设备类型分布饼图
        if (facetMap.device) {
          this.renderPieChart('deviceChart', '设备类型分布', facetMap.device)
        }

        // 频道访问分布柱状图
        if (facetMap.channel) {
          this.renderBarChart('channelChart', '频道', facetMap.channel)
        }

        // 运营商分布饼图
        if (facetMap.clientisp) {
          this.renderPieChart('ispChart', '运营商分布', facetMap.clientisp)
        }

        // 协议版本分布饼图
        if (facetMap.protocol) {
          this.renderPieChart('protocolChart', '协议版本分布', facetMap.protocol)
        }

        // 请求scheme分布饼图
        if (facetMap.scheme) {
          this.renderPieChart('schemeChart', '请求scheme分布', facetMap.scheme)
        }
      })
    },

    renderPieChart(containerId, title, data) {
      const chartDom = document.getElementById(containerId)
      if (!chartDom) return

      const myChart = echarts.init(chartDom)
      this.chartInstances[containerId] = myChart

      const option = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: title,
            type: 'pie',
            radius: '50%',
            data: data.slice(0, 10).map(item => ({
              value: item.hits,
              name: item.field_value
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      myChart.setOption(option)
    },

    renderBarChart(containerId, title, data) {
      const chartDom = document.getElementById(containerId)
      if (!chartDom) return

      const myChart = echarts.init(chartDom)
      this.chartInstances[containerId] = myChart

      const sortedData = data.slice(0, 10).sort((a, b) => b.hits - a.hits)

      const option = {
        title: {
          text: title + '分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: sortedData.map(item => item.field_value),
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '访问量',
            type: 'bar',
            data: sortedData.map(item => item.hits),
            itemStyle: {
              color: '#5470c6'
            }
          }
        ]
      }

      myChart.setOption(option)
    },

    resizeCharts() {
      Object.values(this.chartInstances).forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    },

    // 处理全局时间范围变化
    handleGlobalTimeRangeChange() {
      // 刷新所有数据
      this.refreshAllData()
    },

    // 处理自动刷新开关变化
    handleAutoRefreshChange() {
      if (this.autoRefresh) {
        // 开启自动刷新，每 60 秒刷新一次
        this.refreshTimer = setInterval(() => {
          this.refreshAllData()
        }, 60000) // 60秒 = 60000毫秒

        this.$message.success('自动刷新已开启，每 60 秒刷新一次')
      } else {
        // 关闭自动刷新
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }

        this.$message.info('自动刷新已关闭')
      }
    },

    // 刷新所有数据
    refreshAllData() {
      // 刷新基本统计数据
      this.loadStatistics()
    },

    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    },

    formatBytes(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    async loadTimeSeries() {
      // 验证表单数据
      if (!this.timeseriesForm.channel) {
        this.$message.warning('请输入渠道名称')
        return
      }
      
      if (!this.timeseriesForm.timeRange || this.timeseriesForm.timeRange.length !== 2) {
        this.$message.warning('请选择时间范围')
        return
      }

      this.timeseriesLoading = true
      try {
        const [start, end] = this.timeseriesForm.timeRange
        const response = await getWangsuTimeSeries(
          this.timeseriesForm.channel,
          start,
          end,
          this.timeseriesForm.step,
          this.timeseriesForm.metric
        )
        
        if (response.code === 200) {
          this.renderTimeSeries(response.data)
        } else {
          this.$message.error(response.msg || '查询失败')
        }
      } catch (error) {
        console.error('加载时间序列数据失败:', error)
        this.$message.error('加载时间序列数据失败')
      } finally {
        this.timeseriesLoading = false
      }
    },

    renderTimeSeries(data) {
      const chartDom = document.getElementById('timeseriesChart')
      if (!chartDom) return

      const myChart = echarts.init(chartDom)
      this.chartInstances['timeseriesChart'] = myChart

      // 解析VictoriaLogs返回的数据格式
      let seriesData = []
      let title = '时间序列分析'
      
      if (data && data.data && data.data.result && data.data.result.length > 0) {
        const result = data.data.result[0] // 取第一个结果
        const metric = result.metric
        const values = result.values || []
        
        // 构建图表标题
        const metricName = this.getMetricDisplayName(this.timeseriesForm.metric)
        title = `${this.timeseriesForm.channel} - ${metricName}趋势`
        
        // 转换数据格式
        seriesData = values.map(item => {
          const timestamp = item[0] * 1000 // 转换为毫秒
          const value = parseFloat(item[1])
          return [timestamp, value]
        })
      }

      const option = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const param = params[0]
            const time = new Date(param.value[0]).toLocaleString()
            const value = this.formatMetricValue(param.value[1], this.timeseriesForm.metric)
            return `${time}<br/>${param.seriesName}: ${value}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'time',
          splitLine: {
            show: false
          },
          axisLabel: {
            formatter: (value) => {
              return new Date(value).toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              })
            }
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          },
          axisLabel: {
            formatter: (value) => {
              return this.formatMetricValue(value, this.timeseriesForm.metric)
            }
          }
        },
        series: [
          {
            name: this.getMetricDisplayName(this.timeseriesForm.metric),
            type: 'line',
            data: seriesData,
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2,
              color: '#1890ff'
            },
            itemStyle: {
              color: '#1890ff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                  { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
                ]
              }
            }
          }
        ]
      }

      myChart.setOption(option)
    },

    getMetricDisplayName(metric) {
      const names = {
        'count': '请求数',
        'size': '响应流量',
        'upsize': '请求流量',
        'unique': '独立访客'
      }
      return names[metric] || metric
    },

    formatMetricValue(value, metric) {
      if (metric === 'size' || metric === 'upsize') {
        return this.formatBytes(value)
      } else {
        return this.formatNumber(value)
      }
    },

    async loadChannelRanking() {
      this.rankingLoading = true
      try {
        const response = await getWangsuChannelRanking(this.rankingForm.timeRange)
        
        if (response.code === 200) {
          this.channelRankings = response.data || []
        } else {
          this.$message.error(response.msg || '查询失败')
        }
      } catch (error) {
        console.error('加载渠道排名数据失败:', error)
        this.$message.error('加载渠道排名数据失败')
      } finally {
        this.rankingLoading = false
      }
    },

    selectChannelForTimeSeries(channel) {
      this.timeseriesForm.channel = channel
      this.$message.success(`已选择渠道: ${channel}，正在加载时间序列数据...`)
      
      // 滚动到时间序列图表区域
      const timeseriesElement = document.querySelector('.timeseries-section')
      if (timeseriesElement) {
        timeseriesElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
      
      // 自动执行查询
      this.loadTimeSeries()
    },

    getRankClass(rank) {
      if (rank <= 3) {
        return `rank-${rank}`
      }
      return ''
    },

    getPercentage(count) {
      if (this.channelRankings.length === 0) return '0.00'
      const total = this.channelRankings.reduce((sum, item) => sum + item.count, 0)
      return ((count / total) * 100).toFixed(2)
    },

    renderRankHeader(h, { column }) {
      return h('span', [
        h('i', { class: 'el-icon-trophy', style: { marginRight: '5px', color: '#f5222d' } }),
        '排名'
      ])
    },

    onTimeseriesFormChange() {
      if (this.timeseriesChangeTimer) {
        clearTimeout(this.timeseriesChangeTimer)
      }
      this.timeseriesChangeTimer = setTimeout(() => {
        this.loadTimeSeries()
      }, 500) // 500毫秒防抖
    }
  }
}
</script>

<style scoped>
@import '../../../assets/styles/audit-overview.css';

.wangsu-stat-overview {
  padding: 20px;
}

/* 数据总览卡片样式优化 */
.stat-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 24px 20px 24px 24px;
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 280px;
  position: relative;
  overflow: hidden;
  min-height: 100px;
}

.stat-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  position: absolute;
  left: -50px;
  top: -25px;
  opacity: 0.2;
  z-index: 1;
}

.stat-content {
  width: 100%;
  padding-left: 25px;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-number {
  font-size: 36px;
  font-weight: 500;
  line-height: 1.1;
  margin-bottom: 4px;
  font-family: "Arial", sans-serif;
  letter-spacing: 0.5px;
}

.stat-title {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: normal;
}

/* 时间序列图表样式 */
.timeseries-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 20px;
  margin-bottom: 24px;
}

.subsection-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
  padding-left: 12px;
}

.timeseries-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  min-width: 60px;
}

.timeseries-chart {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

.charts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-item {
  flex: 1;
  min-width: 45%;
  background-color: #fff;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.chart-item > div {
  height: 350px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stat-cards-container {
    gap: 12px;
  }
  
  .stat-card {
    min-width: 220px;
  }
  
  .timeseries-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .control-group {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .stat-cards-container {
    flex-direction: column;
  }
  
  .stat-card {
    min-width: 100%;
  }
  
  .charts-container {
    flex-direction: column;
  }
  
  .chart-item {
    min-width: 100%;
  }
}

/* 渠道排名表格样式 */
.channel-ranking-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 20px;
  margin-bottom: 24px;
}

.ranking-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.ranking-table {
  border-radius: 6px;
  overflow: hidden;
}

.rank-badge {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  font-size: 12px;
  color: #fff;
  background-color: #909399;
}

.rank-badge.rank-1 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-badge.rank-2 {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #333;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-badge.rank-3 {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #fff;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.count-number {
  font-weight: 500;
  color: #1890ff;
}

.percentage {
  font-weight: 500;
  color: #52c41a;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover .rank-badge {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.el-table tbody tr:hover .count-number {
  color: #40a9ff;
  transition: color 0.2s ease;
}
</style> 