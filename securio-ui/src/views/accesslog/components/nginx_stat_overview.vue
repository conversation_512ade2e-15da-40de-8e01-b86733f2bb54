<template>
  <div class="audit-overview nginx-stat-overview" v-loading="loading" element-loading-text="加载中...">
    <!-- 全局控制面板 -->
    <div class="global-controls-panel">
      <div class="global-title">时间范围控制</div>
      <div class="global-controls">
        <el-select v-model="globalTimeRange" placeholder="选择时间范围" size="small" @change="handleGlobalTimeRangeChange">
          <el-option label="最近 5 分钟" value="5m"></el-option>
          <el-option label="最近 30 分钟" value="30m"></el-option>
          <el-option label="最近 1 小时" value="1h"></el-option>
          <el-option label="最近 6 小时" value="6h"></el-option>
          <el-option label="最近 12 小时" value="12h"></el-option>
          <el-option label="最近 24 小时" value="1d"></el-option>
          <el-option label="最近 3 天" value="3d"></el-option>
          <el-option label="最近 7 天" value="7d"></el-option>
          <el-option label="最近 14 天" value="14d"></el-option>
        </el-select>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text=""
          @change="handleAutoRefreshChange">
        </el-switch>
        <el-button type="primary" icon="el-icon-refresh" size="small" @click="refreshAllData">刷新数据</el-button>
      </div>
    </div>

    <!-- 统计数据卡片 -->
    <h3 class="section-title">数据总览</h3>
    <div class="stat-cards-container">
      <div class="stat-card" v-for="(item, index) in statItems" :key="index">
        <div class="stat-circle" :style="{ backgroundColor: item.bgColor }"></div>
        <div class="stat-content">
          <div class="stat-number" :style="{ color: item.color }">{{ item.value }}</div>
          <div class="stat-title">{{ item.title }}</div>
        </div>
      </div>
    </div>



    <!-- 图表分析 -->
    <h3 class="section-title">请求统计</h3>
    <div class="charts-container">
      <div class="chart-item">
        <div ref="methodChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div ref="statusChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <!-- 热门统计 -->
    <h3 class="section-title">热门统计</h3>
    <div class="stats-row">
      <div class="stats-card">
        <h3 class="stats-title">热门客户端 IP</h3>
        <el-table :data="topClients.slice(0, 10)" size="small" border stripe>
          <el-table-column prop="fieldValue" label="客户端 IP" width="180"></el-table-column>
          <el-table-column prop="hits" label="请求次数"></el-table-column>
        </el-table>
      </div>

      <div class="stats-card">
        <h3 class="stats-title">热门请求路径</h3>
        <el-table :data="topUrls.slice(0, 10)" size="small" border stripe>
          <el-table-column prop="fieldValue" label="请求路径" width="180"></el-table-column>
          <el-table-column prop="hits" label="访问次数"></el-table-column>
        </el-table>
      </div>

      <div class="stats-card">
        <h3 class="stats-title">热门上游服务器</h3>
        <el-table :data="topUpstreams.slice(0, 10)" size="small" border stripe>
          <el-table-column prop="fieldValue" label="上游服务器" width="180"></el-table-column>
          <el-table-column prop="hits" label="请求次数"></el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 错误统计 -->
    <h3 class="section-title">错误状态码统计</h3>
    <div class="stats-row">
      <div class="stats-card">
        <h3 class="stats-title">4xx 错误统计</h3>
        <el-table :data="error4xxStats" size="small" border stripe v-loading="errorStatsLoading">
          <el-table-column prop="fieldValue" label="状态码" width="180"></el-table-column>
          <el-table-column prop="count" label="次数">
            <template slot-scope="scope">
              <span class="error-count">{{ scope.row.count }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="stats-card">
        <h3 class="stats-title">5xx 错误统计</h3>
        <el-table :data="error5xxStats" size="small" border stripe v-loading="errorStatsLoading">
          <el-table-column prop="fieldValue" label="状态码" width="180"></el-table-column>
          <el-table-column prop="count" label="次数">
            <template slot-scope="scope">
              <span class="error-count">{{ scope.row.count }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>


  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getNginxErrorStats, getTopClients, getTopUrls } from '@/api/opslog/nginx'
import { getFacets, countUniqueValues } from '@/api/opslog/vmlogs'

export default {
  name: 'NginxStatOverview',
  data() {
    return {
      loading: false,
      statItems: [
        {
          title: '总请求数',
          value: '0',
          color: '#1890ff',
          bgColor: 'rgba(24, 144, 255, 0.15)'
        },
        {
          title: '成功请求',
          value: '0',
          color: '#52c41a',
          bgColor: 'rgba(82, 196, 26, 0.15)'
        },
        {
          title: '错误请求',
          value: '0',
          color: '#f5222d',
          bgColor: 'rgba(245, 34, 45, 0.15)'
        }
      ],
      methodChart: null,
      statusChart: null,
      fieldStats: {}, // 字段统计信息
      topClients: [], // 热门客户端
      topUrls: [], // 热门URL
      topUpstreams: [], // 热门上游服务器
      error4xxStats: [], // 4xx错误统计
      error5xxStats: [], // 5xx错误统计
      errorStatsLoading: false, // 错误统计加载状态
      globalTimeRange: '5m', // 全局时间范围：默认最近 5 分钟
      autoRefresh: false, // 自动刷新开关
      refreshTimer: null // 自动刷新定时器
    }
  },
  mounted() {
    // 初始化图表
    this.initCharts()

    // 获取数据
    this.fetchData()

    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    if (this.methodChart) {
      this.methodChart.dispose()
    }
    if (this.statusChart) {
      this.statusChart.dispose()
    }

    // 清除自动刷新定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  },
  methods: {
    fetchData() {
      this.loading = true

      // 获取字段统计信息
      this.fetchFieldStats()

      // 获取错误统计信息
      this.fetchErrorStats()

      // 获取总请求数量
      this.fetchTotalRequests()
    },

    // 获取总请求数量
    fetchTotalRequests() {
      // 使用 countUniqueValues API 获取总请求数，指定使用 vmlog2 实例
      countUniqueValues('NGINX_ACCESS', 'message.request_id', this.globalTimeRange, 'vmlog2')
        .then(response => {
          if (response.code === 200) {
            // 设置总请求数
            this.statItems[0].value = this.formatNumber(response.data)

            // 获取成功请求数和错误请求数
            this.fetchSuccessAndErrorCounts()
          } else {
            console.error('获取总请求数量失败:', response.msg)
            this.$message.error('获取总请求数量失败: ' + response.msg)

            // 设置默认值
            this.statItems[0].value = '0'
          }
        })
        .catch(error => {
          console.error('获取总请求数量失败:', error)
          this.$message.error('获取总请求数量失败: ' + (error.message || error))

          // 设置默认值
          this.statItems[0].value = '0'
        })
    },

    // 获取成功请求数和错误请求数
    fetchSuccessAndErrorCounts() {
      // 构建查询参数 - 成功请求 (2xx, 3xx)
      const successQuery = `stream:"NGINX_ACCESS" AND _time:${this.globalTimeRange} AND (message.status:~"^2" OR message.status:~"^3")`

      // 构建查询参数 - 错误请求 (4xx, 5xx)
      const errorQuery = `stream:"NGINX_ACCESS" AND _time:${this.globalTimeRange} AND (message.status:~"^4" OR message.status:~"^5")`

      // 获取成功请求数，指定使用 vmlog2 实例
      getFacets(successQuery, this.globalTimeRange, ['message.status'], 100, 'vmlog2')
        .then(response => {
          if (response.code === 200) {
            try {
              // 计算成功请求总数
              let successCount = 0
              if (response.data && response.data.facets && response.data.facets.length > 0) {
                const statusStats = response.data.facets.find(item => item.fieldName === 'message.status')
                if (statusStats && statusStats.values) {
                  successCount = statusStats.values.reduce((sum, item) => sum + item.hits, 0)
                }
              }

              // 设置成功请求数
              this.statItems[1].value = this.formatNumber(successCount)
            } catch (e) {
              console.error('解析成功请求数据失败:', e)
              this.statItems[1].value = '0'
            }
          }
        })
        .catch(error => {
          console.error('获取成功请求数据失败:', error)
          this.statItems[1].value = '0'
        })

      // 获取错误请求数，指定使用 vmlog2 实例
      getFacets(errorQuery, this.globalTimeRange, ['message.status'], 100, 'vmlog2')
        .then(response => {
          if (response.code === 200) {
            try {
              // 计算错误请求总数
              let errorCount = 0
              if (response.data && response.data.facets && response.data.facets.length > 0) {
                const statusStats = response.data.facets.find(item => item.fieldName === 'message.status')
                if (statusStats && statusStats.values) {
                  errorCount = statusStats.values.reduce((sum, item) => sum + item.hits, 0)
                }
              }

              // 设置错误请求数
              this.statItems[2].value = this.formatNumber(errorCount)
            } catch (e) {
              console.error('解析错误请求数据失败:', e)
              this.statItems[2].value = '0'
            }
          }
        })
        .catch(error => {
          console.error('获取错误请求数据失败:', error)
          this.statItems[2].value = '0'
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 获取字段统计信息
    fetchFieldStats() {
      // 创建一个计数器，跟踪请求的完成情况
      let requestsCompleted = 0
      const totalRequests = 3 // 三个请求：facets、热门客户端、热门URL

      const checkAllCompleted = () => {
        requestsCompleted++
        if (requestsCompleted >= totalRequests) {
          this.loading = false
        }
      }

      // 构建查询参数
      const query = `stream:"NGINX_ACCESS"`
      const fields = [
        'message.request_method',
        'message.status',
        'message.upstream_ip'
      ]

      // 调用后端 facets API 获取字段统计信息，指定使用 vmlog2 实例
      getFacets(query, this.globalTimeRange, fields, 100, 'vmlog2')
        .then(response => {
          if (response.code === 200) {
            try {
              // 直接使用返回的数据对象
              const data = response.data
              console.log('Received field stats data:', data) // 输出数据以便调试
              this.fieldStats = data

              // 提取热门上游服务器
              const upstreamStats = data.facets.find(item => item.fieldName === 'message.upstream_ip')
              if (upstreamStats && upstreamStats.values) {
                this.topUpstreams = upstreamStats.values
              }

              // 更新图表数据
              this.updateCharts()
            } catch (e) {
              console.error('解析字段统计数据失败:', e)
            }
          }
        })
        .catch(error => {
          console.error('获取字段统计数据失败:', error)
        })
        .finally(() => {
          checkAllCompleted()
        })

      // 获取热门客户端 IP
      getTopClients(this.globalTimeRange, 20)
        .then(response => {
          if (response.code === 200) {
            console.log('Received top clients data:', response.data)
            this.topClients = response.data.map(item => ({
              fieldValue: item.fieldValue,
              hits: item.hits
            }))
          } else {
            console.error('获取热门客户端失败:', response.msg)
            this.$message.error('获取热门客户端失败: ' + response.msg)
          }
        })
        .catch(error => {
          console.error('获取热门客户端失败:', error)
          this.$message.error('获取热门客户端失败: ' + (error.message || error))
        })
        .finally(() => {
          checkAllCompleted()
        })

      // 获取热门请求路径
      getTopUrls(this.globalTimeRange, 20)
        .then(response => {
          if (response.code === 200) {
            console.log('Received top URLs data:', response.data)
            this.topUrls = response.data.map(item => ({
              fieldValue: item.fieldValue,
              hits: item.hits
            }))
          } else {
            console.error('获取热门请求路径失败:', response.msg)
            this.$message.error('获取热门请求路径失败: ' + response.msg)
          }
        })
        .catch(error => {
          console.error('获取热门请求路径失败:', error)
          this.$message.error('获取热门请求路径失败: ' + (error.message || error))
        })
        .finally(() => {
          checkAllCompleted()
        })
    },

    // 获取错误统计信息
    fetchErrorStats() {
      this.errorStatsLoading = true

      // 创建一个计数器，跟踪两个请求的完成情况
      let requestsCompleted = 0
      const totalRequests = 2

      const checkAllCompleted = () => {
        requestsCompleted++
        if (requestsCompleted >= totalRequests) {
          this.errorStatsLoading = false
        }
      }

      // 获取4xx错误统计
      getNginxErrorStats('4xx', this.globalTimeRange, 10)
        .then(response => {
          if (response.code === 200) {
            this.error4xxStats = response.data || []
            // 如果数据为空，添加一个提示条目
            if (this.error4xxStats.length === 0) {
              this.error4xxStats = [{
                fieldValue: '暂无数据',
                count: 0
              }]
            }
          } else {
            console.error('获取4xx错误统计失败:', response.msg)
            this.$message.error('获取4xx错误统计失败: ' + response.msg)
            this.error4xxStats = [{
              fieldValue: '获取数据失败',
              count: 0
            }]
          }
        })
        .catch(error => {
          console.error('获取4xx错误统计失败:', error)
          this.$message.error('获取4xx错误统计失败: ' + (error.message || error))
          this.error4xxStats = [{
            fieldValue: '获取数据失败',
            count: 0
          }]
        })
        .finally(() => {
          checkAllCompleted()
        })

      // 获取5xx错误统计
      getNginxErrorStats('5xx', this.globalTimeRange, 10)
        .then(response => {
          if (response.code === 200) {
            this.error5xxStats = response.data || []
            // 如果数据为空，添加一个提示条目
            if (this.error5xxStats.length === 0) {
              this.error5xxStats = [{
                fieldValue: '暂无数据',
                count: 0
              }]
            }
          } else {
            console.error('获取5xx错误统计失败:', response.msg)
            this.$message.error('获取5xx错误统计失败: ' + response.msg)
            this.error5xxStats = [{
              fieldValue: '获取数据失败',
              count: 0
            }]
          }
        })
        .catch(error => {
          console.error('获取5xx错误统计失败:', error)
          this.$message.error('获取5xx错误统计失败: ' + (error.message || error))
          this.error5xxStats = [{
            fieldValue: '获取数据失败',
            count: 0
          }]
        })
        .finally(() => {
          checkAllCompleted()
        })
    },

    refreshData() {
      this.fetchData()
    },

    formatNumber(num) {
      return num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    },

    initCharts() {
      // 初始化请求方法图表
      this.methodChart = echarts.init(this.$refs.methodChart)

      // 初始化状态码图表
      this.statusChart = echarts.init(this.$refs.statusChart)

      // 设置初始数据
      this.updateCharts()
    },

    updateCharts() {
      // 准备请求方法图表数据
      const methodData = []
      const methodNames = []

      // 使用从 API 获取的请求方法统计数据
      const methodStats = this.fieldStats.facets?.find(item => item.fieldName === 'message.request_method')
      if (methodStats && methodStats.values && methodStats.values.length > 0) {
        methodStats.values.forEach(item => {
          methodData.push({
            value: item.hits,
            name: item.fieldValue
          })
          methodNames.push(item.fieldValue)
        })
      }

      // 请求方法图表配置 - 南丁格尔玫瑰图
      const methodChartOption = {
        title: {
          text: '请求方法分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: methodNames
        },
        series: [
          {
            name: '请求方法',
            type: 'pie',
            radius: '70%',
            roseType: 'area', // 南丁格尔玫瑰图特性
            itemStyle: {
              borderRadius: 5 // 添加圆角效果
            },
            label: {
              show: true,
              formatter: '{b}: {d}%' // 显示名称和百分比
            },
            emphasis: {
              label: {
                fontSize: '15',
                fontWeight: 'bold'
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            labelLine: {
              smooth: 0.2,
              length: 10,
              length2: 20
            },
            data: methodData
          }
        ]
      }

      // 准备状态码图表数据
      const statusData = []
      const statusNames = []

      // 使用从 API 获取的状态码统计数据
      const statusStats = this.fieldStats.facets?.find(item => item.fieldName === 'message.status')
      if (statusStats && statusStats.values && statusStats.values.length > 0) {
        // 取前 10 个数据
        const topStatus = statusStats.values.slice(0, 10)
        topStatus.forEach(item => {
          statusData.push(item.hits)
          statusNames.push(item.fieldValue || '未知状态码')
        })
      }

      // 状态码统计图表配置
      const statusChartOption = {
        title: {
          text: '状态码分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>${data.seriesName}: ${data.value.toLocaleString()}`;
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: statusNames,
          axisLabel: {
            interval: 0,
            rotate: 45,
            fontSize: 11,
            margin: 15
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              return value >= 1000 ? (value / 1000).toFixed(1) + 'k' : value;
            }
          }
        },
        series: [
          {
            name: '请求次数',
            type: 'bar',
            data: statusData,
            barWidth: '60%',
            itemStyle: {
              color: function(params) {
                // 根据状态码设置不同的颜色
                const status = statusNames[params.dataIndex];
                if (status.startsWith('2')) {
                  return '#67C23A'; // 2xx 绿色
                } else if (status.startsWith('3')) {
                  return '#409EFF'; // 3xx 蓝色
                } else if (status.startsWith('4')) {
                  return '#E6A23C'; // 4xx 黄色
                } else if (status.startsWith('5')) {
                  return '#F56C6C'; // 5xx 红色
                } else {
                  return '#909399'; // 其他灰色
                }
              },
              borderRadius: [5, 5, 0, 0] // 某些版本的 ECharts 支持这个属性
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return params.value > 1000 ? (params.value / 1000).toFixed(1) + 'k' : params.value;
              },
              fontSize: 10,
              color: '#666'
            }
          }
        ]
      }

      // 更新图表
      this.methodChart.setOption(methodChartOption)
      this.statusChart.setOption(statusChartOption)
    },

    resizeCharts() {
      if (this.methodChart) {
        this.methodChart.resize()
      }
      if (this.statusChart) {
        this.statusChart.resize()
      }
    },





    // 处理全局时间范围变化
    handleGlobalTimeRangeChange() {
      // 刷新所有数据
      this.refreshAllData();
    },

    // 处理自动刷新开关变化
    handleAutoRefreshChange() {
      if (this.autoRefresh) {
        // 开启自动刷新，每 60 秒刷新一次
        this.refreshTimer = setInterval(() => {
          this.refreshAllData();
        }, 60000); // 60秒 = 60000毫秒

        this.$message.success('自动刷新已开启，每 60 秒刷新一次');
      } else {
        // 关闭自动刷新
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer);
          this.refreshTimer = null;
        }

        this.$message.info('自动刷新已关闭');
      }
    },

    // 刷新所有数据
    refreshAllData() {
      // 刷新基本统计数据
      this.fetchData();
    }
  }
}
</script>

<style scoped>
@import '../../../assets/styles/audit-overview.css';

/* Nginx特有样式 */
.error-count {
  color: #F56C6C;
  font-weight: bold;
}

.chart-item {
  flex: 1;
  min-width: 45%;
  background-color: #fff;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.charts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

/* 数据总览卡片样式优化 */
.stat-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 24px 20px 24px 24px;
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 300px;
  position: relative;
  overflow: hidden;
  min-height: 100px;
}

.stat-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  position: absolute;
  left: -50px;
  top: -25px;
  opacity: 0.2;
  z-index: 1;
}

.stat-content {
  width: 100%;
  padding-left: 25px;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-number {
  font-size: 36px;
  font-weight: 500;
  line-height: 1.1;
  margin-bottom: 4px;
  font-family: "Arial", sans-serif;
  letter-spacing: 0.5px;
}

.stat-title {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: normal;
}

/* 调整section标题样式 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 30px 0 20px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: #409EFF;
  border-radius: 2px;
}
</style>
