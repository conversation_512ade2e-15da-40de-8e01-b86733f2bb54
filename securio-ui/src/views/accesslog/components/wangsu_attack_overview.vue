<template>
  <div class="wangsu-attack-overview">
    <!-- 全局控制面板 -->
    <div class="global-controls-panel">
      <div class="global-title">时间范围控制</div>
      <div class="global-controls">
        <el-select v-model="globalTimeRange" placeholder="选择时间范围" size="small" @change="handleTimeRangeChange">
          <el-option label="最近 5 分钟" value="5m"></el-option>
          <el-option label="最近 30 分钟" value="30m"></el-option>
          <el-option label="最近 1 小时" value="1h"></el-option>
          <el-option label="最近 6 小时" value="6h"></el-option>
          <el-option label="最近 12 小时" value="12h"></el-option>
          <el-option label="最近 24 小时" value="1d"></el-option>
          <el-option label="最近 3 天" value="3d"></el-option>
          <el-option label="最近 7 天" value="7d"></el-option>
        </el-select>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text=""
          @change="toggleAutoRefresh">
        </el-switch>
        <el-button type="primary" icon="el-icon-refresh" size="small" @click="loadStatistics">刷新数据</el-button>
      </div>
    </div>

    <!-- 统计数据卡片 -->
    <h3 class="section-title">数据总览</h3>
    <div class="stat-cards-container" v-loading="loading">
      <div class="stat-card">
        <div class="stat-circle" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #ff6b6b;">{{ formatNumber(totalAttacks) }}</div>
          <div class="stat-title">总攻击次数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-circle" style="background: linear-gradient(135deg, #feca57, #ff9ff3);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #feca57;">{{ formatNumber(uniqueIps) }}</div>
          <div class="stat-title">独立攻击IP</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-circle" style="background: linear-gradient(135deg, #48dbfb, #0abde3);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #48dbfb;">{{ formatNumber(attackTypes) }}</div>
          <div class="stat-title">攻击类型数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-circle" style="background: linear-gradient(135deg, #1dd1a1, #10ac84);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #1dd1a1;">{{ formatNumber(attackedHosts) }}</div>
          <div class="stat-title">被攻击域名</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-circle" style="background: linear-gradient(135deg, #fd79a8, #e84393);"></div>
        <div class="stat-content">
          <div class="stat-number" style="color: #fd79a8;">{{ formatNumber(highRiskAttacks) }}</div>
          <div class="stat-title">高危攻击数</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <h3 class="section-title">攻击统计</h3>

    <!-- Top攻击IP排名表格 -->
    <div class="top-ips-section">
      <h4 class="subsection-title">Top攻击IP排名</h4>
      <div class="ranking-controls">
        <div class="control-group">
          <label>时间范围：</label>
          <el-select v-model="topIpsForm.timeRange" style="width: 150px;" @change="loadTopIps">
            <el-option label="最近1小时" value="1h"></el-option>
            <el-option label="最近6小时" value="6h"></el-option>
            <el-option label="最近12小时" value="12h"></el-option>
            <el-option label="最近1天" value="1d"></el-option>
            <el-option label="最近3天" value="3d"></el-option>
            <el-option label="最近7天" value="7d"></el-option>
          </el-select>
        </div>
        <el-button type="primary" @click="loadTopIps" :loading="topIpsLoading">刷新排名</el-button>
      </div>
      <div class="ranking-table">
        <el-table
          :data="topIps"
          v-loading="topIpsLoading"
          stripe
          border
          size="small"
          max-height="400"
          style="width: 100%"
        >
          <el-table-column
            prop="rank"
            label="排名"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="rank-badge" :class="getRankClass(scope.row.rank)">
                {{ scope.row.rank }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="ip"
            label="攻击IP"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-link 
                type="danger" 
                :underline="false"
                @click="goToHostAnalysis(scope.row.ip)"
                title="点击跳转到主机综合分析"
              >
                {{ scope.row.ip }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            prop="province"
            label="省份"
          ></el-table-column>
          <el-table-column
            prop="city"
            label="城市"
          ></el-table-column>
          <el-table-column
            prop="count"
            label="攻击次数"
            align="right"
            sortable
          >
            <template slot-scope="scope">
              <span class="count-number">{{ formatNumber(scope.row.count) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="威胁等级"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="getThreatLevel(scope.row.count).type" size="small">
                {{ getThreatLevel(scope.row.count).label }}
              </el-tag>
            </template>
          </el-table-column>

        </el-table>
      </div>
    </div>

    <!-- 多维度图表 -->
    <div class="charts-container">
      <div class="chart-item">
        <div id="attackTypeChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div id="actionChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <div class="charts-container">
      <div class="chart-item">
        <div id="hostChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div id="provinceChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <div class="charts-container">
      <div class="chart-item">
        <div id="methodChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div id="statusCodeChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getWangsuAttackStats, getWangsuAttackCharts, getTopAttackIps } from '@/api/opslog/wangsuAttack'

export default {
  name: 'WangsuAttackOverview',
  data() {
    return {
      loading: false,
      totalAttacks: 0,
      uniqueIps: 0,
      attackTypes: 0,
      attackedHosts: 0,
      highRiskAttacks: 0,
      chartInstances: {},
      globalTimeRange: '1h',
      autoRefresh: false,
      refreshTimer: null,

      topIps: [],
      topIpsForm: {
        timeRange: '1h'
      },
      topIpsLoading: false
    }
  },
  mounted() {
    this.loadStatistics()
    window.addEventListener('resize', this.resizeCharts)
    
    // 加载Top攻击IP数据
    this.loadTopIps()
  },
  beforeDestroy() {
    // 清理图表实例
    Object.values(this.chartInstances).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
    
    // 清理自动刷新定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
    
    window.removeEventListener('resize', this.resizeCharts)
  },
  methods: {
    async loadStatistics() {
      this.loading = true
      try {
        // 并行加载统计数据和图表数据
        const [statsResponse, chartsResponse] = await Promise.all([
          this.loadStatsData(),
          this.loadChartsData()
        ])

        // 处理统计数据
        if (statsResponse) {
          this.processStatsData(statsResponse)
        }

        // 处理图表数据
        if (chartsResponse) {
          // 确保DOM元素已经渲染完成
          this.$nextTick(() => {
            this.renderCharts(chartsResponse)
          })
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        this.$message.error('加载统计数据失败')
      } finally {
        this.loading = false
      }
    },

    // 加载统计指标数据
    async loadStatsData() {
      try {
        const response = await getWangsuAttackStats(this.globalTimeRange)
        if (response.code === 200) {
          return response.data
        } else {
          this.$message.error(response.msg || '获取统计数据失败')
          return null
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败')
        return null
      }
    },



    // 加载图表数据
    async loadChartsData() {
      try {
        const response = await getWangsuAttackCharts(this.globalTimeRange, 1000)
        if (response.code === 200) {
          return response.data
        } else {
          this.$message.error(response.msg || '获取图表数据失败')
          return null
        }
      } catch (error) {
        console.error('获取图表数据失败:', error)
        this.$message.error('获取图表数据失败')
        return null
      }
    },



    // 处理统计数据
    processStatsData(data) {
      this.totalAttacks = data.totalAttacks || 0
      this.uniqueIps = data.uniqueIps || 0
      this.attackTypes = data.attackTypes || 0
      this.attackedHosts = data.attackedHosts || 0
      this.highRiskAttacks = data.highRiskAttacks || 0
    },

    // 渲染图表（参考wangsu_stat_overview.vue的实现）
    renderCharts(chartsData) {
      try {
        console.log('renderCharts 接收到的数据:', chartsData)
        
        // 处理后端返回的数据格式 {facets: [...]}
        let facets = []
        if (chartsData && chartsData.facets) {
          facets = chartsData.facets
        } else if (Array.isArray(chartsData)) {
          facets = chartsData
        } else {
          console.log('没有图表数据，显示空图表')
          return
        }
        
        // 按照wangsu_stat_overview.vue的简单方式处理
        const facetMap = {}
        facets.forEach(facet => {
          facetMap[facet.field_name] = facet.values
        })
        
        console.log('处理后的 facetMap:', facetMap)
        
        this.$nextTick(() => {
          // 渲染攻击类型分布图
          if (facetMap.attack_type) {
            console.log('渲染攻击类型图表，数据:', facetMap.attack_type)
            this.renderAttackTypeChart(facetMap.attack_type)
          }
          
          // 渲染处理动作分布图
          if (facetMap.act) {
            console.log('渲染处理动作图表，数据:', facetMap.act)
            this.renderActionChart(facetMap.act)
          }
          
          // 渲染被攻击域名分布图
          if (facetMap.host) {
            console.log('渲染域名图表，数据:', facetMap.host)
            this.renderHostChart(facetMap.host)
          }
          
          // 渲染地域分布图
          if (facetMap.ip_province_cn) {
            console.log('渲染地域图表，数据:', facetMap.ip_province_cn)
            this.renderProvinceChart(facetMap.ip_province_cn)
          }
          
          // 渲染请求方法分布图
          if (facetMap.mode) {
            console.log('渲染请求方法图表，数据:', facetMap.mode)
            this.renderMethodChart(facetMap.mode)
          }
          
          // 渲染状态码分布图
          if (facetMap.status_code) {
            console.log('渲染状态码图表，数据:', facetMap.status_code)
            this.renderStatusCodeChart(facetMap.status_code)
          }
        })
      } catch (error) {
        console.error('渲染图表失败:', error)
      }
    },

    // 渲染攻击类型分布图
    renderAttackTypeChart(data) {
      console.log('renderAttackTypeChart 开始渲染，数据:', data)
      const chartDom = document.getElementById('attackTypeChart')
      if (!chartDom) {
        console.error('找不到 attackTypeChart DOM 元素')
        return
      }

      const myChart = echarts.init(chartDom)
      this.chartInstances['attackTypeChart'] = myChart

      // 攻击类型名称映射
      const attackTypeMap = {
        'BOT_COOKIEC': 'Cookie检测',
        'BOT_ACB': 'ACB检测',
        'BOT_RATE_LIMIT': '频率限制',
        'BOT_PUBLIC_INTELLIGENCE': '公共情报',
        'DDOS_RATE_LIMIT': 'DDoS频率限制',
        'BOT_IP_INTELLIGENCE': 'IP情报',
        'BOT_UA_INTELLIGENCE': 'UA情报',
        'BOT_JSC': 'JS挑战',
        'BOT_HIC': 'HIC检测',
        'DDOS_ACB': 'DDoS ACB'
      }

      // 按照wangsu_stat_overview.vue的方式处理数据：data是{field_value, hits}数组
      const chartData = data.slice(0, 10).map(item => ({
        name: attackTypeMap[item.field_value] || item.field_value,
        value: item.hits
      })).sort((a, b) => b.value - a.value)
      
      console.log('攻击类型图表数据:', chartData)

      const option = {
        title: {
          text: '攻击类型分布',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const percent = params.percent
            const value = params.value.toLocaleString()
            return `${params.seriesName}<br/>${params.name}: ${value} (${percent}%)`
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '攻击类型',
            type: 'pie',
            radius: ['30%', '70%'],
            center: ['65%', '50%'],
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{b}: {d}%'
            }
          }
        ]
      }

      myChart.setOption(option)
    },

    // 渲染处理动作分布图
    renderActionChart(data) {
      console.log('renderActionChart 开始渲染，数据:', data)
      const chartDom = document.getElementById('actionChart')
      if (!chartDom) {
        console.error('找不到 actionChart DOM 元素')
        return
      }

      const myChart = echarts.init(chartDom)
      this.chartInstances['actionChart'] = myChart

      const actionMap = {
        '1': '阻断',
        '2': '告警',
        '3': '放行',
        '9': '验证码',
        '10': '重定向',
        '12': '延时响应'
      }

      const actionColors = {
        '阻断': '#ff4d4f',
        '告警': '#faad14',
        '放行': '#52c41a',
        '验证码': '#1890ff',
        '重定向': '#722ed1',
        '延时响应': '#fa8c16'
      }

      const chartData = data.map(item => {
        const actionName = actionMap[item.field_value] || `动作${item.field_value}`
        return {
          name: actionName,
          value: item.hits,
          itemStyle: {
            color: actionColors[actionName] || '#8c8c8c'
          }
        }
      }).sort((a, b) => b.value - a.value)

      const option = {
        title: {
          text: '处理动作分布',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const percent = params.percent
            const value = params.value.toLocaleString()
            return `${params.seriesName}<br/>${params.name}: ${value} (${percent}%)`
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '处理动作',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['65%', '50%'],
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{b}: {d}%'
            }
          }
        ]
      }

      myChart.setOption(option)
    },

    // 渲染被攻击域名分布图
    renderHostChart(data) {
      const chartDom = document.getElementById('hostChart')
      if (!chartDom) return

      const myChart = echarts.init(chartDom)
      this.chartInstances['hostChart'] = myChart

      const sortedData = data
        .sort((a, b) => b.hits - a.hits)
        .slice(0, 10)

      const option = {
        title: {
          text: '被攻击域名Top10',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const param = params[0]
            const value = param.value.toLocaleString()
            return `${param.name}<br/>${param.seriesName}: ${value}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          axisLabel: {
            formatter: function(value) {
              if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M'
              } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K'
              }
              return value
            }
          }
        },
        yAxis: {
          type: 'category',
          data: sortedData.map(item => item.field_value),
          axisLabel: {
            interval: 0,
            fontSize: 11
          }
        },
        series: [
          {
            name: '攻击次数',
            type: 'bar',
            data: sortedData.map(item => item.hits),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#ff6b6b' },
                { offset: 1, color: '#ff8e8e' }
              ])
            },
            barWidth: '60%'
          }
        ]
      }

      myChart.setOption(option)
    },

    // 渲染地域分布图
    renderProvinceChart(data) {
      const chartDom = document.getElementById('provinceChart')
      if (!chartDom) return

      const myChart = echarts.init(chartDom)
      this.chartInstances['provinceChart'] = myChart

      const sortedData = data
        .sort((a, b) => b.hits - a.hits)
        .slice(0, 10)

      const option = {
        title: {
          text: '攻击来源地域Top10',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const param = params[0]
            const value = param.value.toLocaleString()
            return `${param.name}<br/>${param.seriesName}: ${value}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: sortedData.map(item => item.field_value || '未知'),
          axisLabel: {
            rotate: 45,
            fontSize: 11
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M'
              } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K'
              }
              return value
            }
          }
        },
        series: [
          {
            name: '攻击次数',
            type: 'bar',
            data: sortedData.map(item => item.hits),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                { offset: 0, color: '#1dd1a1' },
                { offset: 1, color: '#52c41a' }
              ])
            },
            barWidth: '60%'
          }
        ]
      }

      myChart.setOption(option)
    },

    // 渲染请求方法分布图
    renderMethodChart(data) {
      const chartDom = document.getElementById('methodChart')
      if (!chartDom) return

      const myChart = echarts.init(chartDom)
      this.chartInstances['methodChart'] = myChart

      const methodColors = {
        'GET': '#52c41a',
        'POST': '#1890ff',
        'OPTIONS': '#faad14',
        'HEAD': '#722ed1',
        'PUT': '#fa8c16',
        'DELETE': '#ff4d4f'
      }

      const chartData = data.map(item => ({
        name: item.field_value || '未知',
        value: item.hits,
        itemStyle: {
          color: methodColors[item.field_value] || '#8c8c8c'
        }
      })).sort((a, b) => b.value - a.value)

      const option = {
        title: {
          text: '请求方法分布',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const percent = params.percent
            const value = params.value.toLocaleString()
            return `${params.seriesName}<br/>${params.name}: ${value} (${percent}%)`
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '请求方法',
            type: 'pie',
            radius: ['30%', '70%'],
            center: ['65%', '50%'],
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{b}: {d}%'
            }
          }
        ]
      }

      myChart.setOption(option)
    },



    // 渲染状态码分布图
    renderStatusCodeChart(data) {
      const chartDom = document.getElementById('statusCodeChart')
      if (!chartDom) return

      const myChart = echarts.init(chartDom)
      this.chartInstances['statusCodeChart'] = myChart

      const statusCodeColors = {
        '200': '#52c41a',
        '403': '#ff4d4f',
        '304': '#1890ff',
        '307': '#faad14',
        '302': '#722ed1',
        '206': '#13c2c2',
        '503': '#fa541c',
        '301': '#2f54eb',
        '499': '#eb2f96',
        '404': '#f5222d'
      }

      const sortedData = data
        .sort((a, b) => b.hits - a.hits)
        .slice(0, 10)

      const option = {
        title: {
          text: '响应状态码分布Top10',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const param = params[0]
            const value = param.value.toLocaleString()
            return `状态码: ${param.name}<br/>${param.seriesName}: ${value}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: sortedData.map(item => item.field_value),
          axisLabel: {
            fontSize: 11
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M'
              } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K'
              }
              return value
            }
          }
        },
        series: [
          {
            name: '请求次数',
            type: 'bar',
            data: sortedData.map(item => ({
              value: item.hits,
              itemStyle: {
                color: statusCodeColors[item.field_value] || '#8c8c8c'
              }
            })),
            barWidth: '60%'
          }
        ]
      }

      myChart.setOption(option)
    },





    async loadTopIps() {
      this.topIpsLoading = true
      try {
        const response = await getTopAttackIps(this.topIpsForm.timeRange, 20)
        
        if (response.code === 200) {
          this.topIps = response.data || []
        } else {
          this.$message.error(response.msg || '查询失败')
        }
      } catch (error) {
        console.error('加载Top攻击IP数据失败:', error)
        this.$message.error('加载Top攻击IP数据失败')
      } finally {
        this.topIpsLoading = false
      }
    },

    // 跳转到主机综合分析页面
    goToHostAnalysis(ip) {
      // 跳转到主机综合分析页面，并预填IP地址
      this.$router.push({
        path: '/sence/host_analyze',
        query: { hostIp: ip }
      })
    },

    handleTimeRangeChange() {
      this.loadStatistics()
    },

    toggleAutoRefresh() {
      if (this.autoRefresh) {
        this.refreshTimer = setInterval(() => {
          this.loadStatistics()
          this.loadTopIps()
        }, 60000) // 60秒刷新一次
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
      }
    },



    resizeCharts() {
      Object.values(this.chartInstances).forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    },

    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    },



    getRankClass(rank) {
      if (rank <= 3) {
        return `rank-${rank}`
      }
      return ''
    },

    getThreatLevel(count) {
      if (count >= 1000) {
        return { type: 'danger', label: '极高' }
      } else if (count >= 100) {
        return { type: 'warning', label: '高' }
      } else if (count >= 10) {
        return { type: 'info', label: '中' }
      } else {
        return { type: 'success', label: '低' }
      }
    }
  }
}
</script>

<style scoped>
@import '../../../assets/styles/audit-overview.css';

.wangsu-attack-overview {
  padding: 0px 20px;
  border-radius: 4px;
  margin-bottom: 50px;
}

/* 统计卡片样式优化 */
.stat-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  flex: 1;
  min-width: 280px;
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  transition: 0.3s;
  overflow: hidden;
  position: relative;
}

.stat-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  position: absolute;
  left: -50px;
  top: -25px;
  opacity: 0.2;
  z-index: 1;
}

.stat-content {
  width: 100%;
  padding-left: 25px;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-number {
  font-size: 36px;
  font-weight: 500;
  line-height: 1.1;
  margin-bottom: 4px;
  font-family: "Arial", sans-serif;
  letter-spacing: 0.5px;
}

.stat-title {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: normal;
}


/* 子标题样式 */
.subsection-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #ff6b6b;
}

/* Top攻击IP排名表格样式 */
.top-ips-section {
  margin-bottom: 30px;
}

.ranking-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.ranking-table {
  border-radius: 4px;
  overflow: hidden;
}

.rank-badge {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  font-size: 12px;
  color: #fff;
  background-color: #909399;
}

.rank-badge.rank-1 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-badge.rank-2 {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #333;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-badge.rank-3 {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #fff;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.count-number {
  font-weight: 500;
  color: #ff6b6b;
}

/* 图表容器样式 */
.charts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-item {
  flex: 1;
  min-width: 45%;
  background-color: #fff;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.chart-item > div {
  width: 100%;
  height: 350px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stat-cards-container {
    gap: 12px;
  }
  
  .stat-card {
    min-width: 220px;
  }
  
  .timeseries-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .control-group {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .stat-cards-container {
    flex-direction: column;
  }
  
  .stat-card {
    min-width: 100%;
  }
  
  .charts-container {
    flex-direction: column;
  }
  
  .chart-item {
    min-width: 100%;
  }
}
</style> 