<template>
    <div class="app-container">
      <h1>入侵总览</h1>
      
      <!-- URL测试区域 -->
      <el-card class="url-test-card" shadow="hover" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span class="card-title">
            <i class="el-icon-link"></i>
            URL回调测试
          </span>
        </div>
        
        <el-form :model="testForm" label-width="80px">
          <el-form-item label="目标URL:">
            <el-input
              v-model="testForm.url"
              placeholder="请输入要测试的URL"
              :rows="3"
              type="textarea"
              resize="vertical"
              style="width: 100%;">
            </el-input>
          </el-form-item>
          
          <el-form-item>
            <el-button 
              type="primary" 
              @click="sendRequest"
              :loading="loading"
              icon="el-icon-s-promotion">
              发送请求
            </el-button>
            <el-button 
              @click="clearResult"
              icon="el-icon-delete">
              清空结果
            </el-button>
          </el-form-item>
        </el-form>
        
        <!-- 请求结果显示区域 -->
        <div v-if="requestResult" class="result-section">
          <h4>
            <i class="el-icon-document"></i>
            请求结果
            <el-tag 
              :type="requestResult.success ? 'success' : 'danger'" 
              size="mini" 
              style="margin-left: 10px;">
              {{ requestResult.success ? '成功' : '失败' }}
            </el-tag>
          </h4>
          
          <!-- 请求信息 -->
          <el-descriptions :column="2" border size="small" style="margin-bottom: 15px;">
            <el-descriptions-item label="请求URL">
              <span class="url-text">{{ requestResult.url }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="请求时间">
              {{ requestResult.timestamp }}
            </el-descriptions-item>
            <el-descriptions-item label="响应状态">
              <el-tag :type="getStatusType(requestResult.status)" size="mini">
                {{ requestResult.status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="响应时间">
              {{ requestResult.duration }}ms
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 响应内容 -->
          <div class="response-content">
            <h5>响应内容:</h5>
            <el-input
              v-model="requestResult.response"
              type="textarea"
              :rows="8"
              readonly
              style="font-family: 'Courier New', monospace; font-size: 12px;">
            </el-input>
          </div>
          
          <!-- JSON格式化显示 -->
          <div v-if="requestResult.jsonData" class="json-display">
            <h5>JSON格式化:</h5>
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item 
                v-for="(value, key) in requestResult.jsonData" 
                :key="key"
                :label="key">
                <span v-if="typeof value === 'object'">{{ JSON.stringify(value) }}</span>
                <span v-else>{{ value }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>
    </div>
  </template>
    
  <script>
  import axios from 'axios'
  
  export default {
      name: 'RiskOverview',
      components: {
          
      },
      data() {
          return {
          loading:false,
          testForm: {
            url: 'https://callback.5i5j.com/sms/clyz/smsstatus?uid=588309967609663488&statusDesc=%E6%B6%88%E6%81%AF%E5%8F%91%E9%80%81%E6%88%90%E5%8A%9F&receiver=N7724441&pswd=&notifyTime=************&mobile=13699193783&length=1&msgid=25061210135200202205000034037458&status=DELIVRD&reportTime=2506121013'
          },
          requestResult: null
          }
      },
      methods: {
        async sendRequest() {
          if (!this.testForm.url.trim()) {
            this.$message.error('请输入要测试的URL')
            return
          }
          
          this.loading = true
          const startTime = Date.now()
          
          try {
            console.log('发送请求到:', this.testForm.url)
            
            // 发送GET请求到指定URL
            const response = await axios.get(this.testForm.url, {
              timeout: 30000, // 30秒超时
              headers: {
                'Accept': 'application/json, text/plain, */*',
                'User-Agent': 'Securio-Dashboard/1.0'
              }
            })
            
            const endTime = Date.now()
            const duration = endTime - startTime
            
            // 尝试解析JSON响应
            let jsonData = null
            let responseText = ''
            
            if (response.data) {
              if (typeof response.data === 'string') {
                responseText = response.data
                try {
                  jsonData = JSON.parse(response.data)
                } catch (e) {
                  console.log('响应不是有效的JSON格式')
                }
              } else if (typeof response.data === 'object') {
                jsonData = response.data
                responseText = JSON.stringify(response.data, null, 2)
              }
            }
            
            this.requestResult = {
              success: true,
              url: this.testForm.url,
              status: response.status,
              timestamp: new Date().toLocaleString(),
              duration: duration,
              response: responseText,
              jsonData: jsonData
            }
            
            this.$message.success('请求发送成功')
            console.log('请求成功，响应数据:', response.data)
            
          } catch (error) {
            const endTime = Date.now()
            const duration = endTime - startTime
            
            console.error('请求失败:', error)
            
            let errorMessage = '请求失败'
            let status = 'ERROR'
            let responseText = ''
            
            if (error.response) {
              // 服务器响应了错误状态码
              status = error.response.status
              errorMessage = `HTTP ${error.response.status}`
              if (error.response.data) {
                if (typeof error.response.data === 'string') {
                  responseText = error.response.data
                } else {
                  responseText = JSON.stringify(error.response.data, null, 2)
                }
              }
            } else if (error.request) {
              // 请求已发送但没有收到响应
              errorMessage = '网络错误或超时'
              responseText = '请求已发送但没有收到响应，可能是网络错误或服务器超时'
            } else {
              // 其他错误
              errorMessage = error.message || '未知错误'
              responseText = error.message || '未知错误'
            }
            
            this.requestResult = {
              success: false,
              url: this.testForm.url,
              status: status,
              timestamp: new Date().toLocaleString(),
              duration: duration,
              response: responseText,
              jsonData: null
            }
            
            this.$message.error(`请求失败: ${errorMessage}`)
          } finally {
            this.loading = false
          }
        },
        
        clearResult() {
          this.requestResult = null
          this.$message.info('已清空结果')
        },
        
        getStatusType(status) {
          if (typeof status === 'number') {
            if (status >= 200 && status < 300) {
              return 'success'
            } else if (status >= 400 && status < 500) {
              return 'warning'
            } else if (status >= 500) {
              return 'danger'
            }
          }
          return 'info'
        }
      }
  }
  </script>
    
  <style scoped>
  .app-container {
    padding: 20px;
  }

  .url-test-card {
    max-width: 1200px;
  }

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  .result-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
  }

  .result-section h4 {
    margin: 0 0 15px 0;
    color: #303133;
    display: flex;
    align-items: center;
  }

  .result-section h5 {
    margin: 15px 0 8px 0;
    color: #606266;
    font-size: 14px;
  }

  .url-text {
    word-break: break-all;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #409EFF;
  }

  .response-content {
    margin-top: 15px;
  }

  .json-display {
    margin-top: 15px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .app-container {
      padding: 10px;
    }
    
    .url-test-card {
      margin: 10px 0;
    }
  }
  </style>
    