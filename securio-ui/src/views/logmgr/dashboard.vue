<template>
    <div class="app-container">
      <!-- 日志处理性能指标面板 (优化版) -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="performance-overview-card" shadow="hover">
            <div slot="header" class="card-header">
              <div class="card-title">
                <i class="el-icon-odometer"></i>
                <span>日志处理性能</span>
              </div>
            </div>
            <el-row :gutter="20" v-loading="performanceLoading">
              <el-col :span="8">
                <div class="performance-overview-item time-item">
                  <div class="overview-icon">
                    <i class="el-icon-time"></i>
                  </div>
                  <div class="overview-content">
                    <div class="overview-title">平均处理时间</div>
                    <div class="overview-value">{{formatProcessingTime(performanceStats.avgProcessingTime)}}</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="performance-overview-item minute-item">
                  <div class="overview-icon">
                    <i class="el-icon-data-analysis"></i>
                  </div>
                  <div class="overview-content">
                    <div class="overview-title">每分钟处理量</div>
                    <div class="overview-value">{{performanceStats.processingRateMinute}} <span class="overview-unit">条/分钟</span></div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="performance-overview-item hour-item">
                  <div class="overview-icon">
                    <i class="el-icon-odometer"></i>
                  </div>
                  <div class="overview-content">
                    <div class="overview-title">每小时处理量</div>
                    <div class="overview-value">{{performanceStats.processingRateHour}} <span class="overview-unit">条/小时</span></div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="log-stat-main-card" shadow="hover">
            <div slot="header" class="log-stat-header">
              <div class="log-stat-title">
                <i class="el-icon-data-analysis"></i>
                <span>日志处理概览</span>
              </div>
            </div>
            <el-row :gutter="20" v-loading="statsLoading">
              <el-col :span="6" v-for="(item, index) in logStats" :key="index">
                <div class="log-stat-card" :class="`stat-card-${index % 4}`">
                  <div class="log-type">
                    <i :class="getLogTypeIcon(item.logType)" class="log-type-icon"></i>
                    <span>{{item.logType}}</span>
                  </div>
                  <div class="stat-numbers">
                    <div class="stat-item">
                      <span class="label">今日处理：</span>
                      <span class="number">{{formatNumber(item.todayCount)}}</span>
                      <div class="trend-indicator" v-if="item.todayCount > item.yesterdayCount">
                        <i class="el-icon-top" style="color: #67C23A;"></i>
                      </div>
                      <div class="trend-indicator" v-else-if="item.todayCount < item.yesterdayCount">
                        <i class="el-icon-bottom" style="color: #F56C6C;"></i>
                      </div>
                    </div>
                    <div class="stat-item">
                      <span class="label">昨日处理：</span>
                      <span class="number">{{formatNumber(item.yesterdayCount)}}</span>
                    </div>
                    <div class="stat-item total-count">
                      <span class="label">总处理量：</span>
                      <span class="number">{{formatNumber(item.totalCount)}}</span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>

      <!-- 组件健康状态面板 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="component-card" shadow="hover">
            <div slot="header" class="card-header">
              <div class="card-title">
                <i class="el-icon-s-operation"></i>
                <span>管道健康状态</span>
              </div>
            </div>
            <div v-loading="componentLoading">
              <div class="pipeline-list">
                <div v-for="(pipeline, index) in pipelineGroups" :key="index" class="pipeline-row">
                  <el-card shadow="hover" class="pipeline-card">
                    <div class="pipeline-header">
                      <div class="pipeline-info">
                        <span class="pipeline-name">{{pipeline.name}}</span>
                        <el-tag :type="getPipelineHealthType(pipeline)" size="small">
                          {{getPipelineHealthStatus(pipeline)}}
                        </el-tag>
                      </div>
                      <div class="pipeline-summary">
                        <div class="summary-item">
                          <span class="summary-label">总接收:</span>
                          <span class="summary-value">{{getPipelineTotalReceived(pipeline)}}</span>
                        </div>
                        <div class="summary-item">
                          <span class="summary-label">总处理:</span>
                          <span class="summary-value">{{getPipelineTotalProcessed(pipeline)}}</span>
                        </div>
                        <div class="summary-item">
                          <span class="summary-label">总错误:</span>
                          <span class="summary-value" :class="{'error-count': getPipelineTotalErrors(pipeline) > 0}">
                            {{getPipelineTotalErrors(pipeline)}}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div class="component-flow">
                      <!-- 输入组件 -->
                      <div class="component-group">
                        <div class="group-title">输入组件</div>
                        <div class="component-list">
                          <div v-for="(component, cIndex) in getComponentsByType(pipeline, 'input')" :key="'input-'+cIndex"
                               class="component-item">
                            <div class="component-item-header">
                              <span class="component-name" :title="component.componentName">
                                {{getShortComponentName(component.componentName)}}
                              </span>
                              <el-tag :type="getHealthStatusType(component.healthStatus)" size="mini">
                                {{component.healthStatus}}
                              </el-tag>
                            </div>
                            <div class="component-item-stats">
                              <div class="stat-row">
                                <span class="stat-label">接收/处理:</span>
                                <span class="stat-value">{{component.received}} / {{component.processed}}</span>
                              </div>
                              <div class="stat-row">
                                <span class="stat-label">错误/丢弃:</span>
                                <span class="stat-value">
                                  <span :class="{'error-count': component.errors > 0}">{{component.errors}}</span> /
                                  <span :class="{'error-count': component.dropped > 0}">{{component.dropped}}</span>
                                </span>
                              </div>
                              <div class="stat-row">
                                <span class="stat-label">平均处理时间:</span>
                                <span class="stat-value">{{formatProcessingTime(component.avgProcessingTime)}}</span>
                              </div>
                            </div>
                          </div>
                          <div v-if="getComponentsByType(pipeline, 'input').length === 0" class="no-component">
                            无输入组件
                          </div>
                        </div>
                      </div>

                      <div class="flow-arrow">→</div>

                      <!-- 处理组件 -->
                      <div class="component-group">
                        <div class="group-title">处理组件</div>
                        <div class="component-list">
                          <div v-for="(component, cIndex) in getComponentsByType(pipeline, 'processor')" :key="'processor-'+cIndex"
                               class="component-item">
                            <div class="component-item-header">
                              <span class="component-name" :title="component.componentName">
                                {{getShortComponentName(component.componentName)}}
                              </span>
                              <el-tag :type="getHealthStatusType(component.healthStatus)" size="mini">
                                {{component.healthStatus}}
                              </el-tag>
                            </div>
                            <div class="component-item-stats">
                              <div class="stat-row">
                                <span class="stat-label">接收/处理:</span>
                                <span class="stat-value">{{component.received}} / {{component.processed}}</span>
                              </div>
                              <div class="stat-row">
                                <span class="stat-label">错误/丢弃:</span>
                                <span class="stat-value">
                                  <span :class="{'error-count': component.errors > 0}">{{component.errors}}</span> /
                                  <span :class="{'error-count': component.dropped > 0}">{{component.dropped}}</span>
                                </span>
                              </div>
                              <div class="stat-row">
                                <span class="stat-label">平均处理时间:</span>
                                <span class="stat-value">{{formatProcessingTime(component.avgProcessingTime)}}</span>
                              </div>
                            </div>
                          </div>
                          <div v-if="getComponentsByType(pipeline, 'processor').length === 0" class="no-component">
                            无处理组件
                          </div>
                        </div>
                      </div>

                      <div class="flow-arrow">→</div>

                      <!-- 输出组件 -->
                      <div class="component-group">
                        <div class="group-title">输出组件</div>
                        <div class="component-list">
                          <div v-for="(component, cIndex) in getComponentsByType(pipeline, 'output')" :key="'output-'+cIndex"
                               class="component-item">
                            <div class="component-item-header">
                              <span class="component-name" :title="component.componentName">
                                {{getShortComponentName(component.componentName)}}
                              </span>
                              <el-tag :type="getHealthStatusType(component.healthStatus)" size="mini">
                                {{component.healthStatus}}
                              </el-tag>
                            </div>
                            <div class="component-item-stats">
                              <div class="stat-row">
                                <span class="stat-label">接收/处理:</span>
                                <span class="stat-value">{{component.received}} / {{component.processed}}</span>
                              </div>
                              <div class="stat-row">
                                <span class="stat-label">错误/丢弃:</span>
                                <span class="stat-value">
                                  <span :class="{'error-count': component.errors > 0}">{{component.errors}}</span> /
                                  <span :class="{'error-count': component.dropped > 0}">{{component.dropped}}</span>
                                </span>
                              </div>
                              <div class="stat-row">
                                <span class="stat-label">平均处理时间:</span>
                                <span class="stat-value">{{formatProcessingTime(component.avgProcessingTime)}}</span>
                              </div>
                            </div>
                          </div>
                          <div v-if="getComponentsByType(pipeline, 'output').length === 0" class="no-component">
                            无输出组件
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 日志类型分布图表 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card class="chart-card" shadow="hover">
            <div slot="header" class="card-header">
              <div class="card-title">
                <i class="el-icon-pie-chart"></i>
                <span>日志类型分布（按类别分组）</span>
              </div>
              <el-tooltip content="日志类型已按前缀分组，相同颜色表示同一类别" placement="top">
                <i class="el-icon-question" style="margin-left: 5px;"></i>
              </el-tooltip>
            </div>
            <div id="logTypeDistributionChart" style="height: 400px;" v-loading="distributionLoading"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card" shadow="hover">
            <div slot="header" class="card-header">
              <div class="card-title">
                <i class="el-icon-s-data"></i>
                <span>日志处理量排名（前15名）</span>
              </div>
            </div>
            <div id="logTypeRankingChart" style="height: 400px;" v-loading="rankingLoading"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 处理异常监控面板 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="error-monitor-card" shadow="hover">
            <div slot="header" class="card-header">
              <div class="card-title">
                <i class="el-icon-warning"></i>
                <span>处理异常监控</span>
              </div>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="hover">
                  <div class="error-card">
                    <div class="error-title">总错误数</div>
                    <div class="error-value">{{errorStats.totalErrors}}</div>
                    <div class="error-rate">错误率: {{errorStats.errorRate.toFixed(2)}}%</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover">
                  <div class="error-card">
                    <div class="error-title">丢弃消息数</div>
                    <div class="error-value">{{errorStats.totalDropped}}</div>
                    <div class="error-rate">丢弃率: {{errorStats.droppedRate.toFixed(2)}}%</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover">
                  <div class="error-card">
                    <div class="error-title">最近错误时间</div>
                    <div class="error-value">{{formatDate(errorStats.lastErrorTime) || '无'}}</div>
                    <div class="error-component">{{errorStats.lastErrorComponent || '无'}}</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <div id="errorTrendChart" style="height: 300px; margin-top: 20px;" v-loading="errorLoading"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 系统吞吐量监控 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="throughput-card" shadow="hover">
            <div slot="header" class="card-header">
              <div class="card-title">
                <i class="el-icon-odometer"></i>
                <span>系统吞吐量</span>
              </div>
              <el-radio-group v-model="throughputTimeUnit" size="small" style="float: right;" @change="handleThroughputUnitChange">
                <el-radio-button label="minute">每分钟</el-radio-button>
                <el-radio-button label="hour">每小时</el-radio-button>
                <el-radio-button label="day">每天</el-radio-button>
              </el-radio-group>
            </div>
            <div id="throughputChart" style="height: 400px;" v-loading="throughputLoading"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 日志处理趋势 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="trend-card" shadow="hover">
            <div slot="header" class="card-header">
              <div class="card-title">
                <i class="el-icon-data-line"></i>
                <span>日志处理趋势</span>
              </div>
              <el-date-picker
                style="float: right; margin-left: 10px;"
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="trendLoading"
                @change="handleDateRangeChange">
              </el-date-picker>
            </div>
            <div id="logTrendChart" style="height: 400px;" v-loading="trendLoading"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </template>

  <script>
  import { getLogStats, getLogTrend, getPerformanceStats, getComponentStats, getLogDistribution, getErrorStats, getThroughputStats } from "@/api/logmgr/dashboard";
  import * as echarts from 'echarts'

  export default {
    name: "Dashboard",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 弹出层标题
        title: "",
        // 是否显示详细弹出层
        openView: false,
        loading: false,
        // 是否显示弹出层
        open: false,
        showHostUsage: true,
        showCpuUsage: false,
        // 查询参数
        queryParams: {
          appName: null
        },
        statData: {},
        summaryStatData: {},
        // 表单校验
        rules: {},
        // 日志统计数据
        logStats: [],
        // 日期范围
        dateRange: [],
        // 日志类型列表
        logTypes: [
          'SYSLOG_COMMAND', 'SYSLOG_MESSAGE', 'SYSLOG_SECURE', 'SYSLOG_AUDIT',
          'SYSLOG_CRON', 'MUYUN', 'WAF', 'FIREWALL', 'OPENVPN', 'SWITCH',
          'AC', 'ACCESS_LOG_NGINX', 'ACCESS_LOG_TOMCAT', 'ACCESS_LOG_HAPROXY',
          'ERROR_LOG_NGINX', 'WEBLOGIC'
        ],
        // 加载状态
        statsLoading: false,
        trendLoading: false,

        // 性能指标数据
        performanceStats: {
          avgProcessingTime: 0,
          processingRateMinute: 0,
          processingRateHour: 0,
          processingRateDay: 0
        },
        performanceLoading: false,

        // 组件统计数据
        componentStats: [],
        pipelineGroups: [],
        componentLoading: false,

        // 分布图数据
        distributionData: {},
        distributionLoading: false,
        rankingLoading: false,

        // 错误监控数据
        errorStats: {
          totalErrors: 0,
          totalDropped: 0,
          errorRate: 0,
          droppedRate: 0,
          lastErrorTime: null,
          lastErrorComponent: null
        },
        errorLoading: false,

        // 吞吐量数据
        throughputTimeUnit: 'hour',
        throughputData: {},
        throughputLoading: false,

        // 图表实例
        charts: {}
      };
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        // 初始化日期范围为最近7天
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        this.dateRange = [start, end];

        // 获取各种统计数据
        this.getLogStatistics();
        this.getPerformanceData();
        this.getComponentData();
        this.getDistributionData();
        this.getErrorData();
        this.getThroughputData();
        this.getLogTrendData();
      },

      // 格式化处理时间（纳秒转为毫秒）
      formatProcessingTime(nanoseconds) {
        if (!nanoseconds) return '0 ms';
        return (nanoseconds / 1000000).toFixed(2) + ' ms';
      },

      // 格式化日期
      formatDate(date) {
        if (!date) return '';
        if (typeof date === 'string') {
          date = new Date(date);
        }
        return date.toLocaleString();
      },

      // 获取健康状态类型
      getHealthStatusType(status) {
        switch (status) {
          case '异常':
            return 'danger';
          case '阻塞':
            return 'warning';
          case '正常':
            return 'success';
          default:
            return 'info';
        }
      },

      handleDateRangeChange() {
        this.getLogTrendData();
      },

      handleThroughputUnitChange() {
        this.getThroughputData();
      },

      // 获取日志统计数据
      async getLogStatistics() {
        this.statsLoading = true;
        try {
          const response = await getLogStats();
          if (response.code === 200) {
            this.logStats = response.data;
          } else {
            this.$message.error(response.msg || '获取日志统计数据失败');
          }
        } catch (error) {
          console.error('获取日志统计数据失败:', error);
          this.$message.error('获取日志统计数据失败');
        } finally {
          this.statsLoading = false;
        }
      },

      // 获取性能指标数据
      async getPerformanceData() {
        this.performanceLoading = true;
        try {
          const response = await getPerformanceStats();
          if (response.code === 200) {
            this.performanceStats = response.data;
            this.$nextTick(() => {
              this.initPerformanceCharts();
            });
          } else {
            this.$message.error(response.msg || '获取性能指标数据失败');
          }
        } catch (error) {
          console.error('获取性能指标数据失败:', error);
          this.$message.error('获取性能指标数据失败');
        } finally {
          this.performanceLoading = false;
        }
      },

      // 初始化性能图表 (简化版，不再使用图表)
      initPerformanceCharts() {
        // 我们已经移除了图表元素，只需要更新数据即可
        // 数据已经在getPerformanceData方法中更新到this.performanceStats
      },

      // 获取组件统计数据
      async getComponentData() {
        this.componentLoading = true;
        try {
          const response = await getComponentStats();
          if (response.code === 200) {
            this.componentStats = response.data;
            this.groupComponentsByPipeline();
          } else {
            this.$message.error(response.msg || '获取组件统计数据失败');
          }
        } catch (error) {
          console.error('获取组件统计数据失败:', error);
          this.$message.error('获取组件统计数据失败');
        } finally {
          this.componentLoading = false;
        }
      },

      // 按管道分组组件
      groupComponentsByPipeline() {
        const pipelineMap = new Map();

        // 按管道名称分组
        this.componentStats.forEach(component => {
          const pipelineName = component.pipeline;
          if (!pipelineMap.has(pipelineName)) {
            pipelineMap.set(pipelineName, {
              name: pipelineName,
              components: []
            });
          }
          pipelineMap.get(pipelineName).components.push(component);
        });

        // 将Map转换为数组
        this.pipelineGroups = Array.from(pipelineMap.values());

        // 对每个管道内的组件按类型排序（input -> processor -> output）
        this.pipelineGroups.forEach(pipeline => {
          pipeline.components.sort((a, b) => {
            const typeOrder = { 'input': 1, 'processor': 2, 'output': 3 };
            return typeOrder[a.componentType] - typeOrder[b.componentType];
          });
        });
      },

      // 获取管道健康状态
      getPipelineHealthStatus(pipeline) {
        // 如果任何组件异常，则管道异常
        if (pipeline.components.some(c => c.healthStatus === '异常')) {
          return '异常';
        }
        // 如果任何组件阻塞，则管道阻塞
        if (pipeline.components.some(c => c.healthStatus === '阻塞')) {
          return '阻塞';
        }
        // 否则管道正常
        return '正常';
      },

      // 获取管道健康状态类型
      getPipelineHealthType(pipeline) {
        const status = this.getPipelineHealthStatus(pipeline);
        return this.getHealthStatusType(status);
      },

      // 获取管道总接收消息数
      getPipelineTotalReceived(pipeline) {
        return pipeline.components.reduce((sum, component) => sum + component.received, 0);
      },

      // 获取管道总处理消息数
      getPipelineTotalProcessed(pipeline) {
        return pipeline.components.reduce((sum, component) => sum + component.processed, 0);
      },

      // 获取管道总错误数
      getPipelineTotalErrors(pipeline) {
        return pipeline.components.reduce((sum, component) => sum + component.errors, 0);
      },

      // 按组件类型获取组件
      getComponentsByType(pipeline, type) {
        if (!pipeline || !pipeline.components) return [];
        return pipeline.components.filter(component => component.componentType === type);
      },

      // 获取组件名称的简短版本
      getShortComponentName(fullName) {
        if (!fullName) return '未知组件';

        // 如果包含星号，说明是类名形式，例如 *input.SyslogInput
        if (fullName.includes('*')) {
          // 提取最后一部分作为组件名
          const parts = fullName.split('.');
          return parts[parts.length - 1];
        }

        // 如果是路径形式，例如 /path/to/component
        if (fullName.includes('/')) {
          const parts = fullName.split('/');
          return parts[parts.length - 1];
        }

        // 如果是简单名称，直接返回
        return fullName;
      },

      // 获取日志分布数据
      async getDistributionData() {
        this.distributionLoading = true;
        this.rankingLoading = true;
        try {
          const response = await getLogDistribution();
          if (response.code === 200) {
            this.distributionData = response.data;
            this.$nextTick(() => {
              this.initDistributionChart(response.data);
              this.initRankingChart(response.data);
            });
          } else {
            this.$message.error(response.msg || '获取日志分布数据失败');
          }
        } catch (error) {
          console.error('获取日志分布数据失败:', error);
          this.$message.error('获取日志分布数据失败');
        } finally {
          this.distributionLoading = false;
          this.rankingLoading = false;
        }
      },

      // 初始化分布图表
      initDistributionChart(data) {
        const chartDom = document.getElementById('logTypeDistributionChart');
        if (chartDom) {
          const myChart = echarts.init(chartDom);

          // 对日志类型进行分组
          const logGroups = {
            'SECURIO_ZEEK': { color: '#5470c6', items: [] },
            'AUDITLOG_SYSLOG': { color: '#91cc75', items: [] },
            'AUDITLOG_DNS': { color: '#fac858', items: [] },
            'AUDITLOG_SYSTEM': { color: '#ee6666', items: [] },
            'AUDITLOG_DB': { color: '#73c0de', items: [] }, // 包括MYSQL和ORACLE
            'AUDITLOG_NETWORK': { color: '#3ba272', items: [] }, // 包括OPENVPN, SWITCH等
            'AUDITLOG_WEB': { color: '#fc8452', items: [] }, // 包括NGINX等
            'AUDITLOG_OTHER': { color: '#9a60b4', items: [] }
          };

          // 颜色映射函数
          const getColorByGroup = (group, index) => {
            const baseColor = logGroups[group].color;
            // 根据索引调整颜色亮度
            const lighten = index * 0.1;
            return this.adjustColor(baseColor, lighten);
          };

          // 对数据进行分组
          const groupedData = [];
          data.chartData.pieData.forEach(item => {
            const name = item.name;
            let group = 'AUDITLOG_OTHER';

            if (name.startsWith('SECURIO_ZEEK_')) {
              group = 'SECURIO_ZEEK';
            } else if (name.startsWith('AUDITLOG_SYSLOG_')) {
              group = 'AUDITLOG_SYSLOG';
            } else if (name.startsWith('AUDITLOG_DNS_')) {
              group = 'AUDITLOG_DNS';
            } else if (name.startsWith('AUDITLOG_SYSTEM_')) {
              group = 'AUDITLOG_SYSTEM';
            } else if (name.includes('MYSQL') || name.includes('ORACLE')) {
              group = 'AUDITLOG_DB';
            } else if (name.includes('OPENVPN') || name.includes('SWITCH') || name.includes('FIREWALL')) {
              group = 'AUDITLOG_NETWORK';
            } else if (name.includes('NGINX') || name.includes('TOMCAT') || name.includes('HAPROXY')) {
              group = 'AUDITLOG_WEB';
            }

            logGroups[group].items.push({
              name: name,
              value: item.value,
              itemStyle: {
                color: getColorByGroup(group, logGroups[group].items.length)
              }
            });
          });

          // 将分组数据转换为图表数据
          Object.keys(logGroups).forEach(group => {
            if (logGroups[group].items.length > 0) {
              groupedData.push(...logGroups[group].items);
            }
          });

          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              type: 'scroll',
              orient: 'vertical',
              right: 10,
              top: 20,
              bottom: 20,
              data: groupedData.map(item => item.name)
            },
            series: [
              {
                name: '日志类型分布',
                type: 'pie',
                radius: ['30%', '70%'],
                center: ['40%', '50%'],
                roseType: 'radius', // 使用南丁格尔玫瑰图
                itemStyle: {
                  borderRadius: 5,
                  borderColor: '#fff',
                  borderWidth: 1
                },
                label: {
                  show: false
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '14',
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: groupedData
              }
            ]
          };
          myChart.setOption(option);
          this.charts.distributionChart = myChart;
        }
      },

      // 调整颜色亮度
      adjustColor(color, amount) {
        // 如果是十六进制颜色
        if (color.startsWith('#')) {
          let r = parseInt(color.substr(1, 2), 16);
          let g = parseInt(color.substr(3, 2), 16);
          let b = parseInt(color.substr(5, 2), 16);

          r = Math.min(255, Math.max(0, Math.round(r * (1 + amount))));
          g = Math.min(255, Math.max(0, Math.round(g * (1 + amount))));
          b = Math.min(255, Math.max(0, Math.round(b * (1 + amount))));

          return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
        }

        // 如果是rgb颜色
        if (color.startsWith('rgb')) {
          const rgbMatch = color.match(/\d+/g);
          if (rgbMatch && rgbMatch.length >= 3) {
            let r = parseInt(rgbMatch[0]);
            let g = parseInt(rgbMatch[1]);
            let b = parseInt(rgbMatch[2]);

            r = Math.min(255, Math.max(0, Math.round(r * (1 + amount))));
            g = Math.min(255, Math.max(0, Math.round(g * (1 + amount))));
            b = Math.min(255, Math.max(0, Math.round(b * (1 + amount))));

            return `rgb(${r}, ${g}, ${b})`;
          }
        }

        return color;
      },

      // 初始化排名图表
      initRankingChart(data) {
        const chartDom = document.getElementById('logTypeRankingChart');
        if (chartDom) {
          const myChart = echarts.init(chartDom);

          // 对日志类型进行分组，与分布图表保持一致
          const logGroups = {
            'SECURIO_ZEEK': { color: '#5470c6', items: [] },
            'AUDITLOG_SYSLOG': { color: '#91cc75', items: [] },
            'AUDITLOG_DNS': { color: '#fac858', items: [] },
            'AUDITLOG_SYSTEM': { color: '#ee6666', items: [] },
            'AUDITLOG_DB': { color: '#73c0de', items: [] },
            'AUDITLOG_NETWORK': { color: '#3ba272', items: [] },
            'AUDITLOG_WEB': { color: '#fc8452', items: [] },
            'AUDITLOG_OTHER': { color: '#9a60b4', items: [] }
          };

          // 只取前15个数据
          const topCategories = data.chartData.barCategories.slice(0, 15);
          const topValues = data.chartData.barValues.slice(0, 15);

          // 为每个条目分配颜色
          const barData = topCategories.map((category, index) => {
            let group = 'AUDITLOG_OTHER';

            if (category.startsWith('SECURIO_ZEEK_')) {
              group = 'SECURIO_ZEEK';
            } else if (category.startsWith('AUDITLOG_SYSLOG_')) {
              group = 'AUDITLOG_SYSLOG';
            } else if (category.startsWith('AUDITLOG_DNS_')) {
              group = 'AUDITLOG_DNS';
            } else if (category.startsWith('AUDITLOG_SYSTEM_')) {
              group = 'AUDITLOG_SYSTEM';
            } else if (category.includes('MYSQL') || category.includes('ORACLE')) {
              group = 'AUDITLOG_DB';
            } else if (category.includes('OPENVPN') || category.includes('SWITCH') || category.includes('FIREWALL')) {
              group = 'AUDITLOG_NETWORK';
            } else if (category.includes('NGINX') || category.includes('TOMCAT') || category.includes('HAPROXY')) {
              group = 'AUDITLOG_WEB';
            }

            return {
              value: topValues[index],
              itemStyle: {
                color: logGroups[group].color
              }
            };
          });

          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              formatter: function(params) {
                const index = params[0].dataIndex;
                return `${topCategories[index]}: ${params[0].value}`;
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'value',
              name: '处理量'
            },
            yAxis: {
              type: 'category',
              data: topCategories.map(name => {
                // 截断过长的名称
                if (name.length > 20) {
                  return name.substring(0, 17) + '...';
                }
                return name;
              }),
              axisLabel: {
                interval: 0,
                formatter: function(value) {
                  // 对长名称进行处理
                  if (value.length > 15) {
                    return value.substring(0, 12) + '...';
                  }
                  return value;
                }
              }
            },
            series: [
              {
                name: '处理量',
                type: 'bar',
                data: barData,
                barWidth: '60%',
                label: {
                  show: true,
                  position: 'right',
                  formatter: '{c}'
                }
              }
            ]
          };
          myChart.setOption(option);
          this.charts.rankingChart = myChart;
        }
      },

      // 获取错误统计数据
      async getErrorData() {
        this.errorLoading = true;
        try {
          const response = await getErrorStats({
            startTime: this.dateRange[0]?.toISOString(),
            endTime: this.dateRange[1]?.toISOString()
          });
          if (response.code === 200) {
            this.errorStats = response.data;
            this.$nextTick(() => {
              this.initErrorTrendChart(response.data);
            });
          } else {
            this.$message.error(response.msg || '获取错误统计数据失败');
          }
        } catch (error) {
          console.error('获取错误统计数据失败:', error);
          this.$message.error('获取错误统计数据失败');
        } finally {
          this.errorLoading = false;
        }
      },

      // 初始化错误趋势图表
      initErrorTrendChart(data) {
        const chartDom = document.getElementById('errorTrendChart');
        if (chartDom && data.errorTrend) {
          const myChart = echarts.init(chartDom);

          // 确保有xAxis数据，如果没有则使用空数组
          const xAxisData = data.errorTrend.xAxis || [];

          // 确保有错误和丢弃数据，如果没有则使用空数组
          const errorsData = data.errorTrend.errors || [];
          const droppedData = data.errorTrend.dropped || [];

          const option = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['错误数', '丢弃数']
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: xAxisData
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '错误数',
                type: 'line',
                stack: '总量',
                data: errorsData
              },
              {
                name: '丢弃数',
                type: 'line',
                stack: '总量',
                data: droppedData
              }
            ]
          };
          myChart.setOption(option);
          this.charts.errorTrendChart = myChart;
        }
      },

      // 获取吞吐量数据
      async getThroughputData() {
        this.throughputLoading = true;
        try {
          const response = await getThroughputStats({
            timeUnit: this.throughputTimeUnit,
            startTime: this.dateRange[0]?.toISOString(),
            endTime: this.dateRange[1]?.toISOString()
          });
          if (response.code === 200) {
            this.throughputData = response.data;
            this.$nextTick(() => {
              this.initThroughputChart(response.data);
            });
          } else {
            this.$message.error(response.msg || '获取吞吐量数据失败');
          }
        } catch (error) {
          console.error('获取吞吐量数据失败:', error);
          this.$message.error('获取吞吐量数据失败');
        } finally {
          this.throughputLoading = false;
        }
      },

      // 初始化吞吐量图表
      initThroughputChart(data) {
        const chartDom = document.getElementById('throughputChart');
        if (chartDom) {
          const myChart = echarts.init(chartDom);

          // 构建图表数据
          const logTypes = data.logTypeThroughput ? data.logTypeThroughput.slice(0, 10).map(item => item.logType) : [];
          const throughputs = data.logTypeThroughput ? data.logTypeThroughput.slice(0, 10).map(item => item.throughput) : [];

          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            legend: {
              data: ['吞吐量']
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: logTypes
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '吞吐量',
                type: 'bar',
                data: throughputs
              }
            ]
          };
          myChart.setOption(option);
          this.charts.throughputChart = myChart;
        }
      },

      // 获取日志趋势数据
      async getLogTrendData() {
        if (!this.dateRange || this.dateRange.length !== 2) return;

        this.trendLoading = true;
        try {
          const [startDate, endDate] = this.dateRange;
          const response = await getLogTrend({
            startTime: startDate.toISOString(),
            endTime: endDate.toISOString()
          });

          if (response.code === 200) {
            this.initLogTrendChart(response.data);
          } else {
            this.$message.error(response.msg || '获取日志趋势数据失败');
          }
        } catch (error) {
          console.error('获取日志趋势数据失败:', error);
          this.$message.error('获取日志趋势数据失败');
        } finally {
          this.trendLoading = false;
        }
      },

      // 初始化日志趋势图表
      initLogTrendChart(data) {
        const chartDom = document.getElementById('logTrendChart');
        if (chartDom) {
          const myChart = echarts.init(chartDom);

          const option = {
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: data.xAxisData,
              name: '时间',
              boundaryGap: false
            },
            yAxis: [{
              type: 'value'
            }],
            series: data.seriesData,
            legend: {
              data: data.legend,
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
                animation: false
              }
            }
          }

          myChart.setOption(option);
          this.charts.logTrendChart = myChart;
        }
      },

      // 格式化数字（添加千位分隔符）
      formatNumber(num) {
        if (num === undefined || num === null) {
          return 'N/A';
        }
        return Number(num).toLocaleString();
      },
      
      // 根据日志类型返回对应图标
      getLogTypeIcon(logType) {
        const iconMap = {
          'SECURIO_ZEEK_SOFTWARE': 'el-icon-monitor',
          'SECURIO_ZEEK_CONN': 'el-icon-connection',
          'NGINX_ACCESS': 'el-icon-s-platform',
          'AUDITLOG_OPENVPN_STATUS': 'el-icon-lock',
          'SECURIO_ZEEK_KNOWN_HOSTS': 'el-icon-house',
          'SECURIO_ZEEK_SSL': 'el-icon-unlock',
          'AUDITLOG_ORACLE_USER': 'el-icon-user',
          'SECURIO_ZEEK_NOTICE': 'el-icon-bell',
          'AUDITLOG_DNS_BIND_ERROR': 'el-icon-warning',
          'SECURIO_ZEEK_SSH': 'el-icon-key',
          'SECURIO_ZEEK_WEIRD': 'el-icon-question',
          'AUDITLOG_APPLOG_AGENT': 'el-icon-notebook-2'
        };
        
        return iconMap[logType] || 'el-icon-document';
      }
    }
  }
  </script>

  <style scoped>
  .el-card {
    margin-bottom: 20px;
  }
  .el-row{
    margin: 15px 0px;
  }
  /* ::v-deep .el-card__body{
    background-color: #f8f9fc;
  } */
  .log-stat-card {
    text-align: center;
  }
  .log-type {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #303133;
  }
  .stat-numbers {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .stat-item .label {
    font-size: 12px;
    color: #909399;
  }
  .stat-item .number {
    color: #409EFF;
    font-weight: bold;
  }

  /* 性能概览卡片样式 */
  .performance-overview-card {
    background-color: #fff;
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.08);
    transition: all 0.3s;
  }

  .overview-header {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
  }

  .performance-overview-item {
    display: flex;
    align-items: center;
    padding: 24px;
    border-radius: 8px;
    min-height: 120px;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .performance-overview-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .time-item {
    background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
    border-left: 5px solid #00acc1;
  }

  .minute-item {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 5px solid #2196f3;
  }

  .hour-item {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    border-left: 5px solid #9c27b0;
  }

  .overview-icon {
    font-size: 36px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .time-item .overview-icon {
    color: #00acc1;
  }

  .minute-item .overview-icon {
    color: #2196f3;
  }

  .hour-item .overview-icon {
    color: #9c27b0;
  }

  .overview-content {
    flex: 1;
  }

  .overview-title {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.7);
    margin-bottom: 12px;
    font-weight: 500;
  }

  .overview-value {
    font-size: 28px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: baseline;
  }

  .overview-unit {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.55);
    font-weight: normal;
    margin-left: 5px;
  }

  .performance-overview-item::after {
    content: '';
    position: absolute;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    right: -30px;
    bottom: -30px;
    z-index: 0;
  }

  /* 性能卡片样式 */
  .performance-card, .error-card {
    text-align: center;
    padding: 10px;
  }

  .performance-title, .error-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
  }

  .performance-value, .error-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10px;
  }

  .performance-chart {
    height: 100px;
  }

  .error-rate, .error-component {
    font-size: 12px;
    color: #909399;
  }

  .error-count {
    color: #F56C6C;
    font-weight: bold;
  }

  /* 管道卡片样式 */
  .pipeline-list {
    display: flex;
    flex-direction: column;
  }

  .pipeline-row {
    margin-bottom: 15px;
  }

  .pipeline-card {
    margin-bottom: 0;
  }

  .pipeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #EBEEF5;
  }

  .pipeline-info {
    display: flex;
    align-items: center;
  }

  .pipeline-name {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-right: 10px;
  }

  .pipeline-summary {
    display: flex;
    align-items: center;
  }

  .summary-item {
    margin-left: 20px;
    display: flex;
    align-items: center;
  }

  .summary-label {
    color: #909399;
    margin-right: 5px;
  }

  .summary-value {
    font-weight: bold;
    color: #303133;
  }

  /* 组件流程布局 */
  .component-flow {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .flow-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #909399;
    margin: 0 10px;
    padding-top: 30px;
  }

  .component-group {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */
  }

  .group-title {
    font-size: 14px;
    font-weight: bold;
    color: #606266;
    margin-bottom: 10px;
    padding-left: 5px;
    border-left: 3px solid #409EFF;
  }

  .component-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .component-item {
    background-color: #F5F7FA;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
  }

  .component-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .component-name {
    font-size: 13px;
    font-weight: bold;
    color: #606266;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
  }

  .component-item-stats {
    font-size: 12px;
  }

  .stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .stat-label {
    color: #909399;
  }

  .stat-value {
    font-weight: bold;
    color: #606266;
  }

  .no-component {
    color: #909399;
    font-size: 12px;
    text-align: center;
    padding: 10px;
    background-color: #F5F7FA;
    border-radius: 4px;
  }

  /* 底部汇总样式已在上面定义，这里不需要重复 */

  /* 汇总项样式已在上面定义，这里不需要重复 */

  /* 日志处理统计样式 */
  .log-stat-main-card {
    margin-bottom: 20px;
  }

  .log-stat-header, .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .log-stat-title, .card-title {
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
  }

  .log-stat-title i, .card-title i {
    margin-right: 10px;
    font-size: 20px;
    color: #409EFF;
  }

  .log-stat-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px;
    height: 180px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .log-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  .stat-card-0 {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 5px solid #2196F3;
  }

  .stat-card-1 {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    border-left: 5px solid #9C27B0;
  }

  .stat-card-2 {
    background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
    border-left: 5px solid #4CAF50;
  }

  .stat-card-3 {
    background: linear-gradient(135deg, #fff8e1 0%, #ffe0b2 100%);
    border-left: 5px solid #FF9800;
  }

  .log-type {
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
  }

  .log-type-icon {
    margin-right: 8px;
    font-size: 18px;
  }

  .stat-card-0 .log-type-icon {
    color: #2196F3;
  }

  .stat-card-1 .log-type-icon {
    color: #9C27B0;
  }

  .stat-card-2 .log-type-icon {
    color: #4CAF50;
  }

  .stat-card-3 .log-type-icon {
    color: #FF9800;
  }

  .stat-numbers {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .stat-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
  }

  .label {
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    width: 80px;
  }

  .number {
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
  }

  .total-count .number {
    color: #409EFF;
    font-size: 18px;
  }

  .trend-indicator {
    margin-left: 10px;
    font-size: 14px;
  }
  </style>
