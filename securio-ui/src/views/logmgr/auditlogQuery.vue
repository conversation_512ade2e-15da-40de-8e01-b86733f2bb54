<template>
    <div class="app-container">
      <el-row>
        <iframe :src="vmUiUrl" width="100%" height="800" frameborder="0"></iframe>
      </el-row>
    </div>
  </template>

  <script>
  import moment from 'moment';

  export default {
    name: "LogQuery",
    data() {
      return {
        moment,
        // 遮罩层
        loading: true,
        // VM UI 地址
        vmUiUrl: process.env.VUE_APP_VM_UI_URL
      };
    },
    created() {
    },
    methods: {
    }
  };
  </script>
