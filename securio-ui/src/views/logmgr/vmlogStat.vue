<template>
  <div class="app-container">
    <el-card class="main-card" shadow="hover">
      <div slot="header" class="dashboard-header">
        <div class="dashboard-title">
          <i class="el-icon-data-line"></i>
          <span>VictoriaLogs 集群监控</span>
        </div>
        <el-select 
          v-model="selectedNode" 
          placeholder="请选择节点" 
          class="node-selector"
          @change="fetchNodeMetrics">
          <el-option
            v-for="node in nodes"
            :key="node"
            :label="node"
            :value="node">
          </el-option>
        </el-select>
      </div>

      <div v-loading="loading" class="dashboard-content">
        <!-- 核心指标概览 -->
        <div class="metrics-section">
          <div class="section-title">
            <i class="el-icon-s-data"></i>
            <span>核心性能指标</span>
          </div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <div class="metric-card system-card">
                <div class="metric-icon">
                  <i class="el-icon-cpu"></i>
                </div>
                <div class="metric-content">
                  <div class="metric-name">CPU 核心数</div>
                  <div class="metric-value">{{ metrics.process_cpu_cores_available || 'N/A' }}</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <div class="metric-card memory-card">
                <div class="metric-icon">
                  <i class="el-icon-coin"></i>
                </div>
                <div class="metric-content">
                  <div class="metric-name">内存用量</div>
                  <div class="metric-value">
                    {{ formatBytes(metrics.process_resident_memory_bytes) }}
                    <div class="metric-progress">
                      <el-progress 
                        :percentage="getMemoryUsagePercentage()" 
                        :color="getProgressColor(getMemoryUsagePercentage())"
                        :stroke-width="8">
                      </el-progress>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <div class="metric-card disk-card">
                <div class="metric-icon">
                  <i class="el-icon-folder"></i>
                </div>
                <div class="metric-content">
                  <div class="metric-name">存储空间</div>
                  <div class="metric-value">
                    {{ formatBytes(metrics['vl_data_size_bytes{type="storage"}']) }}
                    <div class="metric-sub">可用: {{ formatBytes(metrics['vl_free_disk_space_bytes{path="/data1/victoria-logs1/victoria-logs-data"}']) }}</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <div class="metric-card uptime-card">
                <div class="metric-icon">
                  <i class="el-icon-time"></i>
                </div>
                <div class="metric-content">
                  <div class="metric-name">运行时间</div>
                  <div class="metric-value">{{ formatDuration(metrics.vm_app_uptime_seconds) }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 日志摄入指标 -->
        <div class="metrics-section">
          <div class="section-title">
            <i class="el-icon-upload"></i>
            <span>日志摄入指标</span>
          </div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="stats-card blue-card">
                <div class="stats-icon">
                  <i class="el-icon-upload"></i>
                </div>
                <div class="stats-info">
                  <div class="stats-title">摄入总量</div>
                  <div class="stats-value">
                    {{ formatBytes(metrics['vl_bytes_ingested_total{type="jsonline"}']) }}
                  </div>
                  <div class="stats-trend positive">
                    <i class="el-icon-top"></i>
                  </div>
                </div>
                <div class="stats-footer">
                  <div class="stats-label">摄入行数:</div>
                  <div class="stats-data">{{ formatNumber(metrics['vl_rows_ingested_total{type="jsonline"}']) }}</div>
                </div>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="8">
              <div class="stats-card purple-card">
                <div class="stats-icon">
                  <i class="el-icon-connection"></i>
                </div>
                <div class="stats-info">
                  <div class="stats-title">HTTP请求</div>
                  <div class="stats-value">
                    {{ formatNumber(metrics.vm_http_requests_all_total) }}
                  </div>
                  <div class="stats-trend positive">
                    <i class="el-icon-top"></i>
                  </div>
                </div>
                <div class="stats-footer">
                  <div class="stats-label">查询请求:</div>
                  <div class="stats-data">{{ formatNumber(metrics['vl_http_requests_total{path="/select/logsql/query"}']) }}</div>
                </div>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="8">
              <div class="stats-card red-card">
                <div class="stats-icon">
                  <i class="el-icon-warning"></i>
                </div>
                <div class="stats-info">
                  <div class="stats-title">错误统计</div>
                  <div class="stats-value">
                    {{ formatNumber(getDroppedRowsCount()) }}
                  </div>
                  <div class="stats-trend" :class="getDroppedRowsCount() > 0 ? 'negative' : 'neutral'">
                    <i :class="getDroppedRowsCount() > 0 ? 'el-icon-top' : 'el-icon-minus'"></i>
                  </div>
                </div>
                <div class="stats-footer">
                  <div class="stats-label">跳过长行:</div>
                  <div class="stats-data">{{ formatNumber(metrics.vl_too_long_lines_skipped_total) }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 存储和索引指标 -->
        <div class="metrics-section">
          <div class="section-title">
            <i class="el-icon-files"></i>
            <span>存储和索引指标</span>
          </div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6">
              <div class="storage-stats-card">
                <div class="storage-card-header">
                  <span>数据存储</span>
                  <i class="el-icon-coin"></i>
                </div>
                <div class="storage-card-body">
                  <div class="storage-item">
                    <div class="storage-label">内存中行数:</div>
                    <div class="storage-value">{{ formatNumber(metrics['vl_storage_rows{type="storage/inmemory"}']) }}</div>
                  </div>
                  <div class="storage-item">
                    <div class="storage-label">磁盘行数:</div>
                    <div class="storage-value">{{ formatNumber(metrics['vl_storage_rows{type="storage/big"}']) }}</div>
                  </div>
                  <div class="storage-item">
                    <div class="storage-label">分区数:</div>
                    <div class="storage-value">{{ metrics.vl_partitions || 'N/A' }}</div>
                  </div>
                  <div class="storage-item">
                    <div class="storage-label">流数:</div>
                    <div class="storage-value">{{ metrics.vl_streams_created_total || 'N/A' }}</div>
                  </div>
                </div>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="6">
              <div class="storage-stats-card">
                <div class="storage-card-header">
                  <span>压缩率</span>
                  <i class="el-icon-full-screen"></i>
                </div>
                <div class="storage-card-body">
                  <div class="compression-container">
                    <div class="compression-ratio">
                      <div class="ratio-number">{{ calculateCompressionRatio() }}%</div>
                      <div class="ratio-label">平均压缩率</div>
                    </div>
                    <div class="compression-bar">
                      <div class="bar-container">
                        <div class="bar-fill" :style="{width: calculateCompressionRatio() + '%'}"></div>
                      </div>
                      <div class="bar-labels">
                        <span>0%</span>
                        <span>100%</span>
                      </div>
                    </div>
                  </div>
                  <div class="compression-details">
                    <div class="detail-item">
                      <div class="detail-label">原始大小:</div>
                      <div class="detail-value">{{ formatBytes(getTotalUncompressedSize()) }}</div>
                    </div>
                    <div class="detail-item">
                      <div class="detail-label">压缩后:</div>
                      <div class="detail-value">{{ formatBytes(getTotalCompressedSize()) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="6">
              <div class="storage-stats-card">
                <div class="storage-card-header">
                  <span>存储分布</span>
                  <i class="el-icon-pie-chart"></i>
                </div>
                <div class="storage-card-body">
                  <!-- 容量展示图表 -->
                  <div class="capacity-distribution">
                    <div class="capacity-chart">
                      <el-progress 
                        type="dashboard" 
                        :percentage="getInMemoryPercentage()" 
                        :width="120" 
                        :stroke-width="10" 
                        :color="getStorageChartColors"
                        :format="() => ''"
                      ></el-progress>
                      <div class="chart-overlay">
                        <div class="chart-percentage">{{getInMemoryPercentage()}}%</div>
                        <div class="chart-label">内存存储</div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 存储容量明细 -->
                  <div class="storage-detail">
                    <div class="storage-type memory">
                      <div class="type-indicator">
                        <div class="indicator-dot"></div>
                        <div class="type-name">内存存储</div>
                      </div>
                      <div class="type-value">{{formatBytes(metrics['vl_compressed_data_size_bytes{type="storage/inmemory"}'])}}</div>
                    </div>
                    <div class="storage-type disk">
                      <div class="type-indicator">
                        <div class="indicator-dot"></div>
                        <div class="type-name">磁盘存储</div>
                      </div>
                      <div class="type-value">{{formatBytes(metrics['vl_compressed_data_size_bytes{type="storage/big"}'])}}</div>
                    </div>
                    <div class="storage-total">
                      <div class="total-label">总存储大小</div>
                      <div class="total-value">{{formatBytes(getTotalCompressedSize())}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="6">
              <div class="storage-stats-card">
                <div class="storage-card-header">
                  <span>索引统计</span>
                  <i class="el-icon-document"></i>
                </div>
                <div class="storage-card-body">
                  <div class="index-stats">
                    <div class="stat-item">
                      <div class="stat-label">索引大小:</div>
                      <div class="stat-value highlight-blue">{{ formatBytes(metrics['vl_data_size_bytes{type="indexdb"}']) }}</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-label">索引行数:</div>
                      <div class="stat-value highlight-green">{{ metrics.vl_indexdb_rows || 'N/A' }}</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-label">索引分块:</div>
                      <div class="stat-value highlight-purple">{{ metrics.vl_indexdb_parts || 'N/A' }}</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-label">索引块数:</div>
                      <div class="stat-value highlight-orange">{{ metrics.vl_indexdb_blocks || 'N/A' }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 版本信息 -->
        <div class="metrics-section">
          <div class="section-title">
            <i class="el-icon-info"></i>
            <span>系统信息</span>
          </div>
          <div class="version-card">
            <div class="version-info">
              <div class="info-item">
                <div class="info-label">版本:</div>
                <div class="info-value">{{ getVersionInfo() }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">启动时间:</div>
                <div class="info-value">{{ formatTimestamp(metrics.vm_app_start_timestamp) }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">运行时间:</div>
                <div class="info-value">{{ formatDuration(metrics.vm_app_uptime_seconds) }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">存储路径:</div>
                <div class="info-value">{{ getStoragePath() }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 所有指标表格 -->
        <div class="metrics-section">
          <div class="section-title">
            <i class="el-icon-document"></i>
            <span>所有指标</span>
          </div>
          <div class="metrics-table-container">
            <div class="table-header">
              <el-input
                placeholder="搜索指标"
                v-model="searchQuery"
                prefix-icon="el-icon-search"
                class="search-input">
              </el-input>
            </div>
            <el-table
              :data="filteredMetrics"
              style="width: 100%"
              height="400"
              border
              stripe
              :header-cell-style="{background:'#f5f7fa',color:'#606266'}">
              <el-table-column
                prop="name"
                label="指标名称"
                width="500"
                show-overflow-tooltip>
              </el-table-column>
              <el-table-column
                prop="value"
                label="值"
                show-overflow-tooltip>
                <template slot-scope="scope">
                  <span :class="{'highlight-value': isNumeric(scope.row.value) && scope.row.value > 0}">
                    {{ formatTableValue(scope.row.name, scope.row.value) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getVictoriaLogsNodes, getNodeMetrics } from '@/api/logmgr/vmlogStat'

export default {
  name: 'VmLogStat',
  data() {
    return {
      loading: false,
      nodes: [],
      selectedNode: '',
      metrics: {},
      searchQuery: ''
    }
  },
  computed: {
    filteredMetrics() {
      const result = [];
      const query = this.searchQuery.toLowerCase();

      for (const [key, value] of Object.entries(this.metrics)) {
        if (key.toLowerCase().includes(query)) {
          result.push({
            name: key,
            value: value
          });
        }
      }

      return result.sort((a, b) => a.name.localeCompare(b.name));
    }
  },
  created() {
    this.fetchNodes();
  },
  methods: {
    // 获取节点列表
    fetchNodes() {
      this.loading = true;
      getVictoriaLogsNodes()
        .then(response => {
          this.nodes = response.data;
          if (this.nodes.length > 0) {
            this.selectedNode = this.nodes[0];
            this.fetchNodeMetrics();
          }
        })
        .catch(error => {
          this.$message.error('获取节点列表失败: ' + error.message);
          this.loading = false;
        });
    },

    // 获取节点指标
    fetchNodeMetrics() {
      if (!this.selectedNode) {
        return;
      }

      this.loading = true;
      getNodeMetrics(this.selectedNode)
        .then(response => {
          this.metrics = response.data;
          this.loading = false;
        })
        .catch(error => {
          this.$message.error('获取节点指标失败: ' + error.message);
          this.loading = false;
        });
    },

    // 格式化字节数
    formatBytes(bytes) {
      if (bytes === undefined || bytes === null) {
        return 'N/A';
      }

      bytes = Number(bytes);
      const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
      let i = 0;

      while (bytes >= 1024 && i < units.length - 1) {
        bytes /= 1024;
        i++;
      }

      return bytes.toFixed(2) + ' ' + units[i];
    },

    // 格式化数字（添加千位分隔符）
    formatNumber(num) {
      if (num === undefined || num === null) {
        return 'N/A';
      }

      return Number(num).toLocaleString();
    },

    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (timestamp === undefined || timestamp === null) {
        return 'N/A';
      }

      const date = new Date(Number(timestamp) * 1000);
      return date.toLocaleString();
    },

    // 格式化持续时间（秒转为天时分秒）
    formatDuration(seconds) {
      if (seconds === undefined || seconds === null) {
        return 'N/A';
      }

      seconds = Number(seconds);
      const days = Math.floor(seconds / 86400);
      seconds %= 86400;
      const hours = Math.floor(seconds / 3600);
      seconds %= 3600;
      const minutes = Math.floor(seconds / 60);
      seconds = Math.floor(seconds % 60);

      let result = '';
      if (days > 0) result += days + '天 ';
      if (hours > 0 || days > 0) result += hours + '小时 ';
      if (minutes > 0 || hours > 0 || days > 0) result += minutes + '分钟 ';
      result += seconds + '秒';

      return result;
    },

    // 获取版本信息
    getVersionInfo() {
      // 先尝试从vm_app_version字段获取
      for (const key in this.metrics) {
        if (key.startsWith('vm_app_version{version=')) {
          const match = key.match(/version="([^"]+)"/);
          if (match && match[1]) {
            return match[1];
          }
        }
      }
      
      // 如果没有vm_app_version，尝试获取go_info
      for (const key in this.metrics) {
        if (key.startsWith('go_info{version=')) {
          const match = key.match(/version="([^"]+)"/);
          if (match && match[1]) {
            return 'Go ' + match[1];
          }
        }
      }
      
      return 'N/A';
    },
    
    // 获取存储路径
    getStoragePath() {
      for (const key in this.metrics) {
        if (key.startsWith('flag{name="storageDataPath"')) {
          const match = key.match(/value="([^"]+)"/);
          if (match && match[1]) {
            return match[1];
          }
        }
      }
      return '/data1/victoria-logs1/victoria-logs-data'; // 默认路径
    },
    
    // 计算内存使用百分比
    getMemoryUsagePercentage() {
      if (!this.metrics.process_resident_memory_bytes || !this.metrics.process_memory_limit_bytes) {
        return 0;
      }
      
      const usedMemory = Number(this.metrics.process_resident_memory_bytes);
      const totalMemory = Number(this.metrics.process_memory_limit_bytes);
      
      if (totalMemory === 0) return 0;
      
      return Math.min(Math.round((usedMemory / totalMemory) * 100), 100);
    },
    
    // 获取进度条颜色
    getProgressColor(percentage) {
      if (percentage < 60) return '#67C23A';
      if (percentage < 80) return '#E6A23C';
      return '#F56C6C';
    },
    
    // 计算压缩率
    calculateCompressionRatio() {
      const uncompressedSize = this.getTotalUncompressedSize();
      const compressedSize = this.getTotalCompressedSize();
      
      if (uncompressedSize === 0) return '0';
      
      const ratio = (compressedSize / uncompressedSize) * 100;
      return ratio.toFixed(2);
    },
    
    // 获取总的未压缩大小
    getTotalUncompressedSize() {
      let total = 0;
      
      total += Number(this.metrics['vl_uncompressed_data_size_bytes{type="storage/inmemory"}'] || 0);
      total += Number(this.metrics['vl_uncompressed_data_size_bytes{type="storage/small"}'] || 0);
      total += Number(this.metrics['vl_uncompressed_data_size_bytes{type="storage/big"}'] || 0);
      
      return total;
    },
    
    // 获取总的压缩后大小
    getTotalCompressedSize() {
      let total = 0;
      
      total += Number(this.metrics['vl_compressed_data_size_bytes{type="storage/inmemory"}'] || 0);
      total += Number(this.metrics['vl_compressed_data_size_bytes{type="storage/small"}'] || 0);
      total += Number(this.metrics['vl_compressed_data_size_bytes{type="storage/big"}'] || 0);
      
      return total;
    },
    
    // 计算内存存储百分比
    getInMemoryPercentage() {
      const inMemory = Number(this.metrics['vl_compressed_data_size_bytes{type="storage/inmemory"}'] || 0);
      const total = this.getTotalCompressedSize();
      
      if (total === 0) return 0;
      
      return Math.round((inMemory / total) * 100);
    },
    
    // 判断是否为数字
    isNumeric(value) {
      return !isNaN(parseFloat(value)) && isFinite(value);
    },
    
    // 格式化表格中的值
    formatTableValue(name, value) {
      if (value === undefined || value === null) {
        return 'N/A';
      }
      
      // 针对不同类型的指标进行格式化
      if (name.includes('_bytes') || name.includes('_size')) {
        return this.formatBytes(value);
      }
      
      if (this.isNumeric(value) && value > 1000) {
        return this.formatNumber(value);
      }
      
      if (name.includes('_seconds') && !name.includes('_seconds_')) {
        return this.formatDuration(value);
      }
      
      if (name.includes('_timestamp')) {
        return this.formatTimestamp(value);
      }
      
      return value;
    },

    // 获取丢弃的行数
    getDroppedRowsCount() {
      const key = 'vl_rows_dropped_total{reason="too_many_fields"}';
      return this.metrics[key] || 0;
    },
    
    // 获取错误趋势的CSS类
    getErrorTrendClass() {
      const droppedRows = this.getDroppedRowsCount();
      return droppedRows === 0 ? 'trend-down' : 'trend-up';
    },

    // 获取存储图表颜色
    getStorageChartColors(percentage) {
      if (percentage < 5) return '#E6A23C'; // 几乎没有内存存储时显示黄色
      if (percentage < 30) return '#409EFF'; // 少量内存存储显示蓝色
      return '#67C23A'; // 大量内存存储显示绿色
    },
  }
}
</script>

<style scoped>
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-title {
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.dashboard-title i {
  margin-right: 10px;
  font-size: 20px;
  color: #409EFF;
}

.node-selector {
  width: 300px;
}

.dashboard-content {
  padding: 10px 0;
}

.metrics-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
}

.section-title i {
  margin-right: 8px;
  color: #409EFF;
}

/* 核心指标卡片样式 */
.metric-card {
  height: 130px;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: default;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.system-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-left: 5px solid #2196F3;
}

.memory-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  border-left: 5px solid #9C27B0;
}

.disk-card {
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
  border-left: 5px solid #4CAF50;
}

.uptime-card {
  background: linear-gradient(135deg, #fff8e1 0%, #ffe0b2 100%);
  border-left: 5px solid #FF9800;
}

.metric-icon {
  font-size: 36px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.system-card .metric-icon {
  color: #2196F3;
}

.memory-card .metric-icon {
  color: #9C27B0;
}

.disk-card .metric-icon {
  color: #4CAF50;
}

.uptime-card .metric-icon {
  color: #FF9800;
}

.metric-content {
  flex: 1;
}

.metric-name {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.metric-sub {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 5px;
}

.metric-progress {
  margin-top: 10px;
}

/* 新的日志摄入指标卡片样式 */
.stats-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  height: 180px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.blue-card {
  border-top: 4px solid #2196F3;
}

.purple-card {
  border-top: 4px solid #9C27B0;
}

.red-card {
  border-top: 4px solid #F56C6C;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 0 0 0 100%;
  z-index: 0;
}

.stats-icon {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 28px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.blue-card .stats-icon {
  color: #2196F3;
}

.purple-card .stats-icon {
  color: #9C27B0;
}

.red-card .stats-icon {
  color: #F56C6C;
}

.stats-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.stats-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 15px;
  font-weight: 500;
}

.stats-value {
  font-size: 26px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stats-trend {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 20px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.stats-trend.positive {
  color: #67C23A;
}

.stats-trend.negative {
  color: #F56C6C;
}

.stats-trend.neutral {
  color: #909399;
}

.stats-footer {
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.stats-data {
  font-size: 14px;
  font-weight: bold;
  color: #409EFF;
}

/* 存储卡片样式优化 */
.storage-stats-card {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  height: 280px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.storage-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.storage-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 12px 12px 0 0;
  font-size: 16px;
  font-weight: bold;
  color: #606266;
}

.storage-card-header i {
  font-size: 20px;
  color: #409EFF;
  opacity: 0.8;
}

.storage-card-body {
  padding: 15px;
  height: calc(100% - 56px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 数据存储卡片样式 */
.storage-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 14px;
}

.storage-label {
  color: #909399;
}

.storage-value {
  font-weight: bold;
  color: #606266;
}

/* 压缩率卡片样式 */
.compression-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.compression-ratio {
  text-align: center;
  margin-bottom: 15px;
}

.ratio-number {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
}

.ratio-label {
  font-size: 14px;
  color: #606266;
  margin-top: 5px;
}

.compression-bar {
  width: 100%;
  margin-top: 10px;
}

.bar-container {
  height: 10px;
  background-color: #F2F6FC;
  border-radius: 5px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #67C23A, #409EFF);
  border-radius: 5px;
  transition: width 0.5s ease;
}

.bar-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.compression-details {
  margin-top: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}

.detail-label {
  color: #909399;
}

.detail-value {
  font-weight: bold;
  color: #606266;
}

/* 存储分布卡片专用样式 */
.capacity-distribution {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.capacity-chart {
  position: relative;
  width: 120px;
  height: 120px;
}

.chart-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
}

.chart-percentage {
  font-size: 26px;
  font-weight: bold;
  color: #303133;
  line-height: 1.2;
}

.chart-label {
  font-size: 12px;
  color: #909399;
}

.storage-detail {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
}

.storage-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.type-indicator {
  display: flex;
  align-items: center;
}

.indicator-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
}

.memory .indicator-dot {
  background-color: #409EFF;
}

.disk .indicator-dot {
  background-color: #E6A23C;
}

.type-name {
  font-size: 13px;
  color: #606266;
}

.type-value {
  font-size: 13px;
  font-weight: bold;
  color: #303133;
}

.storage-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 10px;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

.total-label {
  font-size: 13px;
  font-weight: 500;
  color: #606266;
}

.total-value {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
}

/* 覆盖Element UI进度条样式 */
.capacity-chart >>> .el-progress__text {
  display: none;
}

.capacity-chart >>> .el-progress-dashboard {
  margin: 0;
}

/* 索引统计卡片样式 */
.index-stats {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 14px;
}

.stat-label {
  color: #909399;
}

.highlight-blue {
  color: #409EFF;
  font-weight: bold;
}

.highlight-green {
  color: #67C23A;
  font-weight: bold;
}

.highlight-purple {
  color: #9C27B0;
  font-weight: bold;
}

.highlight-orange {
  color: #E6A23C;
  font-weight: bold;
}

/* 版本信息卡片样式 */
.version-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.version-info {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  display: flex;
  margin-bottom: 15px;
  font-size: 14px;
}

.info-label {
  width: 100px;
  color: #909399;
}

.info-value {
  flex: 1;
  font-weight: bold;
  color: #606266;
  word-break: break-all;
}

/* 表格容器样式 */
.metrics-table-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
}

.table-header {
  margin-bottom: 15px;
}

.search-input {
  width: 300px;
}

.highlight-value {
  color: #409EFF;
  font-weight: bold;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .info-item {
    width: 100%;
  }
  
  .metric-card {
    height: auto;
    min-height: 120px;
  }
  
  .storage-stats-card {
    height: auto;
    min-height: 280px;
  }
  
  .chart-wrapper {
    margin: 10px 0;
  }
}
</style>