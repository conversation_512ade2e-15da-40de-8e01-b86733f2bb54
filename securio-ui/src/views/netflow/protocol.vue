<template>
    <div class="app-container">
    <div class="protocol-tabs-container">
      <!-- 时间筛选区域 -->
      <time-range-selector
        ref="timeRangeSelector"
        label="指定时间点"
        :show-timeline="true"
        :default-duration="4"
        :drag-sensitivity="0.5"
        @time-change="handleTimeRangeChange"
      />

      <!-- 协议类型筛选器 -->
      <div class="protocol-filter">
        <el-select 
          v-model="selectedCategory" 
          placeholder="选择协议类型" 
          size="small" 
          style="width: 150px; margin-bottom: 8px;"
          @change="handleCategoryChange">
          <el-option label="全部协议" value="all"></el-option>
          <el-option label="核心网络" value="core"></el-option>
          <el-option label="远程访问" value="remote"></el-option>
          <el-option label="数据库" value="database"></el-option>
          <el-option label="认证服务" value="auth"></el-option>
          <el-option label="文件分析" value="files"></el-option>
          <el-option label="已知信息" value="known"></el-option>
          <el-option label="安全事件" value="security"></el-option>
        </el-select>
      </div>
      
      <div class="protocol-tabs">
        <div
          v-for="(protocol, index) in filteredProtocolTabs"
          :key="protocol.key"
          class="protocol-tab-item"
          :class="{ 'active': activeTab === protocol.key }"
          @click="switchTab(protocol.key)"
        >
          <i :class="protocol.icon"></i>
          <span>{{ protocol.name }}</span>
        </div>
      </div>
    </div>

    <div class="tab-content">
      <!-- 动态渲染当前选中协议的日志查询组件 -->
      <log-query-component
        v-if="currentProtocol"
        :key="activeTab + '_' + timeRangeKey"
        ref="logQueryComponent"
        :title="currentProtocol.title"
        :subtitle="currentProtocol.subtitle"
        :settings="currentProtocol.settings"
        :stream="currentProtocol.stream"
        :default-query="customQuery"
        :default-time-range="customQuery ? null : '1h'"
        instance="vmlog2"
      />
    </div>
    </div>
  </template>
    
  <script>
import LogQueryComponent from '@/components/log_query_component'
import TimeRangeSelector from '@/components/TimeRangeSelector'
  
  export default {
  name: 'ProtocolAnalysis',
      components: {
    LogQueryComponent,
    TimeRangeSelector
      },
      data() {
          return {
      loading: false,
      activeTab: 'conn', // 默认显示连接日志
      selectedCategory: 'all',
      // 时间筛选相关
      currentTimeData: null,  // 当前时间数据
      queryKey: null,         // 查询触发键，每次需要触发查询时才更新
      protocolTabs: [
        // 核心网络协议 - 最常用
        {
          key: 'conn',
          name: '连接日志',
          icon: 'el-icon-link',
          category: 'core',
          stream: 'SECURIO_ZEEK_CONN',
          title: 'Zeek 连接日志',
          subtitle: '网络连接建立和状态信息',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '源IP', width: '120' },
              { prop: 'src_port', label: '源端口', width: '80' },
              { prop: 'dst_ip', label: '目标IP', width: '120' },
              { prop: 'dst_port', label: '目标端口', width: '80' },
              { prop: 'proto', label: '协议', width: '80', tag: true },
              { prop: 'service', label: '服务', width: '100', tag: true },
              { prop: 'duration', label: '持续时间', width: '120' },
              { prop: 'orig_bytes', label: '上传字节', width: '100' },
              { prop: 'resp_bytes', label: '下载字节', width: '100' },
              { prop: 'conn_state', label: '连接状态', width: '100', tag: true },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'dns',
          name: 'DNS',
          icon: 'el-icon-coordinate',
          category: 'core',
          stream: 'SECURIO_ZEEK_DNS',
          title: 'Zeek DNS 查询日志',
          subtitle: 'DNS 域名解析查询记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'src_port', label: '客户端端口', width: '120' },
              { prop: 'dst_ip', label: 'DNS服务器', width: '120' },
              { prop: 'query', label: '查询域名', showOverflowTooltip: true },
              { prop: 'qtype_name', label: '查询类型', width: '80', tag: true },
              { prop: 'rcode_name', label: '响应码', width: '80', tag: true },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'http',
          name: 'HTTP',
          icon: 'el-icon-globe',
          category: 'core',
          stream: 'SECURIO_ZEEK_HTTP',
          title: 'Zeek HTTP 访问日志',
          subtitle: 'HTTP/HTTPS 网页访问记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'method', label: '方法', width: '80', tag: true },
              { prop: 'host', label: '主机', width: '150' },
              { prop: 'uri', label: 'URI', showOverflowTooltip: true },
              { prop: 'status_code', label: '状态码', width: '80', tag: true },
              { prop: 'user_agent', label: '用户代理', showOverflowTooltip: true },
              { prop: 'uid', label: '连接ID', width: '150' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'ssl',
          name: 'SSL/TLS',
          icon: 'el-icon-lock',
          category: 'core',
          stream: 'SECURIO_ZEEK_SSL',
          title: 'Zeek SSL/TLS 连接日志',
          subtitle: 'SSL/TLS 加密连接信息',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'dst_ip', label: '服务器IP', width: '120' },
              { prop: 'server_name', label: '服务器名称', showOverflowTooltip: true },
              { prop: 'version', label: 'SSL版本', width: '100', tag: true },
              { prop: 'cipher', label: '加密套件', width: '150' },
              { prop: 'subject', label: '证书主题', showOverflowTooltip: true },
              { prop: 'uid', label: '连接ID', width: '150' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        // 远程访问协议
        {
          key: 'ssh',
          name: 'SSH',
          icon: 'el-icon-cpu',
          category: 'remote',
          stream: 'SECURIO_ZEEK_SSH',
          title: 'Zeek SSH 连接日志',
          subtitle: 'SSH 远程登录连接记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'src_port', label: '客户端端口', width: '100' },
              { prop: 'dst_ip', label: '服务器IP', width: '120' },
              { prop: 'dst_port', label: '服务器端口', width: '100' },
              { prop: 'auth_attempts', label: '认证尝试', width: '100' },
              { prop: 'server', label: '服务器版本', showOverflowTooltip: true },
              { prop: 'hasshVersion', label: 'HASSH版本', width: '100' },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'ftp',
          name: 'FTP',
          icon: 'el-icon-folder-opened',
          category: 'remote',
          stream: 'SECURIO_ZEEK_FTP',
          title: 'Zeek FTP 传输日志',
          subtitle: 'FTP 文件传输协议记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'src_port', label: '客户端端口', width: '100' },
              { prop: 'dst_ip', label: 'FTP服务器', width: '120' },
              { prop: 'dst_port', label: '服务器端口', width: '100' },
              { prop: 'user', label: '用户名', width: '120' },
              { prop: 'command', label: 'FTP命令', width: '100', tag: true },
              { prop: 'reply_code', label: '响应码', width: '80', tag: true },
              { prop: 'reply_msg', label: '响应消息', showOverflowTooltip: true },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        // 数据库协议
        {
          key: 'mysql',
          name: 'MySQL',
          icon: 'el-icon-coin',
          category: 'database',
          stream: 'SECURIO_ZEEK_MYSQL',
          title: 'Zeek MySQL 数据库日志',
          subtitle: 'MySQL 数据库连接和查询记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'dst_ip', label: '数据库IP', width: '120' },
              { prop: 'dst_port', label: '端口', width: '80' },
              { prop: 'cmd', label: '命令类型', width: '100', tag: true },
              { prop: 'arg', label: 'SQL语句/参数', showOverflowTooltip: true },
              { prop: 'success', label: '执行成功', width: '100', tag: true },
              { prop: 'rows', label: '影响行数', width: '100' },
              { prop: 'uid', label: '连接ID', width: '150' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        // 目录服务协议
        {
          key: 'ldap',
          name: 'LDAP',
          icon: 'el-icon-user',
          category: 'auth',
          stream: 'SECURIO_ZEEK_LDAP',
          title: 'Zeek LDAP 目录服务日志',
          subtitle: 'LDAP 目录访问和认证记录',
          settings: {
            columns: [
              { prop: 'ts', label: '时间戳', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'src_port', label: '客户端端口', width: '100' },
              { prop: 'dst_ip', label: '服务器IP', width: '120' },
              { prop: 'dst_port', label: '服务器端口', width: '100' },
              { prop: 'message_id', label: '消息ID', width: '100' },
              { prop: 'version', label: '版本', width: '80' },
              { prop: 'object', label: '对象'},
              { prop: 'opcode', label: '操作码', width: '100', tag: true },
              { prop: 'result', label: '结果',  tag: true }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        // 认证协议
        {
          key: 'radius',
          name: 'RADIUS',
          icon: 'el-icon-key',
          category: 'auth',
          stream: 'SECURIO_ZEEK_RADIUS',
          title: 'Zeek RADIUS 认证日志',
          subtitle: 'RADIUS 认证授权记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'src_port', label: '客户端端口', width: '100' },
              { prop: 'dst_ip', label: 'RADIUS服务器', width: '120' },
              { prop: 'dst_port', label: '服务器端口', width: '100' },
              { prop: 'username', label: '用户名', width: '120' },
              { prop: 'result', label: '认证结果', width: '120', tag: true },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        // 文件分析
        {
          key: 'files',
          name: '文件传输',
          icon: 'el-icon-document',
          category: 'files',
          stream: 'SECURIO_ZEEK_FILES',
          title: 'Zeek 文件传输日志',
          subtitle: '网络文件传输和下载记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'fuid', label: '文件ID', width: '150' },
              { prop: 'src_ip', label: '源IP', width: '120' },
              { prop: 'dst_ip', label: '目标IP', width: '120' },
              { prop: 'source', label: '来源协议', width: '80', tag: true },
              { prop: 'mime_type', label: 'MIME类型', width: '120' },
              { prop: 'seen_bytes', label: '已传输', width: '100' },
              { prop: 'total_bytes', label: '文件大小', width: '100' },
              { prop: 'md5', label: 'MD5', showOverflowTooltip: true },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'pe',
          name: 'PE 文件',
          icon: 'el-icon-files',
          category: 'files',
          stream: 'SECURIO_ZEEK_PE',
          title: 'Zeek PE 可执行文件日志',
          subtitle: 'Windows PE 可执行文件分析记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'id', label: '文件ID' },
              { prop: 'machine', label: '目标平台', width: '100', tag: true },
              { prop: 'os', label: '操作系统',  tag: true },
              { prop: 'subsystem', label: '子系统', tag: true },
              { prop: 'is_exe', label: '可执行文件', width: '100', tag: true },
              { prop: 'is_64bit', label: '64位', width: '80', tag: true },
              { prop: 'uses_aslr', label: 'ASLR', width: '80', tag: true },
              { prop: 'has_cert_table', label: '数字签名', width: '100', tag: true }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        // 已知信息
        {
          key: 'software',
          name: '软件识别',
          icon: 'el-icon-box',
          category: 'known',
          stream: 'SECURIO_ZEEK_SOFTWARE',
          title: 'Zeek 软件识别日志',
          subtitle: '网络中识别的软件和版本信息',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'host', label: '主机IP', width: '120' },
              { prop: 'software_type', label: '软件类型', width: '150', tag: true },
              { prop: 'name', label: '软件名称', width: '120' },
              { prop: 'version.major', label: '主版本', width: '80' },
              { prop: 'version.minor', label: '次版本', width: '80' },
              { prop: 'unparsed_version', label: '完整版本', showOverflowTooltip: true }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'known_hosts',
          name: '已知主机',
          icon: 'el-icon-monitor',
          category: 'known',
          stream: 'SECURIO_ZEEK_KNOWN_HOSTS',
          title: 'Zeek 已知主机日志',
          subtitle: '网络中已识别的主机信息',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'host', label: '主机IP' },
              { prop: 'msg_id', label: '消息ID'}
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'known_services',
          name: '已知服务',
          icon: 'el-icon-service',
          category: 'known',
          stream: 'SECURIO_ZEEK_KNOWN_SERVICES',
          title: 'Zeek 已知服务日志',
          subtitle: '网络中已识别的服务信息',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'host', label: '主机IP', width: '150' },
              { prop: 'port_num', label: '端口号', width: '100' },
              { prop: 'port_proto', label: '协议', width: '80', tag: true },
              { prop: 'msg_id', label: '消息ID'}
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'known_certs',
          name: '已知证书',
          icon: 'el-icon-medal',
          category: 'known',
          stream: 'SECURIO_ZEEK_CONN_KNOWN_CERTS',
          title: 'Zeek 已知证书日志',
          subtitle: '已识别的SSL/TLS证书信息',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'host', label: '主机IP', width: '120' },
              { prop: 'port_num', label: '端口', width: '80' },
              { prop: 'subject', label: '证书主题', showOverflowTooltip: true },
              { prop: 'issuer_subject', label: '颁发者', showOverflowTooltip: true },
              { prop: 'serial', label: '序列号', width: '150' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'ldap_search',
          name: 'LDAP 搜索',
          icon: 'el-icon-search',
          category: 'auth',
          stream: 'SECURIO_ZEEK_LDAP_SEARCH',
          title: 'Zeek LDAP 搜索日志',
          subtitle: 'LDAP 目录搜索查询记录',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'src_port', label: '客户端端口', width: '100' },
              { prop: 'dst_ip', label: '服务器IP', width: '120' },
              { prop: 'dst_port', label: '服务器端口', width: '100' },
              { prop: 'message_id', label: '消息ID', width: '100' },
              { prop: 'scope', label: '搜索范围', width: '100', tag: true },
              { prop: 'base_object', label: '基础对象', showOverflowTooltip: true },
              { prop: 'filter', label: '搜索过滤器', showOverflowTooltip: true },
              { prop: 'result', label: '结果', width: '100', tag: true },
              { prop: 'result_count', label: '结果数量', width: '100' },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        // 安全事件 - 放在最后但很重要
        {
          key: 'notice',
          name: '异常通知',
          icon: 'el-icon-warning',
          category: 'security',
          stream: 'SECURIO_ZEEK_NOTICE',
          title: 'Zeek 异常通知日志',
          subtitle: '安全事件和异常行为告警',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'note', label: '通知类型', width: '200', tag: true },
              { prop: 'msg', label: '消息内容', showOverflowTooltip: true },
              { prop: 'src_ip', label: '源IP', width: '120' },
              { prop: 'src_port', label: '源端口', width: '100' },
              { prop: 'dst_ip', label: '目标IP', width: '120' },
              { prop: 'dst_port', label: '目标端口', width: '100' },
              { prop: 'proto', label: '协议', width: '80', tag: true },
              { prop: 'fuid', label: '文件ID', width: '150' },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        },
        {
          key: 'weird',
          name: '异常事件',
          icon: 'el-icon-error',
          category: 'security',
          stream: 'SECURIO_ZEEK_WEIRD',
          title: 'Zeek 异常事件日志',
          subtitle: '网络协议异常和错误事件',
          settings: {
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'name', label: '异常事件名称', width: '200', tag: true },
              { prop: 'src_ip', label: '源IP', width: '120' },
              { prop: 'src_port', label: '源端口', width: '100' },
              { prop: 'dst_ip', label: '目标IP', width: '120' },
              { prop: 'dst_port', label: '目标端口', width: '100' },
              { prop: 'source', label: '协议来源', width: '100', tag: true },
              { prop: 'addl', label: '附加信息', showOverflowTooltip: true },
              { prop: 'notice', label: '是否通知', width: '100', tag: true },
              { prop: 'peer', label: '处理节点', width: '120' },
              { prop: 'uid', label: '连接ID' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }
        }
      ],
    }
  },
  created() {
    // 初始化查询键
    this.queryKey = Date.now();
  },
  computed: {
    currentProtocol() {
      return this.protocolTabs.find(tab => tab.key === this.activeTab)
    },
    filteredProtocolTabs() {
      if (this.selectedCategory === 'all') {
        return this.protocolTabs
      }
      return this.protocolTabs.filter(tab => tab.category === this.selectedCategory)
    },
    // 自定义时间范围（用于传递给log-query-component）
    customQuery() {
      if (this.currentProtocol && this.currentTimeData && this.currentTimeData.timeRange && this.currentTimeData.timeRange.start && this.currentTimeData.timeRange.end) {
        // 使用时间范围的开始和结束时间，转换为ISO 8601格式
        const startTime = this.convertToISO8601(this.currentTimeData.timeRange.start);
        const endTime = this.convertToISO8601(this.currentTimeData.timeRange.end);
        
        // 构建时间范围查询，参考主机综合分析的格式
        const timeRange = `_time:[${startTime},${endTime}]`;
        
        // 完整的查询字符串
        // 使用_stream:{stream="流名称"}指定流，匹配VictoriaLogs的格式
        const query = `${timeRange} and _stream:{stream="${this.currentProtocol.stream}"}`;
        return query;
      }
      return null;
    },
    timeRangeKey() {
      // 生成一个用于重新渲染组件的键值
      if (this.customTimeRange) {
        // 使用固定值+时间戳，只在主动触发时才更新
        return `custom_${this.queryKey || 'default'}`;
      }
      return 'default';
    },
    // 时间范围是否有效
    customTimeRange() {
      return this.currentTimeData && this.currentTimeData.timeRange && this.currentTimeData.timeRange.start && this.currentTimeData.timeRange.end
    },
      },
      methods: {
    switchTab(tabKey) {
      this.activeTab = tabKey
    },
    handleCategoryChange() {
      // 处理协议类型筛选器的改变
    },
    
    // 处理时间范围变化
    handleTimeRangeChange(timeData) {
      this.currentTimeData = timeData;
      if (timeData) {
        // 触发查询
        this.executeQuery();
      }
    },
    
    // 执行查询
    executeQuery() {
      // 更新查询键，强制重新渲染组件并触发查询
      this.queryKey = Date.now();
    },
    
    // 转换时间为ISO 8601格式，参考主机综合分析的实现
    convertToISO8601(timeStr) {
      if (!timeStr) return null;
      
      try {
        // 如果已经是ISO格式，直接返回
        if (timeStr.includes('T') && timeStr.includes('Z')) {
          return timeStr;
        }
        
        // 处理 "YYYY-MM-DD HH:mm:ss" 格式
        const date = new Date(timeStr);
        if (isNaN(date.getTime())) {
          console.warn('无效的时间格式:', timeStr);
          return null;
        }
        
        // 转换为ISO 8601格式 (UTC时间)
        return date.toISOString();
      } catch (error) {
        console.error('时间转换失败:', error, timeStr);
        return null;
      }
    },
  },
  }
  </script>

<style scoped>
.app-container {
  padding: 0;
}

.protocol-tabs-container {
  padding: 10px 0px 0px 10px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.protocol-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.protocol-tab-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  background-color: #f5f7fa;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s;
  border: 1px solid #e4e7ed;
  border-bottom: none;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 4px;
}

.protocol-tab-item:hover {
  background-color: #ecf5ff;
  color: #409EFF;
}

.protocol-tab-item.active {
  color: #409EFF;
  background-color: #fff;
  font-weight: 500;
  border-color: #409EFF;
  border-bottom: 2px solid #fff;
  margin-bottom: -1px;
  z-index: 1;
  position: relative;
}

.protocol-tab-item i {
  font-size: 16px;
}

.tab-content {
  background-color: #fff;
  padding: 0;
}

.protocol-filter {
  padding: 0 0 10px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 10px;
}

.protocol-filter .el-select {
  min-width: 150px;
}

.protocol-filter .el-input__inner {
  border-radius: 4px;
  border-color: #dcdfe6;
  transition: border-color 0.3s;
}

.protocol-filter .el-input__inner:focus {
  border-color: #409EFF;
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .protocol-tabs {
    gap: 1px;
  }
  
  .protocol-tab-item {
    padding: 6px 8px;
    font-size: 13px;
  }
  
  .protocol-tab-item span {
    display: none;
  }
}

@media (max-width: 768px) {
  .protocol-tabs-container {
    position: relative;
    padding: 5px;
  }
  
  .protocol-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }
  
  .protocol-tabs::-webkit-scrollbar {
    height: 4px;
  }
  
  .protocol-tabs::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  .protocol-tabs::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }
  
  .protocol-tab-item {
    flex-shrink: 0;
    min-width: 60px;
    justify-content: center;
    padding: 8px 6px;
    font-size: 12px;
  }
  
  .protocol-tab-item span {
    display: none;
  }
  
  .protocol-tab-item i {
    font-size: 18px;
  }
  
  .protocol-tabs-container::after {
    display: block;
  }
  

}

@media (max-width: 480px) {
  .protocol-tab-item {
    min-width: 50px;
    padding: 6px 4px;
  }
  
  .protocol-tab-item i {
    font-size: 16px;
  }
}

/* 提供一些视觉改进 */
.protocol-tab-item {
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.protocol-tab-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.protocol-tab-item.active {
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transform: translateY(-1px);
}

/* 为特定协议类型添加颜色提示 */
.protocol-tab-item[class*="conn"] .el-icon-link {
  color: #67c23a;
}

.protocol-tab-item[class*="dns"] .el-icon-coordinate {
  color: #e6a23c;
}

.protocol-tab-item[class*="http"] .el-icon-globe {
  color: #409eff;
}

.protocol-tab-item[class*="ssl"] .el-icon-lock {
  color: #f56c6c;
}

.protocol-tab-item[class*="ssh"] .el-icon-cpu {
  color: #909399;
}

.protocol-tab-item[class*="notice"] .el-icon-warning,
.protocol-tab-item[class*="weird"] .el-icon-error {
  color: #f56c6c;
}

/* 滚动指示器 */
.protocol-tabs-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 10px;
  width: 20px;
  height: 100%;
  background: linear-gradient(to left, #fff 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
  display: none;
}

@media (max-width: 768px) {
  .protocol-tabs-container {
    position: relative;
  }
  
  .protocol-tabs-container::after {
    display: block;
  }
}
</style>