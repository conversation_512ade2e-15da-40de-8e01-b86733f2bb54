<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">时间筛选组件使用示例</span>
      </div>
      
      <!-- 示例1：基本用法 -->
      <div class="example-section">
        <h3>示例1：基本用法</h3>
        <time-range-selector
          @time-change="handleBasicTimeChange"
        />
        <div v-if="basicTimeData" class="time-data-display">
          <p><strong>选择时间：</strong>{{ basicTimeData.selectedTime }}</p>
          <p><strong>滑块中心时间：</strong>{{ basicTimeData.sliderCenterTime }}</p>
          <p><strong>时间范围：</strong>{{ basicTimeData.timeRange.start }} ~ {{ basicTimeData.timeRange.end }}</p>
          <p><strong>持续时间：</strong>{{ basicTimeData.timeRange.duration }}分钟</p>
        </div>
      </div>

      <!-- 示例2：自定义配置 -->
      <div class="example-section">
        <h3>示例2：自定义配置（10分钟范围，低灵敏度）</h3>
        <time-range-selector
          ref="customTimeSelector"
          label="自定义时间"
          :default-duration="10"
          :drag-sensitivity="0.3"
          @time-change="handleCustomTimeChange"
        />
        <div v-if="customTimeData" class="time-data-display">
          <p><strong>选择时间：</strong>{{ customTimeData.selectedTime }}</p>
          <p><strong>滑块中心时间：</strong>{{ customTimeData.sliderCenterTime }}</p>
          <p><strong>时间范围：</strong>{{ customTimeData.timeRange.start }} ~ {{ customTimeData.timeRange.end }}</p>
          <p><strong>持续时间：</strong>{{ customTimeData.timeRange.duration }}分钟</p>
        </div>
        <div class="button-group">
          <el-button @click="getCurrentData">获取当前时间数据</el-button>
          <el-button @click="setSpecificTime">设置特定时间</el-button>
        </div>
      </div>

      <!-- 示例3：仅时间选择器（不显示时间线） -->
      <div class="example-section">
        <h3>示例3：仅时间选择器（不显示时间线）</h3>
        <time-range-selector
          label="简单时间选择"
          :show-timeline="false"
          @time-change="handleSimpleTimeChange"
        />
        <div v-if="simpleTimeData" class="time-data-display">
          <p><strong>选择时间：</strong>{{ simpleTimeData.selectedTime }}</p>
          <p><strong>时间范围：</strong>{{ simpleTimeData.timeRange.start }} ~ {{ simpleTimeData.timeRange.end }}</p>
        </div>
      </div>

      <!-- 示例4：模拟日志查询 -->
      <div class="example-section">
        <h3>示例4：模拟日志查询应用</h3>
        <time-range-selector
          label="日志查询时间"
          :default-duration="5"
          @time-change="handleLogTimeChange"
        />
        <div v-if="logQuery" class="query-display">
          <p><strong>生成的查询语句：</strong></p>
          <pre>{{ logQuery }}</pre>
        </div>
        <div v-if="logTimeData" class="log-simulation">
          <p><strong>模拟查询结果：</strong></p>
          <el-table :data="mockLogData" border style="width: 100%">
            <el-table-column prop="timestamp" label="时间戳" width="180"></el-table-column>
            <el-table-column prop="level" label="级别" width="80"></el-table-column>
            <el-table-column prop="message" label="日志消息"></el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import TimeRangeSelector from '@/components/TimeRangeSelector'

export default {
  name: 'TimeRangeSelectorExample',
  components: {
    TimeRangeSelector
  },
  data() {
    return {
      basicTimeData: null,
      customTimeData: null,
      simpleTimeData: null,
      logTimeData: null,
      logQuery: '',
      mockLogData: []
    }
  },
  methods: {
    handleBasicTimeChange(timeData) {
      this.basicTimeData = timeData;
      console.log('基本用法时间变化:', timeData);
    },

    handleCustomTimeChange(timeData) {
      this.customTimeData = timeData;
      console.log('自定义配置时间变化:', timeData);
    },

    handleSimpleTimeChange(timeData) {
      this.simpleTimeData = timeData;
      console.log('简单时间选择变化:', timeData);
    },

    handleLogTimeChange(timeData) {
      this.logTimeData = timeData;
      if (timeData) {
        this.buildLogQuery(timeData);
        this.generateMockLogData(timeData);
      }
    },

    buildLogQuery(timeData) {
      const center = new Date(timeData.sliderCenterTime);
      const offsetMinutes = Math.ceil(timeData.timeRange.duration / 2);
      
      // 格式化为带东八区时区的时间格式
      const year = center.getFullYear();
      const month = String(center.getMonth() + 1).padStart(2, '0');
      const day = String(center.getDate()).padStart(2, '0');
      const hours = String(center.getHours()).padStart(2, '0');
      const minutes = String(center.getMinutes()).padStart(2, '0');
      const seconds = String(center.getSeconds()).padStart(2, '0');
      
      const centerTimeISO = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}+08:00`;
      
      this.logQuery = `_stream:{stream="SECURIO_APP_LOG"} _time:${centerTimeISO} offset ${offsetMinutes}m`;
    },

    generateMockLogData(timeData) {
      const startTime = new Date(timeData.timeRange.start);
      const endTime = new Date(timeData.timeRange.end);
      const duration = endTime - startTime;
      
      this.mockLogData = [];
      const logLevels = ['INFO', 'WARN', 'ERROR', 'DEBUG'];
      const messages = [
        '用户登录成功',
        '数据库连接建立',
        '处理请求完成',
        '缓存更新',
        '系统启动',
        '配置加载完成'
      ];

      // 生成5-10条模拟日志
      const logCount = Math.floor(Math.random() * 6) + 5;
      for (let i = 0; i < logCount; i++) {
        const randomTime = new Date(startTime.getTime() + Math.random() * duration);
        this.mockLogData.push({
          timestamp: this.formatDateTime(randomTime),
          level: logLevels[Math.floor(Math.random() * logLevels.length)],
          message: messages[Math.floor(Math.random() * messages.length)]
        });
      }

      // 按时间排序
      this.mockLogData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    },

    getCurrentData() {
      const data = this.$refs.customTimeSelector.getTimeData();
      this.$message.success('当前时间数据已输出到控制台');
      console.log('当前时间数据:', data);
    },

    setSpecificTime() {
      const now = new Date();
      const specificTime = new Date(now.getTime() - 30 * 60 * 1000); // 30分钟前
      
      const timeData = {
        selectedTime: this.formatDateTime(specificTime),
        sliderCenterTime: this.formatDateTime(specificTime),
        timeRange: {
          start: this.formatDateTime(new Date(specificTime.getTime() - 5 * 60 * 1000)),
          end: this.formatDateTime(new Date(specificTime.getTime() + 5 * 60 * 1000)),
          duration: 10
        },
        sliderOffsetPercent: 0
      };

      this.$refs.customTimeSelector.setTimeData(timeData);
      this.$message.success('已设置为30分钟前的时间');
    },

    formatDateTime(date) {
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + ' ' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0') + ':' +
        String(date.getSeconds()).padStart(2, '0');
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #303133;
  font-size: 16px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.time-data-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 12px;
}

.time-data-display p {
  margin: 8px 0;
  color: #606266;
}

.time-data-display strong {
  color: #303133;
}

.button-group {
  margin-top: 15px;
}

.button-group .el-button {
  margin-right: 10px;
}

.query-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.query-display pre {
  margin: 10px 0 0 0;
  padding: 10px;
  background-color: #2d3748;
  color: #e2e8f0;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.log-simulation {
  margin-top: 20px;
}

.log-simulation p {
  margin-bottom: 10px;
  font-weight: 500;
  color: #303133;
  font-size: 12px;
}
</style> 