<template>
  <div class="sankey-container">
    <!-- 桑基图 -->
    <div ref="sankeyChart" :style="{ width: width, height: height }"></div>
    
    <!-- 流量数据表格 -->
    <div class="traffic-table-container" v-if="data && data.links && data.links.length > 0">
      <div class="table-header">
        <h3>流量详细数据</h3>
        <!-- 自循环过滤提示 -->
        <div v-if="filteredSelfLoops > 0" class="self-loop-notice">
          <i class="el-icon-info"></i>
          <span>已自动过滤 {{ filteredSelfLoops }} 个自循环连接以确保图表正常显示</span>
        </div>
      </div>
      
      <!-- 双表格布局 -->
      <div class="dual-table-container">
        <!-- 入站流量表格 -->
        <div class="table-section">
          <div class="section-header">
            <h4>
              <i class="el-icon-download" style="color: #67c23a; margin-right: 5px;"></i>
              入站流量
            </h4>
            <span class="section-count">{{ inboundTableData.length }} 条记录</span>
          </div>
          <el-table
            :data="inboundTableData"
            border
            stripe
            style="width: 100%"
            :default-sort="{prop: 'value', order: 'descending'}"
            :span-method="inboundSpanMethod"
            size="small"
            :height="460"
          >
            <el-table-column
              prop="source"
              label="来源IP"
              min-width="180"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div class="ip-cell">
                  <i :class="getSourceIcon(scope.row.sourceType)" :style="{ color: getSourceColor(scope.row.sourceType) }"></i>
                  <span 
                    class="ip-link" 
                    @click="showHostInfo(scope.row.source)"
                    :title="'点击查看 ' + scope.row.source + ' 的主机信息'">
                    {{ scope.row.source }}
                  </span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column
              label="方向"
              width="60"
              align="center"
            >
              <template slot-scope="scope">
                <i class="el-icon-right" style="color: #67c23a; font-size: 16px;"></i>
              </template>
            </el-table-column>
            
            <el-table-column
              prop="target"
              label="目标IP"
              width="180"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div class="ip-cell target-ip">
                  <i :class="getTargetIcon(scope.row.targetType)" :style="{ color: getTargetColor(scope.row.targetType) }"></i>
                  <span 
                    class="ip-link" 
                    @click="showHostInfo(scope.row.target)"
                    :title="'点击查看 ' + scope.row.target + ' 的主机信息'">
                    {{ scope.row.target }}
                  </span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column
              prop="value"
              label="请求数"
              width="100"
              align="right"
              sortable
            >
              <template slot-scope="scope">
                <span class="request-count">{{ scope.row.value.toLocaleString() }}</span>
              </template>
            </el-table-column>
            
            <el-table-column
              prop="percentage"
              label="占比"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <span class="percentage">{{ scope.row.percentage }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 出站流量表格 -->
        <div class="table-section">
          <div class="section-header">
            <h4>
              <i class="el-icon-upload2" style="color: #409eff; margin-right: 5px;"></i>
              出站流量
            </h4>
            <span class="section-count">{{ outboundTableData.length }} 条记录</span>
          </div>
          <el-table
            :data="outboundTableData"
            border
            stripe
            style="width: 100%"
            :default-sort="{prop: 'value', order: 'descending'}"
            :span-method="outboundSpanMethod"
            size="small"
            :height="460"
          >
            <el-table-column
              prop="source"
              label="来源IP"
              width="180"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div class="ip-cell target-ip">
                  <i :class="getSourceIcon(scope.row.sourceType)" :style="{ color: getSourceColor(scope.row.sourceType) }"></i>
                  <span 
                    class="ip-link" 
                    @click="showHostInfo(scope.row.source)"
                    :title="'点击查看 ' + scope.row.source + ' 的主机信息'">
                    {{ scope.row.source }}
                  </span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column
              label="方向"
              width="60"
              align="center"
            >
              <template slot-scope="scope">
                <i class="el-icon-right" style="color: #409eff; font-size: 16px;"></i>
              </template>
            </el-table-column>
            
            <el-table-column
              prop="target"
              label="目标IP"
              min-width="180"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div class="ip-cell">
                  <i :class="getTargetIcon(scope.row.targetType)" :style="{ color: getTargetColor(scope.row.targetType) }"></i>
                  <span 
                    class="ip-link" 
                    @click="showHostInfo(scope.row.target)"
                    :title="'点击查看 ' + scope.row.target + ' 的主机信息'">
                    {{ scope.row.target }}
                  </span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column
              prop="value"
              label="请求数"
              width="100"
              align="right"
              sortable
            >
              <template slot-scope="scope">
                <span class="request-count">{{ scope.row.value.toLocaleString() }}</span>
              </template>
            </el-table-column>
            
            <el-table-column
              prop="percentage"
              label="占比"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <span class="percentage">{{ scope.row.percentage }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <!-- 统计摘要 -->
      <div class="traffic-summary">
        <div class="summary-item">
          <span class="summary-label">总流量:</span>
          <span class="summary-value">{{ totalRequests.toLocaleString() }} 次请求</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">入站流量:</span>
          <span class="summary-value">{{ inboundRequests.toLocaleString() }} 次</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">出站流量:</span>
          <span class="summary-value">{{ outboundRequests.toLocaleString() }} 次</span>
        </div>
      </div>
    </div>

    <!-- 主机信息抽屉 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="showHostInfoDrawer"
      direction="rtl"
      size="50%"
      :before-close="handleDrawerClose">
      
      <div class="drawer-content">
        <host-basic-info 
          v-if="selectedHostIp" 
          :host-ip="selectedHostIp" 
          @info-loaded="handleHostInfoLoaded" />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import HostBasicInfo from './HostBasicInfo/index.vue'

export default {
  name: 'IPFlowSankeyChart',
  components: {
    HostBasicInfo
  },
  // 组件事件:
  // @node-click: 节点点击事件，参数为节点信息对象 {ip, hostname, originalName, type, typeLabel}
  props: {
    data: {
      type: Object,
      default: () => ({
        nodes: [],
        links: []
      })
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '500px'
    },
    options: {
      type: Object,
      default: () => ({})
    },
    showOwnDrawer: {
      type: Boolean,
      default: true  // 默认显示自己的抽屉，保持向后兼容
    }
  },
  data() {
    return {
      chart: null,
      tableData: [],
      totalRequests: 0,
      inboundRequests: 0,
      outboundRequests: 0,
      inboundTableData: [],
      outboundTableData: [],
      filteredSelfLoops: 0,
      // 抽屉相关数据
      showHostInfoDrawer: false,
      selectedHostIp: '',
      drawerTitle: '主机基本信息'
    }
  },
  watch: {
    data: {
      handler() {
        this.renderChart()
        this.calculateTableData()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    this.renderChart()
    this.calculateTableData()
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    /**
     * 检查有向环并对有环节点拆点，返回处理后的 nodes/links 及映射关系
     */
    preprocessSankeyData(rawData) {
      if (!rawData || !rawData.nodes || !rawData.links) return { nodes: [], links: [], nodeMap: {}, revertMap: {} }
      const nodes = rawData.nodes.map(n => ({ ...n }))
      const links = rawData.links.map(l => ({ ...l }))
      // 1. 统计每个节点的入度和出度
      const inMap = {}, outMap = {}
      links.forEach(l => {
        outMap[l.source] = (outMap[l.source] || 0) + 1
        inMap[l.target] = (inMap[l.target] || 0) + 1
      })
      // 2. 检查环：如果某节点既是 source 又是 target，且存在 A->B->A
      // 用 DFS 检查所有环，记录需要拆点的节点
      const needSplit = new Set()
      // 构建邻接表
      const adj = {}
      nodes.forEach(n => { adj[n.name] = [] })
      links.forEach(l => { adj[l.source].push(l.target) })
      // 检查每条边是否能回到自己
      function hasCycle(start, cur, visited, depth) {
        if (depth > nodes.length) return false // 防止死循环
        if (cur === start && depth > 0) return true
        visited.add(cur)
        for (const next of adj[cur] || []) {
          if (!visited.has(next) || (next === start && depth > 0)) {
            if (hasCycle(start, next, new Set(visited), depth + 1)) return true
          }
        }
        return false
      }
      nodes.forEach(n => {
        if (hasCycle(n.name, n.name, new Set(), 0)) {
          // 只有非 target 类型才需要拆点
          if (n.type !== 'target') {
            needSplit.add(n.name)
          }
        }
      })
      // 3. 拆点：对于需要拆点的节点，分 src/dst
      const nodeMap = {} // 原始名 => 新名
      const revertMap = {} // 新名 => 原始名
      const newNodes = []
      nodes.forEach(n => {
        if (needSplit.has(n.name)) {
          // 既有 src 又有 dst
          newNodes.push({ ...n, name: n.name + '-src' })
          newNodes.push({ ...n, name: n.name + '-dst' })
          nodeMap[n.name + '-src'] = n.name
          nodeMap[n.name + '-dst'] = n.name
          revertMap[n.name] = [n.name + '-src', n.name + '-dst']
        } else {
          newNodes.push(n)
          nodeMap[n.name] = n.name
        }
      })
      // 4. 替换 links
      const newLinks = links.map(l => {
        let source = l.source, target = l.target
        if (needSplit.has(source)) source = source + '-src'
        if (needSplit.has(target)) target = target + '-dst'
        return { ...l, source, target }
      })
      return { nodes: newNodes, links: newLinks, nodeMap, revertMap, needSplit }
    },

    initChart() {
      this.chart = echarts.init(this.$refs.sankeyChart)
    },
    
    renderChart() {
      if (!this.chart || !this.data || !this.data.nodes || !this.data.links) {
        return
      }
      // 新增：预处理数据，自动拆点
      const { nodes, links, nodeMap, revertMap, needSplit } = this.preprocessSankeyData(this.data)
      this._sankeyNodeMap = nodeMap // 缓存映射，供表格/点击用
      this._sankeyRevertMap = revertMap
      this._sankeyNeedSplit = needSplit
      // 处理数据格式 - 提取IP地址作为显示名称
      const nodesWithDisplay = nodes.map(node => {
        // 提取原始IP
        const origName = nodeMap[node.name] || node.name
        const ipMatch = origName.match(/\(([^)]+)\)$/)
        const displayName = ipMatch ? ipMatch[1] : origName
        const hostname = ipMatch ? origName.replace(/\s*\([^)]+\)$/, '') : ''
        return {
          ...node,
          name: node.name, // 用新名
          displayName, // 展示用
          originalName: origName,
          hostname: hostname,
          type: node.type,
          itemStyle: this.getNodeStyle(node.type),
          label: {
            show: true,
            fontSize: 10,      
            color: '#303133',
            fontWeight: 'normal',
            position: 'right',
            offset: [8, 0],
            padding: [2, 6, 2, 6],
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 3,
            borderWidth: 1,
            borderColor: '#e4e7ed',
            formatter: function(params) {
              // label 显示原始IP
              return params.data.displayName
            },
            rich: {
              ip: {
                color: '#303133',
                fontSize: 10,
                fontWeight: 'normal'
              }
            }
          }
        }
      })
      // 处理连接数据 - 更新source和target为新名
      const linksWithDisplay = links.map(link => {
        return {
          ...link,
          source: link.source,
          target: link.target,
          value: link.value
        }
      })

      // 调试输出：查看连接数据
      console.log('桑基图节点数据:', nodesWithDisplay)
      console.log('桑基图连接数据:', linksWithDisplay)
      console.log('连接数据value值范围:', {
        min: Math.min(...linksWithDisplay.map(l => l.value)),
        max: Math.max(...linksWithDisplay.map(l => l.value)),
        total: linksWithDisplay.length
      })
      console.log('传入的桑基图配置选项:', this.options)

      const option = {
        title: {
          text: 'IP 行为分析流量图',
          left: 'center',
          top: '10px',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#303133'
          }
        },
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          backgroundColor: 'rgba(50, 50, 50, 0.9)',
          borderWidth: 0,
          textStyle: {
            color: '#fff'
          },
          formatter: (params) => {
            if (params.dataType === 'node') {
              const nodeData = params.data
              const hostname = nodeData.hostname || ''
              const ip = nodeData.name
              const typeLabel = this.getNodeTypeLabel(nodeData.type)
              
              if (hostname) {
                return `<b>主机名:</b> ${hostname}<br/><b>IP地址:</b> ${ip}<br/><b>类型:</b> ${typeLabel}<br/><span style="color: #409EFF;">点击查看详细信息</span>`
              } else {
                return `<b>IP地址:</b> ${ip}<br/><b>类型:</b> ${typeLabel}<br/><span style="color: #409EFF;">点击查看详细信息</span>`
              }
            } else if (params.dataType === 'edge') {
              return `<b>${params.data.source}</b> → <b>${params.data.target}</b><br/>请求数: ${params.data.value.toLocaleString()}<br/><span style="color: #409EFF;">点击查看详细日志</span>`
            }
            return params.name
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '15%',
          bottom: '5%'
        },
        series: [
          {
            type: 'sankey',
            emphasis: {
              focus: 'adjacency'
            },
            data: nodesWithDisplay,
            links: linksWithDisplay,
            itemStyle: {
              borderWidth: 1,
              borderColor: '#aaa'
            },
            lineStyle: {
              color: 'gradient',
              curveness: 0.5,
              opacity: 0.8
            },
            label: {
              show: true,
              fontSize: 10,      
              color: '#303133',
              fontWeight: 'normal',
              position: 'right',
              offset: [8, 0],     // 增加偏移量确保不被遮挡
              padding: [2, 6, 2, 6], // 增加内边距
              backgroundColor: 'rgba(255, 255, 255, 0.9)', // 添加背景色
              borderRadius: 3,    // 添加圆角
              borderWidth: 1,     // 添加边框
              borderColor: '#e4e7ed', // 边框颜色
              rich: {
                ip: {
                  color: '#303133',
                  fontSize: 10,
                  fontWeight: 'normal'
                }
              }
            },
            nodeGap: 20,
            nodeWidth: 20,      
            layoutIterations: 32,
            orient: 'horizontal',
            draggable: false,
            left: '3%',         
            right: '8%',        // 增加图表右边距从3%到8%
            top: '8%',          
            bottom: '3%',       
            levels: [
              {
                depth: 0,
                itemStyle: {
                  color: '#ff7875'
                },
                lineStyle: {
                  color: 'source',
                  opacity: 0.8
                }
              },
              {
                depth: 1,
                itemStyle: {
                  color: '#40a9ff'
                },
                lineStyle: {
                  color: 'source',
                  opacity: 0.8
                }
              },
              {
                depth: 2,
                itemStyle: {
                  color: '#73d13d'
                },
                lineStyle: {
                  color: 'source',
                  opacity: 0.8
                }
              }
            ],
            // 传入的options会覆盖上面的默认设置
            ...this.options
          }
        ]
      }

      this.chart.setOption(option, true)
      
      // 添加节点点击事件
      this.chart.off('click') // 先移除之前的事件监听器
      this.chart.on('click', (params) => {
        if (params.dataType === 'node') {
          this.handleNodeClick(params.data)
        } else if (params.dataType === 'edge') {
          // 处理连线点击事件
          this.handleLinkClick(params.data)
        }
      })
    },

    getNodeStyle(nodeType) {
      const styles = {
        source: {
          color: '#ff7875',
          nodeType: 'source'
        },
        target: {
          color: '#40a9ff',
          nodeType: 'target'
        },
        upstream: {
          color: '#73d13d',
          nodeType: 'upstream'
        }
      }
      return styles[nodeType] || { color: '#d9d9d9', nodeType: 'unknown' }
    },

    getNodeTypeLabel(nodeType) {
      const labels = {
        source: '来源IP',
        target: '目标IP',
        upstream: '上游IP',
        unknown: '未知'
      }
      return labels[nodeType] || '未知'
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    getSourceIcon(sourceType) {
      const icons = {
        source: 'el-icon-upload2',
        target: 'el-icon-monitor',
        upstream: 'el-icon-download'
      }
      return icons[sourceType] || 'el-icon-question'
    },

    getSourceColor(sourceType) {
      const colors = {
        source: '#ff7875',
        target: '#40a9ff',
        upstream: '#73d13d'
      }
      return colors[sourceType] || '#d9d9d9'
    },

    getTargetIcon(targetType) {
      const icons = {
        source: 'el-icon-upload2',
        target: 'el-icon-monitor',
        upstream: 'el-icon-download'
      }
      return icons[targetType] || 'el-icon-question'
    },

    getTargetColor(targetType) {
      const colors = {
        source: '#ff7875',
        target: '#40a9ff',
        upstream: '#73d13d'
      }
      return colors[targetType] || '#d9d9d9'
    },

    calculateTableData() {
      if (!this.data || !this.data.links || !this.data.nodes) {
        this.tableData = []
        this.totalRequests = 0
        this.inboundRequests = 0
        this.outboundRequests = 0
        this.inboundTableData = []
        this.outboundTableData = []
        return
      }
      // 新增：预处理数据，自动拆点
      const { nodes, links, nodeMap, revertMap, needSplit } = this.preprocessSankeyData(this.data)
      // 找到目标节点
      const targetNode = nodes.find(node => node.type === 'target' || (nodeMap[node.name] && this.data.nodes.find(n => n.name === nodeMap[node.name] && n.type === 'target')))
      const targetName = targetNode ? nodeMap[targetNode.name] || targetNode.name : ''
      // 提取目标IP
      const targetIP = targetNode ? ((targetNode.name.match(/\(([^)]+)\)$/)) ? targetNode.name.match(/\(([^)]+)\)$/)[1] : nodeMap[targetNode.name] || targetNode.name) : ''
      // 计算总请求数（只计算有效连接）
      this.totalRequests = links.reduce((sum, link) => sum + link.value, 0)
      // 处理链接数据
      this.tableData = links.map(link => {
        // 提取源和目标的原始IP
        const sourceOrig = nodeMap[link.source] || link.source
        const targetOrig = nodeMap[link.target] || link.target
        const sourceNode = nodes.find(n => n.name === link.source)
        const targetNode = nodes.find(n => n.name === link.target)
        // 判断流向
        const isInbound = targetOrig === targetIP
        const direction = isInbound ? '入站' : '出站'
        // 计算占比
        const percentage = this.totalRequests > 0 ? ((link.value / this.totalRequests) * 100).toFixed(1) : '0.0'
        return {
          source: sourceOrig,
          target: targetOrig,
          value: link.value,
          direction: direction,
          percentage: percentage,
          sourceType: sourceNode ? sourceNode.type : 'unknown',
          targetType: targetNode ? targetNode.type : 'unknown'
        }
      })
      // 计算入站和出站流量
      this.inboundRequests = this.tableData
        .filter(item => item.direction === '入站')
        .reduce((sum, item) => sum + item.value, 0)
      
      this.outboundRequests = this.tableData
        .filter(item => item.direction === '出站')
        .reduce((sum, item) => sum + item.value, 0)

      // 按请求数排序
      this.tableData.sort((a, b) => b.value - a.value)

      // 分离入站和出站流量
      this.inboundTableData = this.tableData.filter(item => item.direction === '入站')
      this.outboundTableData = this.tableData.filter(item => item.direction === '出站')
    },

    handleNodeClick(nodeData) {
      console.log('Node clicked:', nodeData)
      
      // 安全检查nodeData对象
      if (!nodeData) {
        console.warn('无效的节点信息:', nodeData)
        return
      }
      
      // 从节点数据中提取IP和主机名信息
      let ip = nodeData.name || ''
      let hostname = nodeData.hostname || ''
      
      // 如果没有hostname但有originalName，尝试从originalName中解析
      if (!hostname && nodeData.originalName && typeof nodeData.originalName === 'string') {
        // 如果原始名称包含hostname格式 "hostname (ip)"
        if (nodeData.originalName.includes('(') && nodeData.originalName.includes(')')) {
          const matches = nodeData.originalName.match(/^(.+?)\s*\(([^)]+)\)$/)
          if (matches) {
            hostname = matches[1].trim()
            ip = matches[2].trim()
          }
        }
      }
      
      // 构建标准的节点信息对象
      const nodeInfo = {
        ip: ip,
        hostname: hostname,
        originalName: nodeData.originalName || nodeData.name,
        type: nodeData.type || 'unknown',
        typeLabel: this.getNodeTypeLabel(nodeData.type)
      }
      
      console.log('处理节点点击，最终主机信息:', nodeInfo)
      
      // 发送节点点击事件到父组件
      this.$emit('node-click', nodeInfo)
      
      // 根据showOwnDrawer prop决定是否显示自己的抽屉
      if (this.showOwnDrawer) {
        this.showHostInfo(ip, hostname)
      }
    },

    // 处理连线点击事件
    handleLinkClick(linkData) {
      // 提取连线信息，strip 掉 -src/-dst 后缀
      function stripSuffix(ip) {
        return ip.replace(/(-src|-dst)$/,'')
      }
      const linkInfo = {
        source: stripSuffix(linkData.source),
        target: stripSuffix(linkData.target),
        value: linkData.value
      }
      
      // 发送连线点击事件到父组件
      this.$emit('link-click', linkInfo)
    },

    // 显示主机信息抽屉
    showHostInfo(hostIp, hostname = '') {
      if (!hostIp || hostIp === '-') {
        this.$message.warning('无效的IP地址')
        return
      }

      this.selectedHostIp = hostIp
      this.drawerTitle = hostname ? `主机信息 - ${hostname} (${hostIp})` : `主机信息 - ${hostIp}`
      this.showHostInfoDrawer = true
      
      console.log('显示主机信息:', hostIp, hostname)
    },

    // 处理抽屉关闭
    handleDrawerClose(done) {
      this.selectedHostIp = ''
      this.drawerTitle = '主机基本信息'
      done()
    },

    // 处理主机信息加载完成
    handleHostInfoLoaded(info) {
      console.log('主机信息加载完成:', info)
      // 可以在这里更新抽屉标题，显示更详细的信息
      if (info && info.hostname) {
        this.drawerTitle = `主机信息 - ${info.hostname} (${this.selectedHostIp})`
      }
    },

    inboundSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 入站流量表格：目标IP列（第2列，索引为2）合并相同IP的单元格
      if (columnIndex === 2) { // 目标IP列
        if (rowIndex === 0) {
          // 计算有多少个相同的目标IP
          const sameTargetCount = this.inboundTableData.filter(item => item.target === row.target).length
          return {
            rowspan: sameTargetCount,
            colspan: 1
          }
        } else {
          // 检查当前行的目标IP是否与前一行相同
          const prevRow = this.inboundTableData[rowIndex - 1]
          if (prevRow && prevRow.target === row.target) {
            return {
              rowspan: 0,
              colspan: 0
            }
          } else {
            // 计算剩余相同目标IP的数量
            const remainingSameCount = this.inboundTableData.slice(rowIndex).filter(item => item.target === row.target).length
            return {
              rowspan: remainingSameCount,
              colspan: 1
            }
          }
        }
      }
      return {
        rowspan: 1,
        colspan: 1
      }
    },

    outboundSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 出站流量表格：来源IP列（第0列，索引为0）合并相同IP的单元格
      if (columnIndex === 0) { // 来源IP列
        if (rowIndex === 0) {
          // 计算有多少个相同的来源IP
          const sameSourceCount = this.outboundTableData.filter(item => item.source === row.source).length
          return {
            rowspan: sameSourceCount,
            colspan: 1
          }
        } else {
          // 检查当前行的来源IP是否与前一行相同
          const prevRow = this.outboundTableData[rowIndex - 1]
          if (prevRow && prevRow.source === row.source) {
            return {
              rowspan: 0,
              colspan: 0
            }
          } else {
            // 计算剩余相同来源IP的数量
            const remainingSameCount = this.outboundTableData.slice(rowIndex).filter(item => item.source === row.source).length
            return {
              rowspan: remainingSameCount,
              colspan: 1
            }
          }
        }
      }
      return {
        rowspan: 1,
        colspan: 1
      }
    }
  }
}
</script>

<style scoped>
@import '../../../assets/styles/analysis-result-common.css';

.chart-container {
  width: 100%;
  height: 100%;
}

.sankey-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

/* 桑基图容器样式 */
.sankey-container [ref="sankeyChart"] {
  cursor: pointer;
  width: 100% !important;
  min-height: 500px;
  padding-right: 20px; /* 增加右侧内边距 */
  box-sizing: border-box;
}

.traffic-table-container {
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-header {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e6e6e6;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.ip-cell {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.ip-cell i {
  margin-right: 8px;
  font-size: 14px;
}

.request-count {
  font-weight: 600;
  color: #409eff;
}

.percentage {
  font-weight: 600;
  color: #67c23a;
}

.traffic-summary {
  margin-top: 20px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  margin: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  min-width: 120px;
}

.summary-label {
  font-weight: 600;
  color: #606266;
  font-size: 12px;
  margin-bottom: 5px;
}

.summary-value {
  font-weight: 700;
  font-size: 14px;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .traffic-table-container {
    padding: 15px;
    margin-top: 15px;
  }
  
  .traffic-summary {
    flex-direction: column;
  }
  
  .summary-item {
    margin: 5px 0;
  }
}

/* 表格样式优化 */
.el-table {
  font-size: 12px;
}

.el-table th {
  background-color: #fafafa;
  color: #303133;
  font-weight: 600;
}

.el-table td {
  padding: 8px 0;
}

.el-table .el-tag {
  font-weight: 600;
}

/* 固定高度表格的滚动条样式 */
.el-table__body-wrapper {
  overflow-y: auto;
}

.el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 通用滚动条样式 */
.traffic-table-container ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.traffic-table-container ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.traffic-table-container ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.traffic-table-container ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 双表格布局样式 */
.dual-table-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.table-section {
  flex: 1;
  min-width: 0; /* 防止flex项目溢出 */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 6px;
}

/* 入站流量表格头部样式 */
.table-section:first-child .section-header {
  border-left: 4px solid #67c23a;
}

/* 出站流量表格头部样式 */
.table-section:last-child .section-header {
  border-left: 4px solid #409eff;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.section-count {
  font-size: 12px;
  color: #909399;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px 8px;
  border-radius: 12px;
}

/* 目标IP单元格特殊样式 */
.target-ip {
  background-color: rgba(64, 169, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(64, 169, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dual-table-container {
    flex-direction: column;
    gap: 15px;
  }
  
  .table-section {
    flex: none;
  }
}

/* 自循环过滤提示样式 */
.self-loop-notice {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #0050b3;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.self-loop-notice i {
  margin-right: 6px;
  font-size: 14px;
}

/* 抽屉内容样式 */
.drawer-content {
  padding: 0 20px;
  font-size: 12px;
}

/* 抽屉内主机信息组件的样式调整 */
.drawer-content ::v-deep(.host-basic-info-card) {
  margin-top: 0;
  box-shadow: none;
  border: 1px solid #e4e7ed;
}

.drawer-content ::v-deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

/* 抽屉自定义样式 */
::v-deep(.el-drawer__header) {
  padding: 20px;
  margin-bottom: 0;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}

::v-deep(.el-drawer__title) {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

::v-deep(.el-drawer__body) {
  padding: 0;
}
</style> 