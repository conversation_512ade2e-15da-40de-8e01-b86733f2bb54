<template>
  <div class="host-inventory-stat-info">
    
    <el-card class="stat-card">
      <div slot="header" class="clearfix">
        <span>网络连接统计信息</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text"
          :loading="loading"
          @click="fetchPortStatistics"
        >
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      
      <el-row :gutter="20">
        <!-- 端口状态统计 -->
        <el-col :span="10">
          <div class="chart-container">
            <div class="chart-title">网络连接状态分布</div>
            <div v-loading="loading" class="chart-wrapper">
              <div v-if="!loading && portStats.length === 0" class="no-data-tip">
                <i class="el-icon-pie-chart"></i>
                <p>暂无数据</p>
              </div>
              <div v-else ref="portStatusChart" class="chart"></div>
            </div>
          </div>
        </el-col>
        <!-- 本地监听端口表格 -->
        
        <!-- 网络连接统计表格 -->
        <el-col :span="14">
          <div class="chart-container">
            <div class="chart-title">网络连接统计</div>
            <div v-loading="loading" class="chart-wrapper">
              <div v-if="!loading && networkStats.length === 0" class="no-data-tip">
                <i class="el-icon-connection"></i>
                <p>暂无连接数据</p>
              </div>
              <el-table 
                v-else 
                :data="networkStats" 
                style="width: 100%" 
                max-height="300"
                size="small"
              >
                <el-table-column prop="processName" label="本地进程名称" width="120" />
                <el-table-column prop="remoteIp" label="远程IP" width="130" />
                <el-table-column prop="hostname" label="远程主机名" min-width="120" />
                <el-table-column prop="connectionCount" label="连接数" width="80" />
              </el-table>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { getPortStatistics } from '@/api/agent/host';

// 状态类型映射
const STATUS_TYPES = {
  'LISTEN': 'primary',
  'ESTABLISHED': 'success',
  'CLOSE_WAIT': 'warning',
  'TIME_WAIT': 'info',
  'CLOSED': 'danger'
};

export default {
  name: 'HostInventoryStatInfo',
  props: {
    hostIp: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      portStats: [],
      networkStats: [],
      portChart: null,
      chartResizeHandler: null
    };
  },
  watch: {
    hostIp: {
      immediate: true,
      handler() {
        this.fetchPortStatistics();
      }
    }
  },
  mounted() {
    this.chartResizeHandler = () => {
      this.portChart?.resize();
    };
    window.addEventListener('resize', this.chartResizeHandler);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.chartResizeHandler);
    if (this.portChart) {
      this.portChart.dispose();
      this.portChart = null;
    }
  },
  methods: {
    async fetchPortStatistics() {
      if (!this.hostIp) return;
      
      this.loading = true;
      try {
        const { data } = await getPortStatistics(this.hostIp);
        // 处理端口状态数据
        this.portStats = Object.entries(data.portStats || {}).map(([name, value]) => ({
          name,
          value
        }));
        // 处理网络连接统计数据
        this.networkStats = data.networkStats || [];
        
        this.$nextTick(() => {
          this.initPortStatusChart();
        });
      } catch (error) {
        console.error('获取端口统计信息失败:', error);
        this.$message.error('获取端口统计信息失败');
      } finally {
        this.loading = false;
      }
    },
    
    initPortStatusChart() {
      if (this.portStats.length === 0) return;
      
      const chartDom = this.$refs.portStatusChart;
      if (!chartDom) return;
      
      if (this.portChart) {
        this.portChart.dispose();
      }
      
      this.portChart = echarts.init(chartDom);
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: this.portStats.map(item => item.name)
        },
        series: [
          {
            name: '端口状态',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold',
                formatter: '{b}\n{c} ({d}%)'
              }
            },
            labelLine: {
              show: false
            },
            data: this.portStats
          }
        ]
      };
      
      this.portChart.setOption(option);
    },
    

    
    getStatusType(status) {
      return STATUS_TYPES[status] || 'info';
    },
    
    getStatusColor(status) {
      const colorMap = {
        'ESTABLISHED': '#67C23A', // 绿色
        'LISTEN': '#409EFF',      // 蓝色
        'CLOSE_WAIT': '#E6A23C',  // 黄色
        'TIME_WAIT': '#909399',   // 灰色
        'CLOSED': '#F56C6C'       // 红色
      };
      return colorMap[status] || '#909399';
    }
  }
};
</script>

<style scoped>
.host-inventory-stat-info {
  padding-top: 15px;
}

.stat-card {
  margin-bottom: 20px;
  width: 100%;
}

.chart-container {
  background: #fff;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  height: 350px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #303133;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.chart-wrapper {
  width: 100%;
  height: calc(100% - 30px);
  position: relative;
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

.no-data-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.no-data-tip i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #DCDFE6;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .el-col-12 {
    width: 100%;
  }
  
  .chart-container {
    height: 300px;
  }
}
</style>
