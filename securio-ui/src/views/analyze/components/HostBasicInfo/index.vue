<template>
  <el-card class="host-basic-info-card" >
    <div slot="header" class="clearfix">
      <span class="card-title">主机基本信息</span>
      <span v-if="hostBasicInfo" class="data-source-info">数据源: {{ hostBasicInfo.dataSource || '未知' }}</span>
      <span v-else-if="ipLocationInfo && ipLocationInfo.apiSource" class="data-source-info">数据源: {{ getLocationApiName(ipLocationInfo.apiSource) }}</span>
      <el-button 
        v-if="hostBasicInfo && hostBasicInfo.rawData && Object.keys(hostBasicInfo.rawData).length > 0"
        type="text" 
        size="mini" 
        @click="showRawDataDialog = true"
        style="margin-left: 10px;">
        查看原始数据
      </el-button>
    </div>
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <i class="el-icon-loading"></i>
      <span>正在获取主机信息...</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <i class="el-icon-warning"></i>
      <span>{{ error }}</span>
      <el-button type="text" @click="loadHostBasicInfo">重试</el-button>
    </div>
    
    <!-- 主机信息展示 -->
    <div v-else-if="ipLocationInfo || hostBasicInfo" class="main-content">
      <!-- IP归属信息显示 -->
      <div v-if="ipLocationInfo" class="ip-location-section">
        <div v-if="ipLocationInfo.apiSource!='internal'">
          <h4 style="margin: 0 0 15px 0; font-size: 14px; font-weight: bold; color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 5px;">
            <i class="el-icon-location" style="margin-right: 5px;"></i>IP归属信息
          </h4>
          <el-descriptions :column="3" border size="mini">
            <el-descriptions-item label="IP地址">{{ ipLocationInfo.ip || '-' }}</el-descriptions-item>
            <el-descriptions-item label="国家/地区">
              <span v-if="ipLocationInfo.country">
                <img v-if="ipLocationInfo.countryFlag" :src="ipLocationInfo.countryFlag" style="width: 16px; height: 12px; margin-right: 5px;" />
                {{ ipLocationInfo.country }}
              </span>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="国家代码">{{ ipLocationInfo.countryCode || '-' }}</el-descriptions-item>
            <el-descriptions-item label="省份/州">{{ ipLocationInfo.regionName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="城市">{{ ipLocationInfo.city || '-' }}</el-descriptions-item>
            <el-descriptions-item label="邮政编码">{{ ipLocationInfo.zipcode || '-' }}</el-descriptions-item>
            <el-descriptions-item label="时区">{{ ipLocationInfo.timezone || '-' }}</el-descriptions-item>
            <el-descriptions-item label="ISP运营商">{{ ipLocationInfo.isp || '-' }}</el-descriptions-item>
            <el-descriptions-item label="组织机构">{{ ipLocationInfo.organization || '-' }}</el-descriptions-item>
            <el-descriptions-item label="经纬度" :span="2">
              <span v-if="ipLocationInfo.latitude && ipLocationInfo.longitude">
                {{ ipLocationInfo.latitude }}, {{ ipLocationInfo.longitude }}
              </span>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="数据来源">
              <el-tag :type="getLocationApiType(ipLocationInfo.apiSource)" size="mini">
                {{ getLocationApiName(ipLocationInfo.apiSource) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 地图显示区域 -->
          <div v-if="hasValidCoordinates" class="map-section">
            <h4 style="margin: 20px 0 15px 0; font-size: 14px; font-weight: bold; color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 5px;">
              <i class="el-icon-location-outline" style="margin-right: 5px;"></i>地理位置
            </h4>
            <div class="map-container">
              <a-map-viewer
                :center="mapCenter"
                :zoom="mapZoom"
                height="350px"
                :show-marker="true"
                :marker-title="mapMarkerTitle"
                :marker-content="mapMarkerContent"
                :view-mode3-d="false"
                map-style="normal"
                @map-loaded="handleMapLoaded"
                @marker-click="handleMarkerClick"
              />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 只有内网IP或有hostBasicInfo时才显示主机基本信息 -->
      <div v-if="hostBasicInfo">
      <!-- CMDB数据源渲染 -->
      <div v-if="hostBasicInfo.dataSource === 'cmdb' && hostBasicInfo.rawData">
          <h4 style="margin: 20px 0 15px 0; font-size: 14px; font-weight: bold; color: #67C23A; border-bottom: 2px solid #67C23A; padding-bottom: 5px;">
            <i class="el-icon-monitor" style="margin-right: 5px;"></i>主机基本信息
          </h4>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="主机IP">{{ getCmdbValue('serverInfo.private_ip') }}</el-descriptions-item>
          <el-descriptions-item label="主机名">{{ getCmdbValue('serverInfo.hostname') }}</el-descriptions-item>
          <el-descriptions-item label="在线状态">
            <el-tag :type="getStatusType(getCmdbValue('serverInfo.host_online_status'))">
              {{ getCmdbValue('serverInfo.host_online_status') }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作系统">{{ getCmdbValue('serverInfo.sw_type') }}</el-descriptions-item>
          <el-descriptions-item label="系统版本">{{ getCmdbValue('hostDetailInfo.hostBaseInfo', 'platformVersion') }}</el-descriptions-item>
          <el-descriptions-item label="CPU架构">{{ getCmdbValue('serverInfo.cpu_arch') }}</el-descriptions-item>
          <el-descriptions-item label="CPU核数">{{ getCmdbValue('serverInfo.cpu_count') }}</el-descriptions-item>
          <el-descriptions-item label="CPU型号">{{ getCmdbValue('serverInfo.cpu_name') }}</el-descriptions-item>
          <el-descriptions-item label="内存大小">{{ formatMemorySize(getCmdbValue('serverInfo.ram_size')) }}</el-descriptions-item>
          <el-descriptions-item label="主机类型">{{ getCmdbValue('serverInfo.host_type') }}</el-descriptions-item>
          <el-descriptions-item label="环境">{{ getCmdbValue('serverInfo.env_name') }}</el-descriptions-item>
          <el-descriptions-item label="机房位置">{{ getCmdbValue('serverInfo.rack_location') }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ getCmdbValue('serverInfo.contact_person') }}</el-descriptions-item>
          <el-descriptions-item label="业务组" :span="2">{{ getCmdbValue('serverInfo.n9e_busi_group') }}</el-descriptions-item>
          <el-descriptions-item label="启动时间" :span="3">{{ formatBootTime(getCmdbValue('hostDetailInfo.bootTime')) }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 应用部署信息 -->
        <div v-if="getCmdbValue('serverRelations.appHost') && getCmdbValue('serverRelations.appHost').length > 0" class="app-deploy-section">
          <h4 style="margin: 20px 0 10px 0; font-size: 14px; font-weight: bold;">应用部署信息</h4>
          <el-table :data="getCmdbValue('serverRelations.appHost')" border size="mini">
            <el-table-column prop="app_name" label="应用名称" width="150"></el-table-column>
            <el-table-column prop="port" label="端口" width="80"></el-table-column>
            <el-table-column prop="deploy_path" label="部署路径" min-width="200"></el-table-column>
            <el-table-column prop="env_name" label="环境" width="80"></el-table-column>
            <el-table-column prop="app_source" label="来源" width="80"></el-table-column>
          </el-table>
        </div>
      </div>
      
      <!-- Agent数据源渲染 -->
      <div v-else-if="hostBasicInfo.dataSource === 'agent'">
          <h4 style="margin: 20px 0 15px 0; font-size: 14px; font-weight: bold; color: #67C23A; border-bottom: 2px solid #67C23A; padding-bottom: 5px;">
            <i class="el-icon-monitor" style="margin-right: 5px;"></i>主机基本信息
          </h4>
        <!-- 如果有agentInfo，使用agentInfo渲染 -->
        <div v-if="hostBasicInfo.agentInfo">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="主机IP">{{ hostBasicInfo.agentInfo.ip || '-' }}</el-descriptions-item>
            <el-descriptions-item label="主机名">{{ getAgentValue('hostBaseInfo', 'hostname') }}</el-descriptions-item>
            <el-descriptions-item label="在线状态">
              <el-tag type="success">在线</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="操作系统">{{ getAgentValue('hostBaseInfo', 'platform') }}</el-descriptions-item>
            <el-descriptions-item label="系统版本">{{ getAgentValue('hostBaseInfo', 'platformVersion') }}</el-descriptions-item>
            <el-descriptions-item label="内核版本">{{ getAgentValue('hostBaseInfo', 'kernelVersion') }}</el-descriptions-item>
            <el-descriptions-item label="系统架构">{{ getAgentValue('hostBaseInfo', 'kernelArch') }}</el-descriptions-item>
            <el-descriptions-item label="虚拟化角色">{{ getAgentValue('hostBaseInfo', 'virtualizationRole') }}</el-descriptions-item>
            <el-descriptions-item label="进程数量">{{ hostBasicInfo.agentInfo.processCount || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 如果没有agentInfo但有其他字段，使用兜底渲染 -->
        <div v-else>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="主机IP">{{ hostBasicInfo.hostIp || '-' }}</el-descriptions-item>
            <el-descriptions-item label="主机名">{{ hostBasicInfo.hostname || '-' }}</el-descriptions-item>
            <el-descriptions-item label="在线状态">
              <el-tag :type="getStatusType(hostBasicInfo.onlineStatus)">
                {{ hostBasicInfo.onlineStatus || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="操作系统">{{ hostBasicInfo.osName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="系统版本">{{ hostBasicInfo.osVersion || '-' }}</el-descriptions-item>
            <el-descriptions-item label="CPU信息">{{ hostBasicInfo.cpuInfo || '-' }}</el-descriptions-item>
            <el-descriptions-item label="内存信息">{{ hostBasicInfo.memoryInfo || '-' }}</el-descriptions-item>
            <el-descriptions-item label="位置信息">{{ hostBasicInfo.location || '-' }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ hostBasicInfo.contactPerson || '-' }}</el-descriptions-item>
            <el-descriptions-item label="业务组" :span="3">{{ hostBasicInfo.businessGroup || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <!-- 兜底渲染（使用转换后的字段） -->
      <div v-else>
          <h4 >
            
          </h4>
       
        </div>
      </div>
    </div>
    
    <!-- 暂无数据 -->
    <div v-if="!loading && !error && !ipLocationInfo && !hostBasicInfo" class="no-data-container">
      <i class="el-icon-document"></i>
      <span>暂无主机信息</span>
    </div>
    
    <!-- 原始数据查看对话框 -->
    <el-dialog title="主机原始数据" :visible.sync="showRawDataDialog" width="80%" top="5vh">
      <div v-if="hostBasicInfo && hostBasicInfo.rawData">
        <el-tabs v-model="activeRawDataTab" type="card">
          <el-tab-pane 
            v-for="(data, key) in hostBasicInfo.rawData" 
            :key="key"
            :label="getRawDataTabLabel(key)" 
            :name="key">
            <div class="raw-data-container">
              <pre class="raw-data-content">{{ JSON.stringify(data, null, 2) }}</pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-else class="no-raw-data">
        <p>暂无原始数据</p>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showRawDataDialog = false">关闭</el-button>
        <el-button type="primary" @click="copyRawData">复制数据</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import { getHostBasicInfo } from '@/api/analyze/hostAnalysis'
import moment from 'moment'
import AMapViewer from '@/components/AMapViewer/index.vue'

export default {
  name: 'HostBasicInfo',
  components: {
    AMapViewer
  },
  props: {
    hostIp: {
      type: String,
      required: false
    },
    analysisData: {
      type: Object,
      default: null
    },
    data: {
      type: Object,
      default: null
    },
    analyzerType: {
      type: String,
      default: 'host_basic_info'
    }
  },
  data() {
    return {
      loading: false,
      error: null,
      hostBasicInfoData: null,  // 改名避免与computed属性冲突
      showRawDataDialog: false,
      activeRawDataTab: 'serverInfo'
    }
  },
  computed: {
    // 获取实际的主机基本信息数据
    hostBasicInfo() {
      // 优先从新的data prop（tab模式）中获取
      if (this.data && this.data.hostBasicInfo) {
        return this.data.hostBasicInfo;
      }
      
      // 从analyzerResults数据结构中获取主机基本信息
      if (this.analysisData && this.analysisData.analyzerResults && this.analysisData.analyzerResults.host_basic_info) {
        const hostBasicResult = this.analysisData.analyzerResults.host_basic_info;
        if (hostBasicResult.success && hostBasicResult.data) {
          // 返回增强结果中的hostBasicInfo部分
          return hostBasicResult.data.hostBasicInfo;
        }
      }
      
      // 兼容旧格式：从根级别获取
      if (this.analysisData && this.analysisData.hostBasicInfo) {
        return this.analysisData.hostBasicInfo;
      }
      
      // 处理独立API调用的情况 - 直接访问data中的hostBasicInfo
      if (this.$data.hostBasicInfoData) {
        // 如果hostBasicInfo是一个包含hostBasicInfo字段的对象（独立API调用）
        if (this.$data.hostBasicInfoData.hostBasicInfo) {
          return this.$data.hostBasicInfoData.hostBasicInfo;
        }
        // 如果hostBasicInfo直接就是主机基本信息对象
        return this.$data.hostBasicInfoData;
      }
      
      return null;
    },
    
    // 获取IP归属信息
    ipLocationInfo() {
      // 优先从新的data prop（tab模式）中获取
      if (this.data && this.data.ipLocationInfo) {
        return this.data.ipLocationInfo;
      }
      
      // 从analyzerResults数据结构中获取IP归属信息
      if (this.analysisData && this.analysisData.analyzerResults && this.analysisData.analyzerResults.host_basic_info) {
        const hostBasicResult = this.analysisData.analyzerResults.host_basic_info;
        if (hostBasicResult.success && hostBasicResult.data) {
          // 返回增强结果中的ipLocationInfo部分
          return hostBasicResult.data.ipLocationInfo;
        }
      }
      
      // 处理独立API调用的情况 - 直接访问data中的hostBasicInfo
      if (this.$data.hostBasicInfoData && this.$data.hostBasicInfoData.ipLocationInfo) {
        return this.$data.hostBasicInfoData.ipLocationInfo;
      }
      
      return null;
    },
    
    // 获取当前使用的hostIp
    currentHostIp() {
      // 优先使用传递的hostIp prop
      if (this.hostIp) {
        return this.hostIp;
      }
      
      // 从data prop中获取（可能在data.hostIp或者data.ipLocationInfo.ip）
      if (this.data) {
        if (this.data.hostIp) {
          return this.data.hostIp;
        }
        if (this.data.ipLocationInfo && this.data.ipLocationInfo.ip) {
          return this.data.ipLocationInfo.ip;
        }
        if (this.data.hostBasicInfo && this.data.hostBasicInfo.hostIp) {
          return this.data.hostBasicInfo.hostIp;
        }
      }
      
      return null;
    },
    
    // 地图相关计算属性
    mapCenter() {
      if (this.ipLocationInfo && this.ipLocationInfo.latitude && this.ipLocationInfo.longitude) {
        // 高德地图center格式：[经度, 纬度]
        return [this.ipLocationInfo.longitude, this.ipLocationInfo.latitude];
      }
      return null;
    },
    mapZoom() {
      return 12;
    },
    mapMarkerTitle() {
      return this.ipLocationInfo ? this.ipLocationInfo.city || this.ipLocationInfo.regionName || this.ipLocationInfo.country : '未知位置';
    },
    mapMarkerContent() {
      return this.ipLocationInfo ? `IP: ${this.ipLocationInfo.ip}, 城市: ${this.ipLocationInfo.city}, 国家: ${this.ipLocationInfo.country}` : '未知位置';
    },
    hasValidCoordinates() {
      return this.ipLocationInfo && this.ipLocationInfo.latitude && this.ipLocationInfo.longitude;
    },
    
    // 判断是否为内网IP
    isInternalIp() {
      // 优先从分析结果中获取IP类型
      if (this.data && this.data.ipType) {
        // server和office_client都是内网IP
        return this.data.ipType === 'server' || this.data.ipType === 'office_client';
      }
      
      // 从analysisData中获取IP类型
      if (this.analysisData && this.analysisData.analyzerResults && this.analysisData.analyzerResults.host_basic_info) {
        const hostBasicResult = this.analysisData.analyzerResults.host_basic_info;
        if (hostBasicResult.success && hostBasicResult.data && hostBasicResult.data.ipType) {
          const ipType = hostBasicResult.data.ipType;
          // server和office_client都是内网IP
          return ipType === 'server' || ipType === 'office_client';
        }
      }
      
      // 兜底逻辑：根据IP地址判断
      if (!this.currentHostIp) return false;
      
      // 10.x.x.x 网段
      if (this.currentHostIp.startsWith('10.')) {
        return true;
      }
      
      // 172.16.x.x - 172.31.x.x 网段
      if (this.currentHostIp.startsWith('172.')) {
        const segments = this.currentHostIp.split('.');
        if (segments.length >= 2) {
          const second = parseInt(segments[1]);
          if (second >= 16 && second <= 31) {
            return true;
          }
        }
      }
      
      // 192.168.x.x 网段
      if (this.currentHostIp.startsWith('192.168.')) {
        return true;
      }
      
      // 127.x.x.x 本地回环
      if (this.currentHostIp.startsWith('127.')) {
        return true;
      }
      
      return false;
    },
  },
  watch: {
    hostIp: {
      handler(newVal) {
        if (newVal) {
          this.initializeComponent()
        }
      },
      immediate: true
    },
    data: {
      handler(newVal) {
        console.log('HostBasicInfo 组件收到 data 变化:', newVal)
        if (newVal) {
          // 如果有data数据，不需要调用API
          this.loading = false
          this.error = null
          console.log('使用data中的主机信息:', newVal)
          console.log('提取的IP归属信息:', this.ipLocationInfo)
          console.log('提取的主机基本信息:', this.hostBasicInfo)
          console.log('当前hostIp:', this.currentHostIp)
          console.log('是否为内网IP:', this.isInternalIp)
        } else if (this.currentHostIp) {
          // 如果没有data但有IP，则调用API
          this.loadHostBasicInfo()
        }
      },
      immediate: true,
      deep: true
    },
    analysisData: {
      handler(newVal) {
        console.log('HostBasicInfo 组件收到 analysisData 变化:', newVal)
        if (newVal) {
          // 如果有分析数据，不需要调用API
          this.loading = false
          this.error = null
          console.log('使用分析数据中的主机信息:', newVal)
          console.log('提取的IP归属信息:', this.ipLocationInfo)
          console.log('提取的主机基本信息:', this.hostBasicInfo)
          console.log('是否为内网IP:', this.isInternalIp)
        } else if (this.currentHostIp) {
          // 如果没有分析数据但有IP，则调用API
          this.loadHostBasicInfo()
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    initializeComponent() {
      if (this.analysisData || this.data) {
        // 如果有分析数据或data，直接使用
        this.loading = false
        this.error = null
        console.log('使用已有数据中的主机信息:', this.analysisData || this.data)
      } else if (this.currentHostIp) {
        // 如果没有数据，则调用API
        this.loadHostBasicInfo()
      }
    },
    async loadHostBasicInfo() {
      if (!this.currentHostIp) {
        return
      }
      
      this.loading = true
      this.error = null
      
      try {
        console.log('开始获取主机基本信息，主机IP:', this.currentHostIp)
        const response = await getHostBasicInfo(this.currentHostIp)
        
        if (response.code === 200) {
          // 直接存储整个data对象，包含hostBasicInfo和ipLocationInfo
          this.hostBasicInfoData = response.data
          console.log('主机基本信息API返回:', response.data)
          console.log('解析后的实际主机基本信息:', this.hostBasicInfo)
          console.log('解析后的IP归属信息:', this.ipLocationInfo)
          
          // 发射事件给父组件，通知数据加载完成，传递实际的主机基本信息
          this.$emit('info-loaded', this.hostBasicInfo)
        } else {
          this.error = response.msg || '获取主机信息失败'
          console.error('获取主机基本信息失败:', response)
        }
      } catch (error) {
        console.error('获取主机基本信息异常:', error)
        this.error = '获取主机信息失败: ' + (error.message || '未知错误')
      } finally {
        this.loading = false
      }
    },
    
    getStatusType(status) {
      const statusMap = {
        '在线': 'success',
        '离线': 'danger',
        '异常': 'warning',
        '未知': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    getRawDataTabLabel(key) {
      const labelMap = {
        'serverInfo': '服务器基础信息',
        'hostDetailInfo': '主机详细信息',
        'serverRelations': '服务器关联信息'
      }
      return labelMap[key] || key
    },
    
    copyRawData() {
      if (this.hostBasicInfo && this.hostBasicInfo.rawData) {
        const rawDataText = JSON.stringify(this.hostBasicInfo.rawData, null, 2)
        
        // 创建临时文本区域
        const textArea = document.createElement('textarea')
        textArea.value = rawDataText
        document.body.appendChild(textArea)
        textArea.select()
        
        try {
          document.execCommand('copy')
          this.$message.success('原始数据已复制到剪贴板')
        } catch (err) {
          this.$message.error('复制失败，请手动复制')
        }
        
        document.body.removeChild(textArea)
      }
    },
    
    // CMDB数据获取方法
    getCmdbValue(path, subKey = null) {
      if (!this.hostBasicInfo || !this.hostBasicInfo.rawData) {
        return '-'
      }
      
      const rawData = this.hostBasicInfo.rawData
      const pathParts = path.split('.')
      let value = rawData
      
      // 遍历路径获取值
      for (const part of pathParts) {
        if (value && typeof value === 'object' && value.hasOwnProperty(part)) {
          value = value[part]
        } else {
          return '-'
        }
      }
      
      // 如果需要进一步解析JSON字符串
      if (subKey && typeof value === 'string') {
        try {
          const parsedValue = JSON.parse(value)
          return parsedValue[subKey] || '-'
        } catch (error) {
          return '-'
        }
      }
      
      return value || '-'
    },
    
    // Agent数据获取方法
    getAgentValue(path, subKey = null) {
      if (!this.hostBasicInfo || !this.hostBasicInfo.agentInfo) {
        return '-'
      }
      
      const agentInfo = this.hostBasicInfo.agentInfo
      
      // 如果是hostBaseInfo，需要解析JSON字符串
      if (path === 'hostBaseInfo' && agentInfo.hostBaseInfo) {
        try {
          const hostBaseInfo = typeof agentInfo.hostBaseInfo === 'string' 
            ? JSON.parse(agentInfo.hostBaseInfo) 
            : agentInfo.hostBaseInfo
          const result = hostBaseInfo[subKey] || '-'
          return result
        } catch (error) {
          return '-'
        }
      }
      
      // 直接获取属性值
      const result = agentInfo[path] || '-'
      return result
    },
    
    // 格式化内存大小
    formatMemorySize(bytes) {
      if (!bytes || bytes === '-') return '-'
      
      const bytesNum = parseInt(bytes)
      if (isNaN(bytesNum)) return '-'
      
      const gb = bytesNum / (1024 * 1024 * 1024)
      return `${gb.toFixed(2)} GB`
    },
    
    // 格式化启动时间
    formatBootTime(bootTime) {
      if (!bootTime || bootTime === '-') return '-'
      
      // 如果是时间戳格式，转换为日期字符串
      if (typeof bootTime === 'number') {
        return moment.unix(bootTime).format('YYYY-MM-DD HH:mm:ss')
      }
      
      // 如果已经是日期字符串，直接返回
      return bootTime
    },
    
    getLocationApiType(apiSource) {
      const apiTypeMap = {
        'ipgeolocation': 'success',
        'ip-api': 'info', 
        'freeipapi': 'warning',
        'internal': 'primary'
      }
      return apiTypeMap[apiSource] || 'info'
    },
    
    getLocationApiName(apiSource) {
      const apiNameMap = {
        'ipgeolocation': 'IP Geolocation.io',
        'ip-api': 'IP-API.com',
        'freeipapi': 'FreeIPAPI.com',
        'internal': '内网IP'
      }
      return apiNameMap[apiSource] || '未知'
    },
    
    // 地图相关方法
    handleMapLoaded(map) {
      console.log('地图加载完成:', map)
      // 可以在这里添加地图加载完成后的处理逻辑
    },
    
    handleMarkerClick(event) {
      console.log('地图标记被点击:', event)
      // 可以在这里添加点击地图标记后的处理逻辑
      this.$message.success(`点击了${this.ipLocationInfo.ip}的位置标记`)
    },
  }
}
</script>

<style scoped>
.host-basic-info-card {
  font-size: 12px;
}

.card-title {
  font-size: 14px;
  font-weight: bold;
}

.data-source-info {
  float: right;
  color: #67C23A;
  font-size: 12px;
  margin-left: 15px;
  background-color: #F0F9FF;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #67C23A;
}

.loading-container,
.error-container,
.no-data-container {
  text-align: center;
  padding: 40px;
  color: #909399;
  font-size: 14px;
}

.loading-container i,
.error-container i,
.no-data-container i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

.error-container {
  color: #F56C6C;
}

.error-container .el-button {
  margin-left: 10px;
}

/* 应用部署信息样式 */
.app-deploy-section {
  margin-top: 20px;
  font-size: 12px;
}

.app-deploy-section h4 {
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

.app-deploy-section >>> .el-table th {
  background-color: #f5f7fa !important;
  font-weight: bold !important;
}

/* 原始数据显示样式 */
.raw-data-container {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.raw-data-content {
  margin: 0;
  padding: 15px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #2c3e50;
  background-color: #fafafa;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.no-raw-data {
  text-align: center;
  padding: 40px;
  color: #909399;
  font-size: 14px;
}

/* IP归属信息样式 */
.ip-location-section {
  margin-top: 20px;
  font-size: 12px;
}

.ip-location-section h4 {
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

.ip-location-section >>> .el-descriptions th {
  background-color: #f5f7fa !important;
  font-weight: bold !important;
}

/* 地图区域样式 */
.map-section {
  margin-top: 20px;
  font-size: 12px;
}

.map-section h4 {
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

.map-container {
  margin-top: 15px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style> 