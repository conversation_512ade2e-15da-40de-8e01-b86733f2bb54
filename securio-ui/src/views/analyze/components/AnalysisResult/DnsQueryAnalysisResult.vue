<template>
  <div class="dns-query-analysis-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>DNS查询行为分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标IP：</strong>{{ data.targetIp }}</span>
        <span><strong>记录总数：</strong>{{ data.totalRecords }}</span>
        <span><strong>查询时间：</strong>{{ data.timeRange || '最近1天' }}</span>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div v-if="data.dnsAnalysis" class="statistics-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-connection"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.dnsAnalysis.totalQueries || 0 }}</div>
          <div class="stat-label">DNS查询总数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-link"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.dnsAnalysis.uniqueQueryDomains || 0 }}</div>
          <div class="stat-label">唯一域名</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-service"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.dnsAnalysis.uniqueDnsServers || 0 }}</div>
          <div class="stat-label">DNS服务器</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-collection-tag"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.dnsAnalysis.uniqueQueryTypes || 0 }}</div>
          <div class="stat-label">查询类型</div>
        </div>
      </div>
    </div>
    <!-- DNS查询流向桑基图 -->
    <div v-if="hasSankeyData" class="sankey-section" style="margin-top: 20px;">
      <div class="section-header">
        <h4><i class="el-icon-share"></i> DNS查询流向关系图</h4>
        <p class="section-description">展示该IP的DNS查询流向：查询来源 → 分析主机 → DNS服务器</p>
      </div>
      <IPFlowSankeyChart
        :data="sankeyData"
        height="500px"
        :show-own-drawer="true"
        @node-click="handleSankeyNodeClick"
        @link-click="handleSankeyLinkClick"
      />
    </div>
    
    <!-- 桑基图无数据提示 -->
    <div v-else-if="data.sankeyData && (!data.sankeyData.nodes || data.sankeyData.nodes.length === 0)" class="sankey-no-data" style="margin-top: 20px;">
      <div class="section-header">
        <h4><i class="el-icon-share"></i> DNS查询流向关系图</h4>
        <p class="section-description">展示该IP的DNS查询流向：查询来源 → 分析主机 → DNS服务器</p>
      </div>
      <div class="no-data-tip" style="height: 200px; display: flex; flex-direction: column; justify-content: center; align-items: center;">
        <i class="el-icon-share" style="font-size: 48px; color: #C0C4CC; margin-bottom: 15px;"></i>
        <p style="color: #909399;">暂无DNS查询流向数据</p>
      </div>
    </div>

    <!-- 图表展示区域 -->
    <el-row v-if="data.dnsAnalysis" :gutter="20" style="margin-top: 20px;">
      <!-- 查询域名Top10 -->
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-title">查询域名Top10</div>
          <div v-loading="loading" class="chart-wrapper">
            <div v-if="!hasQueryDomainsData" class="no-data-tip">
              <i class="el-icon-link"></i>
              <p>暂无域名查询数据</p>
            </div>
            <div v-else ref="queryDomainsChart" class="chart"></div>
          </div>
        </div>
      </el-col>
      
      <!-- 查询类型分布 -->
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-title">DNS查询类型分布</div>
          <div v-loading="loading" class="chart-wrapper">
            <div v-if="!hasQueryTypesData" class="no-data-tip">
              <i class="el-icon-pie-chart"></i>
              <p>暂无查询类型数据</p>
            </div>
            <div v-else ref="queryTypesChart" class="chart"></div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- DNS查询日志详情 -->
    <div class="log-details-section" style="margin-top: 20px;">
      <div class="custom-tabs-container">
        <div class="custom-tabs">
          <div class="custom-tab-item active">
            DNS查询日志
          </div>
        </div>
      </div>
      
      <div class="tab-content">
        <log-query-component
          ref="dnsLogQuery"
          :key="logQueryKey"
          :title="'DNS查询日志'"
          :subtitle="`查询分析IP ${data.targetIp} 的DNS查询记录`"
          :settings="dnsLogSettings"
          :defaultQuery="builtLogQLQuery"
          :externalTimeRange="timeConfig"
        />
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="!hasAnyData" class="no-data-placeholder">
      <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
      <p style="margin-top: 15px; color: #606266;">暂无DNS查询记录</p>
      <p style="color: #909399; font-size: 12px;">
        在指定时间范围内未发现该IP的DNS查询记录，可能该IP未进行DNS查询活动
      </p>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import LogQueryComponent from '@/components/log_query_component'
import IPFlowSankeyChart from '../IPFlowSankeyChart.vue'
import { convertTimeRange } from '@/utils/timeFormatter'
import { buildLogQL } from '@/api/log/logQuery'

export default {
  name: 'DnsQueryAnalysisResult',
  components: {
    LogQueryComponent,
    IPFlowSankeyChart
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'dns_query_analysis'
    }
  },
  data() {
    return {
      sankeyOptions: {
        nodeGap: 20,
        nodeWidth: 30,
        layoutIterations: 50
      },
      loading: false,
      showDetailDialog: false,
      selectedRecord: null,
      queryDomainsChart: null,
      queryTypesChart: null,
      dnsServersChart: null,
      chartResizeHandler: null,
      logQueryKey: 1,
      builtLogQLQuery: '',
      timeConfig: {},
      // DNS日志组件设置
      dnsLogSettings: {
        columns: [
          { prop: 'event_time', label: '查询时间', width: '180', sortable: true },
          { prop: 'query_domain', label: '查询域名', minWidth: '200', showOverflowTooltip: true },
          { prop: 'query_type', label: '查询类型', width: '100', tag: true },
          { prop: 'dst_ip', label: 'DNS服务器', width: '140' },
          { prop: 'src_port', label: '源端口', width: '100' },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  computed: {
    hasQueryDomainsData() {
      return this.data.dnsAnalysis && 
             this.data.dnsAnalysis.queryDomains && 
             this.data.dnsAnalysis.queryDomains.length > 0
    },
    hasQueryTypesData() {
      return this.data.dnsAnalysis && 
             this.data.dnsAnalysis.queryTypes && 
             this.data.dnsAnalysis.queryTypes.length > 0
    },
    hasDnsServersData() {
      return this.data.dnsAnalysis && 
             this.data.dnsAnalysis.dnsServers && 
             this.data.dnsAnalysis.dnsServers.length > 0
    },
    hasAnyData() {
      const hasChartData = this.hasQueryDomainsData || this.hasQueryTypesData || this.hasDnsServersData
      const hasSankey = this.hasSankeyData
      console.log('hasAnyData计算:', { hasChartData, hasSankey, result: hasChartData || hasSankey })
      return hasChartData || hasSankey
    },
    hasSankeyData() {
      const result = this.sankeyData && this.sankeyData.nodes && this.sankeyData.nodes.length > 0
      console.log('hasSankeyData计算:', { sankeyData: this.sankeyData, result })
      return result
    },
    sankeyData() {
      // 从data.sankeyData获取桑基图数据
      return this.data.sankeyData || null
    },
    // 从分析时间范围推导默认时间范围
    defaultTimeRange() {
      return convertTimeRange(this.data.timeRange, '30m')
    },
    // 构建基础查询条件：指定src_ip
    baseQueryCondition() {
      return {
        field: 'src_ip',
        operator: '=',
        value: this.data.targetIp
      }
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.initAllCharts()
          // 当数据变化时，重新构建LogQL查询
          this.buildLogQLQuery()
        })
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.chartResizeHandler = () => {
      this.queryDomainsChart?.resize()
      this.queryTypesChart?.resize()
      this.dnsServersChart?.resize()
    }
    window.addEventListener('resize', this.chartResizeHandler)
    
    // 调试桑基图数据
    console.log('DNS分析组件挂载，数据:', this.data)
    console.log('桑基图数据:', this.sankeyData)
    console.log('是否有桑基图数据:', this.hasSankeyData)
    console.log('是否有任何数据:', this.hasAnyData)
    
    this.$nextTick(() => {
      this.initAllCharts()
      // 初始化时构建LogQL查询
      this.buildLogQLQuery()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.chartResizeHandler)
    this.disposeAllCharts()
  },
  methods: {
    initAllCharts() {
      this.initQueryDomainsChart()
      this.initQueryTypesChart()
      this.initDnsServersChart()
    },
    
    disposeAllCharts() {
      if (this.queryDomainsChart) {
        this.queryDomainsChart.dispose()
        this.queryDomainsChart = null
      }
      if (this.queryTypesChart) {
        this.queryTypesChart.dispose()
        this.queryTypesChart = null
      }
      if (this.dnsServersChart) {
        this.dnsServersChart.dispose()
        this.dnsServersChart = null
      }
    },
    
    initQueryDomainsChart() {
      if (!this.hasQueryDomainsData) return
      
      const chartDom = this.$refs.queryDomainsChart
      if (!chartDom) return
      
      if (this.queryDomainsChart) {
        this.queryDomainsChart.dispose()
      }
      
      this.queryDomainsChart = echarts.init(chartDom)
      
      const queryDomains = this.data.dnsAnalysis.queryDomains.slice(0, 10)
      
      const option = {
        title: {
          text: '查询域名Top10',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}: ${params[0].value} 次`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        yAxis: {
          type: 'category',
          data: queryDomains.map(item => item.name),
          axisLabel: {
            fontSize: 11,
            formatter: function(value) {
              if (value.length > 20) {
                return value.substring(0, 17) + '...'
              }
              return value
            }
          }
        },
        series: [{
          name: '查询次数',
          type: 'bar',
          data: queryDomains.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }]
      }
      
      this.queryDomainsChart.setOption(option)
    },
    
    initQueryTypesChart() {
      if (!this.hasQueryTypesData) return
      
      const chartDom = this.$refs.queryTypesChart
      if (!chartDom) return
      
      if (this.queryTypesChart) {
        this.queryTypesChart.dispose()
      }
      
      this.queryTypesChart = echarts.init(chartDom)
      
      const queryTypes = this.data.dnsAnalysis.queryTypes
      
      const option = {
        title: {
          text: 'DNS查询类型分布',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: queryTypes.map(item => item.name)
        },
        series: [
          {
            name: '查询类型',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold',
                formatter: '{b}\n{c} ({d}%)'
              }
            },
            labelLine: {
              show: false
            },
            data: queryTypes
          }
        ]
      }
      
      this.queryTypesChart.setOption(option)
    },
    
    initDnsServersChart() {
      if (!this.hasDnsServersData) return
      
      const chartDom = this.$refs.dnsServersChart
      if (!chartDom) return
      
      if (this.dnsServersChart) {
        this.dnsServersChart.dispose()
      }
      
      this.dnsServersChart = echarts.init(chartDom)
      
      const dnsServers = this.data.dnsAnalysis.dnsServers
      
      const option = {
        title: {
          text: 'DNS服务器使用统计',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}: ${params[0].value} 次`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dnsServers.map(item => item.name),
          axisLabel: {
            fontSize: 11,
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        series: [{
          name: '查询次数',
          type: 'bar',
          data: dnsServers.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }]
      }
      
      this.dnsServersChart.setOption(option)
    },
    async buildLogQLQuery() {
      if (!this.data.targetIp) {
        console.warn('DNS查询分析：缺少目标IP，无法构建LogQL查询')
        return
      }

      try {
        // 构建请求参数
        const params = {
          stream: 'AUDITLOG_DNS_BIND_QUERY',
          srcIp: this.data.targetIp
        }

        // 处理时间范围
        if (this.data.startTime && this.data.endTime) {
          // 如果有具体的开始和结束时间，使用时间范围查询
          params.startTime = this.data.startTime
          params.endTime = this.data.endTime
        } else if (this.data.timeRange) {
          // 否则使用快速时间选择
          params.timeRange = convertTimeRange(this.data.timeRange, '30m')
        } else {
          // 默认时间范围
          params.timeRange = '30m'
        }

        console.log('DNS查询分析构建LogQL请求参数:', params)

        // 调用后端接口构建LogQL
        const response = await buildLogQL(params)

        if (response.code === 200) {
          this.builtLogQLQuery = response.data.logQL
          
          // 设置时间配置
          if (response.data.startTime && response.data.endTime) {
            this.timeConfig = {
              startTime: response.data.startTime,
              endTime: response.data.endTime
            }
          } else if (response.data.timeRange) {
            this.timeConfig = {
              quickTime: response.data.timeRange
            }
          }

          console.log('DNS查询分析后端构建的LogQL:', this.builtLogQLQuery)
          console.log('DNS查询分析时间配置:', this.timeConfig)

          // 强制日志查询组件重新渲染
          this.logQueryKey = Date.now()
        } else {
          console.error('DNS查询分析构建LogQL失败:', response.msg)
          this.$message.error('构建DNS查询语句失败: ' + response.msg)
        }
      } catch (error) {
        console.error('DNS查询分析构建LogQL异常:', error)
        this.$message.error('构建DNS查询语句异常: ' + error.message)
      }
    },

    // 桑基图节点点击事件处理
    handleSankeyNodeClick(nodeInfo) {
      console.log('DNS桑基图节点点击:', nodeInfo)
      
      // IPFlowSankeyChart组件已经处理了节点点击和主机信息抽屉显示
      // 这里只需要处理额外的业务逻辑（如果有的话）
      
      // 可以在这里添加更多处理逻辑，比如跳转到该IP的分析页面
      if (nodeInfo.ip && nodeInfo.ip !== this.data.targetIp) {
        this.$message.info(`点击了节点: ${nodeInfo.ip}`)
      }
    },

    // 桑基图连线点击事件处理
    handleSankeyLinkClick(linkInfo) {
      console.log('DNS桑基图连线点击:', linkInfo)
      this.$message.info(`DNS查询连接: ${linkInfo.source} → ${linkInfo.target}, 查询次数: ${linkInfo.value}`)
      // 可以在这里添加更多处理逻辑，比如显示详细的DNS查询记录
    },

    lightenColor(color) {
      // 将颜色变浅30%
      const hex = color.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)
      
      const lightenedR = Math.min(255, Math.floor(r + (255 - r) * 0.3))
      const lightenedG = Math.min(255, Math.floor(g + (255 - g) * 0.3))
      const lightenedB = Math.min(255, Math.floor(b + (255 - b) * 0.3))
      
      return `#${lightenedR.toString(16).padStart(2, '0')}${lightenedG.toString(16).padStart(2, '0')}${lightenedB.toString(16).padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

.dns-query-analysis-result {
  font-size: 12px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 5px;
}

.chart-container {
  height: 350px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fff;
  padding: 15px;
}

.chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.chart-wrapper {
  height: calc(100% - 30px);
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.no-data-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.no-data-tip i {
  font-size: 48px;
  margin-bottom: 10px;
}

.no-data-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.no-data-placeholder p {
  font-size: 14px;
  margin: 8px 0;
}

/* 表格样式调整 */
.el-table {
  font-size: 12px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .stat-icon {
    font-size: 24px;
    margin-right: 10px;
  }
  
  .stat-value {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .statistics-cards {
    grid-template-columns: 1fr;
  }
}

/* 日志详情区域样式 */
.log-details-section {
  margin-top: 20px;
}

.custom-tabs-container {
  background: #f5f7fa;
  border-radius: 4px 4px 0 0;
  border: 1px solid #e4e7ed;
  border-bottom: none;
}

.custom-tabs {
  display: flex;
  padding: 0;
  margin: 0;
}

.custom-tab-item {
  padding: 12px 20px;
  cursor: pointer;
  border-right: 1px solid #e4e7ed;
  color: #606266;
  font-size: 12px;
  transition: all 0.3s ease;
  user-select: none;
  position: relative;
}

.custom-tab-item:last-child {
  border-right: none;
}

.custom-tab-item.active {
  background: white;
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  font-weight: 500;
}

.custom-tab-item:hover:not(.active) {
  background: #ecf5ff;
  color: #409EFF;
}

.tab-content {
  border: 1px solid #e4e7ed;
  border-radius: 0 0 4px 4px;
  background: white;
  min-height: 400px;
}

/* 桑基图区域样式 */
.sankey-section {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sankey-no-data {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 20px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.section-header h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header h4 i {
  color: #409eff;
}

.section-description {
  margin: 0;
  color: #606266;
  font-size: 12px;
  line-height: 1.5;
}
</style> 