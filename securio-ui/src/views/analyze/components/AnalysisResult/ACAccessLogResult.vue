<template>
  <div class="ac-access-log-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>AC访问日志分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标IP：</strong>{{ data.targetIp }}</span>
        <span><strong>记录总数：</strong>{{ data.totalRecords }}</span>
        <span><strong>查询时间：</strong>最近1天</span>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div v-if="data.statistics" class="statistics-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-user"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.statistics.uniqueUsers || 0 }}</div>
          <div class="stat-label">唯一用户</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-connection"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.statistics.uniqueAPs || 0 }}</div>
          <div class="stat-label">接入点</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-position"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.statistics.uniqueSSIDs || 0 }}</div>
          <div class="stat-label">无线网络</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-document"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.totalRecords || 0 }}</div>
          <div class="stat-label">日志记录</div>
        </div>
      </div>
    </div>

    <!-- AC记录详情表格 -->
    <div v-if="data.acRecords && data.acRecords.length > 0" class="ac-records-section">
      <h4>AC访问记录详情</h4>
      <el-table 
        :data="data.acRecords" 
        stripe 
        border
        style="width: 100%"
        :default-sort="{prop: 'timestamp', order: 'descending'}"
        size="small">
        
        <el-table-column 
          prop="timestamp" 
          label="时间" 
          width="120"
          sortable>
          <template slot-scope="scope">
            <span v-if="scope.row.timestamp">{{ scope.row.timestamp }}</span>
            <span v-else style="color: #909399;">未知</span>
          </template>
        </el-table-column>
        
        <el-table-column 
          prop="username" 
          label="用户名" 
          width="120"
          sortable>
          <template slot-scope="scope">
            <el-tag 
              v-if="scope.row.username && scope.row.username !== '-NA-'"
              :type="getUsernameTagType(scope.row.username)"
              size="small">
              {{ scope.row.username }}
            </el-tag>
            <span v-else style="color: #909399;">未知用户</span>
          </template>
        </el-table-column>
        
        <el-table-column 
          prop="clientMAC" 
          label="客户端MAC" 
          show-overflow-tooltip>
          <template slot-scope="scope">
            <code style="font-size: 12px;">{{ scope.row.clientMAC || '-' }}</code>
          </template>
        </el-table-column>
        
        <el-table-column 
          prop="clientIP" 
          label="客户端IP" 
          width="120">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: 500;">{{ scope.row.clientIP || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column 
          prop="apName" 
          label="接入点名称" 
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.apName || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column 
          prop="ssid" 
          label="无线网络(SSID)" 
          width="150"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag 
              v-if="scope.row.ssid"
              type="info" 
              size="mini">
              {{ scope.row.ssid }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column 
          prop="bssid" 
          label="BSSID" 
          show-overflow-tooltip>
          <template slot-scope="scope">
            <code style="font-size: 11px; color: #606266;">{{ scope.row.bssid || '-' }}</code>
          </template>
        </el-table-column>
        
        <el-table-column 
          label="操作" 
          width="80"
          fixed="right">
          <template slot-scope="scope">
            <el-button 
              @click="showLogDetail(scope.row)" 
              type="text" 
              size="mini"
              icon="el-icon-view">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 无数据提示 -->
    <div v-else class="no-data-placeholder">
      <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
      <p style="margin-top: 15px; color: #606266;">暂无AC访问日志数据</p>
      <p style="color: #909399; font-size: 12px;">
        在最近1天内未发现该IP的AC访问日志记录，可能该IP未通过无线网络接入
      </p>
    </div>

    <!-- 用户统计图表 -->
    <div v-if="data.statistics && hasUserStatistics" class="charts-section">
      <div class="chart-container">
        <h4>用户访问统计</h4>
        <div ref="userChart" class="chart"></div>
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      title="AC访问日志详情"
      :visible.sync="showDetailDialog"
      width="80%"
      append-to-body>
      <div v-if="selectedRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间戳">
            {{ selectedRecord.timestamp || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            <el-tag 
              v-if="selectedRecord.username && selectedRecord.username !== '-NA-'"
              :type="getUsernameTagType(selectedRecord.username)">
              {{ selectedRecord.username }}
            </el-tag>
            <span v-else>未知用户</span>
          </el-descriptions-item>
          <el-descriptions-item label="客户端MAC">
            <code>{{ selectedRecord.clientMAC || '-' }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="客户端IP">
            <span style="color: #409EFF; font-weight: 500;">{{ selectedRecord.clientIP || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="接入点名称">
            {{ selectedRecord.apName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="无线网络(SSID)">
            <el-tag type="info">{{ selectedRecord.ssid || '-' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="BSSID">
            <code>{{ selectedRecord.bssid || '-' }}</code>
          </el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;">
          <h5>原始日志记录</h5>
          <el-input
            type="textarea"
            :value="selectedRecord.rawLog"
            :rows="6"
            readonly
            style="font-family: monospace; font-size: 12px;">
          </el-input>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ACAccessLogResult',
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'ac_access_log'
    }
  },
  data() {
    return {
      showDetailDialog: false,
      selectedRecord: null,
      userChart: null
    }
  },
  computed: {
    hasUserStatistics() {
      return this.data.statistics && 
             this.data.statistics.userCounts && 
             Object.keys(this.data.statistics.userCounts).length > 0
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.hasUserStatistics) {
        this.initUserChart()
      }
    })
  },
  beforeDestroy() {
    if (this.userChart) {
      this.userChart.dispose()
    }
  },
  methods: {
    showLogDetail(record) {
      this.selectedRecord = record
      this.showDetailDialog = true
    },
    
    getUsernameTagType(username) {
      if (!username || username === '-NA-') {
        return 'info'
      }
      // 根据用户名特征返回不同颜色
      if (username.includes('admin') || username.includes('管理')) {
        return 'danger'
      } else if (username.length > 6) {
        return 'success'
      } else {
        return 'primary'
      }
    },
    
    initUserChart() {
      if (!this.$refs.userChart || !this.hasUserStatistics) {
        return
      }
      
      this.userChart = echarts.init(this.$refs.userChart)
      
      const userCounts = this.data.statistics.userCounts
      const userData = Object.entries(userCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10) // 只显示前10个用户
      
      const option = {
        title: {
          text: '用户访问次数分布',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}: ${params[0].value} 次`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: userData.map(item => item[0]),
          axisLabel: {
            fontSize: 11,
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        series: [{
          name: '访问次数',
          type: 'bar',
          data: userData.map(item => item[1]),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2378f7' },
                { offset: 0.7, color: '#2378f7' },
                { offset: 1, color: '#83bff6' }
              ])
            }
          }
        }]
      }
      
      this.userChart.setOption(option)
      
      // 图表自适应
      window.addEventListener('resize', () => {
        if (this.userChart) {
          this.userChart.resize()
        }
      })
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

.ac-access-log-result {
  font-size: 12px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 5px;
}

.ac-records-section {
  margin-bottom: 20px;
}

.ac-records-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.no-data-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.no-data-placeholder p {
  font-size: 14px;
  margin: 8px 0;
}

.charts-section {
  margin-top: 20px;
}

.chart {
  width: 100%;
  height: 300px;
  min-height: 300px;
}

/* 表格样式调整 */
.el-table {
  font-size: 12px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .stat-icon {
    font-size: 24px;
    margin-right: 10px;
  }
  
  .stat-value {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .statistics-cards {
    grid-template-columns: 1fr;
  }
}
</style> 