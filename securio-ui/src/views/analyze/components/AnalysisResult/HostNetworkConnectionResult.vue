<template>
  <div class="host-network-connection-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info" v-if="data.summary">
        <span><strong>目标IP：</strong>{{ data.summary.hostIp }}</span>
        <span><strong>总端口数：</strong>{{ data.summary.totalPorts }}</span>
        <span><strong>监听端口数：</strong>{{ data.summary.listeningPortCount }}</span>
        <span><strong>外部连接数：</strong>{{ data.summary.externalConnectionCount }}</span>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 端口状态统计图表 -->
      <el-col :span="10">
        <div class="chart-container">
          <div class="chart-title">网络连接状态分布</div>
          <div v-loading="loading" class="chart-wrapper">
            <div v-if="!hasPortData" class="no-data-tip">
              <i class="el-icon-pie-chart"></i>
              <p>暂无数据</p>
            </div>
            <div v-else ref="portStatusChart" class="chart"></div>
          </div>
        </div>
      </el-col>
      
      <!-- 外部网络连接统计表格 -->
      <el-col :span="14">
        <div class="chart-container">
          <div class="chart-title">外部网络连接统计</div>
          <div v-loading="loading" class="chart-wrapper">
            <div v-if="!hasNetworkData" class="no-data-tip">
              <i class="el-icon-connection"></i>
              <p>暂无连接数据</p>
            </div>
            <el-table 
              v-else 
              :data="data.networkStats" 
              style="width: 100%" 
              max-height="300"
              size="small"
            >
              <el-table-column prop="pid" label="本地进程ID" width="120" />
              <el-table-column prop="processName" label="本地进程名称" width="120" />
              <el-table-column prop="remoteIp" label="远程IP" width="130">
                <template slot-scope="scope">
                  <span 
                    v-if="scope.row.remoteIp" 
                    class="ip-link" 
                    @click="handleIpClick(scope.row.remoteIp, scope.row.hostname)"
                    :title="scope.row.hostname ? `${scope.row.hostname} (${scope.row.remoteIp})` : scope.row.remoteIp">
                    {{ scope.row.remoteIp }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="hostname" label="远程主机名" min-width="120" />
              <el-table-column prop="connectionCount" label="连接数" width="80" />
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 本地监听端口表格 -->
    <div class="chart-container" style="margin-top: 20px;">
      <div class="chart-title">本地监听端口列表</div>
      <div v-loading="loading" class="chart-wrapper">
        <div v-if="!hasListeningPortData" class="no-data-tip">
          <i class="el-icon-connection"></i>
          <p>暂无监听端口数据</p>
        </div>
        <el-table 
          v-else 
          :data="data.listeningPorts" 
          style="width: 100%" 
          max-height="400"
          size="small"
          border
          stripe
        >
          <el-table-column prop="localPort" label="端口号" width="100" sortable>
            <template slot-scope="scope">
              <el-tag size="small" type="primary">{{ scope.row.localPort }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="protocol" label="协议" width="80">
            <template slot-scope="scope">
              <el-tag 
                size="mini" 
                :type="scope.row.protocol === 'tcp' ? 'success' : 'warning'">
                {{ scope.row.protocol.toUpperCase() }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="localIp" label="监听地址" width="140" />
          <el-table-column prop="process" label="进程名称" min-width="120" />
          <el-table-column prop="pid" label="进程ID" width="100" />
          <el-table-column prop="state" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag 
                size="mini" 
                :type="getStatusTagType(scope.row.state)">
                {{ scope.row.state }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 主机信息抽屉 -->
    <el-drawer
        :title="`主机信息 - ${selectedHostInfo.hostname || selectedHostInfo.ip}`"
        :visible.sync="showHostDrawer"
        direction="rtl"
        size="50%">
        <div style="padding: 20px;">
        <host-basic-info 
            v-if="selectedHostInfo.ip"
            :host-ip="selectedHostInfo.ip" 
            @info-loaded="handleHostInfoLoaded" />
        </div>
    </el-drawer>
  </div>


</template>

<script>
import * as echarts from 'echarts'
import HostBasicInfo from '../HostBasicInfo/index.vue'

export default {
  name: 'HostNetworkConnectionResult',
  components: {
    HostBasicInfo
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'host_network_connection'
    }
  },
  data() {
    return {
      loading: false,
      portChart: null,
      chartResizeHandler: null,
      showHostDrawer: false,
      selectedHostInfo: {
        ip: '',
        hostname: ''
      }
    }
  },
  computed: {
    hasPortData() {
      return this.data.portStats && Object.keys(this.data.portStats).length > 0
    },
    hasNetworkData() {
      return this.data.networkStats && this.data.networkStats.length > 0
    },
    hasListeningPortData() {
      return this.data.listeningPorts && this.data.listeningPorts.length > 0
    },
    portStatsData() {
      if (!this.hasPortData) return []
      return Object.entries(this.data.portStats).map(([name, value]) => ({
        name,
        value
      }))
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.initPortStatusChart()
        })
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.chartResizeHandler = () => {
      this.portChart?.resize()
    }
    window.addEventListener('resize', this.chartResizeHandler)
    
    this.$nextTick(() => {
      this.initPortStatusChart()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.chartResizeHandler)
    if (this.portChart) {
      this.portChart.dispose()
      this.portChart = null
    }
  },
  methods: {
    initPortStatusChart() {
      if (!this.hasPortData) return
      
      const chartDom = this.$refs.portStatusChart
      if (!chartDom) return
      
      if (this.portChart) {
        this.portChart.dispose()
      }
      
      this.portChart = echarts.init(chartDom)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: this.portStatsData.map(item => item.name)
        },
        series: [
          {
            name: '端口状态',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold',
                formatter: '{b}\n{c} ({d}%)'
              }
            },
            labelLine: {
              show: false
            },
            data: this.portStatsData
          }
        ]
      }
      
      this.portChart.setOption(option)
    },
    
    getStatusTagType(status) {
      const statusMap = {
        'LISTEN': 'primary',
        'ESTABLISHED': 'success',
        'CLOSE_WAIT': 'warning',
        'TIME_WAIT': 'info',
        'CLOSED': 'danger'
      }
      return statusMap[status] || 'info'
    },
    handleIpClick(ip, hostname) {
      console.log('处理IP点击，IP:', ip, '主机名:', hostname)
      
      // 安全检查IP
      if (!ip) {
        console.warn('无效的IP地址:', ip)
        return
      }
      
      // 保存选中的主机信息
      this.selectedHostInfo = {
        ip: ip,
        hostname: hostname || ''
      }
      
      console.log('设置选中的主机信息:', this.selectedHostInfo)
      
      // 显示主机信息抽屉
      this.showHostDrawer = true
    },
    handleHostInfoLoaded(info) {
      // 处理主机信息加载完成
      console.log('主机信息加载完成:', info)
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

.host-network-connection-result {
  font-size: 12px;
}

.chart-container {
  height: 350px;
}

.chart-wrapper {
  height: calc(100% - 30px);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .el-col-10,
  .el-col-14 {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .chart-container {
    height: 300px;
  }
}
</style> 