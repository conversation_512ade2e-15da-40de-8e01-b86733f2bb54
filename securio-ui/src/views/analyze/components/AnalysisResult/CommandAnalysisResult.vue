<template>
  <div class="command-analysis-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>主机命令行为分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标主机：</strong>{{ data.targetIp }}</span>
        <span><strong>命令总数：</strong>{{ data.commandAnalysis && data.commandAnalysis.totalCommands || 0 }}</span>
        <span><strong>分析时间：</strong>{{ data.timeRangeDescription || '最近1天' }}</span>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div v-if="data.commandAnalysis" class="statistics-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-tickets"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.commandAnalysis.totalCommands || 0 }}</div>
          <div class="stat-label">命令执行总数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-menu"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.commandAnalysis.uniqueCommands || 0 }}</div>
          <div class="stat-label">唯一命令</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-user"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.commandAnalysis.uniqueUsers || 0 }}</div>
          <div class="stat-label">执行用户</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-monitor"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.commandAnalysis.uniqueHostnames || 0 }}</div>
          <div class="stat-label">主机名数量</div>
        </div>
      </div>
    </div>

    <!-- 图表展示区域 -->
    <el-row v-if="data.commandAnalysis" :gutter="20" style="margin-top: 20px;">
      <!-- 命令执行Top10 -->
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-title">命令执行Top10</div>
          <div v-loading="loading" class="chart-wrapper">
            <div v-if="!hasCommandsData" class="no-data-tip">
              <i class="el-icon-menu"></i>
              <p>暂无命令执行数据</p>
            </div>
            <div v-else ref="commandsChart" class="chart"></div>
          </div>
        </div>
      </el-col>
      
      <!-- 命令类型分布 -->
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-title">命令类型分布</div>
          <div v-loading="loading" class="chart-wrapper">
            <div v-if="!hasCommandTypesData" class="no-data-tip">
              <i class="el-icon-pie-chart"></i>
              <p>暂无命令类型数据</p>
            </div>
            <div v-else ref="commandTypesChart" class="chart"></div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 用户活动分布 -->
    <div v-if="data.commandAnalysis" class="chart-container" style="margin-top: 20px;">
      <div class="chart-title">用户活动分布</div>
      <div v-loading="loading" class="chart-wrapper">
        <div v-if="!hasUsersData" class="no-data-tip">
          <i class="el-icon-user"></i>
          <p>暂无用户活动数据</p>
        </div>
        <div v-else ref="usersChart" class="chart"></div>
      </div>
    </div>

    <!-- 命令执行日志详情 -->
    <div class="log-details-section" style="margin-top: 20px;">
      <div class="custom-tabs-container">
        <div class="custom-tabs">
          <div class="custom-tab-item active">
            命令执行日志
          </div>
        </div>
      </div>
      
      <div class="tab-content">
        <log-query-component
          ref="commandLogQuery"
          :key="logQueryKey"
          :title="'命令执行日志'"
          :subtitle="`查询主机 ${data.targetIp} 的命令执行记录`"
          :settings="commandLogSettings"
          :defaultQuery="builtLogQLQuery"
          :externalTimeRange="timeConfig"
        />
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="!hasAnyData" class="no-data-placeholder">
      <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
      <p style="margin-top: 15px; color: #606266;">暂无命令执行记录</p>
      <p style="color: #909399; font-size: 12px;">
        在指定时间范围内未发现该主机的命令执行记录，可能该主机未执行命令或日志收集异常
      </p>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import LogQueryComponent from '@/components/log_query_component'
import { convertTimeRange } from '@/utils/timeFormatter'
import { buildLogQL } from '@/api/log/logQuery'

export default {
  name: 'CommandAnalysisResult',
  components: {
    LogQueryComponent
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'command_analysis'
    }
  },
  data() {
    return {
      loading: false,
      commandsChart: null,
      commandTypesChart: null,
      usersChart: null,
      chartResizeHandler: null,
      logQueryKey: 1,
      builtLogQLQuery: '',
      timeConfig: {},
      // 命令日志组件设置
      commandLogSettings: {
        columns: [
          { prop: 'event_time', label: '执行时间', width: '180', sortable: true },
          { prop: 'user', label: '执行用户', width: '120' },
          { prop: 'hostname', label: '主机名', width: '140' },
          { prop: 'command', label: '命令', minWidth: '300', showOverflowTooltip: true },
          { prop: 'working_dir', label: '工作目录', width: '200', showOverflowTooltip: true },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  computed: {
    hasCommandsData() {
      return this.data.commandAnalysis && 
             this.data.commandAnalysis.commands && 
             this.data.commandAnalysis.commands.length > 0
    },
    hasCommandTypesData() {
      return this.data.commandAnalysis && 
             this.data.commandAnalysis.behaviorCharacteristics &&
             this.data.commandAnalysis.behaviorCharacteristics.commandTypes &&
             Object.keys(this.data.commandAnalysis.behaviorCharacteristics.commandTypes).length > 0
    },
    hasUsersData() {
      return this.data.commandAnalysis && 
             this.data.commandAnalysis.users && 
             this.data.commandAnalysis.users.length > 0
    },
    hasAnyData() {
      return this.hasCommandsData || this.hasCommandTypesData || this.hasUsersData
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.initAllCharts()
          // 当数据变化时，重新构建LogQL查询
          this.buildLogQLQuery()
        })
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.chartResizeHandler = () => {
      this.commandsChart?.resize()
      this.commandTypesChart?.resize()
      this.usersChart?.resize()
    }
    window.addEventListener('resize', this.chartResizeHandler)
    
    this.$nextTick(() => {
      this.initAllCharts()
      // 初始化时构建LogQL查询
      this.buildLogQLQuery()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.chartResizeHandler)
    this.disposeAllCharts()
  },
  methods: {
    initAllCharts() {
      this.initCommandsChart()
      this.initCommandTypesChart()
      this.initUsersChart()
    },
    
    disposeAllCharts() {
      if (this.commandsChart) {
        this.commandsChart.dispose()
        this.commandsChart = null
      }
      if (this.commandTypesChart) {
        this.commandTypesChart.dispose()
        this.commandTypesChart = null
      }
      if (this.usersChart) {
        this.usersChart.dispose()
        this.usersChart = null
      }
    },
    
    initCommandsChart() {
      if (!this.hasCommandsData) return
      
      const chartDom = this.$refs.commandsChart
      if (!chartDom) return
      
      if (this.commandsChart) {
        this.commandsChart.dispose()
      }
      
      this.commandsChart = echarts.init(chartDom)
      
      const commands = this.data.commandAnalysis.commands.slice(0, 10)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}: ${params[0].value} 次`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: commands.map(item => {
            const name = item.name || ''
            return name.length > 15 ? name.substring(0, 12) + '...' : name
          }),
          axisLabel: {
            fontSize: 11,
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        series: [{
          name: '执行次数',
          type: 'bar',
          data: commands.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#ff6b6b' },
              { offset: 0.7, color: '#ee5a24' },
              { offset: 1, color: '#ff3838' }
            ])
          }
        }]
      }
      
      this.commandsChart.setOption(option)
    },
    
    initCommandTypesChart() {
      if (!this.hasCommandTypesData) return
      
      const chartDom = this.$refs.commandTypesChart
      if (!chartDom) return
      
      if (this.commandTypesChart) {
        this.commandTypesChart.dispose()
      }
      
      this.commandTypesChart = echarts.init(chartDom)
      
      const commandTypes = this.data.commandAnalysis.behaviorCharacteristics.commandTypes
      const data = Object.entries(commandTypes)
        .filter(([key, value]) => value > 0)
        .map(([key, value]) => ({ name: key, value: value }))
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name),
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '命令类型',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold',
                formatter: '{b}\n{c} ({d}%)'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      }
      
      this.commandTypesChart.setOption(option)
    },
    
    initUsersChart() {
      if (!this.hasUsersData) return
      
      const chartDom = this.$refs.usersChart
      if (!chartDom) return
      
      if (this.usersChart) {
        this.usersChart.dispose()
      }
      
      this.usersChart = echarts.init(chartDom)
      
      const users = this.data.commandAnalysis.users
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}: ${params[0].value} 次`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: users.map(item => item.name),
          axisLabel: {
            fontSize: 11,
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        series: [{
          name: '执行次数',
          type: 'bar',
          data: users.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#4facfe' },
              { offset: 0.7, color: '#00f2fe' },
              { offset: 1, color: '#43e97b' }
            ])
          }
        }]
      }
      
      this.usersChart.setOption(option)
    },
    
    async buildLogQLQuery() {
      if (!this.data.targetIp) {
        console.warn('命令分析：缺少目标IP，无法构建LogQL查询')
        return
      }

      try {
        // ✅ 正确：使用customFilters自定义条件过滤传递agent_hostip参数
        const params = {
          stream: 'AUDITLOG_SYSLOG_COMMAND',
          customFilters: [
            { field: 'agent_hostip', operator: '=', value: this.data.targetIp }
          ]
        }

        // 处理时间范围
        if (this.data.startTime && this.data.endTime) {
          // 如果有具体的开始和结束时间，使用时间范围查询
          params.startTime = this.data.startTime
          params.endTime = this.data.endTime
        } else if (this.data.timeRange) {
          // 否则使用快速时间选择
          params.timeRange = convertTimeRange(this.data.timeRange, '1d')
        } else {
          // 默认时间范围
          params.timeRange = '1d'
        }

        console.log('命令分析构建LogQL请求参数:', params)

        // 调用后端接口构建LogQL
        const response = await buildLogQL(params)

        if (response.code === 200) {
          this.builtLogQLQuery = response.data.logQL
          
          // 设置时间配置
          if (response.data.startTime && response.data.endTime) {
            this.timeConfig = {
              startTime: response.data.startTime,
              endTime: response.data.endTime
            }
          } else if (response.data.timeRange) {
            this.timeConfig = {
              quickTime: response.data.timeRange
            }
          }

          console.log('命令分析后端构建的LogQL:', this.builtLogQLQuery)
          console.log('命令分析时间配置:', this.timeConfig)

          // 强制日志查询组件重新渲染
          this.logQueryKey = Date.now()
        } else {
          console.error('命令分析构建LogQL失败:', response.msg)
          this.$message.error('构建命令查询语句失败: ' + response.msg)
        }
      } catch (error) {
        console.error('命令分析构建LogQL异常:', error)
        this.$message.error('构建命令查询语句异常: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

.command-analysis-result {
  font-size: 12px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 5px;
}

.chart-container {
  height: 350px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fff;
  padding: 15px;
}

.chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.chart-wrapper {
  height: calc(100% - 30px);
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.no-data-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.no-data-tip i {
  font-size: 48px;
  margin-bottom: 10px;
}

.no-data-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.no-data-placeholder p {
  font-size: 14px;
  margin: 8px 0;
}

/* 日志详情区域样式 */
.log-details-section {
  margin-top: 20px;
}

.custom-tabs-container {
  background: #f5f7fa;
  border-radius: 4px 4px 0 0;
  border: 1px solid #e4e7ed;
  border-bottom: none;
}

.custom-tabs {
  display: flex;
  padding: 0;
  margin: 0;
}

.custom-tab-item {
  padding: 12px 20px;
  cursor: pointer;
  border-right: 1px solid #e4e7ed;
  color: #606266;
  font-size: 12px;
  transition: all 0.3s ease;
  user-select: none;
  position: relative;
}

.custom-tab-item:last-child {
  border-right: none;
}

.custom-tab-item.active {
  background: white;
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  font-weight: 500;
}

.custom-tab-item:hover:not(.active) {
  background: #ecf5ff;
  color: #409EFF;
}

.tab-content {
  border: 1px solid #e4e7ed;
  border-radius: 0 0 4px 4px;
  background: white;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .stat-icon {
    font-size: 24px;
    margin-right: 10px;
  }
  
  .stat-value {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .statistics-cards {
    grid-template-columns: 1fr;
  }
}
</style> 