<template>
  <div class="zeek-http-analysis-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>Zeek HTTP连接分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标IP：</strong>{{ data.targetIp }}</span>
        <span><strong>时间范围：</strong>{{ data.timeRange }}</span>
      </div>
    </div>

    <!-- 桑基图展示 -->
    <div v-if="data.sankeyData" class="sankey-section">
      <div class="section-header">
        <h4>HTTP连接流向关系图</h4>
        <div class="chart-legend">
          <span class="legend-item">
            <span class="legend-color source"></span>
            来源IP
          </span>
          <span class="legend-item">
            <span class="legend-color target"></span>
            目标IP
          </span>
          <span class="legend-item">
            <span class="legend-color upstream"></span>
            上游IP
          </span>
        </div>
      </div>
      
      <div v-if="hasValidFlowData" >
        <IPFlowSankeyChart 
          :data="data.sankeyData" 
          height="600px"
          :options="sankeyOptions"
          :show-own-drawer="true"
          @node-click="handleNodeClick"
          @link-click="handleLinkClick" />
      </div>
      <div v-else class="no-data-placeholder">
        <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
        <p style="margin-top: 15px; color: #606266;">暂无HTTP连接流向数据</p>
        <p style="color: #909399; font-size: 12px;">在该时间段内未发现相关的Zeek HTTP连接记录</p>
        <div v-if="data.sankeyData && data.sankeyData.nodes && data.sankeyData.nodes.length === 1" class="single-node-info">
          <el-divider></el-divider>
          <p style="color: #606266; font-size: 12px;">
            <i class="el-icon-info"></i>
            目标IP {{ data.targetIp }} 在此时间段内没有HTTP连接活动
          </p>
        </div>
      </div>
    </div>

    <!-- 连线日志查询对话框 -->
    <el-dialog
      :title="`HTTP连接详细日志 - ${selectedLinkInfo.source} → ${selectedLinkInfo.target}`"
      :visible.sync="showLinkLogDialog"
      width="90%"
      top="5vh"
      append-to-body>
      <div style="padding: 10px;">
        <div class="link-info-summary">
          <div class="info-item">
            <span class="label">来源IP：</span>
            <span class="value">{{ selectedLinkInfo.source }}</span>
          </div>
          <div class="info-item">
            <span class="label">目标IP：</span>
            <span class="value">{{ selectedLinkInfo.target }}</span>
          </div>
          <div class="info-item">
            <span class="label">连接数：</span>
            <span class="value">{{ selectedLinkInfo.value.toLocaleString() }}</span>
          </div>
        </div>
        
        <log-query-component
          :key="queryComponentKey"
          :title="`Zeek HTTP连接日志`"
          :subtitle="`${selectedLinkInfo.source} → ${selectedLinkInfo.target} 的详细HTTP连接记录`"
          :default-query="linkLogQuery"
          :default-time-range="'custom'"
          :external-time-range="linkLogTimeConfig"
          stream="SECURIO_ZEEK_HTTP"
          instance="vmlog2"
          :settings="{
            columns: [
              { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '客户端IP', width: '120' },
              { prop: 'method', label: '方法', width: '80', tag: true },
              { prop: 'host', label: '主机', width: '150' },
              { prop: 'uri', label: 'URI', showOverflowTooltip: true },
              { prop: 'status_code', label: '状态码', width: '80', tag: true },
              { prop: 'user_agent', label: '用户代理', showOverflowTooltip: true },
              { prop: 'uid', label: '连接ID', width: '150' }
            ],
            showDetailButton: true,
            detailButtonWidth: '100',
            detailButtonFixed: 'right'
          }"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import IPFlowSankeyChart from '../IPFlowSankeyChart.vue'
import LogQueryComponent from '@/components/log_query_component.vue'
import { buildLogQL } from '@/api/log/logQuery'

export default {
  name: 'ZeekHttpAnalysisResult',
  components: {
    IPFlowSankeyChart,
    LogQueryComponent
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'zeek_http_analysis'
    }
  },
  data() {
    return {
      sankeyOptions: {
        nodeGap: 20,
        nodeWidth: 30,
        layoutIterations: 50
      },
      showLinkLogDialog: false,
      selectedLinkInfo: {
        source: '',
        target: '',
        value: 0
      },
      linkLogQuery: '',
      linkLogTimeConfig: null,
      queryComponentKey: 0
    }
  },
  computed: {
    // 判断是否有有效的流向数据（不仅仅是节点，还要有连接）
    hasValidFlowData() {
      return this.data.sankeyData && 
             this.data.sankeyData.nodes && 
             this.data.sankeyData.nodes.length > 0 &&
             this.data.sankeyData.links && 
             this.data.sankeyData.links.length > 0
    }
  },
  methods: {
    handleNodeClick(nodeInfo) {
      console.log('Zeek HTTP节点点击事件:', nodeInfo)
      
      // IPFlowSankeyChart组件已经处理了节点点击和主机信息抽屉显示
      // 这里只需要处理额外的业务逻辑（如果有的话）
      
      // 可以在这里添加更多处理逻辑，比如记录用户行为等
    },
    
    handleLinkClick(linkInfo) {
      console.log('处理Zeek HTTP连线点击，连线信息:', linkInfo)
      
      if (!linkInfo) {
        console.warn('无效的连线信息:', linkInfo)
        return
      }
      
      this.selectedLinkInfo = {
        source: linkInfo.source || '',
        target: linkInfo.target || '',
        value: linkInfo.value || 0
      }
      
      this.buildLogQLForLink()
    },
    
    async buildLogQLForLink() {
      try {
        const timeRange = this.data.timeRange || '30m'
        const startTime = this.data.startTime
        const endTime = this.data.endTime
        
        const params = {
          stream: 'SECURIO_ZEEK_HTTP',
          srcIp: this.selectedLinkInfo.source,
          dstIp: this.selectedLinkInfo.target
        }
        
        if (startTime && endTime) {
          params.startTime = startTime
          params.endTime = endTime
        } else {
          params.timeRange = timeRange
        }
        
        console.log('构建Zeek HTTP LogQL请求参数:', params)
        
        const response = await buildLogQL(params)
        
        if (response.code === 200) {
          this.linkLogQuery = response.data.logQL
          console.log('后端构建的Zeek HTTP LogQL查询语句:', this.linkLogQuery)
          
          this.linkLogTimeConfig = null
          if (response.data.startTime && response.data.endTime) {
            this.linkLogTimeConfig = {
              startTime: response.data.startTime,
              endTime: response.data.endTime
            }
          } else if (response.data.timeRange) {
            this.linkLogTimeConfig = {
              quickTime: response.data.timeRange
            }
          }
          
          this.queryComponentKey += 1
          this.showLinkLogDialog = true
        } else {
          console.error('构建Zeek HTTP LogQL失败:', response.msg)
          this.$message.error('构建查询语句失败: ' + response.msg)
        }
      } catch (error) {
        console.error('构建Zeek HTTP LogQL异常:', error)
        this.$message.error('构建查询语句异常: ' + error.message)
      }
    },
    
    formatRequestTime(value) {
      if (!value || value === '-') return '-'
      const time = parseFloat(value)
      if (isNaN(time)) return value
      
      if (time === 0) return '0ms'
      
      // 如果小于1秒，显示毫秒
      if (time < 1) {
        return (time * 1000).toFixed(0) + 'ms'
      } else {
        // 大于1秒，显示秒数
        return time.toFixed(3) + 's'
      }
    }
  }
}
</script>

<style scoped>
.zeek-http-analysis-result {
  font-size: 12px;
}

.analysis-summary {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analysis-summary h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.summary-text {
  color: #606266;
  margin: 0 0 15px 0;
  line-height: 1.6;
  font-size: 12px;
}

.analysis-info {
  display: flex;
  gap: 30px;
  font-size: 12px;
}

.analysis-info span {
  color: #909399;
}

.analysis-info strong {
  color: #303133;
}

.sankey-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.chart-legend {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.source {
  background-color: #ff7875;
}

.legend-color.target {
  background-color: #40a9ff;
}

.legend-color.upstream {
  background-color: #73d13d;
}

.no-data-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.no-data-placeholder p {
  font-size: 14px;
  margin: 8px 0;
}

.link-info-summary {
  display: flex;
  justify-content: space-around;
  padding: 15px;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.info-item .label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.info-item .value {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.single-node-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.single-node-info p {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.single-node-info i {
  color: #409EFF;
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .chart-legend {
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}
</style> 