<template>
  <div class="h3c-behavior-log-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>H3C上网行为分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标IP：</strong>{{ data.targetIp }}</span>
        <span><strong>记录总数：</strong>{{ data.behaviorAnalysis && data.behaviorAnalysis.totalHits ? data.behaviorAnalysis.totalHits : data.totalRecords }}</span>
        <span><strong>分析时间：</strong>{{ data.timeRange }}</span>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div v-if="data.behaviorAnalysis" class="statistics-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-collection-tag"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.behaviorAnalysis.uniqueUrlCategories || 0 }}</div>
          <div class="stat-label">网站分类</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-link"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.behaviorAnalysis.uniqueUrlDomains || 0 }}</div>
          <div class="stat-label">访问域名</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-s-grid"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.behaviorAnalysis.uniqueApps || 0 }}</div>
          <div class="stat-label">使用应用</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-share"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.behaviorAnalysis.uniquedstIps || 0 }}</div>
          <div class="stat-label">目标服务器</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="el-icon-document"></i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ data.behaviorAnalysis.totalHits || data.totalRecords || 0 }}</div>
          <div class="stat-label">访问记录</div>
        </div>
      </div>
    </div>

    <!-- 图表分析区域 -->
    <div v-if="hasStatisticsData" class="charts-section">
      <el-row :gutter="20">
        <!-- 网站分类分布饼图 -->
        <el-col :span="12">
          <div class="chart-container">
            <h4>网站分类分布</h4>
            <div ref="urlCategoryChart" class="chart"></div>
          </div>
        </el-col>
        
        <!-- 访问域名Top10柱状图 -->
        <el-col :span="12">
          <div class="chart-container">
            <h4>访问域名TOP10</h4>
            <div ref="urlDomainChart" class="chart"></div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <!-- 应用使用统计柱状图 -->
        <el-col :span="12">
          <div class="chart-container">
            <h4>应用使用统计</h4>
            <div ref="appChart" class="chart"></div>
          </div>
        </el-col>
        
        <!-- 目标IP Top10柱状图 -->
        <el-col :span="12">
          <div class="chart-container">
            <h4>目标IP TOP10</h4>
            <div ref="dstIpChart" class="chart"></div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <!-- 用户组分布饼图 -->
        <el-col :span="12">
          <div class="chart-container">
            <h4>用户组分布</h4>
            <div ref="userGroupChart" class="chart"></div>
          </div>
        </el-col>
        
        <!-- 日志类型分布饼图 -->
        <el-col :span="12">
          <div class="chart-container">
            <h4>日志类型分布</h4>
            <div ref="logTypeChart" class="chart"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 日志查询详情区域 -->
    <div class="log-details-section">
      <div class="custom-tabs-container">
        <div class="custom-tabs">
          <div
            class="custom-tab-item"
            :class="{ 'active': activeTab === 'web_access' }"
            @click="activeTab = 'web_access'"
          >
            网页访问日志
          </div>
          <div
            class="custom-tab-item"
            :class="{ 'active': activeTab === 'app_access' }"
            @click="activeTab = 'app_access'"
          >
            应用访问日志
          </div>
          <div
            class="custom-tab-item"
            :class="{ 'active': activeTab === 'file_transfer' }"
            @click="activeTab = 'file_transfer'"
          >
            文件传输日志
          </div>
          <div
            class="custom-tab-item"
            :class="{ 'active': activeTab === 'all_logs' }"
            @click="activeTab = 'all_logs'"
          >
            全部日志
          </div>
        </div>
      </div>

      <div class="tab-content">
        <log-query-component
          v-if="activeTab === 'web_access'"
          key="web_access"
          ref="webAccessLogQuery"
          :key="logQueryKeys.web_access"
          :title="'网页访问日志'"
          :subtitle="`查询IP ${data.targetIp} 的网页访问记录`"
          :settings="webAccessSettings"
          :defaultQuery="logQueries.web_access"
          :externalTimeRange="timeConfig"
        />
        <log-query-component
          v-else-if="activeTab === 'app_access'"
          key="app_access"
          ref="appAccessLogQuery"
          :key="logQueryKeys.app_access"
          :title="'应用访问日志'"
          :subtitle="`查询IP ${data.targetIp} 的应用访问记录`"
          :settings="appAccessSettings"
          :defaultQuery="logQueries.app_access"
          :externalTimeRange="timeConfig"
        />
        <log-query-component
          v-else-if="activeTab === 'file_transfer'"
          key="file_transfer"
          ref="fileTransferLogQuery"
          :key="logQueryKeys.file_transfer"
          :title="'文件传输日志'"
          :subtitle="`查询IP ${data.targetIp} 的文件传输记录`"
          :settings="fileTransferSettings"
          :defaultQuery="logQueries.file_transfer"
          :externalTimeRange="timeConfig"
        />
        <log-query-component
          v-else-if="activeTab === 'all_logs'"
          key="all_logs"
          ref="allLogsQuery"
          :key="logQueryKeys.all_logs"
          :title="'全部行为日志'"
          :subtitle="`查询IP ${data.targetIp} 的所有上网行为记录`"
          :settings="allLogsSettings"
          :defaultQuery="logQueries.all_logs"
          :externalTimeRange="timeConfig"
        />
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="!hasAnyData" class="no-data-placeholder">
      <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
      <p style="margin-top: 15px; color: #606266;">暂无H3C上网行为数据</p>
      <p style="color: #909399; font-size: 12px;">
        在指定时间范围内未发现该IP的上网行为记录
      </p>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import LogQueryComponent from '@/components/log_query_component'
import { convertTimeRange } from '@/utils/timeFormatter'
import { buildLogQL } from '@/api/log/logQuery'

export default {
  name: 'H3CBehaviorLogResult',
  components: {
    LogQueryComponent
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'h3c_behavior_log'
    },
    hostIp: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeTab: 'web_access',
      urlCategoryChart: null,
      urlDomainChart: null,
      appChart: null,
      dstIpChart: null,
      userGroupChart: null,
      logTypeChart: null,
      logQueryKeys: {
        web_access: 1,
        app_access: 1,
        file_transfer: 1,
        all_logs: 1
      },
      logQueries: {
        web_access: '',
        app_access: '',
        file_transfer: '',
        all_logs: ''
      },
      timeConfig: {},
      // 网页访问日志设置
      webAccessSettings: {
        columns: [
          { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
          { prop: 'user_name', label: '用户IP', width: '120' },
          { prop: 'user_group_name', label: '用户组', width: '140', tag: true },
          { prop: 'url_domain', label: '访问域名', width: '200', showOverflowTooltip: true },
          { prop: 'url_category', label: '网站分类', width: '120', tag: true },
          { prop: 'dst_ip', label: '目标IP', width: '120' },
          { prop: 'dst_port', label: '端口', width: '80' },
          { prop: 'handle_action', label: '处理动作', width: '100', tag: true, formatter: this.formatHandleAction },
          { prop: 'term_platform', label: '终端平台', width: '120' },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right',
        defaultFilters: [
          { field: 'log_type', operator: '=', value: 'web_access' }
        ]
      },
      // 应用访问日志设置
      appAccessSettings: {
        columns: [
          { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
          { prop: 'user_name', label: '用户IP', width: '120' },
          { prop: 'user_group_name', label: '用户组', width: '140', tag: true },
          { prop: 'app_name', label: '应用名称', width: '180', showOverflowTooltip: true, tag: true },
          { prop: 'app_cat_name', label: '应用分类', width: '120', tag: true },
          { prop: 'dst_ip', label: '目标IP', width: '120' },
          { prop: 'dst_port', label: '端口', width: '80' },
          { prop: 'content', label: '操作内容', width: '200', showOverflowTooltip: true },
          { prop: 'handle_action', label: '处理动作', width: '100', tag: true, formatter: this.formatHandleAction },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right',
        defaultFilters: [
          { field: 'log_type', operator: '=', value: 'other_app' }
        ]
      },
      // 文件传输日志设置
      fileTransferSettings: {
        columns: [
          { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
          { prop: 'user_name', label: '用户IP', width: '120' },
          { prop: 'user_group_name', label: '用户组', width: '140', tag: true },
          { prop: 'file_name', label: '文件名', width: '200', showOverflowTooltip: true },
          { prop: 'app_name', label: '传输方式', width: '150', tag: true },
          { prop: 'dst_ip', label: '目标IP', width: '120' },
          { prop: 'dst_port', label: '端口', width: '80' },
          { prop: 'action_name', label: '操作类型', width: '100', tag: true },
          { prop: 'handle_action', label: '处理动作', width: '100', tag: true, formatter: this.formatHandleAction },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right',
        defaultFilters: [
          { field: 'log_type', operator: '=', value: 'file_transfer' }
        ]
      },
      // 全部日志设置
      allLogsSettings: {
        columns: [
          { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
          { prop: 'user_name', label: '用户IP', width: '120' },
          { prop: 'user_group_name', label: '用户组', width: '140', tag: true },
          { prop: 'log_type', label: '日志类型', width: '120', tag: true, formatter: this.formatLogType },
          { prop: 'app_name', label: '应用名称', width: '150', showOverflowTooltip: true, tag: true },
          { prop: 'url_domain', label: '访问域名', width: '180', showOverflowTooltip: true },
          { prop: 'dst_ip', label: '目标IP', width: '120' },
          { prop: 'handle_action', label: '处理动作', width: '100', tag: true, formatter: this.formatHandleAction },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  computed: {
    hasStatisticsData() {
      return this.data.behaviorAnalysis && 
             (this.data.behaviorAnalysis.urlCategoriesChart || 
              this.data.behaviorAnalysis.urlDomainsChart ||
              this.data.behaviorAnalysis.appNamesChart ||
              this.data.behaviorAnalysis.dstIpsChart)
    },
    hasAnyData() {
      return (this.data.behaviorRecords && this.data.behaviorRecords.length > 0) ||
             (this.data.behaviorAnalysis && this.data.behaviorAnalysis.totalHits > 0) ||
             this.hasStatisticsData
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.hasStatisticsData) {
        // 延迟初始化，确保DOM完全渲染
        setTimeout(() => {
          this.initCharts()
          // 确保图表正确计算尺寸
          setTimeout(() => {
            this.resizeCharts()
          }, 200)
        }, 100)
      }
    })
    
    // 添加窗口resize监听
    window.addEventListener('resize', this.handleResize)
    
    // 初始化构建所有LogQL查询
    this.buildAllLogQLQueries()
  },
  beforeDestroy() {
    this.disposeCharts()
    // 移除窗口resize监听
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    // 监听标签页切换
    activeTab(newTab, oldTab) {
      console.log('H3C行为日志切换标签页:', newTab)
      // 当切换到日志查询相关的标签页时，确保组件正确初始化
      this.$nextTick(() => {
        // 切换Tab时也重新计算图表尺寸
        if (this.hasStatisticsData) {
          setTimeout(() => {
            this.resizeCharts()
          }, 100)
        }
      })
    },
    
    // 监听数据变化，重新初始化图表和LogQL查询
    'data.behaviorAnalysis': {
      handler(newVal, oldVal) {
        if (newVal && this.hasStatisticsData) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.initCharts()
            }, 100)
          })
        }
        // 数据变化时重新构建LogQL查询
        this.buildAllLogQLQueries()
      },
      deep: true
    },
    
    // 监听目标IP变化
    'data.targetIp': {
      handler() {
        this.buildAllLogQLQueries()
      }
    },
    
    // 监听时间范围变化
    'data.timeRange': {
      handler() {
        this.buildAllLogQLQueries()
      }
    }
  },
  methods: {
    // 添加窗口resize处理方法
    handleResize() {
      this.resizeCharts()
    },
    
    disposeCharts() {
      if (this.urlCategoryChart) this.urlCategoryChart.dispose()
      if (this.urlDomainChart) this.urlDomainChart.dispose()
      if (this.appChart) this.appChart.dispose()
      if (this.dstIpChart) this.dstIpChart.dispose()
      if (this.userGroupChart) this.userGroupChart.dispose()
      if (this.logTypeChart) this.logTypeChart.dispose()
    },
    
    // 格式化处理动作
    formatHandleAction(row, column, cellValue) {
      const actionMap = {
        '0': '允许',
        '1': '阻断',
        '2': '监控',
        '3': '警告'
      }
      return actionMap[cellValue] || cellValue || '未知'
    },
    
    // 格式化日志类型
    formatLogType(row, column, cellValue) {
      const typeMap = {
        'web_access': '网页访问',
        'statistic_traffic': '流量统计',
        'other_app': '应用访问',
        'file_transfer': '文件传输',
        'email': '邮件',
        'im': '即时通讯',
        'p2p': 'P2P下载',
        'game': '游戏',
        'video': '视频',
        'music': '音乐'
      }
      return typeMap[cellValue] || cellValue || '未知'
    },
    
    initCharts() {
      // 确保所有容器都存在且有宽度
      this.$nextTick(() => {
        this.initUrlCategoryChart()
        this.initUrlDomainChart()
        this.initAppChart()
        this.initdstIpChart()
        this.initUserGroupChart()
        this.initLogTypeChart()
        
        // 初始化完成后强制resize
        setTimeout(() => {
          this.resizeCharts()
        }, 100)
      })
    },
    
    initUrlCategoryChart() {
      if (!this.$refs.urlCategoryChart || !this.data.behaviorAnalysis || !this.data.behaviorAnalysis.urlCategoriesChart) {
        return
      }
      
      this.urlCategoryChart = echarts.init(this.$refs.urlCategoryChart)
      
      const urlCategories = this.data.behaviorAnalysis.urlCategoriesChart
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          bottom: '0%',
          left: 'center',
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '网站分类',
          type: 'pie',
          radius: ['0%', '60%'],
          center: ['50%', '45%'],
          data: urlCategories.map(item => ({
            name: item.name,
            value: item.value
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          label: {
            show: false
          }
        }]
      }
      
      this.urlCategoryChart.setOption(option)
    },
    
    initUrlDomainChart() {
      if (!this.$refs.urlDomainChart || !this.data.behaviorAnalysis || !this.data.behaviorAnalysis.urlDomainsChart) {
        return
      }
      
      this.urlDomainChart = echarts.init(this.$refs.urlDomainChart)
      
      const urlDomains = this.data.behaviorAnalysis.urlDomainsChart
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}: ${params[0].value} 次`
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: urlDomains.map(item => item.name),
          axisLabel: {
            fontSize: 10,
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        series: [{
          name: '访问次数',
          type: 'bar',
          data: urlDomains.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }]
      }
      
      this.urlDomainChart.setOption(option)
    },
    
    initAppChart() {
      if (!this.$refs.appChart || !this.data.behaviorAnalysis || !this.data.behaviorAnalysis.appNamesChart) {
        return
      }
      
      this.appChart = echarts.init(this.$refs.appChart)
      
      const appNames = this.data.behaviorAnalysis.appNamesChart
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}: ${params[0].value} 次`
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: appNames.map(item => item.name),
          axisLabel: {
            fontSize: 10,
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        series: [{
          name: '使用次数',
          type: 'bar',
          data: appNames.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#ffd666' },
              { offset: 0.5, color: '#faad14' },
              { offset: 1, color: '#fa8c16' }
            ])
          }
        }]
      }
      
      this.appChart.setOption(option)
    },
    
    initdstIpChart() {
      if (!this.$refs.dstIpChart || !this.data.behaviorAnalysis || !this.data.behaviorAnalysis.dstIpsChart) {
        return
      }
      
      this.dstIpChart = echarts.init(this.$refs.dstIpChart)
      
      const dstIps = this.data.behaviorAnalysis.dstIpsChart
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}: ${params[0].value} 次`
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dstIps.map(item => item.name),
          axisLabel: {
            fontSize: 10,
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 11
          }
        },
        series: [{
          name: '访问次数',
          type: 'bar',
          data: dstIps.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#b7eb8f' },
              { offset: 0.5, color: '#73d13d' },
              { offset: 1, color: '#52c41a' }
            ])
          }
        }]
      }
      
      this.dstIpChart.setOption(option)
    },
    
    initUserGroupChart() {
      if (!this.$refs.userGroupChart || !this.data.behaviorAnalysis || !this.data.behaviorAnalysis.userGroupsChart) {
        return
      }
      
      this.userGroupChart = echarts.init(this.$refs.userGroupChart)
      
      const userGroups = this.data.behaviorAnalysis.userGroupsChart
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          bottom: '0%',
          left: 'center',
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '用户组',
          type: 'pie',
          radius: ['0%', '60%'],
          center: ['50%', '45%'],
          data: userGroups.map(item => ({
            name: item.name,
            value: item.value
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          label: {
            show: false
          }
        }]
      }
      
      this.userGroupChart.setOption(option)
    },
    
    initLogTypeChart() {
      if (!this.$refs.logTypeChart || !this.data.behaviorAnalysis || !this.data.behaviorAnalysis.logTypesChart) {
        return
      }
      
      this.logTypeChart = echarts.init(this.$refs.logTypeChart)
      
      const logTypes = this.data.behaviorAnalysis.logTypesChart
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          bottom: '0%',
          left: 'center',
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '日志类型',
          type: 'pie',
          radius: ['0%', '60%'],
          center: ['50%', '45%'],
          data: logTypes.map(item => ({
            name: this.formatLogType(null, null, item.name),
            value: item.value
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          label: {
            show: false
          }
        }]
      }
      
      this.logTypeChart.setOption(option)
    },
    
    resizeCharts() {
      // 重新计算图表尺寸
      this.$nextTick(() => {
        if (this.urlCategoryChart && this.$refs.urlCategoryChart) {
          this.urlCategoryChart.resize()
        }
        if (this.urlDomainChart && this.$refs.urlDomainChart) {
          this.urlDomainChart.resize()
        }
        if (this.appChart && this.$refs.appChart) {
          this.appChart.resize()
        }
        if (this.dstIpChart && this.$refs.dstIpChart) {
          this.dstIpChart.resize()
        }
        if (this.userGroupChart && this.$refs.userGroupChart) {
          this.userGroupChart.resize()
        }
        if (this.logTypeChart && this.$refs.logTypeChart) {
          this.logTypeChart.resize()
        }
      })
    },
    
    // 初始化构建所有LogQL查询
    buildAllLogQLQueries() {
      if (!this.hostIp && !this.data.targetIp) {
        console.warn('H3C行为日志：缺少目标IP，无法构建LogQL查询')
        return
      }

      const targetIp = this.hostIp || this.data.targetIp

      // 构建四个不同类型的LogQL查询
      this.buildLogQLForTab('web_access', 'web_access', targetIp)
      this.buildLogQLForTab('app_access', 'other_app', targetIp)
      this.buildLogQLForTab('file_transfer', 'file_transfer', targetIp)
      this.buildLogQLForTab('all_logs', null, targetIp) // 全部日志不需要日志类型过滤
    },

    async buildLogQLForTab(tabName, logType, targetIp) {
      try {
        // 构建请求参数
        const params = {
          stream: 'AUDITLOG_H3C_BEHAVIOR',
          srcIp: targetIp,
          customFilters: []
        }

        // 添加日志类型过滤条件（如果指定）
        if (logType) {
          params.customFilters.push({
            field: 'log_type',
            operator: '=',
            value: logType
          })
        }

        // 处理时间范围
        if (this.data.startTime && this.data.endTime) {
          // 如果有具体的开始和结束时间，使用时间范围查询
          params.startTime = this.data.startTime
          params.endTime = this.data.endTime
        } else if (this.data.timeRange) {
          // 否则使用快速时间选择
          params.timeRange = convertTimeRange(this.data.timeRange, '30m')
        } else {
          // 默认时间范围
          params.timeRange = '30m'
        }

        console.log(`H3C行为日志构建${tabName}的LogQL请求参数:`, params)

        // 调用后端接口构建LogQL
        const response = await buildLogQL(params)

        if (response.code === 200) {
          this.logQueries[tabName] = response.data.logQL
          
          // 设置时间配置
          if (response.data.startTime && response.data.endTime) {
            this.timeConfig = {
              startTime: response.data.startTime,
              endTime: response.data.endTime
            }
          } else if (response.data.timeRange) {
            this.timeConfig = {
              quickTime: response.data.timeRange
            }
          }

          console.log(`H3C行为日志${tabName}后端构建的LogQL:`, this.logQueries[tabName])
          console.log(`H3C行为日志${tabName}时间配置:`, this.timeConfig)

          // 强制对应的日志查询组件重新渲染
          this.logQueryKeys[tabName] = Date.now()
        } else {
          console.error(`H3C行为日志构建${tabName}的LogQL失败:`, response.msg)
        }
      } catch (error) {
        console.error(`H3C行为日志构建${tabName}的LogQL异常:`, error)
      }
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

/* 全局字体大小设置为12px */
.h3c-behavior-log-result {
  font-size: 12px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 5px;
}

/* 日志详情区域样式 */
.log-details-section {
  margin-top: 30px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  padding: 0;
}

.custom-tabs-container {
  padding: 15px 15px 0px 15px;
}

.custom-tabs {
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden;
}

.custom-tab-item {
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
  position: relative;
  border-bottom: 2px solid transparent;
}

.custom-tab-item.active {
  color: #67C23A;
  font-weight: 500;
  border-bottom-color: #67C23A;
}

.tab-content {
  padding: 20px;
}

.charts-section {
  margin: 20px 0 30px 0;
}

.chart-container {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
}

.chart-container h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.chart {
  width: 100%;
  height: 350px;
  min-height: 350px;
  position: relative;
  display: block;
}

/* 确保图表容器的父级有正确的宽度 */
.charts-section .el-col {
  width: 100%;
  box-sizing: border-box;
}

.charts-section .el-row {
  width: 100%;
  margin: 0;
}

.no-data-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
  margin-top: 20px;
}

.no-data-placeholder p {
  font-size: 14px;
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .stat-icon {
    font-size: 24px;
    margin-right: 10px;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .custom-tabs {
    flex-wrap: wrap;
  }
  
  .custom-tab-item {
    padding: 6px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .statistics-cards {
    grid-template-columns: 1fr;
  }
}
</style>

<style>
/* 自定义表格样式 */
.log-message {
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 12px;
}

.el-table__row:hover .log-message {
  color: #409EFF;
}

/* 标签样式 */
.el-tag {
  margin-right: 5px;
  font-size: 12px;
}

/* 流量数据样式 */
.el-table__cell {
  font-size: 12px;
}
</style> 