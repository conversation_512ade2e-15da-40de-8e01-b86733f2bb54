<template>
  <div class="syslog-analysis-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>主机系统日志分析</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标主机：</strong>{{ data.targetIp }}</span>
        <span><strong>支持日志类型：</strong>{{ data.supportedStreams ? data.supportedStreams.length : 0 }} 种</span>
        <span><strong>分析时间：</strong>{{ data.timeRangeDescription || '最近1天' }}</span>
      </div>
    </div>

    <!-- Tab式日志查询 -->
    <div class="log-details-section" style="margin-top: 20px;">
      <div class="custom-tabs-container">
        <div class="custom-tabs">
          <div 
            class="custom-tab-item" 
            :class="{ 'active': activeTab === 'system_audit' }"
            @click="switchTab('system_audit')">
            系统审计日志
          </div>
          <div 
            class="custom-tab-item" 
            :class="{ 'active': activeTab === 'command' }"
            @click="switchTab('command')">
            命令日志
          </div>
          <div 
            class="custom-tab-item" 
            :class="{ 'active': activeTab === 'secure' }"
            @click="switchTab('secure')">
            安全日志
          </div>
          <div 
            class="custom-tab-item" 
            :class="{ 'active': activeTab === 'message' }"
            @click="switchTab('message')">
            系统消息
          </div>
          <div 
            class="custom-tab-item" 
            :class="{ 'active': activeTab === 'cron' }"
            @click="switchTab('cron')">
            计划任务
          </div>
        </div>
      </div>
      
      <!-- Tab内容区域 -->
      <div class="tab-content">
        <!-- 系统审计日志 -->
        <log-query-component
          v-if="activeTab === 'system_audit'"
          :key="logQueryKeys.system_audit"
          ref="systemAuditLogQuery"
          :title="'系统审计日志'"
          :subtitle="`查询主机 ${data.targetIp} 的系统审计记录`"
          :settings="logSettings"
          :defaultQuery="logQueries.system_audit"
          :externalTimeRange="timeConfig"
        />
        
        <!-- 命令日志 -->
        <log-query-component
          v-if="activeTab === 'command'"
          :key="logQueryKeys.command"
          ref="commandLogQuery"
          :title="'命令日志'"
          :subtitle="`查询主机 ${data.targetIp} 的命令执行记录`"
          :settings="logSettings"
          :defaultQuery="logQueries.command"
          :externalTimeRange="timeConfig"
        />
        
        <!-- 安全日志 -->
        <log-query-component
          v-if="activeTab === 'secure'"
          :key="logQueryKeys.secure"
          ref="secureLogQuery"
          :title="'安全日志'"
          :subtitle="`查询主机 ${data.targetIp} 的安全相关记录`"
          :settings="logSettings"
          :defaultQuery="logQueries.secure"
          :externalTimeRange="timeConfig"
        />
        
        <!-- 系统消息 -->
        <log-query-component
          v-if="activeTab === 'message'"
          :key="logQueryKeys.message"
          ref="messageLogQuery"
          :title="'系统消息'"
          :subtitle="`查询主机 ${data.targetIp} 的系统消息记录`"
          :settings="logSettings"
          :defaultQuery="logQueries.message"
          :externalTimeRange="timeConfig"
        />
        
        <!-- 计划任务 -->
        <log-query-component
          v-if="activeTab === 'cron'"
          :key="logQueryKeys.cron"
          ref="cronLogQuery"
          :title="'计划任务'"
          :subtitle="`查询主机 ${data.targetIp} 的计划任务记录`"
          :settings="logSettings"
          :defaultQuery="logQueries.cron"
          :externalTimeRange="timeConfig"
        />
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="!data.targetIp" class="no-data-placeholder">
      <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
      <p style="margin-top: 15px; color: #606266;">暂无主机信息</p>
      <p style="color: #909399; font-size: 12px;">
        请确认主机IP地址正确并重新进行分析
      </p>
    </div>
  </div>
</template>

<script>
import LogQueryComponent from '@/components/log_query_component'
import { convertTimeRange } from '@/utils/timeFormatter'
import { buildLogQL } from '@/api/log/logQuery'

export default {
  name: 'SyslogAnalysisResult',
  components: {
    LogQueryComponent
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'syslog_analysis'
    }
  },
  data() {
    return {
      activeTab: 'system_audit',
      logQueryKeys: {
        system_audit: 1,
        command: 1,
        secure: 1,
        message: 1,
        cron: 1
      },
      logQueries: {
        system_audit: '',
        command: '',
        secure: '',
        message: '',
        cron: ''
      },
      timeConfig: {},
      // 日志设置 - 参考host.vue的配置
      logSettings: {
        columns: [
          { prop: 'event_time', label: '时间', width: '180', sortable: true },
          { prop: '_msg', label: '消息内容', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      },
      // 流映射配置
      streamMapping: {
        system_audit: 'AUDITLOG_SYSTEM_AUDIT',
        command: 'AUDITLOG_SYSLOG_COMMAND',
        secure: 'AUDITLOG_SYSTEM_SECURE',
        message: 'AUDITLOG_SYSLOG_MESSAGE',
        cron: 'AUDITLOG_SYSLOG_CRON'
      }
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          // 当数据变化时，重新构建所有LogQL查询
          this.buildAllLogQLQueries()
        })
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 初始化时构建LogQL查询
      this.buildAllLogQLQueries()
    })
  },
  methods: {
    switchTab(tabName) {
      this.activeTab = tabName
      
      // 切换Tab时，如果当前Tab的查询为空，则构建查询
      if (!this.logQueries[tabName]) {
        this.buildLogQLForTab(tabName)
      }
    },
    
    async buildAllLogQLQueries() {
      if (!this.data.targetIp) {
        console.warn('SYSLOG分析：缺少目标IP，无法构建LogQL查询')
        return
      }

      try {
        // 为所有Tab构建LogQL查询
        for (const tabName of Object.keys(this.streamMapping)) {
          await this.buildLogQLForTab(tabName)
        }

        console.log('SYSLOG分析：所有LogQL查询构建完成')
      } catch (error) {
        console.error('SYSLOG分析构建LogQL异常:', error)
        this.$message.error('构建系统日志查询异常: ' + error.message)
      }
    },
    
    async buildLogQLForTab(tabName) {
      if (!this.data.targetIp || !this.streamMapping[tabName]) {
        return
      }

      try {
        // ✅ 正确：使用customFilters自定义条件过滤传递agent_hostip参数
        const params = {
          stream: this.streamMapping[tabName],
          customFilters: [
            { field: 'agent_hostip', operator: '=', value: this.data.targetIp }
          ]
        }

        // 处理时间范围
        if (this.data.startTime && this.data.endTime) {
          // 如果有具体的开始和结束时间，使用时间范围查询
          params.startTime = this.data.startTime
          params.endTime = this.data.endTime
        } else if (this.data.timeRange) {
          // 否则使用快速时间选择
          params.timeRange = convertTimeRange(this.data.timeRange, '1d')
        } else {
          // 默认时间范围
          params.timeRange = '1d'
        }

        console.log(`SYSLOG分析构建LogQL请求参数(${tabName}):`, params)

        // 调用后端接口构建LogQL
        const response = await buildLogQL(params)

        if (response.code === 200) {
          this.logQueries[tabName] = response.data.logQL
          
          // 设置时间配置（只需要设置一次）
          if (!this.timeConfig.quickTime && !this.timeConfig.startTime) {
            if (response.data.startTime && response.data.endTime) {
              this.timeConfig = {
                startTime: response.data.startTime,
                endTime: response.data.endTime
              }
            } else if (response.data.timeRange) {
              this.timeConfig = {
                quickTime: response.data.timeRange
              }
            }
          }

          console.log(`SYSLOG分析后端构建的LogQL(${tabName}):`, this.logQueries[tabName])

          // 强制对应Tab的日志查询组件重新渲染
          this.logQueryKeys[tabName] = Date.now()
        } else {
          console.error(`SYSLOG分析构建LogQL失败(${tabName}):`, response.msg)
          this.$message.error(`构建${this.getTabLabel(tabName)}查询失败: ` + response.msg)
        }
      } catch (error) {
        console.error(`SYSLOG分析构建LogQL异常(${tabName}):`, error)
        this.$message.error(`构建${this.getTabLabel(tabName)}查询异常: ` + error.message)
      }
    },
    
    getTabLabel(tabName) {
      const labelMap = {
        system_audit: '系统审计日志',
        command: '命令日志',
        secure: '安全日志',
        message: '系统消息',
        cron: '计划任务'
      }
      return labelMap[tabName] || tabName
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

.syslog-analysis-result {
  font-size: 12px;
}

/* 日志详情区域样式 */
.log-details-section {
  margin-top: 20px;
}

.custom-tabs-container {
  background: #f5f7fa;
  border-radius: 4px 4px 0 0;
  border: 1px solid #e4e7ed;
  border-bottom: none;
}

.custom-tabs {
  display: flex;
  padding: 0;
  margin: 0;
}

.custom-tab-item {
  padding: 12px 20px;
  cursor: pointer;
  border-right: 1px solid #e4e7ed;
  color: #606266;
  font-size: 12px;
  transition: all 0.3s ease;
  user-select: none;
  position: relative;
}

.custom-tab-item:last-child {
  border-right: none;
}

.custom-tab-item.active {
  background: white;
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  font-weight: 500;
}

.custom-tab-item:hover:not(.active) {
  background: #ecf5ff;
  color: #409EFF;
}

.tab-content {
  border: 1px solid #e4e7ed;
  border-radius: 0 0 4px 4px;
  background: white;
  min-height: 400px;
}

.no-data-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.no-data-placeholder p {
  font-size: 14px;
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-tabs {
    flex-wrap: wrap;
  }

  .custom-tab-item {
    padding: 8px 12px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .custom-tab-item {
    padding: 6px 8px;
    font-size: 10px;
  }
}
</style> 