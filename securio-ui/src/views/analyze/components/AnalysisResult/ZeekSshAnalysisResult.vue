<template>
  <div class="zeek-ssh-analysis-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
    </div>

    <!-- 统计信息卡片 -->
    <div v-if="totalStats" class="statistics-grid">
      <div class="stat-item" @click="() => {}">
        <div class="stat-icon" :style="{ background: `linear-gradient(135deg, #409EFF 0%, ${lightenColor('#409EFF')} 100%)` }">
          <i class="el-icon-connection"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalStats.totalSshConnections || 0 }}</div>
          <div class="stat-label">SSH连接总数</div>
        </div>
      </div>
      
      <div class="stat-item" @click="() => {}">
        <div class="stat-icon" :style="{ background: `linear-gradient(135deg, #67C23A 0%, ${lightenColor('#67C23A')} 100%)` }">
          <i class="el-icon-upload2"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalStats.totalOutboundConnections || 0 }}</div>
          <div class="stat-label">出站连接数</div>
        </div>
      </div>
      
      <div class="stat-item" @click="() => {}">
        <div class="stat-icon" :style="{ background: `linear-gradient(135deg, #E6A23C 0%, ${lightenColor('#E6A23C')} 100%)` }">
          <i class="el-icon-download"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalStats.totalInboundConnections || 0 }}</div>
          <div class="stat-label">入站连接数</div>
        </div>
      </div>
      
      <div class="stat-item" @click="() => {}">
        <div class="stat-icon" :style="{ background: `linear-gradient(135deg, #F56C6C 0%, ${lightenColor('#F56C6C')} 100%)` }">
          <i class="el-icon-share"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalStats.uniqueDestinations || 0 }}</div>
          <div class="stat-label">唯一目标服务器</div>
        </div>
      </div>
      
      <div class="stat-item" @click="() => {}">
        <div class="stat-icon" :style="{ background: `linear-gradient(135deg, #909399 0%, ${lightenColor('#909399')} 100%)` }">
          <i class="el-icon-coordinate"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalStats.uniqueSources || 0 }}</div>
          <div class="stat-label">唯一连接来源</div>
        </div>
      </div>
      
      <div class="stat-item" @click="() => {}">
        <div class="stat-icon" :style="{ background: `linear-gradient(135deg, #9C27B0 0%, ${lightenColor('#9C27B0')} 100%)` }">
          <i class="el-icon-s-grid"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalStats.totalUniqueHosts || 0 }}</div>
          <div class="stat-label">关联主机总数</div>
        </div>
      </div>
    </div>

    <!-- 图表展示区域 -->
    <el-row v-if="data.outboundAnalysis || data.inboundAnalysis" :gutter="20" style="margin-top: 20px;">
      <el-col :span="12" >
        <div class="chart-container">
          <h4>出站SSH连接目标分布（Top 10）</h4>
          <div ref="outboundChart" class="chart"></div>
        </div>
      </el-col>
      <el-col :span="12" >
        <div class="chart-container">
          <h4>入站SSH连接来源分布（Top 10）</h4>
          <div ref="inboundChart" class="chart"></div>
        </div>
      </el-col>
    </el-row>

    <!-- SSH连接详细信息表格 - 并排显示 -->
    <div v-if="(outboundDestinations && outboundDestinations.length > 0) || (inboundSources && inboundSources.length > 0)" 
         class="connection-details" style="margin-top: 20px;">
      <h4>SSH连接详细统计</h4>
      
      <el-row :gutter="20" class="connection-tables">
        <!-- 出站连接表格 -->
        <el-col :span="12" >
          <div class="connection-table">
            <h5><i class="el-icon-upload2"></i> 出站连接目标</h5>
            <el-table :data="outboundDestinations" size="small" max-height="300" stripe>
              <el-table-column prop="ip" label="目标IP" min-width="120" show-overflow-tooltip></el-table-column>
              <el-table-column prop="count" label="连接次数" width="100" sortable align="center">
                <template #default="scope">
                  <el-tag size="small" type="primary">{{ scope.row.count }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>

        <!-- 入站连接表格 -->
        <el-col :span="12" >
          <div class="connection-table">
            <h5><i class="el-icon-download"></i> 入站连接来源</h5>
            <el-table :data="inboundSources" size="small" max-height="300" stripe>
              <el-table-column prop="ip" label="来源IP" min-width="120" show-overflow-tooltip></el-table-column>
              <el-table-column prop="count" label="连接次数" width="100" sortable align="center">
                <template #default="scope">
                  <el-tag size="small" type="success">{{ scope.row.count }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- Tab式日志查询 -->
    <div class="log-details-section" style="margin-top: 20px;">
      <div class="custom-tabs-container">
        <div class="custom-tabs">
          <div class="custom-tab-item" 
               :class="{ 'active': activeTab === 'outbound_ssh' }"
               @click="switchTab('outbound_ssh')">
            出站SSH连接日志
          </div>
          <div class="custom-tab-item" 
               :class="{ 'active': activeTab === 'inbound_ssh' }"
               @click="switchTab('inbound_ssh')">
            入站SSH连接日志
          </div>
          <div class="custom-tab-item" 
               :class="{ 'active': activeTab === 'all_ssh' }"
               @click="switchTab('all_ssh')">
            全部SSH连接日志
          </div>
        </div>
      </div>
      
      <div class="tab-content">
        <!-- 出站SSH连接日志 -->
        <div v-show="activeTab === 'outbound_ssh'">
          <log-query-component
            ref="outboundLogQuery"
            :key="`outbound-${logQueryKey}`"
            :title="'出站SSH连接日志'"
            :subtitle="`查询主机 ${data.targetIp} 发起的SSH连接记录`"
            instance="vmlog2"
            :settings="sshLogSettings"
            :defaultQuery="logQueries.outbound_ssh"
            :externalTimeRange="timeConfig"
          />
        </div>

        <!-- 入站SSH连接日志 -->
        <div v-show="activeTab === 'inbound_ssh'">
          <log-query-component
            ref="inboundLogQuery"
            :key="`inbound-${logQueryKey}`"
            :title="'入站SSH连接日志'"
            :subtitle="`查询连接到主机 ${data.targetIp} 的SSH记录`"
            instance="vmlog2"
            :settings="sshLogSettings"
            :defaultQuery="logQueries.inbound_ssh"
            :externalTimeRange="timeConfig"
          />
        </div>

        <!-- 全部SSH连接日志 -->
        <div v-show="activeTab === 'all_ssh'">
          <log-query-component
            ref="allLogQuery"
            :key="`all-${logQueryKey}`"
            :title="'全部SSH连接日志'"
            :subtitle="`查询主机 ${data.targetIp} 相关的所有SSH连接记录`"
            instance="vmlog2"
            :settings="sshLogSettings"
            :defaultQuery="logQueries.all_ssh"
            :externalTimeRange="timeConfig"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import LogQueryComponent from '@/components/log_query_component'
import { convertTimeRange } from '@/utils/timeFormatter'
import { buildLogQL } from '@/api/log/logQuery'

export default {
  name: 'ZeekSshAnalysisResult',
  components: {
    LogQueryComponent
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'zeek_ssh_analysis'
    },
    hostIp: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      activeTab: 'outbound_ssh',
      logQueryKey: 1,
      logQueries: {
        outbound_ssh: '',
        inbound_ssh: '',
        all_ssh: ''
      },
      timeConfig: {},
      
      // SSH日志组件设置
      sshLogSettings: {
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          { prop: 'src_ip', label: '源IP', width: '140' },
          { prop: 'src_port', label: '源端口', width: '80' },
          { prop: 'dst_ip', label: '目标IP', width: '140' },
          { prop: 'dst_port', label: '目标端口', width: '80' },
          { prop: 'auth_success', label: '认证成功', width: '100' },
          { prop: 'auth_attempts', label: '认证尝试', width: '100' },
          { prop: 'client', label: '客户端', width: '150', showOverflowTooltip: true },
          { prop: 'server', label: '服务端', width: '150', showOverflowTooltip: true },
          { prop: 'cipher_alg', label: '加密算法', width: '120', showOverflowTooltip: true },
          { prop: 'mac_alg', label: 'MAC算法', width: '120', showOverflowTooltip: true },
          { prop: 'kex_alg', label: '密钥交换', width: '120', showOverflowTooltip: true }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  computed: {
    totalStats() {
      return this.data.totalStats || {}
    },
    outboundDestinations() {
      return this.data.outboundAnalysis?.destinationList || []
    },
    inboundSources() {
      return this.data.inboundAnalysis?.sourceList || []
    }
  },
  mounted() {
    this.initializeComponent()
  },
  methods: {
    async initializeComponent() {
      try {
        // 构建所有Tab的LogQL查询
        await this.buildAllLogQLQueries()
        
        // 初始化图表
        this.$nextTick(() => {
          this.initCharts()
        })
      } catch (error) {
        console.error('初始化Zeek SSH分析组件失败:', error)
      }
    },

    async buildAllLogQLQueries() {
      try {
        const timeRange = convertTimeRange(this.data.timeRange, '1d')
        
        // 构建出站SSH连接查询
        const outboundParams = {
          stream: 'SECURIO_ZEEK_SSH',
          customFilters: [
            { field: 'src_ip', operator: '=', value: this.data.targetIp }
          ],
          timeRange: timeRange
        }
        
        // 构建入站SSH连接查询
        const inboundParams = {
          stream: 'SECURIO_ZEEK_SSH',
          customFilters: [
            { field: 'dst_ip', operator: '=', value: this.data.targetIp }
          ],
          timeRange: timeRange
        }
        
        // 构建全部SSH连接查询
        const allParams = {
          stream: 'SECURIO_ZEEK_SSH',
          timeRange: timeRange
        }

        // 并行构建所有查询
        const [outboundResponse, inboundResponse, allResponse] = await Promise.all([
          buildLogQL(outboundParams),
          buildLogQL(inboundParams),
          buildLogQL(allParams)
        ])

        if (outboundResponse.code === 200) {
          this.logQueries.outbound_ssh = outboundResponse.data.logQL
          this.timeConfig = outboundResponse.data.timeConfig
        }

        if (inboundResponse.code === 200) {
          this.logQueries.inbound_ssh = inboundResponse.data.logQL
        }

        if (allResponse.code === 200) {
          this.logQueries.all_ssh = allResponse.data.logQL
        }

        this.logQueryKey = Date.now()
      } catch (error) {
        console.error('构建SSH LogQL查询异常:', error)
        this.$message.error('构建SSH查询语句异常: ' + error.message)
      }
    },

    switchTab(tabName) {
      this.activeTab = tabName
    },

    initCharts() {
      // 延迟初始化图表，确保DOM已经渲染
      this.$nextTick(() => {
        setTimeout(() => {
          // 初始化出站连接图表
          if (this.outboundDestinations.length > 0 && this.$refs.outboundChart) {
            this.initOutboundChart()
          }
          
          // 初始化入站连接图表
          if (this.inboundSources.length > 0 && this.$refs.inboundChart) {
            this.initInboundChart()
          }
        }, 200) // 延迟200ms确保DOM完全渲染
      })
    },

    initOutboundChart() {
      try {
        if (!this.$refs.outboundChart) {
          console.warn('出站连接图表容器未找到')
          return
        }

        const chart = echarts.init(this.$refs.outboundChart)
        
        const data = this.outboundDestinations.slice(0, 10).map(item => ({
          name: item.ip,
          value: item.count
        }))

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            show: false
          },
          series: [
            {
              name: '出站连接',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['50%', '50%'],
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              label: {
                show: true,
                formatter: '{b}\n{c}次',
                fontSize: 11
              },
              labelLine: {
                show: true
              }
            }
          ]
        }

        chart.setOption(option)
        
        // 响应式调整
        const resizeHandler = () => {
          chart.resize()
        }
        window.addEventListener('resize', resizeHandler)
        
        // 组件销毁时移除监听器
        this.$once('hook:beforeDestroy', () => {
          window.removeEventListener('resize', resizeHandler)
          chart.dispose()
        })
        
        console.log('出站连接图表初始化成功')
      } catch (error) {
        console.error('初始化出站连接图表失败:', error)
      }
    },

    initInboundChart() {
      try {
        if (!this.$refs.inboundChart) {
          console.warn('入站连接图表容器未找到')
          return
        }

        const chart = echarts.init(this.$refs.inboundChart)
        
        const data = this.inboundSources.slice(0, 10).map(item => ({
          name: item.ip,
          value: item.count
        }))

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            show: false
          },
          series: [
            {
              name: '入站连接',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['50%', '50%'],
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              label: {
                show: true,
                formatter: '{b}\n{c}次',
                fontSize: 11
              },
              labelLine: {
                show: true
              }
            }
          ]
        }

        chart.setOption(option)
        
        // 响应式调整
        const resizeHandler = () => {
          chart.resize()
        }
        window.addEventListener('resize', resizeHandler)
        
        // 组件销毁时移除监听器
        this.$once('hook:beforeDestroy', () => {
          window.removeEventListener('resize', resizeHandler)
          chart.dispose()
        })
        
        console.log('入站连接图表初始化成功')
      } catch (error) {
        console.error('初始化入站连接图表失败:', error)
      }
    },

    lightenColor(color) {
      // 将颜色变浅30%
      const hex = color.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)
      
      const lightenedR = Math.min(255, Math.floor(r + (255 - r) * 0.3))
      const lightenedG = Math.min(255, Math.floor(g + (255 - g) * 0.3))
      const lightenedB = Math.min(255, Math.floor(b + (255 - b) * 0.3))
      
      return `#${lightenedR.toString(16).padStart(2, '0')}${lightenedG.toString(16).padStart(2, '0')}${lightenedB.toString(16).padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

.zeek-ssh-analysis-result {
  font-size: 12px;
}

/* 统计卡片网格 - 参考主机资产信息组件样式 */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 16px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  background-color: #FFFFFF;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-size: 12px;
  cursor: pointer;
}

.stat-item:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.stat-item:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.stat-icon i {
  font-size: 20px;
  color: #FFFFFF;
}

.stat-content {
  flex: 1;
  text-align: left;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  line-height: 1;
}

/* 图表容器样式 */
.chart-container {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}

.chart {
  height: 300px;
  width: 100%;
  min-height: 300px;
}

/* 连接详情样式 */
.connection-details {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.connection-details h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.connection-tables {
  margin-top: 0;
}

.connection-table {
  background: white;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.connection-table h5 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-table h5 i {
  color: #409eff;
}

/* Tab样式 */
.log-details-section {
  margin-top: 20px;
}

.custom-tabs-container {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;
}

.custom-tabs {
  display: flex;
  gap: 0;
}

.custom-tab-item {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #606266;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  margin-right: -1px;
  position: relative;
}

.custom-tab-item:first-child {
  border-top-left-radius: 6px;
}

.custom-tab-item:last-child {
  border-top-right-radius: 6px;
  margin-right: 0;
}

.custom-tab-item:hover {
  color: #409eff;
  background: #ecf5ff;
}

.custom-tab-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background: white;
  font-weight: 600;
  z-index: 1;
}

.tab-content {
  background: white;
  border-radius: 0 0 6px 6px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-top: none;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .statistics-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }
  
  .stat-item {
    padding: 15px;
  }
  
  .stat-icon {
    margin-right: 10px;
  }
  
  .stat-icon i {
    font-size: 20px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .connection-tables .el-col {
    margin-bottom: 15px;
  }
}

/* Element UI 表格样式覆盖 */
.connection-table >>> .el-table {
  font-size: 12px;
}

.connection-table >>> .el-table th {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 600;
  padding: 8px 0;
}

.connection-table >>> .el-table td {
  padding: 8px 0;
}

.connection-table >>> .el-tag {
  font-size: 11px;
}

.connection-table >>> .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #fafafa;
}

/* 确保图表容器有明确的尺寸 */
.chart-container .chart {
  width: 100% !important;
  height: 300px !important;
}
</style> 