<template>
  <div class="nginx-access-log-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标IP：</strong>{{ data.targetIp }}</span>
        <span><strong>时间范围：</strong>{{ data.timeRange }}</span>
      </div>
    </div>

    <!-- 桑基图展示 -->
    <div v-if="data.sankeyData" class="sankey-section">
      <div class="section-header">
        <h4>网络流量关系图</h4>
        <div class="chart-legend">
          <span class="legend-item">
            <span class="legend-color source"></span>
            来源IP
          </span>
          <span class="legend-item">
            <span class="legend-color target"></span>
            目标IP
          </span>
          <span class="legend-item">
            <span class="legend-color upstream"></span>
            上游IP
          </span>
        </div>
      </div>
      
      <div v-if="hasValidFlowData" >
        <IPFlowSankeyChart 
          :data="data.sankeyData" 
          height="600px"
          :options="sankeyOptions"
          :show-own-drawer="true"
          @node-click="handleNodeClick"
          @link-click="handleLinkClick" />
      </div>
      <div v-else class="no-data-placeholder">
        <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
        <p style="margin-top: 15px; color: #606266;">暂无网络流量数据</p>
        <p style="color: #909399; font-size: 12px;">在该时间段内未发现相关的Nginx访问日志记录</p>
        <div v-if="data.sankeyData && data.sankeyData.nodes && data.sankeyData.nodes.length === 1" class="single-node-info">
          <el-divider></el-divider>
          <p style="color: #606266; font-size: 12px;">
            <i class="el-icon-info"></i>
            目标IP {{ data.targetIp }} 在此时间段内没有与其他主机的网络流量交互
          </p>
        </div>
      </div>
    </div>

    <!-- 连线日志查询对话框 -->
    <el-dialog
      :title="`连线详细日志 - ${selectedLinkInfo.source} → ${selectedLinkInfo.target}`"
      :visible.sync="showLinkLogDialog"
      width="90%"
      top="5vh"
      append-to-body>
      <div style="padding: 10px;">
        <div class="link-info-summary">
          <div class="info-item">
            <span class="label">来源IP：</span>
            <span class="value">{{ selectedLinkInfo.source }}</span>
          </div>
          <div class="info-item">
            <span class="label">目标IP：</span>
            <span class="value">{{ selectedLinkInfo.target }}</span>
          </div>
          <div class="info-item">
            <span class="label">请求数：</span>
            <span class="value">{{ selectedLinkInfo.value.toLocaleString() }}</span>
          </div>
        </div>
        
        <log-query-component
          :key="queryComponentKey"
          :title="`Nginx访问日志`"
          :subtitle="`${selectedLinkInfo.source} → ${selectedLinkInfo.target} 的详细访问记录`"
          :default-query="linkLogQuery"
          :default-time-range="'custom'"
          :external-time-range="linkLogTimeConfig"
          stream="NGINX_ACCESS"
          instance="vmlog2"
          :settings="{
            columns: [
              { prop: 'event_time', label: '时间', width: '180', sortable: true },
              { prop: 'src_ip', label: '来源IP', width: '120' },
              { prop: 'dst_ip', label: '目标IP', width: '120' },
              { prop: 'dst_port', label: '端口', width: '80' },
              { prop: 'message.request_method', label: '方法', width: '80' },
              { prop: 'message.request_uri', label: '请求URI', showOverflowTooltip: true, minWidth: '200' },
              { 
                prop: 'message.status', 
                label: '状态码', 
                width: '80',
                formatter: (row, column, cellValue) => {
                  return this.formatStatusCode(cellValue)
                }
              },
              { 
                prop: 'message.body_bytes_sent', 
                label: '响应大小', 
                width: '100',
                formatter: (row, column, cellValue) => {
                  return this.formatBytes(cellValue)
                }
              },
              { 
                prop: 'message.request_time', 
                label: '耗时(s)', 
                width: '100',
                formatter: (row, column, cellValue) => {
                  return this.formatRequestTime(cellValue)
                }
              },
              { prop: 'message.http_host', label: '主机名', width: '180', showOverflowTooltip: true },
              { prop: 'message.remote_addr', label: '客户端IP', width: '120' },
              { prop: 'message.http_x_forwarded_for', label: 'X-Forwarded-For', width: '150', showOverflowTooltip: true },
              { prop: 'message.upstream', label: '上游服务器', width: '120' },
              { 
                prop: 'message.upstream_response_time', 
                label: '上游耗时(s)', 
                width: '120',
                formatter: (row, column, cellValue) => {
                  return this.formatRequestTime(cellValue)
                }
              },
              { prop: 'message.http_user_agent', label: 'User Agent', showOverflowTooltip: true, minWidth: '200' }
            ],
            showDetailButton: true,
            detailButtonWidth: '80'
          }"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import IPFlowSankeyChart from '../IPFlowSankeyChart.vue'
import LogQueryComponent from '@/components/log_query_component.vue'
import { buildLogQL } from '@/api/log/logQuery'

export default {
  name: 'NginxAccessLogResult',
  components: {
    IPFlowSankeyChart,
    LogQueryComponent
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    analyzerType: {
      type: String,
      default: 'nginx_access_log'
    }
  },
  data() {
    return {
      sankeyOptions: {
        nodeGap: 20,
        nodeWidth: 30,
        layoutIterations: 50
      },
      showLinkLogDialog: false,
      selectedLinkInfo: {
        source: '',
        target: '',
        value: 0
      },
      linkLogQuery: '',
      linkLogTimeConfig: null,
      queryComponentKey: 0  // 添加key来强制重新渲染
    }
  },
  computed: {
    // 判断是否有有效的流向数据（不仅仅是节点，还要有连接）
    hasValidFlowData() {
      return this.data.sankeyData && 
             this.data.sankeyData.nodes && 
             this.data.sankeyData.nodes.length > 0 &&
             this.data.sankeyData.links && 
             this.data.sankeyData.links.length > 0
    }
  },
  methods: {
    handleNodeClick(nodeInfo) {
      // IPFlowSankeyChart组件已经处理了节点点击和主机信息抽屉显示
      // 这里只需要处理额外的业务逻辑（如果有的话）
      console.log('Nginx分析器收到节点点击事件:', nodeInfo)
      
      // 如果需要额外的处理逻辑，可以在这里添加
      // 例如：记录用户行为、更新状态等
    },
    
    handleLinkClick(linkInfo) {
      console.log('处理连线点击，连线信息:', linkInfo)
      
      // 安全检查linkInfo对象
      if (!linkInfo) {
        console.warn('无效的连线信息:', linkInfo)
        return
      }
      
      // 保存选中的连线信息
      this.selectedLinkInfo = {
        source: linkInfo.source || '',
        target: linkInfo.target || '',
        value: linkInfo.value || 0
      }
      
      // 使用后端接口构建查询语句
      this.buildLogQLForLink()
    },
    
    async buildLogQLForLink() {
      try {
        // 获取分析结果中的时间信息
        const timeRange = this.data.timeRange || '30m'
        const startTime = this.data.startTime
        const endTime = this.data.endTime
        
        // 构建请求参数
        const params = {
          stream: 'NGINX_ACCESS',
          srcIp: this.selectedLinkInfo.source,
          dstIp: this.selectedLinkInfo.target
        }
        
        // 如果有具体的开始和结束时间，使用时间范围查询
        if (startTime && endTime) {
          params.startTime = startTime
          params.endTime = endTime
        } else {
          // 否则使用快速时间选择
          params.timeRange = timeRange
        }
        
        console.log('构建LogQL请求参数:', params)
        
        // 调用后端接口构建LogQL
        const response = await buildLogQL(params)
        
        if (response.code === 200) {
          this.linkLogQuery = response.data.logQL
          console.log('后端构建的LogQL查询语句:', this.linkLogQuery)
          
          // 设置日志查询组件的外部时间参数
          this.linkLogTimeConfig = null
          if (response.data.startTime && response.data.endTime) {
            this.linkLogTimeConfig = {
              startTime: response.data.startTime,
              endTime: response.data.endTime
            }
          } else if (response.data.timeRange) {
            this.linkLogTimeConfig = {
              quickTime: response.data.timeRange
            }
          }
          
          // 强制日志查询组件重新渲染
          this.queryComponentKey += 1
          
          // 显示日志查询对话框
          this.showLinkLogDialog = true
        } else {
          console.error('构建LogQL失败:', response.msg)
          this.$message.error('构建查询语句失败: ' + response.msg)
        }
      } catch (error) {
        console.error('构建LogQL异常:', error)
        this.$message.error('构建查询语句异常: ' + error.message)
      }
    },
    
    calculateChartHeight() {
      if (!this.data || !this.data.sankeyData || !this.data.sankeyData.nodes) {
        return '600px'
      }
      
      const nodeCount = this.data.sankeyData.nodes.length
      
      // 根据节点数量动态计算高度
      // 基础高度400px，每个节点增加20px
      const baseHeight = 400
      const heightPerNode = 20
      const calculatedHeight = baseHeight + (nodeCount * heightPerNode)
      
      // 设置最小高度500px，最大高度1200px
      const minHeight = 500
      const maxHeight = 600
      const finalHeight = Math.max(minHeight, Math.min(maxHeight, calculatedHeight))
      
      console.log(`节点数量: ${nodeCount}, 计算高度: ${finalHeight}px`)
      
      return `${finalHeight}px`
    },
    formatStatusCode(value) {
      if (!value) return '-'
      const status = parseInt(value)
      if (isNaN(status)) return value
      
      // 根据状态码添加颜色标识
      if (status >= 200 && status < 300) {
        return value // 成功：绿色（通过CSS类处理）
      } else if (status >= 300 && status < 400) {
        return value // 重定向：蓝色
      } else if (status >= 400 && status < 500) {
        return value // 客户端错误：橙色
      } else if (status >= 500) {
        return value // 服务端错误：红色
      }
      return value
    },
    formatBytes(value) {
      if (!value || value === '-') return '-'
      const bytes = parseInt(value)
      if (isNaN(bytes)) return value
      
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return (bytes / Math.pow(k, i)).toFixed(1) + ' ' + sizes[i]
    },
    formatRequestTime(value) {
      if (!value || value === '-') return '-'
      const time = parseFloat(value)
      if (isNaN(time)) return value
      
      if (time === 0) return '0ms'
      
      // 如果小于1秒，显示毫秒
      if (time < 1) {
        return (time * 1000).toFixed(0) + 'ms'
      } else {
        // 大于1秒，显示秒数
        return time.toFixed(3) + 's'
      }
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

.nginx-access-log-result {
  font-size: 12px;
}

.sankey-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.chart-legend {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.source {
  background-color: #ff7875;
}

.legend-color.target {
  background-color: #40a9ff;
}

.legend-color.upstream {
  background-color: #73d13d;
}

.sankey-container {
  width: 100%;
  min-height: 500px;
  max-height: 1200px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: white;
  padding: 10px;
  overflow: auto;
  position: relative;
}

.no-data-placeholder {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.no-data-placeholder p {
  font-size: 14px;
  margin: 8px 0;
}

/* 连线信息摘要样式 */
.link-info-summary {
  display: flex;
  justify-content: space-around;
  padding: 15px;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.info-item .label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.info-item .value {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.single-node-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.single-node-info p {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.single-node-info i {
  color: #409EFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .chart-legend {
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}
</style> 