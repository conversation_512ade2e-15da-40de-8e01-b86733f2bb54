<template>
  <div class="host-asset-info">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
    </div>
    <!-- 资产卡片网格 -->
    <div v-loading="loading" class="asset-grid">
      <div class="asset-item" 
           v-for="(item, key) in assetItems" 
           :key="key"
           v-if="showAllAssets || assetStats[key] > 0"
           @click="openAssetDetail(key, item.label)">
        <div class="asset-icon" :style="{ background: `linear-gradient(135deg, ${item.color} 0%, ${lightenColor(item.color)} 100%)` }">
          <i :class="item.icon"></i>
        </div>
        <div class="asset-content">
          <div class="asset-number">{{ assetStats[key] || 0 }}</div>
          <div class="asset-label">{{ item.label }}</div>
        </div>
      </div>
      
      <!-- 无数据时显示 -->
      <div v-if="!loading && Object.keys(assetStats).length > 0 && Object.values(assetStats).every(val => val === 0)" class="no-data-tip">
        <i class="el-icon-info"></i>
        <p>暂无资产数据</p>
      </div>
    </div>


    <!-- 资产详情弹窗 -->
    <el-dialog :title="`主机${activeAssetTypeLabel}详情`" :visible.sync="showAssetDetailDialog" width="60%" top="5vh" >
      <host-inventory-table-info 
        v-if="currentHostIp" 
        :host-ip="currentHostIp"
        :asset-type="activeAssetType"
        ref="hostInventoryInfo" />
    </el-dialog>
  </div>
</template>

<script>
import { getInventoryStats } from '@/api/agent/inventory'
import HostInventoryTableInfo from '../HostInventoryTableInfo/index.vue'

export default {
  name: 'HostAssetInfo',
  components: {
    HostInventoryTableInfo
  },
  props: {
    hostIp: {
      type: String,
      required: false  // 当作为分析器结果组件时，hostIp可能来自data
    },
    // 是否自动加载数据
    autoLoad: {
      type: Boolean,
      default: true
    },
    // 是否显示所有资产卡片（包括数量为0的）
    showAllAssets: {
      type: Boolean,
      default: false
    },
    // 分析数据
    analysisData: {
      type: Object,
      default: null
    },
    // 分析器结果数据（当作为分析器结果组件使用时）
    data: {
      type: Object,
      default: null
    },
    // 分析器类型（当作为分析器结果组件使用时）
    analyzerType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      assetStats: {},
      assetItems: {
        userCount: { label: '用户', icon: 'el-icon-user', color: '#409EFF' },
        processCount: { label: '进程', icon: 'el-icon-cpu', color: '#67C23A' },
        portCount: { label: '网络连接', icon: 'el-icon-connection', color: '#E6A23C' },
        softwareCount: { label: '软件', icon: 'el-icon-box', color: '#F56C6C' },
        startupCount: { label: '启动项', icon: 'el-icon-video-play', color: '#909399' },
        scheduledTaskCount: { label: '计划任务', icon: 'el-icon-timer', color: '#9C27B0' },
        kernelModuleCount: { label: '内核模块', icon: 'el-icon-cpu', color: '#FF9800' },
        networkInterfaceCount: { label: '网络接口', icon: 'el-icon-connection', color: '#00BCD4' },
        dockerImageCount: { label: 'Docker镜像', icon: 'el-icon-picture-outline', color: '#1E88E5' },
        dockerContainerCount: { label: 'Docker容器', icon: 'el-icon-box', color: '#43A047' },
        dockerNetworkCount: { label: 'Docker网络', icon: 'el-icon-share', color: '#26A69A' },
        riskCount: { label: '风险项', icon: 'el-icon-warning', color: '#D32F2F' }
      },
      showAssetDetailDialog: false,
      activeAssetType: null,
      activeAssetTypeLabel: '',
      activeTab: 'statistics'  // 默认显示统计标签页
    }
  },
  computed: {
    // 获取当前使用的主机IP
    currentHostIp() {
      // 优先从data prop中获取（分析器结果）
      if (this.data && this.data.hostIp) {
        return this.data.hostIp
      }
      // 其次从hostIp prop获取
      return this.hostIp
    }
  },
  created() {
    console.log('HostAssetInfo组件创建，props:', {
      hostIp: this.hostIp,
      data: this.data,
      analyzerType: this.analyzerType,
      currentHostIp: this.currentHostIp
    })
    
    // 如果是分析器结果数据，直接从data加载
    if (this.data && this.data.assetStats) {
      console.log('使用分析器结果数据加载资产统计')
      this.loadAssetStatsFromData(this.data)
    } else if (this.data) {
      console.log('分析器结果数据中没有assetStats字段:', this.data)
      this.loadAssetStatsFromData(this.data)
    } else if (this.autoLoad && this.currentHostIp) {
      console.log('使用API加载资产统计')
      this.fetchAssetStats()
    } else {
      console.log('未满足加载条件，autoLoad:', this.autoLoad, 'currentHostIp:', this.currentHostIp)
    }
  },
  methods: {
    fetchAssetStats() {
      if (!this.currentHostIp) {
        this.$message.warning('主机IP不能为空')
        return
      }
      
      this.loading = true
      getInventoryStats(this.currentHostIp)
        .then(response => {
          if (response.code === 200) {
            // 数据字段映射
            const rawData = response.data || {}
            this.assetStats = {
              userCount: rawData.users || 0,
              processCount: rawData.processes || 0,
              portCount: rawData.ports || 0,
              softwareCount: rawData.software || 0,
              startupCount: rawData.startups || 0,
              scheduledTaskCount: rawData.scheduledTasks || 0,
              kernelModuleCount: rawData.kernelModules || 0,
              networkInterfaceCount: rawData.networkInterfaces || 0, // 修改字段名
              dockerImageCount: rawData.dockerImages || 0,
              dockerContainerCount: rawData.dockerContainers || 0,
              dockerNetworkCount: rawData.dockerNetworks || 0,
              riskCount: rawData.risks || 0
            }
            this.$emit('stats-loaded', this.assetStats)
          } else {
            this.$message.error(response.msg || '获取资产统计信息失败')
          }
        })
        .catch(error => {
          console.error('获取资产统计信息失败:', error)
          this.$message.error('获取资产统计信息失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    
    lightenColor(color) {
      // 将颜色变浅30%
      const hex = color.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)
      
      const lightenedR = Math.min(255, Math.floor(r + (255 - r) * 0.3))
      const lightenedG = Math.min(255, Math.floor(g + (255 - g) * 0.3))
      const lightenedB = Math.min(255, Math.floor(b + (255 - b) * 0.3))
      
      return `#${lightenedR.toString(16).padStart(2, '0')}${lightenedG.toString(16).padStart(2, '0')}${lightenedB.toString(16).padStart(2, '0')}`
    },
    
    openAssetDetail(key, label) {
      this.activeAssetType = key
      this.activeAssetTypeLabel = label
      this.activeTab = 'statistics'  // 每次打开对话框时重置为统计标签页
      this.showAssetDetailDialog = true
    },
    
    // 供父组件调用的刷新方法
    refresh() {
      this.fetchAssetStats()
    },
    
    // 新增方法：从分析结果中加载资产统计
    loadAssetStatsFromAnalysisData(analysisData) {
      if (!analysisData || !analysisData.analyzerResults || !analysisData.analyzerResults.host_asset) {
        console.log('分析结果中没有资产信息')
        return
      }
      
      const hostAssetResult = analysisData.analyzerResults.host_asset
      if (hostAssetResult.success && hostAssetResult.data && hostAssetResult.data.assetStats) {
        const rawData = hostAssetResult.data.assetStats
        this.assetStats = {
          userCount: rawData.users || 0,
          processCount: rawData.processes || 0,
          portCount: rawData.ports || 0,
          softwareCount: rawData.software || 0,
          startupCount: rawData.startups || 0,
          scheduledTaskCount: rawData.scheduledTasks || 0,
          kernelModuleCount: rawData.kernelModules || 0,
          networkInterfaceCount: rawData.networkInterfaces || 0,
          dockerImageCount: rawData.dockerImages || 0,
          dockerContainerCount: rawData.dockerContainers || 0,
          dockerNetworkCount: rawData.dockerNetworks || 0,
          riskCount: rawData.risks || 0
        }
        console.log('从分析结果加载资产统计数据:', this.assetStats)
        this.$emit('stats-loaded', this.assetStats)
      }
    },
    loadAssetStatsFromData(data) {
      console.log('loadAssetStatsFromData收到数据:', data)
      
      // 如果data包含assetStats字段，直接映射
      if (data && data.assetStats) {
        console.log('从data.assetStats加载数据:', data.assetStats)
        const rawData = data.assetStats
        this.assetStats = {
          userCount: rawData.users || 0,
          processCount: rawData.processes || 0,
          portCount: rawData.ports || 0,
          softwareCount: rawData.software || 0,
          startupCount: rawData.startups || 0,
          scheduledTaskCount: rawData.scheduledTasks || 0,
          kernelModuleCount: rawData.kernelModules || 0,
          networkInterfaceCount: rawData.networkInterfaces || 0,
          dockerImageCount: rawData.dockerImages || 0,
          dockerContainerCount: rawData.dockerContainers || 0,
          dockerNetworkCount: rawData.dockerNetworks || 0,
          riskCount: rawData.risks || 0
        }
      } else if (data) {
        console.log('尝试直接使用data作为统计数据:', data)
        // 如果data直接就是统计数据，检查是否有预期的字段
        if (data.users !== undefined || data.processes !== undefined) {
          this.assetStats = {
            userCount: data.users || 0,
            processCount: data.processes || 0,
            portCount: data.ports || 0,
            softwareCount: data.software || 0,
            startupCount: data.startups || 0,
            scheduledTaskCount: data.scheduledTasks || 0,
            kernelModuleCount: data.kernelModules || 0,
            networkInterfaceCount: data.networkInterfaces || 0,
            dockerImageCount: data.dockerImages || 0,
            dockerContainerCount: data.dockerContainers || 0,
            dockerNetworkCount: data.dockerNetworks || 0,
            riskCount: data.risks || 0
          }
        } else {
          console.warn('数据格式不符合预期，无法解析资产统计信息:', data)
          this.assetStats = {}
        }
      } else {
        console.warn('未收到有效数据')
        this.assetStats = {}
      }
      
      console.log('最终设置的资产统计数据:', this.assetStats)
      this.$emit('stats-loaded', this.assetStats)
    }
  },
  watch: {
    hostIp(newVal) {
      if (newVal && this.autoLoad) {
        this.fetchAssetStats()
      }
    },
    analysisData(newVal) {
      if (newVal) {
        this.loadAssetStatsFromAnalysisData(newVal)
      }
    },
    data: {
      handler(newVal) {
        console.log('watch data prop变化:', newVal)
        if (newVal) {
          this.loadAssetStatsFromData(newVal)
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';

.host-asset-info {
  font-size: 12px;
}

.asset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.asset-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  background-color: #FFFFFF;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-size: 12px;
  cursor: pointer;
}

.asset-item:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.asset-item:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.asset-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.asset-icon i {
  font-size: 20px;
  color: #FFFFFF;
}

.asset-content {
  flex: 1;
  text-align: left;
}

.asset-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1;
}

.asset-label {
  font-size: 12px;
  color: #909399;
  line-height: 1;
}

/* HostAssetInfo特有的无数据提示样式 */
.asset-grid .no-data-tip {
  grid-column: 1 / -1;
  height: 120px;
  background-color: #f8f8f8;
  border-radius: 8px;
}

.asset-grid .no-data-tip i {
  font-size: 30px;
  margin-bottom: 10px;
}

.asset-grid .no-data-tip p {
  font-size: 14px;
  margin: 0;
}
</style> 