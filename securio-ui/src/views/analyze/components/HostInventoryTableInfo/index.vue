<template>
  <div class="host-asset-info">
    <component :is="componentMap[assetType]" :hostIp="hostIp" :hideHostIpSearch="!!hostIp" />
  </div>
</template>

<script>
import InventoryUserInfo from '@/views/agent/inventoryUserInfo/index.vue';
import InventoryProcessInfo from '@/views/agent/processInfo/index.vue';
import KernelModuleInfo from '@/views/agent/kernelModuleInfo/index.vue';
import NetworkInfo from '@/views/agent/networkInfo/index.vue';
import PackageInfo from '@/views/agent/packageInfo/index.vue';
import PortInfo from '@/views/agent/portInfo/index.vue';
import StartupInfo from '@/views/agent/startupInfo/index.vue';
import ScheduledTaskInfo from '@/views/agent/scheduledTaskInfo/index.vue';

export default {
  name: 'HostInventoryTableInfo',
  components: {
    InventoryUserInfo,
    InventoryProcessInfo,
    KernelModuleInfo,
    NetworkInfo,
    PackageInfo,
    PortInfo,
    StartupInfo,
    ScheduledTaskInfo
  },
  props: {
    hostIp: {
      type: String,
      default: ''
    },
    assetType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      componentMap: {
        'userCount': 'InventoryUserInfo',
        'processCount': 'InventoryProcessInfo',
        'kernelModuleCount': 'KernelModuleInfo',
        'networkInterfaceCount': 'NetworkInfo',
        'softwareCount': 'PackageInfo',
        'portCount': 'PortInfo',
        'startupCount': 'StartupInfo',
        'scheduledTaskCount': 'ScheduledTaskInfo'
      }
    }
  },
  computed: {
  },
  watch: {
  },
  methods: {
    
  }
}
</script>
