<template>
    <div class="app-container">
      <h1>HTTP 请求分析</h1>
    
    <!-- URI分析区域 -->
    <el-card class="analysis-card" shadow="hover" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span class="card-title">
          <i class="el-icon-search"></i>
          URI 请求分析
        </span>
      </div>
      
      <el-form :model="analysisForm" label-width="100px" :rules="rules" ref="analysisForm">
        <el-form-item label="请求URI:" prop="uri">
          <el-input
            v-model="analysisForm.uri"
            placeholder="请输入要分析的URI，例如：/mobile-restful//mobile/fast-reply/select"
            style="width: 100%;"
            clearable>
            <i slot="prefix" class="el-icon-link"></i>
          </el-input>
        </el-form-item>
        
        <el-form-item label="时间范围:">
          <el-select v-model="analysisForm.timeRange" placeholder="选择查询时间范围" style="width: 200px;">
            <el-option label="最近1小时" value="1h"></el-option>
            <el-option label="最近6小时" value="6h"></el-option>
            <el-option label="最近1天" value="1d"></el-option>
            <el-option label="最近3天" value="3d"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="analyzeRequest"
            :loading="loading"
            icon="el-icon-cpu">
            开始分析
          </el-button>
          <el-button 
            @click="clearResult"
            icon="el-icon-delete">
            清空结果
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分析进度 -->
    <el-card v-if="analysisSteps.length > 0" class="progress-card" style="margin-top: 20px;">
      <div slot="header">
        <span class="card-title">
          <i class="el-icon-loading" v-if="loading"></i>
          <i class="el-icon-circle-check" v-else></i>
          分析进度
        </span>
      </div>
      
      <el-steps :active="currentStep" align-center>
        <el-step 
          v-for="(step, index) in analysisSteps" 
          :key="index"
          :title="step.title" 
          :description="step.description"
          :status="step.status">
        </el-step>
      </el-steps>
    </el-card>

    <!-- 分析结果 -->
    <div v-if="analysisResult" class="results-section">
      <!-- 请求日志信息 -->
      <el-card class="result-card" style="margin-top: 20px;">
        <div slot="header">
          <span class="card-title">
            <i class="el-icon-document"></i>
            请求日志信息
          </span>
        </div>
        
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="请求URI">
            <span class="uri-text">{{ analysisResult.log.request_uri }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="请求方法">
            <el-tag :type="getMethodType(analysisResult.log.request_method)" size="mini">
              {{ analysisResult.log.request_method }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态码">
            <el-tag :type="getStatusType(analysisResult.log.status)" size="mini">
              {{ analysisResult.log.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="响应时间">
            {{ analysisResult.log.request_time }}s
          </el-descriptions-item>
          <el-descriptions-item label="客户端IP">
            <code>{{ analysisResult.log.remote_addr }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="目标服务">
            <code>{{ analysisResult.log.dst_ip }}:{{ analysisResult.log.dst_port }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="User Agent">
            <span class="user-agent">{{ analysisResult.log.http_user_agent }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="请求时间">
            {{ analysisResult.log.event_time }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 应用信息 -->
      <el-card v-if="analysisResult.appInfo" class="result-card" style="margin-top: 20px;">
        <div slot="header">
          <span class="card-title">
            <i class="el-icon-s-platform"></i>
            应用信息
          </span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="info-sub-card" shadow="never">
              <div slot="header">
                <i class="el-icon-mobile-phone"></i>
                应用基本信息
              </div>
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="应用名称">
                  <strong>{{ analysisResult.appInfo.app.app_name }}</strong>
                </el-descriptions-item>
                <el-descriptions-item label="项目名称">
                  {{ analysisResult.appInfo.app.project_name }}
                </el-descriptions-item>
                <el-descriptions-item label="应用类型">
                  <el-tag size="mini">{{ analysisResult.appInfo.app.app_type }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="部署类型">
                  <el-tag type="info" size="mini">{{ analysisResult.appInfo.app.deploy_type }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="资产等级">
                  <el-tag :type="getAssetLevelType(analysisResult.appInfo.app.asset_level)" size="mini">
                    {{ analysisResult.appInfo.app.asset_level }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card class="info-sub-card" shadow="never">
              <div slot="header">
                <i class="el-icon-server"></i>
                部署信息
              </div>
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="部署主机">
                  <code>{{ analysisResult.appInfo.app_host.private_ip }}</code>
                </el-descriptions-item>
                <el-descriptions-item label="端口">
                  <code>{{ analysisResult.appInfo.app_host.port }}</code>
                </el-descriptions-item>
                <el-descriptions-item label="环境">
                  <el-tag type="success" size="mini">{{ analysisResult.appInfo.app_host.env_name }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="主机类型">
                  {{ analysisResult.appInfo.app_host.host_type }}
                </el-descriptions-item>
                <el-descriptions-item label="机房位置">
                  <span class="location-text">{{ analysisResult.appInfo.app_host.rack_location }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card class="info-sub-card" shadow="never">
              <div slot="header">
                <i class="el-icon-folder-opened"></i>
                项目信息
              </div>
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="项目中文名">
                  <strong>{{ analysisResult.appInfo.project.project_cn }}</strong>
                </el-descriptions-item>
                <el-descriptions-item label="产品负责人">
                  {{ analysisResult.appInfo.project.product_owner }}
                </el-descriptions-item>
                <el-descriptions-item label="研发负责人">
                  <span v-for="(rd, index) in analysisResult.appInfo.project.rd_duty" :key="index">
                    <el-tag type="warning" size="mini" style="margin-right: 5px;">{{ rd }}</el-tag>
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="系统名称">
                  {{ analysisResult.appInfo.project.sys_name }}
                </el-descriptions-item>
                <el-descriptions-item label="Git路径">
                  <a :href="analysisResult.appInfo.project.git_path" target="_blank" class="git-link">
                    {{ analysisResult.appInfo.project.git_path }}
                  </a>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
      </el-card>

      <!-- 原始日志数据 -->
      <el-card class="result-card" style="margin-top: 20px;">
        <div slot="header">
          <span class="card-title">
            <i class="el-icon-view"></i>
            原始日志数据
          </span>
        </div>
        
        <el-collapse>
          <el-collapse-item title="查看完整日志数据" name="rawlog">
            <el-input
              v-model="rawLogDisplay"
              type="textarea"
              :rows="15"
              readonly
              style="font-family: 'Courier New', monospace; font-size: 12px;">
            </el-input>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>

    <!-- 错误信息 -->
    <el-alert
      v-if="errorMessage"
      :title="errorMessage"
      type="error"
      style="margin-top: 20px;"
      :closable="false">
    </el-alert>
    </div>
  </template>
    
  <script>
import axios from 'axios'
  
  export default {
      name: 'HttpRequestAnalysis',
      components: {
          
      },
      data() {
          return {
      loading: false,
      analysisForm: {
        uri: '/mobile-restful//mobile/fast-reply/select',
        timeRange: '1h'
      },
      rules: {
        uri: [
          { required: true, message: '请输入要分析的URI', trigger: 'blur' },
          { min: 1, message: 'URI不能为空', trigger: 'blur' }
        ]
      },
      analysisSteps: [],
      currentStep: 0,
      analysisResult: null,
      rawLogDisplay: '',
      errorMessage: ''
    }
  },
  methods: {
    async analyzeRequest() {
      this.$refs.analysisForm.validate(async (valid) => {
        if (!valid) return
        
        this.loading = true
        this.errorMessage = ''
        this.analysisResult = null
        this.rawLogDisplay = ''
        this.currentStep = 0
        
        // 初始化分析步骤
        this.analysisSteps = [
          { title: '查询日志', description: '从VictoriaLogs获取请求日志', status: 'process' },
          { title: '解析数据', description: '解析日志获取目标服务信息', status: 'wait' },
          { title: '获取应用信息', description: '从CMDB获取应用详细信息', status: 'wait' },
          { title: '完成分析', description: '展示分析结果', status: 'wait' }
        ]
        
        try {
          // 步骤1: 查询VictoriaLogs
          await this.step1QueryLogs()
          
          // 步骤2: 解析日志数据
          await this.step2ParseLogData()
          
          // 步骤3: 获取应用信息
          await this.step3GetAppInfo()
          
          // 步骤4: 完成分析
          this.step4CompleteAnalysis()
          
        } catch (error) {
          console.error('分析失败:', error)
          this.errorMessage = error.message || '分析过程中发生未知错误'
          this.analysisSteps[this.currentStep].status = 'error'
        } finally {
          this.loading = false
        }
      })
    },

    async step1QueryLogs() {
      this.analysisSteps[0].status = 'process'
      
      const query = `_time:${this.analysisForm.timeRange} and stream:"NGINX_ACCESS" and "${this.analysisForm.uri}" | limit 1`
      
      try {
        const response = await axios.post('http://10.20.200.202:9429/select/logsql/query', 
          `query=${encodeURIComponent(query)}`,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            timeout: 30000
          }
        )
        
        if (!response.data) {
          throw new Error('未获取到日志数据')
        }
        
        // 解析VictoriaLogs返回的数据
        const logData = typeof response.data === 'string' ? response.data : JSON.stringify(response.data)
        this.rawLogDisplay = JSON.stringify(JSON.parse(logData), null, 2)
        
        // 解析JSON数据
        const parsedData = JSON.parse(logData)
        this.analysisResult = { rawLog: parsedData }
        
        this.analysisSteps[0].status = 'finish'
        this.currentStep = 1
        
      } catch (error) {
        console.error('查询VictoriaLogs失败:', error)
        throw new Error(`查询日志失败: ${error.message}`)
      }
    },

    async step2ParseLogData() {
      this.analysisSteps[1].status = 'process'
      
      try {
        const rawLog = this.analysisResult.rawLog
        
        // 解析日志数据，提取关键信息
        const logInfo = {
          request_uri: rawLog['message.request_uri'] || rawLog.request_uri,
          request_method: rawLog['message.request_method'] || rawLog.request_method,
          status: rawLog['message.status'] || rawLog.status,
          request_time: rawLog['message.request_time'] || rawLog.request_time,
          remote_addr: rawLog['message.remote_addr'] || rawLog.remote_addr,
          http_user_agent: rawLog['message.http_user_agent'] || rawLog.http_user_agent,
          event_time: rawLog.event_time,
          dst_ip: rawLog.dst_ip,
          dst_port: rawLog.dst_port
        }
        
        if (!logInfo.dst_ip || !logInfo.dst_port) {
          throw new Error('无法从日志中解析出目标IP和端口信息')
        }
        
        this.analysisResult.log = logInfo
        
        this.analysisSteps[1].status = 'finish'
        this.currentStep = 2
        
      } catch (error) {
        console.error('解析日志数据失败:', error)
        throw new Error(`解析日志数据失败: ${error.message}`)
      }
    },

    async step3GetAppInfo() {
      this.analysisSteps[2].status = 'process'
      
      try {
        const { dst_ip, dst_port } = this.analysisResult.log
        
        const response = await axios.get(
          `https://wukong.5i5j.com/prod-api/openapi/cmdb/getAppInfoByIpAndPort`,
          {
            params: {
              ip: dst_ip,
              port: dst_port
            },
            headers: {
              'Authorization': 'Bearer B237bKzVu6TmvPChY3B3TDpab2xnj82XXAJFucJrFmGE7GurFadczDz8eM5AZNGd',
              'Content-Type': 'application/json'
            },
            timeout: 15000
          }
        )
        
        if (response.data.code !== 200) {
          throw new Error(response.data.msg || '获取应用信息失败')
        }
        
        this.analysisResult.appInfo = response.data.data
        
        this.analysisSteps[2].status = 'finish'
        this.currentStep = 3
        
      } catch (error) {
        console.error('获取应用信息失败:', error)
        throw new Error(`获取应用信息失败: ${error.message}`)
      }
    },

    step4CompleteAnalysis() {
      this.analysisSteps[3].status = 'finish'
      this.currentStep = 4
      this.$message.success('URI分析完成！')
    },

    clearResult() {
      this.analysisResult = null
      this.rawLogDisplay = ''
      this.errorMessage = ''
      this.analysisSteps = []
      this.currentStep = 0
      this.$message.info('已清空分析结果')
    },

    getMethodType(method) {
      const typeMap = {
        'GET': 'info',
        'POST': 'success',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'warning'
      }
      return typeMap[method] || 'info'
    },

    getStatusType(status) {
      const statusCode = parseInt(status)
      if (statusCode >= 200 && statusCode < 300) {
        return 'success'
      } else if (statusCode >= 400 && statusCode < 500) {
        return 'warning'
      } else if (statusCode >= 500) {
        return 'danger'
      }
      return 'info'
    },

    getAssetLevelType(level) {
      const typeMap = {
        'A': 'danger',
        'B': 'warning',
        'C': 'success',
        'D': 'info'
      }
      return typeMap[level] || 'info'
    }
      }
  }
  </script>

<style scoped>
.app-container {
  padding: 20px;
}

.analysis-card, .progress-card, .result-card {
  max-width: 1200px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.info-sub-card {
  height: 100%;
  border: 1px solid #e4e7ed;
}

.info-sub-card .el-card__header {
  background-color: #f5f7fa;
  padding: 10px 15px;
  font-size: 14px;
  font-weight: bold;
}

.uri-text {
  font-family: 'Courier New', monospace;
  color: #409EFF;
  font-weight: 500;
}

.user-agent {
  font-size: 12px;
  color: #606266;
}

.location-text {
  font-size: 12px;
  color: #909399;
}

.git-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 12px;
}

.git-link:hover {
  text-decoration: underline;
}

.results-section {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .analysis-card, .progress-card, .result-card {
    margin: 10px 0;
  }
}
</style>
    