<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">主机综合分析</span>
        <el-button 
          style="float: right;" 
          type="text" 
          @click="showAnalyzerConfig = true"
          icon="el-icon-setting">
          分析器配置
        </el-button>
      </div>
      
      <!-- 分析参数输入区域 -->
      <el-form :model="analysisForm" :rules="rules" ref="analysisForm" label-width="120px" class="analysis-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="主机IP地址" prop="hostIp">
              <el-input 
                v-model="analysisForm.hostIp" 
                placeholder="请输入主机IP地址" 
                clearable 
                @blur="loadSupportedAnalyzers" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="时间范围" prop="timeRange">
              <time-range-selector
                ref="timeRangeSelector"
                label="分析时间点"
                :show-timeline="true"
                :default-duration="30"
                :drag-sensitivity="0.4"
                @time-change="handleTimeRangeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- IP类型和支持的分析器展示 -->
        <el-row v-if="ipTypeInfo" :gutter="20">
          <el-col :span="24">
            <div class="ip-type-info">
              <el-tag :type="getIpTypeTagType(ipTypeInfo.ipType)" size="medium">
                {{ ipTypeInfo.ipTypeDescription }}
              </el-tag>
              <span class="analyzer-count">支持 {{ ipTypeInfo.analyzers.length }} 个分析器</span>
              <el-button 
                type="text" 
                size="mini" 
                @click="showAnalyzerDetails = !showAnalyzerDetails">
                {{ showAnalyzerDetails ? '隐藏' : '查看' }}分析器详情
              </el-button>
            </div>
          </el-col>
        </el-row>
        
        <!-- 分析器详情展示 -->
        <el-row v-if="showAnalyzerDetails && ipTypeInfo" :gutter="20">
          <el-col :span="24">
            <div class="analyzer-details">
              <h4>将执行的分析器</h4>
              <div class="analyzer-grid">
                <div 
                  v-for="analyzer in ipTypeInfo.analyzers" 
                  :key="analyzer.type"
                  class="analyzer-card">
                  <div class="analyzer-header">
                    <i :class="getAnalyzerIcon(analyzer.type)"></i>
                    <span class="analyzer-name">{{ analyzer.name }}</span>
                  </div>
                  <div class="analyzer-meta">
                    <span class="priority">优先级: {{ analyzer.priority }}</span>
                    <span class="estimated-time">预估: {{ formatTime(analyzer.estimatedTime) }}</span>
                  </div>
                  <div class="analyzer-description">{{ analyzer.description }}</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="startAnalysis" :loading="loading" icon="el-icon-search">
            开始分析
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button 
            v-if="lastTaskId && analysisResult && configForm.useSSE" 
            type="text" 
            @click="showProgressDialog = true" 
            icon="el-icon-view">
            查看分析过程
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分析结果展示区域 -->
    <div v-if="analysisResult" class="analysis-result">
      <!-- 分析摘要 -->
      <el-card class="result-card" v-if="analysisResult.analysisSummary">
        <div slot="header" class="clearfix">
          <span class="card-title">分析摘要</span>
          <span class="generate-time">生成时间: {{ analysisResult.generateTime }}</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="summary-section">
              <h4>总体评估</h4>
              <p class="assessment-text">{{ analysisResult.analysisSummary.overallAssessment }}</p>
              
              <!-- 显示精确时间范围信息 -->
              <div v-if="preciseTimeData && analysisForm.timeRange === 'precise'" class="precise-time-info">
                <h5>分析时间范围详情</h5>
                <p><strong>中心时间点：</strong>{{ preciseTimeData.sliderCenterTime }}</p>
                <p><strong>分析范围：</strong>{{ preciseTimeData.timeRange.start }} ~ {{ preciseTimeData.timeRange.end }}</p>
                <p><strong>时间跨度：</strong>{{ preciseTimeData.timeRange.duration }}分钟</p>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="summary-section">
              <h4>关键发现</h4>
              <ul class="findings-list">
                <li v-for="(finding, index) in analysisResult.analysisSummary.keyFindings" :key="index">
                  {{ finding }}
                </li>
              </ul>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="summary-section">
              <h4>建议措施</h4>
              <ul class="recommendations-list">
                <li v-for="(recommendation, index) in analysisResult.analysisSummary.recommendations" :key="index">
                  {{ recommendation }}
                </li>
              </ul>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 分析器结果展示 -->
      <el-card class="result-card" v-if="analysisResult.analyzerResults">
        <div slot="header" class="clearfix">
          <span class="card-title">分析器执行结果</span>
        </div>
        
        <el-tabs :value="defaultActiveTab" type="card" @tab-click="handleTabClick">
          <el-tab-pane 
            v-for="(result, analyzerType) in filteredAnalyzerResults" 
            :key="analyzerType"
            :label="getAnalyzerTabLabel(analyzerType, result)"
            :name="analyzerType">
            
            <!-- 分析器结果内容 -->
            <div class="analyzer-result-content">
              <div v-if="result.success" class="success-result">
                <el-alert 
                  :title="`${result.analyzerName} 执行成功`" 
                  type="success" 
                  :closable="false"
                  style="margin-bottom: 20px;">
                  <div slot="description">
                    执行时间: {{ result.executionTime }}ms
          </div>
                </el-alert>
                
                <!-- 根据分析器类型渲染不同的结果组件 -->
                <component 
                  :is="getResultComponent(analyzerType)"
                  :data="result.data"
                  :analyzer-type="analyzerType"
                  :host-ip="analysisForm.hostIp" />
        </div>
        
              <div v-else class="error-result">
                <el-alert 
                  :title="`${result.analyzerName} 执行失败`" 
                  type="error" 
                  :closable="false">
                  <div slot="description">
                    错误信息: {{ result.errorMessage }}
        </div>
                </el-alert>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 分析器配置对话框 -->
    <el-dialog title="分析器配置" :visible.sync="showAnalyzerConfig" width="60%">
      <div class="analyzer-config-content">
        <p>这里可以配置不同IP类型对应的分析器组合...</p>
        <!-- 未来可以添加分析器的启用/禁用配置 -->
            </div>
      <div slot="footer">
        <el-button @click="showAnalyzerConfig = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分析进度对话框 -->
    <el-dialog 
      title="主机分析进度" 
      :visible.sync="showProgressDialog" 
      width="70%" 
      top="5vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="!isAnalyzing">
      
      <div class="progress-container">
        <!-- 进度条 -->
        <div class="progress-header">
          <el-progress 
            :percentage="currentProgress" 
            :status="progressStatus"
            :stroke-width="8"
            class="progress-bar">
          </el-progress>
          <div class="progress-info">
            <span class="current-step">{{ currentStepDescription }}</span>
            <span class="progress-percentage">{{ currentProgress }}%</span>
          </div>
        </div>
        
        <!-- 日志输出区域 -->
        <div class="log-container">
          <div class="log-header">
            <span>分析日志</span>
            <el-button 
              type="text" 
              size="mini" 
              @click="clearLogs"
              :disabled="isAnalyzing">
              清空日志
            </el-button>
          </div>
          <div class="log-content" ref="logContent">
            <div 
              v-for="(log, index) in progressLogs" 
              :key="index"
              :class="['log-item', `log-${log.status.toLowerCase()}`]">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-step">[{{ log.currentStep }}]</span>
              <span class="log-message">{{ log.message }}</span>
              <span v-if="log.errorMessage" class="log-error">错误: {{ log.errorMessage }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button 
          v-if="!isAnalyzing" 
          @click="showProgressDialog = false">
          关闭
        </el-button>
        <el-button 
          v-if="isAnalyzing" 
          type="warning" 
          @click="cancelAnalysis">
          取消分析
        </el-button>
        <el-button 
          type="primary" 
          @click="exportLogs"
          :disabled="progressLogs.length === 0">
          导出日志
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { analyzeHost, startAnalyzeHost, getProgressHistory, getSupportedAnalyzers } from '@/api/analyze/hostAnalysis'
import { getHostAnalysisConfig } from '@/api/analyze/hostAnalysisConfig'
import TimeRangeSelector from '@/components/TimeRangeSelector'
import moment from 'moment'
import HostInventoryTableInfo from './components/HostInventoryTableInfo/index.vue'
import HostAssetInfo from './components/HostAssetInfo/index.vue'
import HostBasicInfo from './components/HostBasicInfo/index.vue'
import NginxAccessLogResult from './components/AnalysisResult/NginxAccessLogResult'
import HostNetworkConnectionResult from './components/AnalysisResult/HostNetworkConnectionResult'
import ACAccessLogResult from './components/AnalysisResult/ACAccessLogResult'
import H3CBehaviorLogResult from './components/AnalysisResult/H3CBehaviorLogResult'
import DnsQueryAnalysisResult from './components/AnalysisResult/DnsQueryAnalysisResult'
import CommandAnalysisResult from './components/AnalysisResult/CommandAnalysisResult'
import SyslogAnalysisResult from './components/AnalysisResult/SyslogAnalysisResult'
import ZeekSshAnalysisResult from './components/AnalysisResult/ZeekSshAnalysisResult'
import ZeekHttpAnalysisResult from './components/AnalysisResult/ZeekHttpAnalysisResult'
import ZeekConnAnalysisResult from './components/AnalysisResult/ZeekConnAnalysisResult'

export default {
  name: 'HostAnalyze',
  components: {
    TimeRangeSelector,
    HostInventoryTableInfo,
    HostAssetInfo,
    HostBasicInfo,
    NginxAccessLogResult,
    HostNetworkConnectionResult,
    ACAccessLogResult,
    H3CBehaviorLogResult,
    DnsQueryAnalysisResult,
    CommandAnalysisResult,
    SyslogAnalysisResult,
    ZeekSshAnalysisResult,
    ZeekHttpAnalysisResult,
    ZeekConnAnalysisResult
  },
  data() {
    return {
      loading: false,
      analysisForm: {
        hostIp: '************',
        timeRange: '30m',
        startTime: '',
        endTime: ''
      },
      preciseTimeData: null, // 存储精确时间数据
      rules: {
        hostIp: [
          { required: true, message: '请输入主机IP地址', trigger: 'blur' },
          { pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/, message: 'IP地址格式不正确', trigger: 'blur' }
        ],
        timeRange: [
          { required: true, message: '请选择时间范围', trigger: 'change' }
        ]
      },
      analysisResult: null,
      configForm: {
        basicInfoSource: 'cmdb',
        timeout: 30,
        useSSE: false
      },
      showRawDataDialog: false,
      activeRawDataTab: 'serverInfo',
      
      // SSE 相关数据
      showProgressDialog: false,
      isAnalyzing: false,
      currentProgress: 0,
      currentStepDescription: '',
      progressStatus: null,
      progressLogs: [],
      eventSource: null,
      lastTaskId: null,
      userQueryParams: {
        username: '',
        hostIp: '',
        pageNum: 1,
        pageSize: 10
      },
      userLoading: false,
      userList: [],
      userTotal: 0,
      
      // 新增轮询相关数据
      pollingTimer: null,
      pollingRetryCount: 0,
      
      // 新增分析器相关数据
      showAnalyzerConfig: false,
      showAnalyzerDetails: false,
      ipTypeInfo: null
    }
  },
  computed: {
    // 返回所有分析器结果，包括主机基本信息
    filteredAnalyzerResults() {
      if (!this.analysisResult || !this.analysisResult.analyzerResults) {
        return {}
      }
      
      // 返回所有分析器结果，不再过滤host_basic_info
      return this.analysisResult.analyzerResults
    },
    
    // 获取默认激活的tab页，优先显示主机基本信息
    defaultActiveTab() {
      const analyzerTypes = Object.keys(this.filteredAnalyzerResults)
      
      // 优先显示主机基本信息
      if (analyzerTypes.includes('host_basic_info')) {
        return 'host_basic_info'
      }
      
      // 如果没有主机基本信息，显示第一个可用的分析器
      return analyzerTypes.length > 0 ? analyzerTypes[0] : 'nginx_access_log'
    }
  },
  methods: {
    handleTimeRangeChange(timeData) {
      if (timeData) {
        if (timeData.rangeType === 'quick' && timeData.quickTimeRange) {
          // 快速时间选择
          this.analysisForm.timeRange = timeData.quickTimeRange;
          this.analysisForm.startTime = '';
          this.analysisForm.endTime = '';
          this.preciseTimeData = null;
          console.log('使用快速时间选择:', timeData.quickTimeRange);
        } else if (timeData.rangeType === 'precise' && timeData.timeRange.start && timeData.timeRange.end) {
          // 精确时间选择
          this.analysisForm.timeRange = 'precise';
        this.analysisForm.startTime = timeData.timeRange.start;
        this.analysisForm.endTime = timeData.timeRange.end;
          this.preciseTimeData = timeData;
          console.log('使用精确时间选择:', timeData.timeRange.start, '~', timeData.timeRange.end);
        }
      } else {
        this.analysisForm.startTime = '';
        this.analysisForm.endTime = '';
        this.analysisForm.timeRange = '30m';
        this.preciseTimeData = null;
      }
    },
    
    startAnalysis() {
      this.$refs.analysisForm.validate((valid) => {
        if (valid) {
          // 验证时间范围 - 快速时间选择或精确时间选择
          if (!this.analysisForm.timeRange || 
              (this.analysisForm.timeRange === 'precise' && (!this.analysisForm.startTime || !this.analysisForm.endTime))) {
            this.$message.warning('请选择时间范围');
              return;
            }
          
          // 根据配置选择交互模式
          if (this.configForm.useSSE) {
            this.startAnalysisWithSSE();
          } else {
            this.startAnalysisWithHTTP();
          }
        }
      })
    },
    
    // SSE模式分析
    startAnalysisWithSSE() {
          this.loading = true
          
      const requestData = this.buildRequestData();
      
      console.log('发送主机分析请求(SSE模式):', requestData);
      
          startAnalyzeHost(requestData).then(response => {
            this.lastTaskId = response.data
            this.initializeSSE(this.lastTaskId)
            this.showProgressDialog = true
            this.isAnalyzing = true
            this.currentProgress = 0
            this.currentStepDescription = '正在启动分析任务...'
            this.progressStatus = null
            this.progressLogs = []
          }).catch(error => {
            this.$message.error('启动分析失败: ' + (error.msg || error.message))
          }).finally(() => {
            this.loading = false
          })
    },
    
    // 传统HTTP模式分析
    startAnalysisWithHTTP() {
      this.loading = true
      this.analysisResult = null // 清空之前的结果
      
      const requestData = this.buildRequestData();
      
      console.log('发送主机分析请求(HTTP模式):', requestData);
      
      // 显示分析中的提示
      const loadingMessage = this.$message({
        message: '正在进行主机综合分析，请稍候...',
        type: 'info',
        duration: 0, // 不自动关闭
        showClose: true
      });
      
      // 调用同步分析接口
      analyzeHost(requestData).then(response => {
        if (response.code === 200) {
          this.analysisResult = response.data
          console.log('接收到分析结果:', response.data)
          console.log('分析器结果:', response.data.analyzerResults)
          this.$message.success('主机综合分析完成')
          
          // 滚动到结果区域
          this.$nextTick(() => {
            const resultElement = document.querySelector('.analysis-result')
            if (resultElement) {
              resultElement.scrollIntoView({ behavior: 'smooth' })
            }
            
            // 延迟触发resize事件，确保桑基图正确渲染
            setTimeout(() => {
              window.dispatchEvent(new Event('resize'))
              console.log('分析完成后触发resize事件')
            }, 300)
          })
        } else {
          this.$message.error('分析失败: ' + (response.msg || '未知错误'))
        }
      }).catch(error => {
        console.error('分析请求失败:', error)
        this.$message.error('分析失败: ' + (error.msg || error.message || '网络请求失败'))
      }).finally(() => {
        this.loading = false
        loadingMessage.close() // 关闭loading提示
      })
    },
    
    // 构建请求数据的公共方法
    buildRequestData() {
      const requestData = {
        hostIp: this.analysisForm.hostIp,
        timeRange: this.analysisForm.timeRange
      }
      
      // 只有在精确时间选择时才传递startTime和endTime
      if (this.analysisForm.timeRange === 'precise') {
        requestData.startTime = this.analysisForm.startTime;
        requestData.endTime = this.analysisForm.endTime;
        // 如果有精确时间数据，添加额外的时间信息
        if (this.preciseTimeData) {
          requestData.preciseTimeData = {
            selectedTime: this.preciseTimeData.selectedTime,
            sliderCenterTime: this.preciseTimeData.sliderCenterTime,
            duration: this.preciseTimeData.timeRange.duration
          };
        }
      } else {
        // 快速时间选择时，传递空字符串以满足后端验证
        requestData.startTime = '';
        requestData.endTime = '';
      }
      
      return requestData;
    },
    
    resetForm() {
      this.$refs.analysisForm.resetFields()
      this.analysisResult = null
      this.analysisForm.timeRange = '30m'
      this.preciseTimeData = null
      this.ipTypeInfo = null
      this.showAnalyzerDetails = false
      
      // 重置时间范围选择器
      if (this.$refs.timeRangeSelector) {
        this.$refs.timeRangeSelector.setTimeData(null);
      }
    },
    
    getStatusType(status) {
      const statusMap = {
        '在线': 'success',
        '离线': 'danger',
        '异常': 'warning',
        '未知': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    getRiskLevelType(level) {
      const levelMap = {
        '低': 'success',
        '中': 'warning',
        '高': 'danger',
        '未知': 'info'
      }
      return levelMap[level] || 'info'
    },
    
    getRawDataTabLabel(key) {
      const labelMap = {
        'serverInfo': '服务器基础信息',
        'hostDetailInfo': '主机详细信息',
        'serverRelations': '服务器关联信息'
      }
      return labelMap[key] || key
    },
    
    copyRawData() {
      if (this.analysisResult && this.analysisResult.hostBasicInfo && this.analysisResult.hostBasicInfo.rawData) {
        const rawDataText = JSON.stringify(this.analysisResult.hostBasicInfo.rawData, null, 2)
        
        // 创建临时文本区域
        const textArea = document.createElement('textarea')
        textArea.value = rawDataText
        document.body.appendChild(textArea)
        textArea.select()
        
        try {
          document.execCommand('copy')
          this.$message.success('原始数据已复制到剪贴板')
        } catch (err) {
          this.$message.error('复制失败，请手动复制')
        }
        
        document.body.removeChild(textArea)
      }
    },
    
    showConfigDialog() {
      this.loadConfig()
      this.configDialogVisible = true
    },
    
    async loadConfig() {
      try {
        const response = await getHostAnalysisConfig()
        if (response.code === 200) {
          // 合并配置，保持默认值
          this.configForm = { 
            basicInfoSource: 'cmdb',
            timeout: 30,
            useSSE: false,
            ...response.data 
          }
          console.log('从后端加载配置:', this.configForm)
        } else {
          // 使用默认配置
          this.configForm = {
            basicInfoSource: 'cmdb',
            timeout: 30,
            useSSE: false
          }
          console.log('使用默认配置:', this.configForm)
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        // 使用默认配置
        this.configForm = {
          basicInfoSource: 'cmdb',
          timeout: 30,
          useSSE: false
        }
        console.log('配置加载失败，使用默认配置:', this.configForm)
      }
    },
    
    async saveConfig() {
      this.configSaving = true
      try {
        // 保存配置到Cookie（有效期30天）
        Cookies.set('hostAnalysisConfig', JSON.stringify(this.configForm), { expires: 30 })
        console.log('配置已保存到Cookie:', this.configForm)
          this.$message.success('配置保存成功')
          this.configDialogVisible = false
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败')
      } finally {
        this.configSaving = false
      }
    },
    
    // CMDB数据获取方法
    getCmdbValue(path, subKey = null) {
      if (!this.analysisResult || !this.analysisResult.hostBasicInfo || !this.analysisResult.hostBasicInfo.rawData) {
        return '-'
      }
      
      const rawData = this.analysisResult.hostBasicInfo.rawData
      const pathParts = path.split('.')
      let value = rawData
      
      // 遍历路径获取值
      for (const part of pathParts) {
        if (value && typeof value === 'object' && value.hasOwnProperty(part)) {
          value = value[part]
        } else {
          return '-'
        }
      }
      
      // 如果需要进一步解析JSON字符串
      if (subKey && typeof value === 'string') {
        try {
          const parsedValue = JSON.parse(value)
          return parsedValue[subKey] || '-'
        } catch (error) {
          return '-'
        }
      }
      
      return value || '-'
    },
    
    // Agent数据获取方法
    getAgentValue(path, subKey = null) {
      if (!this.analysisResult || !this.analysisResult.hostBasicInfo || !this.analysisResult.hostBasicInfo.agentInfo) {
        return '-'
      }
      
      const agentInfo = this.analysisResult.hostBasicInfo.agentInfo
      
      // 如果是hostBaseInfo，需要解析JSON字符串
      if (path === 'hostBaseInfo' && agentInfo.hostBaseInfo) {
        try {
          const hostBaseInfo = typeof agentInfo.hostBaseInfo === 'string' 
            ? JSON.parse(agentInfo.hostBaseInfo) 
            : agentInfo.hostBaseInfo
          const result = hostBaseInfo[subKey] || '-'
          return result
        } catch (error) {
          return '-'
        }
      }
      
      // 直接获取属性值
      const result = agentInfo[path] || '-'
      return result
    },
    
    // 格式化内存大小
    formatMemorySize(bytes) {
      if (!bytes || bytes === '-') return '-'
      
      const bytesNum = parseInt(bytes)
      if (isNaN(bytesNum)) return '-'
      
      const gb = bytesNum / (1024 * 1024 * 1024)
      return `${gb.toFixed(2)} GB`
    },
    
    // 格式化启动时间
    formatBootTime(bootTime) {
      if (!bootTime || bootTime === '-') return '-'
      
      // 如果是时间戳格式，转换为日期字符串
      if (typeof bootTime === 'number') {
        return moment.unix(bootTime).format('YYYY-MM-DD HH:mm:ss')
      }
      
      // 如果已经是日期字符串，直接返回
      return bootTime
    },
    
    // SSE 相关方法
    initializeSSE(taskId) {
      if (this.eventSource) {
        this.eventSource.close()
      }
      
      const baseURL = process.env.VUE_APP_BASE_API || ''
      const sseUrl = `${window.location.protocol}//${window.location.host}${baseURL}/app/host-analysis/analyze/progress/${taskId}`
      
      console.log('正在建立SSE连接:', sseUrl)
      
      // 添加重连逻辑
      this.connectToSSE(sseUrl, taskId, 0)
    },
    
    connectToSSE(sseUrl, taskId, retryCount) {
      const maxRetries = 5
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000) // 指数退避，最大10秒
      
      console.log(`尝试建立SSE连接 (第${retryCount + 1}次)`)
      
      this.eventSource = new EventSource(sseUrl)
      
      this.eventSource.onopen = () => {
        console.log('SSE连接已建立')
        this.currentStepDescription = 'SSE连接已建立，等待分析开始...'
        // 重置重试计数
        this.sseRetryCount = 0
      }
      
      this.eventSource.addEventListener('progress', (event) => {
        try {
          if (event.data && event.data !== 'undefined' && event.data.trim() !== '') {
            const progressData = JSON.parse(event.data)
            this.handleProgressUpdate(progressData)
          }
        } catch (error) {
          console.error('解析进度数据失败:', error, 'event.data:', event.data)
        }
      })
      
      this.eventSource.addEventListener('complete', (event) => {
        try {
          if (event.data && event.data !== 'undefined' && event.data.trim() !== '') {
            const progressData = JSON.parse(event.data)
            this.handleAnalysisComplete(progressData)
          }
        } catch (error) {
          console.error('解析完成数据失败:', error, 'event.data:', event.data)
        }
      })
      
      this.eventSource.addEventListener('error', (event) => {
        try {
          if (event.data && event.data !== 'undefined' && event.data.trim() !== '') {
            const progressData = JSON.parse(event.data)
            this.handleAnalysisError(progressData)
          }
        } catch (error) {
          console.error('解析错误数据失败:', error, 'event.data:', event.data)
        }
      })
      
      // 处理心跳消息
      this.eventSource.addEventListener('heartbeat', (event) => {
        try {
          if (event.data && event.data !== 'undefined' && event.data.trim() !== '') {
            const heartbeatData = JSON.parse(event.data)
            console.log('收到心跳消息:', heartbeatData.timestamp)
            // 心跳消息不需要显示给用户，只用于保持连接
          }
        } catch (error) {
          console.error('解析心跳数据失败:', error, 'event.data:', event.data)
        }
      })
      
      // 处理超时消息
      this.eventSource.addEventListener('timeout', (event) => {
        console.warn('SSE连接超时:', event.data)
        if (this.isAnalyzing) {
          this.$message.warning('连接超时，尝试重连...')
          // 触发重连逻辑
          this.eventSource.close()
        }
      })
      
      this.eventSource.onerror = (error) => {
        console.error('SSE连接错误:', error)
        
        // 检查连接状态
        if (this.eventSource.readyState === EventSource.CLOSED) {
          console.log('SSE连接已关闭')
          
          if (this.isAnalyzing && retryCount < maxRetries) {
            console.log(`连接断开，将在${retryDelay}ms后重试 (${retryCount + 1}/${maxRetries})`)
            this.$message.warning(`连接中断，${Math.ceil(retryDelay/1000)}秒后重试...`)
            
            // 延迟重连
            setTimeout(() => {
              if (this.isAnalyzing) { // 确保用户没有取消分析
                this.connectToSSE(sseUrl, taskId, retryCount + 1)
              }
            }, retryDelay)
          } else if (this.isAnalyzing) {
            // 超过最大重试次数，尝试轮询获取结果
            console.log('SSE重连失败，切换到轮询模式')
            this.$message.error('实时连接失败，切换到轮询模式')
            this.startPolling(taskId)
          }
        } else if (this.eventSource.readyState === EventSource.CONNECTING) {
          console.log('SSE正在重连...')
          if (this.isAnalyzing) {
            this.currentStepDescription = '连接中断，正在重连...'
          }
        }
      }
    },
    
    handleProgressUpdate(progressData) {
      this.currentProgress = progressData.progress || 0
      this.currentStepDescription = progressData.stepDescription || ''
      this.progressStatus = this.getProgressStatus(progressData.status)
      
      // 添加到日志
      this.progressLogs.push(progressData)
      
      // 自动滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    handleAnalysisComplete(progressData) {
      this.isAnalyzing = false
      this.currentProgress = 100
      this.currentStepDescription = '分析完成'
      this.progressStatus = 'success'
      
      // 添加到日志
      this.progressLogs.push(progressData)
      
      // 设置分析结果
      if (progressData.result) {
        this.analysisResult = progressData.result
      }
      
      // 关闭SSE连接
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
      }
      
      this.$message.success('主机综合分析完成')
      
      // 自动滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
        
        // 1秒后自动关闭进度对话框
        setTimeout(() => {
          this.showProgressDialog = false
          
          // 关闭对话框后延迟触发resize事件
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'))
            console.log('SSE分析完成后触发resize事件')
          }, 200)
        }, 1000)
      })
    },
    
    handleAnalysisError(progressData) {
      this.isAnalyzing = false
      this.progressStatus = 'exception'
      this.currentStepDescription = '分析失败'
      
      // 添加到日志
      this.progressLogs.push(progressData)
      
      // 关闭SSE连接
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
      }
      
      this.$message.error('分析失败: ' + (progressData.errorMessage || '未知错误'))
      
      // 自动滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    getProgressStatus(status) {
      switch (status) {
        case 'COMPLETED':
          return 'success'
        case 'ERROR':
          return 'exception'
        case 'RUNNING':
        default:
          return null // 不设置status，使用默认样式
      }
    },
    
    scrollToBottom() {
      if (this.$refs.logContent) {
        this.$refs.logContent.scrollTop = this.$refs.logContent.scrollHeight
      }
    },
    
    cancelAnalysis() {
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
      }
      
      // 停止轮询
      this.stopPolling()
      
      this.isAnalyzing = false
      this.progressStatus = 'exception'
      this.currentStepDescription = '分析已取消'
      
      this.$message.warning('分析任务已取消')
    },
    
    clearLogs() {
      this.progressLogs = []
    },
    
    exportLogs() {
      if (this.progressLogs.length === 0) {
        this.$message.warning('暂无日志可导出')
        return
      }
      
      const logText = this.progressLogs.map(log => {
        const time = this.formatTime(log.timestamp)
        const step = log.currentStep || ''
        const message = log.message || ''
        const error = log.errorMessage ? ` 错误: ${log.errorMessage}` : ''
        return `${time} [${step}] ${message}${error}`
      }).join('\n')
      
      const blob = new Blob([logText], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `主机分析日志_${new Date().toISOString().replace(/[-:]/g, '').replace('T', '_').replace('Z', '')}.txt`
      link.click()
      URL.revokeObjectURL(url)
    },
    
    formatTime(timestamp) {
      return moment(timestamp).format('YYYY-MM-DD HH:mm:ss')
    },
    
    handleAssetStatsLoaded(stats) {
      // 处理资产统计数据加载后的逻辑
    },
    
    // 新增轮询方法作为SSE失败时的备选方案
    startPolling(taskId) {
      if (!this.isAnalyzing) return
      
      this.pollingTimer = setInterval(async () => {
        try {
          const response = await getProgressHistory(taskId)
          if (response.code === 200 && response.data && response.data.length > 0) {
            const latestProgress = response.data[response.data.length - 1]
            
            // 更新进度
            this.handleProgressUpdate(latestProgress)
            
            // 检查是否完成
            if (latestProgress.status === 'COMPLETED') {
              this.handleAnalysisComplete(latestProgress)
              this.stopPolling()
            } else if (latestProgress.status === 'ERROR') {
              this.handleAnalysisError(latestProgress)
              this.stopPolling()
          }
        }
      } catch (error) {
          console.error('轮询进度失败:', error)
          if (this.pollingRetryCount++ > 10) {
            this.stopPolling()
            this.$message.error('无法获取分析进度，请稍后查看结果')
            this.isAnalyzing = false
          }
        }
      }, 2000) // 每2秒轮询一次
    },
    
    stopPolling() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer)
        this.pollingTimer = null
      }
      this.pollingRetryCount = 0
    },
    
    handleHostBasicInfoLoaded(info) {
      // 处理主机基本信息加载后的逻辑
    },
    
    handleAssetStatsLoaded(stats) {
      // 处理资产统计数据加载后的逻辑
    },
    
    // 新增分析器相关方法
    async loadSupportedAnalyzers() {
      if (!this.analysisForm.hostIp) {
        this.ipTypeInfo = null
        return
      }
      
      try {
        const response = await getSupportedAnalyzers(this.analysisForm.hostIp)
        if (response.code === 200) {
          this.ipTypeInfo = response.data
        }
      } catch (error) {
        console.error('加载分析器列表失败:', error)
      }
    },
    
    getIpTypeTagType(ipType) {
      const typeMap = {
        'server': 'success',
        'office_client': 'warning', 
        'public': 'danger',
        'unknown': 'info'
      }
      return typeMap[ipType] || 'info'
    },
    
    getAnalyzerIcon(analyzerType) {
      const iconMap = {
        'host_basic_info': 'el-icon-monitor',
        'dns_log': 'el-icon-connection',
        'dns_query_analysis': 'el-icon-connection',
        'nginx_access_log': 'el-icon-tickets',
        'zeek_conn_log': 'el-icon-link',
        'zeek_http_log': 'el-icon-collection',
        'zeek_files_log': 'el-icon-folder',
        'syslog': 'el-icon-document',
        'sys_command': 'el-icon-terminal',
        'h3c_web_behavior': 'el-icon-view',
        'host_network_connection': 'el-icon-network',
        'ac_access_log': 'el-icon-user',
        'h3c_behavior_log': 'el-icon-s-grid'
      }
      return iconMap[analyzerType] || 'el-icon-cpu'
    },
    
    getAnalyzerTabLabel(analyzerType, result) {
      const name = result.analyzerName || analyzerType
      const status = result.success ? '✓' : '✗'
      return `${status} ${name}`
    },
    
    getResultComponent(analyzerType) {
      // 根据分析器类型返回对应的结果展示组件
      console.log('获取结果组件，分析器类型:', analyzerType)
      
      if (analyzerType === 'nginx_access_log') {
        return NginxAccessLogResult
      }
      if (analyzerType === 'host_basic_info') {
        return HostBasicInfo // 使用现有的HostBasicInfo组件
      }
      if (analyzerType === 'host_asset') {
        return HostAssetInfo // 使用HostAssetInfo组件显示资产分析结果
      }
      if (analyzerType === 'host_network_connection') {
        return HostNetworkConnectionResult
      }
      if (analyzerType === 'ac_access_log') {
        return ACAccessLogResult
      }
      if (analyzerType === 'h3c_behavior_log') {
        return H3CBehaviorLogResult
      }
      if (analyzerType === 'dns_query_analysis') {
        return DnsQueryAnalysisResult
      }
      if (analyzerType === 'command_analysis') {
        return CommandAnalysisResult
      }
      if (analyzerType === 'syslog_analysis') {
        return SyslogAnalysisResult
      }
      if (analyzerType === 'zeek_ssh_analysis') {
        return ZeekSshAnalysisResult
      }
      if (analyzerType === 'zeek_http_analysis') {
        return ZeekHttpAnalysisResult
      }
      if (analyzerType === 'zeek_conn_analysis') {
        return ZeekConnAnalysisResult
      }
      
      // 默认使用div，后续可以扩展为具体组件
      return 'div'
    },
    
    handleTabClick(tab) {
      // 处理tab切换的逻辑
      console.log('切换到Tab:', tab.label, 'name:', tab.name)
      
      // 等待DOM更新后，触发桑基图重新计算尺寸
      this.$nextTick(() => {
        setTimeout(() => {
          // 触发窗口resize事件，让所有ECharts图表重新计算尺寸
          window.dispatchEvent(new Event('resize'))
          console.log('触发全局resize事件')
        }, 150) // 延迟150ms确保tab内容完全渲染
      })
    }
  },
  
  mounted() {
    if (!this.analysisForm.timeRange || this.analysisForm.timeRange === '') {
      this.analysisForm.timeRange = '30m';
    }
    
    // 从URL参数中获取hostIp
    if (this.$route.query.hostIp) {
      this.analysisForm.hostIp = this.$route.query.hostIp;
      // 当从URL参数获取到IP地址时，自动加载支持的分析器
      this.$nextTick(() => {
        this.loadSupportedAnalyzers();
      });
    }
    
    this.loadConfig();
  },
  
  beforeDestroy() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
    
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
    }
  }
}
</script>

<style scoped>
/* 全局字体大小设置为12px */
.app-container {
  font-size: 12px;
}

.analysis-form {
  margin-bottom: 20px;
  font-size: 12px;
}

.analysis-result {
  margin-top: 20px;
  font-size: 12px;
}

.result-card {
  margin-bottom: 20px;
  font-size: 12px;
}

.card-title {
  font-size: 14px;
  font-weight: bold;
}

.generate-time {
  float: right;
  color: #909399;
  font-size: 12px;
}

/* IP类型信息样式 */
.ip-type-info {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  margin-bottom: 10px;
}

.analyzer-count {
  margin-left: 15px;
  font-size: 12px;
  color: #606266;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 12px;
}

/* 分析器详情样式 */
.analyzer-details {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.analyzer-details h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.analyzer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.analyzer-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.analyzer-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.analyzer-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.analyzer-header i {
  font-size: 18px;
  color: #409EFF;
  margin-right: 8px;
}

.analyzer-name {
  font-weight: bold;
  font-size: 13px;
  color: #303133;
}

.analyzer-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 11px;
  color: #909399;
}

.analyzer-description {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

/* 分析器结果样式 */
.analyzer-result-content {
  min-height: 200px;
}

.success-result {
  /* 成功结果的样式 */
}

.error-result {
  /* 错误结果的样式 */
}

/* 分析器配置对话框样式 */
.analyzer-config-content {
  padding: 20px;
  text-align: center;
  color: #909399;
}

/* 摘要区域样式 */
.summary-section {
  margin-bottom: 20px;
  font-size: 12px;
}

.summary-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-size: 12px;
  font-weight: bold;
}

.assessment-text {
  line-height: 1.6;
  color: #606266;
  font-size: 12px;
}

.findings-list, .recommendations-list {
  margin: 0;
  padding-left: 20px;
  font-size: 12px;
}

.findings-list li, .recommendations-list li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #606266;
  font-size: 12px;
}

/* 精确时间信息显示样式 */
.precise-time-info {
  margin-top: 15px;
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 12px;
}

.precise-time-info h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: bold;
  color: #409EFF;
}

.precise-time-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #606266;
}

.precise-time-info strong {
  color: #303133;
}

/* 进度对话框样式 */
.progress-container {
  font-size: 12px;
}

.progress-header {
  margin-bottom: 20px;
}

.progress-bar {
  margin-bottom: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.current-step {
  color: #303133;
  font-weight: bold;
}

.progress-percentage {
  color: #409EFF;
  font-weight: bold;
}

.log-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  font-size: 12px;
  font-weight: bold;
}

.log-content {
  height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #ffffff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.log-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  word-wrap: break-word;
}

.log-item.log-running {
  background-color: #f0f9ff;
  border-left: 3px solid #409EFF;
}

.log-item.log-completed {
  background-color: #f0f9ff;
  border-left: 3px solid #67C23A;
}

.log-item.log-error {
  background-color: #fef0f0;
  border-left: 3px solid #F56C6C;
}

.log-time {
  color: #909399;
  margin-right: 8px;
  min-width: 60px;
  font-size: 11px;
}

.log-step {
  color: #409EFF;
  margin-right: 8px;
  min-width: 100px;
  font-weight: bold;
  font-size: 11px;
}

.log-message {
  color: #303133;
  flex: 1;
  font-size: 12px;
}

.log-error {
  color: #F56C6C;
  margin-left: 8px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .analyzer-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}
}

@media (max-width: 768px) {
  .analyzer-grid {
    grid-template-columns: 1fr;
  }
  
  .ip-type-info {
    flex-direction: column;
    align-items: flex-start;
}

  .analyzer-count {
    margin-left: 0;
    margin-top: 10px;
  }
}
</style>

    