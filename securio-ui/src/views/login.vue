<template>
  <div class="login">
    <div class="login-logo">
      <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="30" cy="30" r="30" fill="#4285F4"/>
        <path d="M30 15C22.8 15 17 20.8 17 28V32H21V28C21 23 25 19 30 19C35 19 39 23 39 28V32H43V28C43 20.8 37.2 15 30 15Z" fill="white"/>
        <rect x="20" y="32" width="20" height="15" rx="2" fill="white"/>
        <circle cx="30" cy="39" r="3" fill="#4285F4"/>
      </svg>
    </div>
    
    <div class="login-container">
      <div class="login-title">Securio 安全运营平台</div>
      <div class="login-subtitle">输入您的凭据以访问安全控制台</div>
      
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
        <el-form-item prop="username" class="form-item">
          <div class="input-label">用户名</div>
          <el-input
            v-model="loginForm.username"
            type="text"
            auto-complete="off"
            placeholder="输入用户名"
            prefix-icon="el-icon-user"
          >
          </el-input>
        </el-form-item>
        
        <el-form-item prop="password" class="form-item">
          <div class="input-label">密码</div>
          <el-input
            v-model="loginForm.password"
            type="password"
            auto-complete="off"
            placeholder="输入密码"
            @keyup.enter.native="handleLogin"
            prefix-icon="el-icon-lock"
          >
          </el-input>
        </el-form-item>
        
        <el-form-item prop="code" v-if="captchaEnabled" class="form-item">
          <div class="input-label">验证码</div>
          <el-input
            v-model="loginForm.code"
            auto-complete="off"
            placeholder="输入验证码"
            style="width: 63%"
            @keyup.enter.native="handleLogin"
            prefix-icon="el-icon-key"
          >
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img"/>
          </div>
        </el-form-item>
        
        <el-form-item class="form-item">
          <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
        </el-form-item>
        
        <el-form-item class="form-item">
          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click.native.prevent="handleLogin"
          >
            <span v-if="!loading">登录</span>
            <span v-else>登录中...</span>
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="security-footer">
        安全防护 · 实时监控 · 高级防御
      </div>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "admin",
        password: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        // 获取redirect参数，并进行URL解码
        const redirectParam = route.query && route.query.redirect;
        console.log('Route changed, redirect param:', redirectParam);
        
        if (redirectParam) {
          try {
            // 对URL编码的redirect参数进行解码
            this.redirect = decodeURIComponent(redirectParam);
            console.log('解码后的redirect参数:', this.redirect);
          } catch (error) {
            console.warn('解码redirect参数失败:', error);
            this.redirect = redirectParam;
            console.log('使用原始redirect参数:', this.redirect);
          }
        } else {
          this.redirect = undefined;
          console.log('无redirect参数');
        }
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            // 处理重定向逻辑
            const redirectPath = this.redirect || "/report/host";
            
            console.log('登录成功，原始redirect:', this.redirect);
            console.log('最终redirectPath:', redirectPath);
            
            // 尝试多种跳转方式
            if (redirectPath && redirectPath !== "/report/host") {
              console.log('尝试跳转到redirect路径:', redirectPath);
              
              // 方法1: 直接使用字符串路径
              this.$router.push(redirectPath).then(() => {
                console.log('路由跳转成功');
              }).catch(err => {
                console.error('字符串路径跳转失败:', err);
                
                // 方法2: 尝试使用window.location进行跳转
                try {
                  const baseUrl = window.location.origin;
                  const fullUrl = baseUrl + redirectPath;
                  console.log('尝试使用window.location跳转到:', fullUrl);
                  window.location.href = fullUrl;
                } catch (locationErr) {
                  console.error('window.location跳转也失败:', locationErr);
                  // 最后回退到默认页面
                  this.$router.push('/report/host').catch(() => {});
                }
              });
            } else {
              console.log('使用默认页面跳转');
              this.$router.push('/report/host').catch(() => {});
            }
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f5f5f5;
  
  .login-logo {
    margin-bottom: 20px;
  }
  
  .login-container {
    width: 400px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 30px;
    text-align: center;
  }
  
  .login-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  
  .login-subtitle {
    font-size: 14px;
    color: #606266;
    margin-bottom: 30px;
  }
  
  .login-form {
    text-align: left;
    
    .form-item {
      margin-bottom: 20px;
    }
    
    .input-label {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .el-input {
      .el-input__inner {
        height: 40px;
        border-radius: 4px;
      }
    }
    
    .login-button {
      width: 100%;
      height: 40px;
      border-radius: 4px;
      font-size: 16px;
      background-color: #409EFF;
      border-color: #409EFF;
    }
  }
  
  .login-code {
    width: 33%;
    height: 38px;
    float: right;
    
    .login-code-img {
      height: 38px;
      cursor: pointer;
      vertical-align: middle;
    }
  }
  
  .security-footer {
    margin-top: 20px;
    font-size: 12px;
    color: #909399;
  }
}
</style>
