<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="用户名称" prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入用户名称"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input
              v-model="queryParams.phonenumber"
              placeholder="请输入手机号码"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="用户状态"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:user:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:user:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:user:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-hasPermi="['system:user:import']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:user:export']"
            >导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-user"
              size="mini"
              @click="handleLdapSync"
              v-hasPermi="['system:user:add']"
            >同步LDAP用户</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" />
          <el-table-column label="用户名称" align="center" key="userName" prop="userName" v-if="columns[1].visible" :show-overflow-tooltip="true" />
          <el-table-column label="用户昵称" align="center" key="nickName" prop="nickName" v-if="columns[2].visible" :show-overflow-tooltip="true" />
          <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" v-if="columns[3].visible" :show-overflow-tooltip="true" />
          <el-table-column label="手机号码" align="center" key="phonenumber" prop="phonenumber" v-if="columns[4].visible" width="120" />
          <el-table-column label="用户来源" align="center" key="userType" v-if="columns[5].visible" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.remark && scope.row.remark.includes('LDAP')" type="primary" size="mini">LDAP</el-tag>
              <el-tag v-else type="info" size="mini">本地</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" key="status" v-if="columns[6].visible">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[7].visible" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="160"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:user:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:user:remove']"
              >删除</el-button>
              <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['system:user:resetPwd', 'system:user:edit']">
                <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="handleResetPwd" icon="el-icon-key"
                    v-hasPermi="['system:user:resetPwd']" 
                    v-if="!scope.row.remark || !scope.row.remark.includes('LDAP')">重置密码</el-dropdown-item>
                  <el-dropdown-item command="handleAuthRole" icon="el-icon-circle-check"
                    v-hasPermi="['system:user:edit']">分配角色</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect v-model="form.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择性别">
                <el-option
                  v-for="dict in dict.type.sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="角色">
              <el-select v-model="form.roleIds" multiple placeholder="请选择角色">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- LDAP用户同步对话框 -->
    <el-dialog title="同步LDAP用户" :visible.sync="ldapSync.open" width="500px" append-to-body>
      <el-form ref="ldapSyncForm" :model="ldapSync.form" :rules="ldapSync.rules" label-width="100px">
        <el-form-item label="员工工号" prop="employeeId">
          <el-input 
            v-model="ldapSync.form.employeeId" 
            placeholder="请输入员工工号，支持多个工号用逗号分隔"
            type="textarea"
            :rows="3"
            maxlength="1000"
            show-word-limit
          />
          <div style="font-size: 12px; color: #999; margin-top: 5px;">
            支持单个工号（如：8404693）或多个工号（如：8404693,8404694,8404695）
          </div>
        </el-form-item>
        <el-form-item label="默认部门" prop="deptId">
          <treeselect 
            v-model="ldapSync.form.deptId" 
            :options="deptOptions" 
            :show-count="true" 
            placeholder="请选择默认部门（可选）" 
          />
          <div style="font-size: 12px; color: #999; margin-top: 5px;">
            如果不选择，将使用系统默认部门
          </div>
        </el-form-item>
        <el-form-item label="同步选项">
          <el-checkbox v-model="ldapSync.form.updateExisting">更新已存在的用户信息</el-checkbox>
          <br/>
          <el-checkbox v-model="ldapSync.form.assignDefaultRole">自动分配默认角色</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button 
            type="info" 
            size="mini" 
            @click="testLdapConnection"
            :loading="ldapSync.testing"
          >
            {{ ldapSync.testing ? '测试中...' : '测试LDAP连接' }}
          </el-button>
          <span v-if="ldapSync.testResult" 
                :style="{ color: ldapSync.testResult.success ? '#67c23a' : '#f56c6c', marginLeft: '10px' }">
            {{ ldapSync.testResult.message }}
          </span>
        </el-form-item>
      </el-form>
      
      <div v-if="ldapSync.syncing" style="margin-top: 20px;">
        <el-progress 
          :percentage="ldapSync.progress.percentage" 
          :status="ldapSync.progress.status"
          :stroke-width="20"
        ></el-progress>
        <div style="margin-top: 10px; font-size: 12px; color: #666;">
          {{ ldapSync.progress.detail }}
        </div>
      </div>

      <div v-if="ldapSync.result.show" style="margin-top: 20px;">
        <el-alert
          :title="ldapSync.result.title"
          :type="ldapSync.result.type"
          :closable="false"
          show-icon
        >
          <div>{{ ldapSync.result.summary }}</div>
          <div v-if="ldapSync.result.details && ldapSync.result.details.length > 0" style="margin-top: 10px;">
            <el-collapse size="small">
              <el-collapse-item title="查看详细结果" name="details">
                <div v-for="(detail, index) in ldapSync.result.details" :key="index" 
                     style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-size: 12px;">
                  <el-tag :type="detail.success ? 'success' : 'danger'" size="mini" style="margin-right: 8px;">
                    {{ detail.success ? '成功' : '失败' }}
                  </el-tag>
                  <span style="font-weight: bold;">工号 {{ detail.employeeId }}:</span>
                  <span style="margin-left: 5px;">{{ detail.message }}</span>
                  <span v-if="detail.userName" style="margin-left: 10px; color: #666;">
                    ({{ detail.userName }})
                  </span>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-alert>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button 
          type="primary" 
          @click="submitLdapSync" 
          :loading="ldapSync.syncing"
          :disabled="ldapSync.syncing"
        >
          {{ ldapSync.syncing ? '同步中...' : '开始同步' }}
        </el-button>
        <el-button @click="cancelLdapSync" :disabled="ldapSync.syncing">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect, syncLdapUsers } from "@/api/system/user";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `用户来源`, visible: true },
        { key: 6, label: `状态`, visible: true },
        { key: 7, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "用户名称不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" }
        ],
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
          { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur" }
        ],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        phonenumber: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      },
      // LDAP同步参数
      ldapSync: {
        open: false,
        syncing: false,
        form: {
          employeeId: '',
          deptId: '',
          updateExisting: false,
          assignDefaultRole: true
        },
        rules: {
          employeeId: [
            { required: true, message: "员工工号不能为空", trigger: "blur" }
          ]
        },
        progress: {
          percentage: 0,
          status: '',
          text: '正在同步...',
          detail: ''
        },
        result: {
          show: false,
          title: '',
          type: '',
          summary: '',
          details: []
        },
        testing: false,
        testResult: null
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getList();
    this.getDeptTree();
    this.getConfigKey("sys.user.initPassword").then(response => {
      this.initPassword = response.msg;
    });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗？').then(function() {
        return changeUserStatus(row.userId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      getUser().then(response => {
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.open = true;
        this.title = "添加用户";
        this.form.password = this.initPassword;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids;
      getUser(userId).then(response => {
        this.form = response.data;
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.$set(this.form, "postIds", response.postIds);
        this.$set(this.form, "roleIds", response.roleIds);
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
        inputValidator: (value) => {
          if (/<|>|"|'|\||\\/.test(value)) {
            return "不能包含非法字符：< > \" ' \\\ |"
          }
        },
      }).then(({ value }) => {
          resetUserPwd(row.userId, value).then(response => {
            this.$modal.msgSuccess("修改成功，新密码是：" + value);
          });
        }).catch(() => {});
    },
    /** 分配角色操作 */
    handleAuthRole: function(row) {
      const userId = row.userId;
      this.$router.push("/system/user-auth/role/" + userId);
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId != undefined) {
            updateUser(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUser(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal.confirm('是否确认删除用户编号为"' + userIds + '"的数据项？').then(function() {
        return delUser(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 同步LDAP用户按钮操作
    handleLdapSync() {
      this.resetLdapSync();
      this.ldapSync.open = true;
    },
    // 重置LDAP同步表单
    resetLdapSync() {
      this.ldapSync.form = {
        employeeId: '',
        deptId: '',
        updateExisting: false,
        assignDefaultRole: true
      };
      this.ldapSync.syncing = false;
      this.ldapSync.progress = {
        percentage: 0,
        status: '',
        text: '正在同步...',
        detail: ''
      };
      this.ldapSync.result = {
        show: false,
        title: '',
        type: '',
        summary: '',
        details: []
      };
    },
    // 提交LDAP同步
    submitLdapSync() {
      this.$refs.ldapSyncForm.validate(valid => {
        if (valid) {
          this.performLdapSync();
        }
      });
    },
    // 执行LDAP同步
    async performLdapSync() {
      this.ldapSync.syncing = true;
      this.ldapSync.result.show = false;
      
      try {
        // 解析员工工号
        const employeeIds = this.ldapSync.form.employeeId
          .split(',')
          .map(id => id.trim())
          .filter(id => id.length > 0);
        
        if (employeeIds.length === 0) {
          this.$modal.msgError("请输入有效的员工工号");
          this.ldapSync.syncing = false;
          return;
        }

        // 初始化进度
        this.ldapSync.progress.percentage = 0;
        this.ldapSync.progress.status = '';
        this.ldapSync.progress.text = '准备同步...';
        this.ldapSync.progress.detail = `准备同步 ${employeeIds.length} 个用户`;

        // 更新进度到50%
        this.ldapSync.progress.percentage = 50;
        this.ldapSync.progress.text = '正在同步用户...';
        this.ldapSync.progress.detail = `正在处理 ${employeeIds.length} 个用户`;

        // 构建同步请求数据
        const syncData = {
          employeeIds: employeeIds,
          deptId: this.ldapSync.form.deptId || null,
          updateExisting: this.ldapSync.form.updateExisting,
          assignDefaultRole: this.ldapSync.form.assignDefaultRole
        };

        // 调用批量同步API
        const response = await syncLdapUsers(syncData);
        
        let successCount = 0;
        let failCount = 0;
        let results = [];

        if (response.code === 200) {
          // 处理后端返回的结果
          if (response.results && response.results.length > 0) {
            results = response.results.map(item => ({
              employeeId: item.employeeId,
              success: item.success,
              message: item.message,
              userName: item.userName || null
            }));
            
            // 统计成功和失败数量
            results.forEach(item => {
              if (item.success) {
                successCount++;
              } else {
                failCount++;
              }
            });
          } else {
            // 如果没有详细结果，根据响应状态判断
            successCount = employeeIds.length;
            failCount = 0;
            results = employeeIds.map(id => ({
              employeeId: id,
              success: true,
              message: '同步成功'
            }));
          }
        } else {
          // 整个请求失败
          failCount = employeeIds.length;
          successCount = 0;
          results = employeeIds.map(id => ({
            employeeId: id,
            success: false,
            message: response.msg || '同步失败'
          }));
        }

        // 显示同步结果
        this.ldapSync.progress.percentage = 100;
        this.ldapSync.progress.status = successCount === employeeIds.length ? 'success' : 
                                       failCount === employeeIds.length ? 'exception' : 'warning';
        this.ldapSync.progress.text = '同步完成';
        this.ldapSync.progress.detail = `成功: ${successCount}, 失败: ${failCount}`;

        this.ldapSync.result = {
          show: true,
          title: '同步结果',
          type: successCount === employeeIds.length ? 'success' : 
                failCount === employeeIds.length ? 'error' : 'warning',
          summary: `共处理 ${employeeIds.length} 个用户，成功 ${successCount} 个，失败 ${failCount} 个`,
          details: results
        };

        // 如果有成功的同步，刷新用户列表
        if (successCount > 0) {
          this.getList();
        }

        // 显示总体结果消息
        if (successCount === employeeIds.length) {
          this.$modal.msgSuccess(`所有用户同步成功！`);
        } else if (failCount === employeeIds.length) {
          this.$modal.msgError(`所有用户同步失败！`);
        } else {
          this.$modal.msgWarning(`部分用户同步成功，请查看详细结果`);
        }

      } catch (error) {
        this.ldapSync.progress.percentage = 100;
        this.ldapSync.progress.status = 'exception';
        this.ldapSync.progress.text = '同步失败';
        this.ldapSync.progress.detail = error.message || '未知错误';
        
        this.$modal.msgError('同步过程中发生错误: ' + (error.message || '未知错误'));
      } finally {
        this.ldapSync.syncing = false;
      }
    },
    // 取消LDAP同步
    cancelLdapSync() {
      if (this.ldapSync.syncing) {
        this.$modal.confirm('同步正在进行中，确定要取消吗？').then(() => {
          this.ldapSync.syncing = false;
          this.ldapSync.open = false;
        }).catch(() => {});
      } else {
        this.ldapSync.open = false;
      }
    },
    async testLdapConnection() {
      this.ldapSync.testing = true;
      this.ldapSync.testResult = null;
      
      try {
        // 构建测试请求数据 - 使用一个不存在的测试工号
        const testData = {
          employeeIds: ['test_connection_999999'],
          deptId: null,
          updateExisting: false,
          assignDefaultRole: false
        };

        // 调用测试API
        const response = await syncLdapUsers(testData);
        
        if (response.code === 200) {
          // 检查是否真的有LDAP连接（即使测试用户不存在，连接应该是成功的）
          this.ldapSync.testResult = {
            success: true,
            message: 'LDAP连接测试成功！服务器可正常访问'
          };
        } else if (response.msg && response.msg.includes('连接')) {
          this.ldapSync.testResult = {
            success: false,
            message: 'LDAP连接失败: ' + response.msg
          };
        } else {
          // 如果只是用户不存在，说明连接是正常的
          this.ldapSync.testResult = {
            success: true,
            message: 'LDAP连接测试成功！服务器可正常访问'
          };
        }
      } catch (error) {
        if (error.response && error.response.data) {
          const errorMsg = error.response.data.msg || error.message;
          if (errorMsg.includes('连接') || errorMsg.includes('超时') || errorMsg.includes('网络')) {
            this.ldapSync.testResult = {
              success: false,
              message: 'LDAP连接失败: ' + errorMsg
            };
          } else {
            this.ldapSync.testResult = {
              success: true,
              message: 'LDAP连接测试成功！服务器可正常访问'
            };
          }
        } else {
          this.ldapSync.testResult = {
            success: false,
            message: '连接测试失败: ' + (error.message || '未知错误')
          };
        }
      } finally {
        this.ldapSync.testing = false;
      }
    }
  }
};
</script>

<style scoped>
.el-form-item__tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.ldap-sync-progress {
  margin-top: 20px;
}

.ldap-sync-result {
  margin-top: 20px;
}

.ldap-user-tag {
  margin-right: 5px;
}

.sync-detail-item {
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.sync-detail-item:last-child {
  border-bottom: none;
}
</style>
