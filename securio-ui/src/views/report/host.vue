<template>
    <div class="app-container" v-loading="loading">
      <!-- Security Overview Section -->
      <el-row :gutter="14">
        <!-- Security Score Card -->
        <el-col :span="10" class="overview-col">
          <div class="overview-card">
            <el-row class="score-row">
              <el-col :span="8">
                <div class="score-shield-container">
                  <div class="shield-bg">
                    <div class="shield-inner">
                      <div class="score-value">{{ securityScore }}</div>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :span="16" class="risk-stats">
                <el-row>
                  <el-col :span="12">
                    <div class="risk-item">
                      <span class="risk-label danger">严重风险主机</span>
                      <span class="risk-value">{{ criticalRisks }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="risk-item">
                      <span class="risk-label normal">低危风险主机</span>
                      <span class="risk-value">{{ minorRisks }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div class="risk-item">
                      <span class="risk-label warning">高危风险主机</span>
                      <span class="risk-value">{{ majorRisks }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="risk-item">
                      <span class="risk-label success">无风险主机</span>
                      <span class="risk-value">{{ completedTasks }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div class="risk-item">
                      <span class="risk-label medium">中危风险主机</span>
                      <span class="risk-value">0</span>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
        </el-col>

        <!-- Protection Status Card -->
        <el-col :span="14" class="overview-col">
          <div class="overview-card protection-card">
            <el-row class="protection-row">
              <el-col :span="6">
                <div class="protection-circle-container">
                  <div class="protection-circle">
                    <div class="circle-inner"></div>
                    <ul class="circle-dots">
                      <li></li>
                      <li></li>
                      <li></li>
                    </ul>
                  </div>
                </div>
              </el-col>
              <el-col :span="18" class="protection-details">
                <div class="protection-days">Securio 已保护您的服务器 <span>{{ protectionDays }}</span> 天</div>
                <div class="protection-status">
                  <div class="status-row">
                    <span>主机资产持续采集中…</span>
                    <span>上次应急漏洞扫描 <span class="highlight">5 个月前</span></span>
                    <span class="highlight">尚未进行基线检查</span>
                  </div>
                  <div class="status-row">
                    <span>入侵事件持续监控中…</span>
                    <span>上次通用漏洞扫描 <span class="highlight">4 个月前</span></span>
                    <span class="highlight">尚未进行补丁风险扫描</span>
                  </div>
                  <div class="status-row">
                    <span>安全防护持续进行中…</span>
                    <span class="highlight">尚未进行弱口令扫描</span>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>

      <!-- Resource Statistics Section -->
      <el-row :gutter="14" class="statistics-row">
        <!-- Event Statistics Card -->
        <el-col :span="5" class="statistics-col">
          <div class="stats-card event-card">
            <div class="event-header">
              <div class="event-total">
                <div class="total-value">{{ totalEvents }}</div>
                <div class="total-label">探针总数 <i class="el-icon-question"></i></div>
              </div>
              <div class="event-chart">
                <div class="chart-circle">
                  <svg viewBox="0 0 36 36" class="circular-chart">
                    <path class="circle-bg" d="M18 2.0845
                      a 15.9155 15.9155 0 0 1 0 31.831
                      a 15.9155 15.9155 0 0 1 0 -31.831"/>
                    <path class="circle" :stroke-dasharray="`${(processedEvents / totalEvents) * 100}, 100`" d="M18 2.0845
                      a 15.9155 15.9155 0 0 1 0 31.831
                      a 15.9155 15.9155 0 0 1 0 -31.831"/>
                  </svg>
                </div>
              </div>
            </div>

            <div class="event-status">
              <div class="status-row">
                <div class="status-item online">
                  <div class="status-value">{{ processedEvents }}</div>
                  <div class="status-label">在线</div>
                </div>
                <div class="status-item abnormal">
                  <div class="status-value">{{ unprocessedEvents }}</div>
                  <div class="status-label">异常</div>
                </div>
              </div>
              <div class="status-row">
                <div class="status-item disabled">
                  <div class="status-value">{{ anomalyEvents }}</div>
                  <div class="status-label">停用</div>
                </div>
                <div class="status-item offline">
                  <div class="status-value">{{ highRiskEvents }}</div>
                  <div class="status-label">离线</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <!-- Resource Statistics Card -->
        <el-col :span="19" class="statistics-col">
          <div class="stats-card resource-card" v-loading="inventoryLoading">
            <div class="resource-list">
              <div class="resource-row">
                <div class="resource-item">
                  <i class="el-icon-s-operation"></i>
                  <div class="resource-label">进程</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.processes) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-connection"></i>
                  <div class="resource-label">端口</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.ports) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-box"></i>
                  <div class="resource-label">软件</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.software) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-user"></i>
                  <div class="resource-label">用户</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.users) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-s-grid"></i>
                  <div class="resource-label">网站</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.websites) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-s-platform"></i>
                  <div class="resource-label">启动项</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.startups) }}</div>
                </div>
              </div>
              <div class="resource-row">
                <div class="resource-item">
                  <i class="el-icon-cpu"></i>
                  <div class="resource-label">内核模块</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.kernelModules) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-time"></i>
                  <div class="resource-label">计划任务</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.scheduledTasks) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-s-platform"></i>
                  <div class="resource-label">Docker 镜像</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.dockerImages) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-s-platform"></i>
                  <div class="resource-label">Docker 容器</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.dockerContainers) }}</div>
                </div>
                <div class="resource-item">
                  <i class="el-icon-s-platform"></i>
                  <div class="resource-label">Docker 网络</div>
                  <div class="resource-value">{{ formatNumber(inventoryStats.dockerNetworks) }}</div>
                </div>
                <div class="resource-item empty">
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- Activity Charts and Alerts Section -->
      <el-row :gutter="20">
        <!-- Activity Chart -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header-title">30天内新增事件</div>
            </template>
            <div class="chart-container">
              <activity-chart ref="activityChart" :chartData="activityChartData" />
            </div>
          </el-card>
        </el-col>

        <!-- Recent Alerts -->
        <el-col :span="12">
          <el-card class="alerts-card">
            <template #header>
              <div class="card-header-title" style="display:flex;justify-content:space-between;align-items:center;">
                <span>最近入侵事件</span>
                <el-button type="text" @click="viewAllAlerts">查看更多</el-button>
              </div>
            </template>
            <div class="alerts-list">
              <div v-for="(alert, index) in recentAlerts" :key="index" class="alert-item">
                <div class="alert-type">
                  <el-tag :type="getAlertTagType(alert.level)">{{ alert.dstIp || '未知IP' }}</el-tag>
                </div>
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-time">{{ alert.time }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </template>

  <script>
  import ActivityChart from './components/ActivityChart'
  import request from '@/utils/request'
  import { getDailyStatistics, getRecentIntrusionEvents } from '@/api/alert/alert'

  export default {
    name: 'HostOverview',
    components: {
      ActivityChart
    },
    data() {
      return {
        loading: false,
        // Security Score
        securityScore: 81,
        criticalRisks: 19,
        majorRisks: 1920,
        minorRisks: 0,
        completedTasks: 131,

        // Protection Status
        protectionDays: 334,
        lastScanTime: '5个小时前',
        lastPatchTime: '5个小时前',
        pendingAlerts: 7,
        pendingVulnerabilities: 12,

        // Anomaly Events - 探针统计数据
        totalEvents: 0,
        processedEvents: 0,
        unprocessedEvents: 0,
        anomalyEvents: 0,
        highRiskEvents: 0,

        // Resource Statistics - 资产清单统计数据
        inventoryLoading: false,
        inventoryStats: {
          processes: 0,       // 启动项
          ports: 0,           // 端口
          software: 0,        // 软件
          users: 0,           // 用户
          websites: 0,        // 网站
          startups: 0,        // 启动项
          kernelModules: 0,   // 内核模块
          scheduledTasks: 0,  // 计划任务
          dockerImages: 0,    // Docker镜像
          dockerContainers: 0, // Docker容器
          dockerNetworks: 0   // Docker网络
        },

        // Activity Chart Data
        activityChartData: null,

        // Recent Alerts
        recentAlerts: [
          {
            dstIp: '*************',
            title: 'SSH登录失败次数过多，检测到暴力破解攻击',
            time: '2024-01-15 14:37:09',
            level: 'danger'
          },
          {
            dstIp: '*********',
            title: '检测到用户尝试关闭系统防火墙服务',
            time: '2024-01-15 12:25:31',
            level: 'warning'
          },
          {
            dstIp: '***********',
            title: '发现非授权用户尝试访问敏感目录',
            time: '2024-01-15 10:15:22',
            level: 'danger'
          },
          {
            dstIp: '************',
            title: '系统进程异常退出，可能存在安全风险',
            time: '2024-01-15 09:08:17',
            level: 'warning'
          },
          {
            dstIp: '*************',
            title: '检测到来自外部IP的端口扫描行为',
            time: '2024-01-15 08:30:45',
            level: 'danger'
          },
          {
            dstIp: '************',
            title: '发现可疑文件执行，疑似恶意软件活动',
            time: '2024-01-15 07:15:32',
            level: 'danger'
          },
          {
            dstIp: '***********',
            title: '检测到用户尝试获取管理员权限',
            time: '2024-01-15 06:42:18',
            level: 'warning'
          },
          {
            dstIp: '*********',
            title: '系统关键文件被非法修改',
            time: '2024-01-15 05:30:26',
            level: 'danger'
          },
          {
            dstIp: '*************',
            title: '检测到敏感数据异常传输行为',
            time: '2024-01-15 04:15:41',
            level: 'danger'
          },
          {
            dstIp: '************',
            title: '发现外部IP尝试入侵系统',
            time: '2024-01-15 03:08:55',
            level: 'danger'
          }
        ]
      }
    },
    created() {
      this.fetchOverviewData()
      this.fetchRecentEvents()
      this.fetchActivityStats()
      this.fetchInventoryStats()
      this.fetchAgentStats()
    },
    methods: {
      // 格式化数字，增加千位分隔符
      formatNumber(num) {
        if (num === undefined || num === null) return '0'
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },
      fetchOverviewData() {
        this.loading = true
        // In a real implementation, this would call the API
        // getSecurityOverview().then(response => {
        //   const data = response.data
        //   this.securityScore = data.securityScore
        //   this.criticalRisks = data.criticalRisks
        //   this.majorRisks = data.majorRisks
        //   this.minorRisks = data.minorRisks
        //   this.completedTasks = data.completedTasks
        //   this.protectionDays = data.protectionDays
        //   this.lastScanTime = data.lastScanTime
        //   this.lastPatchTime = data.lastPatchTime
        //   this.pendingAlerts = data.pendingAlerts
        //   this.pendingVulnerabilities = data.pendingVulnerabilities
        //   this.hostCount = data.hostCount
        //   this.internalHostCount = data.internalHostCount
        //   this.portCount = data.portCount
        //   this.scheduledTaskCount = data.scheduledTaskCount
        //   this.physicalMachineCount = data.physicalMachineCount
        //   this.dockerImageCount = data.dockerImageCount
        //   this.userCount = data.userCount
        //   this.dockerContainerCount = data.dockerContainerCount
        //   this.serviceCount = data.serviceCount
        //   this.dockerNetworkCount = data.dockerNetworkCount
        //   this.processCount = data.processCount
        //   this.kernelModuleCount = data.kernelModuleCount
        //   this.loading = false
        // }).catch(() => {
        //   this.loading = false
        // })

        // For demonstration, we'll use the mock data already in the component
        setTimeout(() => {
          this.loading = false
        }, 500)
      },
      fetchRecentEvents() {
        // 调用真实的API获取最近入侵事件
        getRecentIntrusionEvents(10).then(response => {
          if (response.code === 200) {
            this.recentAlerts = response.data.map(item => ({
              dstIp: item.dstIp,
              title: item.title,
              time: item.time,
              level: this.getSeverityLevel(item.severity)
            }))
            console.log('获取最近入侵事件成功:', response.data)
          } else {
            this.$message.error(response.msg || '获取最近入侵事件失败')
            console.error('获取最近入侵事件失败:', response.msg)
          }
        }).catch(error => {
          console.error('获取最近入侵事件失败:', error)
          this.$message.error('获取最近入侵事件失败')
          // 保持默认数据
        })
      },
      fetchActivityStats() {
        // 调用真实的API获取30天内新增事件统计
        getDailyStatistics(30).then(response => {
          if (response.code === 200) {
            const apiData = response.data || []
            
            // 生成最近30天的完整日期数组
            const dates = []
            const dataMap = {}
            
            // 建立API数据的映射
            apiData.forEach(item => {
              if (item.date) {
                dataMap[item.date] = item.count || 0
              }
            })
            
            // 生成完整的30天数据
            const today = new Date()
            const chartData = {
              dates: [],
              values: []
            }
            
            for (let i = 29; i >= 0; i--) {
              const date = new Date(today)
              date.setDate(date.getDate() - i)
              
              const dateStr = date.toISOString().split('T')[0] // YYYY-MM-DD格式
              const displayDate = `${date.getMonth() + 1}/${date.getDate()}` // M/D格式
              
              chartData.dates.push(displayDate)
              chartData.values.push(dataMap[dateStr] || 0)
            }
            
            this.activityChartData = chartData
            
            // 更新图表
            this.$nextTick(() => {
              if (this.$refs.activityChart) {
                this.$refs.activityChart.setChartData(this.activityChartData)
              }
            })
            
            console.log('获取30天告警统计成功，处理后数据:', chartData)
          } else {
            this.$message.error(response.msg || '获取30天告警统计失败')
            console.error('获取30天告警统计失败:', response.msg)
            // 使用默认数据
            // this.setDefaultActivityData()
          }
        }).catch(error => {
          console.error('获取30天告警统计失败:', error)
          this.$message.error('获取30天告警统计失败')
          // 使用默认数据
          // this.setDefaultActivityData()
        })
      },

      // 设置默认的活动数据
      setDefaultActivityData() {
        // 生成最近30天的日期和随机数据
        const today = new Date()
        const chartData = {
          dates: [],
          values: []
        }
        
        for (let i = 29; i >= 0; i--) {
          const date = new Date(today)
          date.setDate(date.getDate() - i)
          const displayDate = `${date.getMonth() + 1}/${date.getDate()}`
          
          chartData.dates.push(displayDate)
          // 生成0-15之间的随机数，模拟真实的告警数据分布
          chartData.values.push(Math.floor(Math.random() * 16))
        }

        this.activityChartData = chartData

        this.$nextTick(() => {
          if (this.$refs.activityChart) {
            this.$refs.activityChart.setChartData(this.activityChartData)
          }
        })
        
        console.log('使用默认30天活动数据:', chartData)
      },
      fetchInventoryStats() {
        this.inventoryLoading = true
        request({
          url: '/agent/agentInfo/inventory/global/stats',
          method: 'get'
        }).then(response => {
          if (response.code === 200) {
            this.inventoryStats = response.data || this.inventoryStats
            console.log('获取资产统计数据成功:', this.inventoryStats)
          } else {
            this.$message.error(response.msg || '获取资产统计数据失败')
          }
          this.inventoryLoading = false
        }).catch(error => {
          console.error('获取资产统计数据失败:', error)
          this.$message.error('获取资产统计数据失败')
          this.inventoryLoading = false
        })
      },

      fetchAgentStats() {
        this.loading = true
        request({
          url: '/agent/agentInfo/stats',
          method: 'get'
        }).then(response => {
          if (response.code === 200) {
            const data = response.data || {}
            this.totalEvents = data.total || 0
            this.processedEvents = data.online || 0
            this.unprocessedEvents = data.abnormal || 0
            this.anomalyEvents = data.disabled || 0
            this.highRiskEvents = data.offline || 0
            console.log('获取探针统计数据成功:', data)
          } else {
            this.$message.error(response.msg || '获取探针统计数据失败')
          }
          this.loading = false
        }).catch(error => {
          console.error('获取探针统计数据失败:', error)
          this.$message.error('获取探针统计数据失败')
          this.loading = false
        })
      },
      viewAllAlerts() {
        this.$router.push('/alert/list')
      },
      getAlertTagType(level) {
        return level === 'danger' ? 'danger' : 'warning'
      },
      getSeverityLevel(severity) {
        // 根据严重程度返回对应的级别
        if (!severity) return 'warning'
        
        switch (severity.toLowerCase()) {
          case 'critical':
          case 'high':
            return 'danger'
          case 'medium':
          case 'warning':
            return 'warning'
          case 'low':
          case 'info':
            return 'info'
          default:
            return 'warning'
        }
      },
    }
  }
  </script>

  <style lang="scss" scoped>
  .app-container {
    padding: 20px;
  }

  .overview-card {
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    overflow: hidden;
    color: #303133;
    transition: 0.3s;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

    .score-row {
      display: flex;
      align-items: center;
    }

    // Security Score Shield
    .score-shield-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 130px;

      .shield-bg {
        position: relative;
        width: 115px;
        height: 120px;
        background-image: url('data:image/svg+xml;base64,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');
        background-size: contain;
        background-repeat: no-repeat;
        display: flex;
        justify-content: center;
        align-items: center;

        .shield-inner {
          position: absolute;
          top: 24px;
          left: 50%;
          transform: translateX(-50%);
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #F8D25D, #ED9F1D);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .score-value {
            font-size: 36px;
            font-weight: bold;
            color: #fff;
          }
        }
      }
    }

    // Risk Stats
    .risk-stats {
      padding: 0 10px;

      .risk-item {
        justify-content: space-between;
        margin-bottom: 15px;

        .risk-label {
          font-size: 12px;

          &.danger {
            color: #f56c6c;
          }

          &.warning {
            color: #e6a23c;
          }

          &.medium {
            color: #E6A23C;
          }

          &.normal {
            color: #409eff;
          }

          &.success {
            color: #67c23a;
          }
        }

        .risk-value {
          font-size: 12px;
          margin-left: 15px;
        }
      }
    }
  }

  // Protection Card
  .protection-card {
    .protection-row {
      display: flex;
      align-items: flex-start;
    }

    .protection-circle-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 130px;

      .protection-circle {
        position: relative;
        width: 80px;
        height: 80px;

        .circle-inner {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #52c41a, #1da57a);
          border-radius: 50%;
        }

        .circle-dots {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
            animation-name: dotMove;
            animation-duration: 12s;
            animation-timing-function: ease-in-out;
            animation-iteration-count: infinite;

            &:nth-child(1) {
              top: 20%;
              left: 15%;
              animation-delay: 0s;
            }

            &:nth-child(2) {
              top: 60%;
              left: 75%;
              animation-delay: 2s;
            }

            &:nth-child(3) {
              top: 40%;
              left: 85%;
              animation-delay: 4s;
            }
          }

          @keyframes dotMove {
            0% {
              transform: translate(0, 0) scale(1);
              opacity: 0.8;
            }
            20% {
              transform: translate(10px, 15px) scale(1.2);
              opacity: 1;
            }
            40% {
              transform: translate(15px, 0px) scale(0.9);
              opacity: 0.7;
            }
            60% {
              transform: translate(-5px, 20px) scale(1.1);
              opacity: 0.9;
            }
            80% {
              transform: translate(-15px, 5px) scale(0.8);
              opacity: 0.6;
            }
            100% {
              transform: translate(0, 0) scale(1);
              opacity: 0.8;
            }
          }
        }
      }
    }

    .protection-details {
      padding-left: 10px;

      .protection-days {
        font-size: 16px;
        margin-bottom: 15px;
        font-weight: 500;

        span {
          font-weight: bold;
          color: #52c41a;
        }
      }

      .protection-status {
        .status-row {
          display: flex;
          flex-wrap: wrap;
          margin-bottom: 10px;

          span {
            font-size: 12px;
            display: inline-block;
            width: 32%;
          }

          &.highlight {
            color: #f56c6c;
          }

          .highlight {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // Statistics Section
  .statistics-row {
    margin-top: 20px;
  }

  .statistics-col {
    height: 245px;
  }

  .stats-card {
    padding: 0;
    overflow: hidden;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    // Event Card
    &.event-card {
      height: 100%;

      .event-header {
        padding: 15px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .event-total {
          .total-value {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
          }

          .total-label {
            font-size: 12px;
            color: #606266;

            i {
              color: #909399;
              cursor: pointer;
            }
          }
        }

        .event-chart {
          .chart-circle {
            width: 60px;
            height: 60px;

            .circular-chart {
              display: block;
              width: 100%;
              height: 100%;

              .circle-bg {
                fill: none;
                stroke: #eee;
                stroke-width: 3.8;
              }

              .circle {
                fill: none;
                stroke: #67c23a;
                stroke-width: 3.8;
                stroke-linecap: round;
                transform: rotate(-90deg);
                transform-origin: 50% 50%;
              }
            }
          }
        }
      }

      .event-status {
        padding: 15px;

        .status-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          .status-item {
            flex: 1;
            text-align: center;
            cursor: pointer;

            .status-value {
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 5px;
            }

            .status-label {
              font-size: 12px;
              color: #606266;
            }

            &.online {
              color: #67c23a;
            }

            &.abnormal {
              color: #e6a23c;
            }

            &.disabled {
              color: #909399;
            }

            &.offline {
              color: #f56c6c;
            }
          }
        }
      }
    }

    // Resource Card
    &.resource-card {
      height: 100%;

      .resource-list {
        padding: 15px;

        .resource-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .resource-item {
            flex: 1;
            text-align: center;
            padding: 10px 5px;
            display: flex;
            flex-direction: column;
            align-items: center;

            &.empty {
              visibility: hidden;
            }

            i {
              font-size: 20px;
              color: #409EFF;
              margin-bottom: 8px;
            }

            .resource-label {
              font-size: 12px;
              color: #606266;
              margin-bottom: 8px;
            }

            .resource-value {
              font-size: 18px;
              font-weight: bold;
              color: #303133;
            }
          }
        }
      }
    }
  }

  // Chart Card
  .chart-card {
    margin-top: 20px;
    height: 380px;

    .chart-header {
      font-size: 13px;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-container {
      width: 100%;
      height: 350px;
      overflow: hidden;
      position: relative;
      box-sizing: border-box;
      padding: 0;
      border: none !important;
    }
  }

  // Alerts Card
  .alerts-card {
    margin-top: 20px;
    height: 380px;

    .alerts-header {
      font-size: 14px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #303133;
    }

    .alerts-list {
      .alert-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.3s ease;
        min-height: 50px;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #f9f9f9;
          border-radius: 4px;
          margin: 0 -8px;
          padding: 12px 8px;
        }

        .alert-type {
          width: 120px;
          margin-right: 15px;
          flex-shrink: 0;
          display: flex;
          align-items: center;

          .el-tag {
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 11px;
            padding: 6px 8px;
            border-radius: 4px;
            border: none;
            font-weight: 500;
            line-height: 1.2;
          }
        }

        .alert-title {
          flex: 1;
          font-size: 13px;
          color: #606266;
          line-height: 1.4;
          margin-right: 15px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: flex;
          align-items: center;
        }

        .alert-time {
          width: 140px;
          text-align: right;
          color: #909399;
          font-size: 12px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
      }
    }
  }

  .chart-container {
    width: 100%;
    height: 350px;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
    padding: 0;
  }

  .card-header-title {
    height: 20px;
    line-height: 32px;
    font-size: 16px;
    font-weight: bold;
    padding-left: 4px;
    display: flex;
    align-items: center;
  }
  </style>
