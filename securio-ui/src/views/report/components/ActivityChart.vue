<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    chartData: {
      type: Object,
      default: () => ({ dates: [], values: [] })
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(newData) {
        if (this.chart && newData) {
          this.updateChart(newData)
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.updateChart(this.chartData)
    },
    update<PERSON>hart(data) {
      if (!this.chart) return
      const maxValue = data.values.length ? Math.max(...data.values) : 0
      const yMax = maxValue === 0 ? 18 : Math.ceil(maxValue * 1.2)
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: function(params) {
            const param = params[0]
            const realValue = param.data.realValue !== undefined ? param.data.realValue : param.value
            return `${param.name}<br/>${realValue} 个事件`
          }
        },
        grid: {
          top: 20,
          left: '3%',
          right: '4%',
          bottom: 36,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.dates,
          axisTick: { show: false },
          axisLabel: {
            show: true,
            color: '#409EFF',
            fontSize: 12,
            interval: 4
          },
          axisLine: { show: true },
          splitLine: { show: false }
        },
        yAxis: {
          type: 'log',
          logBase: 10,
          min: 1,
          axisLabel: { show: true, color: '#409EFF', fontSize: 12 },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: true, lineStyle: { color: ['#f0f0f0'], type: 'solid', width: 1 } },
          splitArea: { show: true, areaStyle: { color: ['rgba(245,245,245,0.2)', 'rgba(255,255,255,0.1)'] } }
        },
        series: [
          {
            name: '安全事件',
            type: 'bar',
            barWidth: '70%',
            data: data.values.map(value => {
              if (value === 0) {
                return {
                  value: 1, // log坐标不能为0，显示为1
                  realValue: 0, // 用于tooltip
                  itemStyle: {
                    color: '#e0e0e0',
                    borderType: 'dashed',
                    borderColor: '#bbb',
                    opacity: 0.5,
                    barBorderRadius: [6, 6, 0, 0]
                  }
                }
              } else {
                return {
                  value,
                  realValue: value,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#83bff6' },
                      { offset: 1, color: '#188df0' }
                    ]),
                    barBorderRadius: [6, 6, 0, 0]
                  }
                }
              }
            }),
            label: { show: false },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2378f7' },
                  { offset: 0.7, color: '#2378f7' },
                  { offset: 1, color: '#83bff6' }
                ])
              }
            },
            animationDelay: function (idx) { return idx * 10 }
          }
        ],
        animationEasing: 'elasticOut',
        animationDelayUpdate: function (idx) { return idx * 5 }
      })
    }
  }
}
</script>
