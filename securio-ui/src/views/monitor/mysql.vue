<template>
  <div class="app-container">
    <!-- 标题 -->
    <div class="page-header">
      <h1>MySQL 连接统计监控</h1>
      <p class="page-description">监控 MySQL 连接行为和异常活动</p>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <div class="filter-row">
          <!-- 数据库IP下拉框 -->
          <div class="filter-item">
            <label>数据库 IP：</label>
            <el-select
              v-model="filters.databaseIp"
              placeholder="请选择数据库IP地址"
              style="width: 200px;"
              clearable
              filterable
              @change="handleQuery"
            >
              <el-option
                v-for="ip in databaseIpOptions"
                :key="ip"
                :label="ip"
                :value="ip"
            />
            </el-select>
          </div>

          <!-- 连接规则选择 -->
          <div class="filter-item">
            <label>连接事件类型：</label>
            <el-select
              v-model="filters.rule"
              placeholder="请选择连接规则"
              style="width: 200px;"
              @change="handleQuery"
            >
              <el-option label="连接建立" value="mysql_connection_established" />
              <el-option label="连接关闭" value="mysql_connection_closed" />
              <el-option label="客户端请求" value="mysql_client_request" />
            </el-select>
          </div>

          <!-- 查询按钮 -->
          <div class="filter-item">
            <el-button
              type="primary"
              :loading="loading"
              @click="handleQuery"
              icon="el-icon-search"
            >
              查询
            </el-button>
            <el-button @click="handleReset" icon="el-icon-refresh">重置</el-button>
          </div>
        </div>

        <!-- 时间选择器 -->
        <div class="time-range-row">
          <TimeRangeSelector
            ref="timeRangeSelector"
            label="时间范围"
            :show-timeline="true"
            default-mode="quick"
            default-quick-range="1d"
            :default-time="getCurrentTime()"
            @time-change="handleTimeChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-section" v-if="hasData">
      <!-- 桑基图 -->
      <el-card class="chart-card">
        <div slot="header" class="chart-header">
          <span class="chart-title">
            <i class="el-icon-share"></i>
            MySQL 连接流向图
          </span>
          <div class="chart-legend">
            <span class="legend-item">
              <span class="legend-color source"></span>
              来源IP
            </span>
            <span class="legend-item">
              <span class="legend-color target"></span>
              数据库IP
            </span>
          </div>
        </div>
        <div v-loading="sankeyLoading" class="chart-container">
          <div v-if="sankeyData.nodes && sankeyData.nodes.length > 0">
            <IPFlowSankeyChart
              :data="sankeyData"
              height="500px"
              :show-own-drawer="true"
              @node-click="handleSankeyNodeClick"
              @link-click="handleSankeyLinkClick"
            />
          </div>
          <div v-else class="no-data-placeholder">
            <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
            <p style="margin-top: 15px; color: #606266;">暂无连接流向数据</p>
            <p style="color: #909399; font-size: 12px;">在指定条件下未发现 MySQL 相关连接记录</p>
          </div>
        </div>
      </el-card>

      <!-- 时间序列图 -->
      <el-card class="chart-card">
        <div slot="header" class="chart-header">
          <span class="chart-title">
            <i class="el-icon-time"></i>
            MySQL 连接趋势图
          </span>
          <div class="chart-controls">
            <el-select v-model="timeSeriesGroupBy" @change="updateTimeSeriesChart" size="small">
              <el-option label="按来源IP分组" value="src_ip" />
              <el-option label="按数据库IP分组" value="agent_hostip" />
            </el-select>
          </div>
        </div>
        <div v-loading="timeSeriesLoading" class="chart-container">
          <!-- 始终渲染图表容器，避免DOM查找失败 -->
          <div ref="timeSeriesChart" class="time-series-chart" style="height: 400px;"></div>
          <div v-if="!timeSeriesData || !timeSeriesData.data || !timeSeriesData.data.result || timeSeriesData.data.result.length === 0" class="no-data-placeholder" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
            <i class="el-icon-time" style="font-size: 48px; color: #F56C6C;"></i>
            <p style="margin-top: 15px; color: #606266;">暂无时间序列数据</p>
            <p style="color: #909399; font-size: 12px;">在指定时间范围内未发现相关连接记录</p>
          </div>
        </div>
      </el-card>

      <!-- 流量图表 (仅在mysql_client_request规则时显示) -->
      <el-card v-if="filters.rule === 'mysql_client_request'" class="chart-card">
        <div slot="header" class="chart-header">
          <span class="chart-title">
            <i class="el-icon-upload"></i>
            MySQL 流量趋势图
          </span>
          <div class="chart-legend">
            <span class="legend-item">
              <span class="legend-color" style="background-color: #409EFF;"></span>
              入站流量
            </span>
            <span class="legend-item">
              <span class="legend-color" style="background-color: #67C23A;"></span>
              出站流量
            </span>
          </div>
        </div>
        <div v-loading="trafficLoading" class="chart-container">
          <!-- 始终渲染图表容器，避免DOM查找失败 -->
          <div ref="trafficChart" class="traffic-chart" style="height: 400px;"></div>
          <div v-if="!trafficData || (!trafficData.inbound && !trafficData.outbound)" class="no-data-placeholder" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
            <i class="el-icon-upload" style="font-size: 48px; color: #F56C6C;"></i>
            <p style="margin-top: 15px; color: #606266;">暂无流量数据</p>
            <p style="color: #909399; font-size: 12px;">在指定时间范围内未发现相关流量记录</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 无数据提示 -->
    <div v-else-if="!loading && queryExecuted" class="no-data-section">
      <el-card>
        <div class="no-data-placeholder large">
          <i class="el-icon-data-line" style="font-size: 64px; color: #C0C4CC;"></i>
          <h3 style="margin: 20px 0 10px 0; color: #606266;">暂无数据</h3>
          <p style="color: #909399;">请调整筛选条件后重新查询</p>
        </div>
      </el-card>
    </div>

    <!-- 日志查询组件 -->
    <div v-if="queryExecuted" class="log-section">
      <LogQueryComponent
        :key="logQueryKey"
        :title="'MySQL 连接日志'"
        :subtitle="'基于 SECURIO_FALCO_EVENTS 数据源的 MySQL 连接详细日志'"
        :default-query="logQuery"
        :default-time-range="'1d'"
        :stream="'SECURIO_FALCO_EVENTS'"
        :instance="'vmlog1'"
        :settings="logQuerySettings"
        ref="logQueryComponent"
      />
    </div>

    <!-- 桑基图连线点击日志详情对话框 -->
    <el-dialog
      title="连接详细日志"
      :visible.sync="linkDetailDialogVisible"
      width="90%"
      append-to-body
      :before-close="handleLinkDetailClose">
      <div class="link-detail-content">
        <div class="link-detail-header">
          <el-tag type="info" size="small">来源IP</el-tag>
          <span class="ip-text">{{ selectedLinkInfo.sourceIp }}</span>
          <i class="el-icon-right" style="margin: 0 10px; color: #909399;"></i>
          <el-tag type="warning" size="small">数据库IP</el-tag>
          <span class="ip-text">{{ selectedLinkInfo.targetIp }}</span>
          <span class="connection-count">{{ selectedLinkInfo.connectionCount }} 次连接</span>
        </div>
        
        <LogQueryComponent
          v-if="linkDetailDialogVisible"
          :title="'连接详细日志'"
          :subtitle="`${selectedLinkInfo.sourceIp} → ${selectedLinkInfo.targetIp} 的 MySQL 连接记录`"
          :default-query="buildLinkDetailQuery()"
          :default-time-range="'1d'"
          :stream="'SECURIO_FALCO_EVENTS'"
          :instance="'vmlog1'"
          :settings="linkDetailLogSettings"
          ref="linkDetailLogComponent"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TimeRangeSelector from '@/components/TimeRangeSelector'
import IPFlowSankeyChart from '@/views/analyze/components/IPFlowSankeyChart'
import LogQueryComponent from '@/components/log_query_component'
import * as echarts from 'echarts'
import { getMysqlSankeyData, getMysqlTimeSeriesData, getMysqlOverviewStats, getMysqlTrafficData } from '@/api/opslog/mysql-monitor'

export default {
  name: 'MysqlMonitor',
  components: {
    TimeRangeSelector,
    IPFlowSankeyChart,
    LogQueryComponent
  },
  data() {
    return {
      loading: false,
      queryExecuted: false,
      hasData: false,
      
      // 筛选条件
      filters: {
        databaseIp: '',
        rule: 'mysql_connection_established',
        timeRange: '1d',
        startTime: '',
        endTime: ''
      },
      
      // 数据库IP选项列表
      databaseIpOptions: [
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************'
      ],
      
      // 时间范围数据
      timeRangeData: null,
      preciseTimeData: null, // 存储精确时间数据
      
      // 桑基图相关
      sankeyLoading: false,
      sankeyData: {
        nodes: [],
        links: []
      },
      
      // 时间序列图相关
      timeSeriesLoading: false,
      timeSeriesData: {},
      timeSeriesChart: null,
      timeSeriesGroupBy: 'src_ip',
      chartInitRetryCount: 0,
      maxRetryCount: 5,
      
      // 流量图表相关
      trafficLoading: false,
      trafficData: {},
      trafficChart: null,
      
      // 统计概览数据
      overviewStats: {},

      // 日志查询相关
      logQuery: '',
      logQueryKey: Date.now(),
      logQuerySettings: {
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          { prop: 'agent_hostip', label: '数据库IP', width: '130', tag: true },
          { prop: 'agent_hostname', label: '数据库主机名', width: '150' },
          { prop: 'rule', label: '规则类型', width: '180', tag: true },
          { prop: 'src_ip', label: '来源IP', width: '130', tag: true },
          { prop: 'src_port', label: '来源端口', width: '90' },
          { prop: 'dst_ip', label: '目标IP', width: '130', tag: true },
          { prop: 'dst_port', label: '目标端口', width: '90' },
          { prop: '_msg', label: '消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right',
        defaultFilters: []
      },
      linkDetailDialogVisible: false,
      selectedLinkInfo: {
        sourceIp: '',
        targetIp: '',
        connectionCount: 0
      },
      linkDetailLogSettings: {
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          { prop: 'rule', label: '规则类型', width: '180', tag: true },
          { prop: 'src_port', label: '来源端口', width: '90', tag: true },
          { prop: 'agent_hostname', label: '数据库主机名', width: '150' },
          { prop: '_msg', label: '消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right',
        defaultFilters: []
      }
    }
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
    
    // 不在mounted时初始化图表，等到真正需要显示数据时再初始化
    console.log('MySQL监控页面加载完成，等待用户查询')
  },
  beforeDestroy() {
    if (this.timeSeriesChart) {
      this.timeSeriesChart.dispose()
    }
    if (this.trafficChart) {
      this.trafficChart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 获取当前时间
    getCurrentTime() {
      const now = new Date()
      return now.getFullYear() + '-' +
        String(now.getMonth() + 1).padStart(2, '0') + '-' +
        String(now.getDate()).padStart(2, '0') + ' ' +
        String(now.getHours()).padStart(2, '0') + ':' +
        String(now.getMinutes()).padStart(2, '0') + ':' +
        String(now.getSeconds()).padStart(2, '0')
    },
    
    // 处理时间范围变化
    handleTimeChange(timeData) {
      this.timeRangeData = timeData
      this.preciseTimeData = timeData
      
      if (timeData) {
        if (timeData.rangeType === 'quick' && timeData.quickTimeRange) {
          // 快速时间选择
          this.filters.timeRange = timeData.quickTimeRange;
          this.filters.startTime = '';
          this.filters.endTime = '';
          console.log('使用快速时间选择:', timeData.quickTimeRange);
        } else if (timeData.rangeType === 'precise' && timeData.timeRange.start && timeData.timeRange.end) {
          // 精确时间选择
          this.filters.timeRange = 'precise';
          this.filters.startTime = timeData.timeRange.start;
          this.filters.endTime = timeData.timeRange.end;
          console.log('使用精确时间选择:', timeData.timeRange.start, '~', timeData.timeRange.end);
        }
      } else {
        this.filters.startTime = '';
        this.filters.endTime = '';
        this.filters.timeRange = '1d';
      }
      
      console.log('时间范围变化:', timeData, '当前过滤器:', this.filters)
    },
    
    // 处理查询
    handleQuery() {
      // 基本验证
      if (this.loading) {
        this.$message.warning('查询正在进行中，请稍候...')
        return
      }
      
      // 如果没有选择数据库IP，提示用户
      if (!this.filters.databaseIp || this.filters.databaseIp.trim() === '') {
        this.$message.warning('请选择数据库IP地址')
        return
      }
      
      // IP格式简单验证
      const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/
      if (!ipRegex.test(this.filters.databaseIp.trim())) {
        this.$message.warning('请输入有效的IP地址格式')
        return
      }
      
      this.loading = true
      this.queryExecuted = true
      
      // 更新日志查询语句和key
      this.logQuery = this.buildLogQuery()
      this.logQueryKey = Date.now()
      
      console.log('开始查询MySQL监控数据...', {
        databaseIp: this.filters.databaseIp,
        rule: this.filters.rule
      })
      
      // 构建查询任务列表
      const queryTasks = [
        this.fetchSankeyData(),
        this.fetchTimeSeriesData(),
        this.fetchOverviewStats()
      ]
      
      // 如果是mysql_client_request规则，添加流量数据查询
      if (this.filters.rule === 'mysql_client_request') {
        queryTasks.push(this.fetchTrafficData())
      }
      
      // 并行执行所有查询
      Promise.all(queryTasks).then(() => {
        this.$message.success('查询完成')
      }).catch((error) => {
        console.error('查询过程中出现错误:', error)
        this.$message.error('查询失败，请检查网络连接或稍后重试')
      }).finally(() => {
        this.loading = false
        this.checkHasData()
      })
    },
    
    // 重置筛选条件
    handleReset() {
      this.filters = {
        databaseIp: '',
        rule: 'mysql_connection_established',
        timeRange: '1d',
        startTime: '',
        endTime: ''
      }
      this.queryExecuted = false
      this.hasData = false
      this.sankeyData = { nodes: [], links: [] }
      this.timeSeriesData = {}
      this.trafficData = {}
      this.overviewStats = {}
      this.timeRangeData = null
      this.preciseTimeData = null
      
      // 安全地清空图表
      if (this.timeSeriesChart) {
        try {
          this.timeSeriesChart.clear()
        } catch (error) {
          console.warn('清空时间序列图表失败:', error)
        }
      }
      
      if (this.trafficChart) {
        try {
          this.trafficChart.clear()
        } catch (error) {
          console.warn('清空流量图表失败:', error)
        }
      }
      
      // 重置时间范围选择器
      if (this.$refs.timeRangeSelector) {
        this.$refs.timeRangeSelector.setTimeData(null);
      }
      
      this.$message.info('已重置筛选条件')
    },
    
    // 检查是否有数据
    checkHasData() {
      this.hasData = (this.sankeyData.nodes && this.sankeyData.nodes.length > 0) ||
                     (this.timeSeriesData && this.timeSeriesData.data && this.timeSeriesData.data.result && this.timeSeriesData.data.result.length > 0) ||
                     (this.trafficData && (this.trafficData.inbound || this.trafficData.outbound)) ||
                     (this.overviewStats.totalConnections && this.overviewStats.totalConnections > 0)
    },
    
    // 获取桑基图数据
    async fetchSankeyData() {
      this.sankeyLoading = true
      
      try {
        // 构建时间参数
        const timeParams = this.buildTimeParams()
        
        const response = await getMysqlSankeyData(
          this.filters.rule,
          this.filters.databaseIp,
          timeParams.timeRange,
          10,
          timeParams.startTime,
          timeParams.endTime
        )
        
        if (response.code === 200 && response.data) {
          this.sankeyData = this.convertToSankeyFormat(response.data)
          console.log('桑基图数据:', this.sankeyData)
        } else {
          console.warn('桑基图查询返回异常:', response)
          this.sankeyData = { nodes: [], links: [] }
        }
        
      } catch (error) {
        console.error('获取桑基图数据失败:', error)
        this.$message.error('获取桑基图数据失败: ' + (error.message || error))
        this.sankeyData = { nodes: [], links: [] }
      } finally {
        this.sankeyLoading = false
      }
    },
    
    // 获取时间序列数据
    async fetchTimeSeriesData() {
      this.timeSeriesLoading = true
      
      try {
        // 构建时间参数
        const timeParams = this.buildTimeParams()
        
        const response = await getMysqlTimeSeriesData(
          this.filters.rule,
          this.filters.databaseIp,
          this.timeSeriesGroupBy,
          timeParams.timeRange,
          timeParams.step,
          timeParams.startTime,
          timeParams.endTime
        )
        
        if (response.code === 200 && response.data) {
          this.timeSeriesData = response.data
          console.log('时间序列数据:', this.timeSeriesData)
          console.log('时间序列数据结构检查:', {
            hasData: !!this.timeSeriesData.data,
            hasResult: !!(this.timeSeriesData.data && this.timeSeriesData.data.result),
            resultLength: this.timeSeriesData.data && this.timeSeriesData.data.result ? this.timeSeriesData.data.result.length : 0,
            firstResult: this.timeSeriesData.data && this.timeSeriesData.data.result && this.timeSeriesData.data.result[0]
          })
          
          // 确保图表初始化后再更新数据
          this.$nextTick(() => {
            console.log('时间序列数据获取成功，准备初始化图表')
            if (!this.timeSeriesChart) {
              this.chartInitRetryCount = 0 // 重置重试计数器
              this.initTimeSeriesChart()
            }
            // 延迟更新，确保图表初始化完成
            setTimeout(() => {
              console.log('开始更新时间序列图表，数据:', this.timeSeriesData)
              this.updateTimeSeriesChart()
            }, 300)
          })
        } else {
          console.warn('时间序列查询返回异常:', response)
          this.timeSeriesData = {}
        }
        
      } catch (error) {
        console.error('获取时间序列数据失败:', error)
        this.$message.error('获取时间序列数据失败: ' + (error.message || error))
        this.timeSeriesData = {}
      } finally {
        this.timeSeriesLoading = false
      }
    },
    
    // 获取统计概览数据
    async fetchOverviewStats() {
      try {
        // 构建时间参数
        const timeParams = this.buildTimeParams()
        
        const response = await getMysqlOverviewStats(
          this.filters.rule,
          this.filters.databaseIp,
          timeParams.timeRange,
          timeParams.startTime,
          timeParams.endTime
        )
        
        if (response.code === 200 && response.data) {
          this.overviewStats = response.data
          console.log('统计概览数据:', this.overviewStats)
        } else {
          console.warn('统计概览查询返回异常:', response)
          this.overviewStats = {}
        }
        
      } catch (error) {
        console.error('获取统计概览数据失败:', error)
        this.overviewStats = {}
      }
    },

    // 获取流量数据
    async fetchTrafficData() {
      this.trafficLoading = true
      
      try {
        // 构建时间参数
        const timeParams = this.buildTimeParams()
        
        const response = await getMysqlTrafficData(
          this.filters.databaseIp,
          timeParams.timeRange,
          timeParams.step,
          timeParams.startTime,
          timeParams.endTime
        )
        
        if (response.code === 200 && response.data) {
          this.trafficData = response.data
          console.log('流量数据:', this.trafficData)
          console.log('流量数据结构检查:', {
            hasInbound: !!this.trafficData.inbound,
            hasOutbound: !!this.trafficData.outbound,
            inboundData: this.trafficData.inbound && this.trafficData.inbound.data,
            outboundData: this.trafficData.outbound && this.trafficData.outbound.data
          })
          
          // 确保图表初始化后再更新数据
          this.$nextTick(() => {
            console.log('流量数据获取成功，准备初始化图表')
            if (!this.trafficChart) {
              this.initTrafficChart()
            }
            // 延迟更新，确保图表初始化完成
            setTimeout(() => {
              console.log('开始更新流量图表，数据:', this.trafficData)
              this.updateTrafficChart()
            }, 300)
          })
        } else {
          console.warn('流量查询返回异常:', response)
          this.trafficData = {}
        }
        
      } catch (error) {
        console.error('获取流量数据失败:', error)
        this.$message.error('获取流量数据失败: ' + (error.message || error))
        this.trafficData = {}
      } finally {
        this.trafficLoading = false
      }
    },
    
    // 构建时间参数的辅助方法
    buildTimeParams() {
      const params = {
        timeRange: this.filters.timeRange || '1d',
        step: '1h',
        startTime: null,
        endTime: null
      }
      
      // 如果是精确时间选择，传递具体的开始和结束时间
      if (this.filters.timeRange === 'precise' && this.filters.startTime && this.filters.endTime) {
        // 转换时间格式为 VictoriaLogs 期望的格式
        params.startTime = this.formatTimeForVictoriaLogs(this.filters.startTime)
        params.endTime = this.formatTimeForVictoriaLogs(this.filters.endTime)
        
        // 精确时间选择时，根据时间跨度调整步长
        if (this.preciseTimeData && this.preciseTimeData.timeRange.duration) {
          const duration = this.preciseTimeData.timeRange.duration
          if (duration <= 60) { // 1小时内
            params.step = '1m'
          } else if (duration <= 60 * 24) { // 24小时内
            params.step = '5m'
          } else {
            params.step = '1h'
          }
        }
      } else {
        // 快速时间选择时，根据时间范围调整步长
        const timeRange = this.filters.timeRange
        if (timeRange === '1h' || timeRange === '2h' || timeRange === '6h') {
          params.step = '5m'
        } else if (timeRange === '12h' || timeRange === '1d') {
          params.step = '1h'
        } else {
          params.step = '1h'
        }
      }
      
      console.log('构建的时间参数:', params)
      return params
    },
    
    // 格式化时间为 VictoriaLogs 格式
    formatTimeForVictoriaLogs(timeStr) {
      if (!timeStr) return null
      
      try {
        // 如果已经是 UTC 格式且包含 Z，直接返回
        if (timeStr.endsWith('Z') && timeStr.includes('T')) {
          return timeStr
        }
        
        let date
        
        // 处理不同的时间格式
        if (timeStr.includes('T')) {
          // ISO 格式：2024-01-01T12:00:00
          date = new Date(timeStr)
        } else if (timeStr.includes(' ')) {
          // 简单格式：2024-01-01 12:00:00
          date = new Date(timeStr.replace(' ', 'T'))
        } else {
          date = new Date(timeStr)
        }
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.warn('无效的时间格式:', timeStr)
          return timeStr
        }
        
        // 转换为 UTC 时间并格式化为 VictoriaLogs 期望的格式
        // 假设输入时间是东八区时间，需要减去8小时转换为UTC
        const utcDate = new Date(date.getTime() - 8 * 60 * 60 * 1000)
        const result = utcDate.toISOString().replace('.000Z', 'Z')
        
        console.log('时间转换:', timeStr, '->', result)
        return result
        
      } catch (error) {
        console.warn('时间格式转换失败:', timeStr, error)
        return timeStr
      }
    },
    
    // 转换后端数据为桑基图格式
    convertToSankeyFormat(backendData) {
      const nodes = []
      const links = []
      
      if (backendData.nodes && Array.isArray(backendData.nodes)) {
        backendData.nodes.forEach(node => {
          nodes.push({
            name: node.name,
            type: node.type
          })
        })
      }
      
      if (backendData.links && Array.isArray(backendData.links)) {
        backendData.links.forEach(link => {
          links.push({
            source: link.source,
            target: link.target,
            value: link.value
          })
        })
      }
      
      return { nodes, links }
    },
    
    // 初始化时间序列图表
    initTimeSeriesChart() {
      try {
        console.log('尝试初始化时间序列图表，DOM元素:', this.$refs.timeSeriesChart)
        if (this.$refs.timeSeriesChart) {
          // 确保元素已经渲染并且有尺寸
          const rect = this.$refs.timeSeriesChart.getBoundingClientRect()
          console.log('时间序列图表容器尺寸:', rect.width, 'x', rect.height)
          
          if (rect.width > 0 && rect.height > 0) {
            this.timeSeriesChart = echarts.init(this.$refs.timeSeriesChart)
            this.chartInitRetryCount = 0 // 重置重试计数器
            console.log('时间序列图表初始化成功')
          } else {
            console.warn('时间序列图表容器尺寸为0，延迟重试')
            if (this.chartInitRetryCount < this.maxRetryCount) {
              this.chartInitRetryCount++
              setTimeout(() => {
                this.initTimeSeriesChart()
              }, 500)
            }
          }
        } else {
          console.warn(`时间序列图表DOM元素未找到，重试次数: ${this.chartInitRetryCount}/${this.maxRetryCount}`)
          
          // 检查是否超过最大重试次数
          if (this.chartInitRetryCount < this.maxRetryCount) {
            this.chartInitRetryCount++
            // 延迟重试
            setTimeout(() => {
              this.initTimeSeriesChart()
            }, 500)
          } else {
            console.error('时间序列图表初始化失败：超过最大重试次数，DOM元素可能不存在')
            this.chartInitRetryCount = 0 // 重置计数器
          }
        }
      } catch (error) {
        console.error('初始化时间序列图表失败:', error)
        this.chartInitRetryCount = 0 // 重置计数器
      }
    },
    
    // 更新时间序列图表
    updateTimeSeriesChart() {
      try {
        if (!this.timeSeriesChart) {
          console.log('图表未初始化，尝试重新初始化...')
          // 重置重试计数器，然后尝试初始化
          this.chartInitRetryCount = 0
          this.initTimeSeriesChart()
          
          // 只有在图表初始化成功后才继续更新
          if (this.timeSeriesChart) {
            console.log('图表初始化成功，继续更新...')
          } else {
            console.warn('图表初始化失败，跳过更新')
            return
          }
        }
        
        if (!this.timeSeriesData || !this.timeSeriesData.data || !this.timeSeriesData.data.result || this.timeSeriesData.data.result.length === 0) {
          console.log('暂无时间序列数据，显示空状态', {
            hasTimeSeriesData: !!this.timeSeriesData,
            hasData: !!(this.timeSeriesData && this.timeSeriesData.data),
            hasResult: !!(this.timeSeriesData && this.timeSeriesData.data && this.timeSeriesData.data.result),
            resultLength: this.timeSeriesData && this.timeSeriesData.data && this.timeSeriesData.data.result ? this.timeSeriesData.data.result.length : 0
          })
          this.timeSeriesChart.setOption({
            title: {
              text: 'MySQL 连接趋势图',
              left: 'center',
              textStyle: {
                fontSize: 16,
                fontWeight: 600
              }
            },
            xAxis: {
              type: 'time',
              boundaryGap: false
            },
            yAxis: {
              type: 'value',
              name: '连接数'
            },
            series: [],
            graphic: {
              type: 'text',
              left: 'center',
              top: 'middle',
              style: {
                text: '暂无数据',
                fontSize: 14,
                fill: '#999'
              }
            }
          })
          return
        }
        
        // 构建图表选项
        const option = {
          title: {
            text: 'MySQL 连接趋势图',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 600
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: function(params) {
              let result = params[0].axisValueLabel + '<br/>'
              params.forEach(function(item) {
                result += item.marker + ' ' + item.seriesName + ': ' + item.value[1] + '<br/>'
              })
              return result
            }
          },
          legend: {
            bottom: '5%',
            type: 'scroll'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            axisLabel: {
              formatter: function(value) {
                const date = new Date(value)
                return (date.getMonth() + 1) + '/' + date.getDate() + ' ' + date.getHours() + ':00'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '连接数',
            axisLabel: {
              formatter: function(value) {
                if (value >= 1000000) {
                  return (value / 1000000).toFixed(1) + 'M'
                } else if (value >= 1000) {
                  return (value / 1000).toFixed(1) + 'K'
                }
                return value
              }
            }
          },
          series: this.buildTimeSeriesSeries()
        }
        
        this.timeSeriesChart.setOption(option, true)
        console.log('时间序列图表更新成功')
        
      } catch (error) {
        console.error('更新时间序列图表失败:', error)
      }
    },
    
    // 构建时间序列图表数据
    buildTimeSeriesSeries() {
      const series = []
      
      console.log('构建时间序列数据，原始数据:', this.timeSeriesData)
      
      // 根据数据结构构建系列
      if (this.timeSeriesData.data && this.timeSeriesData.data.result) {
        console.log('开始构建时间序列，结果数量:', this.timeSeriesData.data.result.length)
        this.timeSeriesData.data.result.forEach((result, index) => {
          const metric = result.metric || {}
          const groupValue = metric[this.timeSeriesGroupBy] || `未知${index + 1}`
          
          const data = (result.values || []).map(item => [
            item[0] * 1000, // 时间戳转毫秒
            parseInt(item[1]) || 0
          ])
          
          console.log(`系列 ${index}: ${groupValue}, 数据点数量: ${data.length}`, data.slice(0, 3))
          
          series.push({
            name: groupValue,
            type: 'line',
            smooth: true,
            data: data,
            itemStyle: {
              color: this.getSeriesColor(index)
            },
            lineStyle: {
              width: 2
            },
            areaStyle: {
              opacity: 0.1
            },
            emphasis: {
              focus: 'series'
            }
          })
        })
      } else {
        console.log('时间序列数据结构不正确，无法构建系列')
      }
      
      console.log('构建的时间序列系列:', series)
      return series
    },
    
    // 获取系列颜色
    getSeriesColor(index) {
      const colors = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#6e7074'
      ]
      return colors[index % colors.length]
    },
    
    // 处理桑基图节点点击
    handleSankeyNodeClick(nodeData) {
      console.log('桑基图节点点击:', nodeData)
      this.selectedLinkInfo = nodeData
      this.linkDetailDialogVisible = true
    },
    
    // 处理窗口大小变化
    handleResize() {
      if (this.timeSeriesChart) {
        this.timeSeriesChart.resize()
      }
      if (this.trafficChart) {
        this.trafficChart.resize()
      }
    },

    // 初始化流量图表
    initTrafficChart() {
      try {
        console.log('尝试初始化流量图表，DOM元素:', this.$refs.trafficChart)
        if (this.$refs.trafficChart) {
          // 确保元素已经渲染并且有尺寸
          const rect = this.$refs.trafficChart.getBoundingClientRect()
          console.log('流量图表容器尺寸:', rect.width, 'x', rect.height)
          
          if (rect.width > 0 && rect.height > 0) {
            this.trafficChart = echarts.init(this.$refs.trafficChart)
            console.log('流量图表初始化成功')
          } else {
            console.warn('流量图表容器尺寸为0，延迟重试')
            setTimeout(() => {
              this.initTrafficChart()
            }, 500)
          }
        } else {
          console.warn('流量图表DOM元素未找到')
        }
      } catch (error) {
        console.error('初始化流量图表失败:', error)
      }
    },

    // 更新流量图表
    updateTrafficChart() {
      try {
        if (!this.trafficChart) {
          console.log('流量图表未初始化，尝试重新初始化...')
          this.initTrafficChart()
          if (!this.trafficChart) {
            console.warn('流量图表初始化失败，跳过更新')
            return
          }
        }

        if (!this.trafficData || (!this.trafficData.inbound && !this.trafficData.outbound)) {
          console.log('暂无流量数据，显示空状态', {
            hasTrafficData: !!this.trafficData,
            hasInbound: !!(this.trafficData && this.trafficData.inbound),
            hasOutbound: !!(this.trafficData && this.trafficData.outbound)
          })
          this.trafficChart.setOption({
            title: {
              text: 'MySQL 流量趋势图',
              left: 'center',
              textStyle: {
                fontSize: 16,
                fontWeight: 600
              }
            },
            xAxis: {
              type: 'time',
              boundaryGap: false
            },
            yAxis: {
              type: 'value',
              name: '流量 (bytes)'
            },
            series: [],
            graphic: {
              type: 'text',
              left: 'center',
              top: 'middle',
              style: {
                text: '暂无数据',
                fontSize: 14,
                fill: '#999'
              }
            }
          })
          return
        }

        // 构建图表选项
        const option = {
          title: {
            text: 'MySQL 流量趋势图',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 600
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: function(params) {
              let html = params[0].axisValueLabel + '<br/>'
              params.forEach(item => {
                const value = item.value[1] || 0
                const unit = value > 1024 * 1024 ? 'MB' : (value > 1024 ? 'KB' : 'bytes')
                const formattedValue = value > 1024 * 1024 ? 
                  (value / (1024 * 1024)).toFixed(2) : 
                  (value > 1024 ? (value / 1024).toFixed(2) : value)
                html += `${item.marker}${item.seriesName}: ${formattedValue} ${unit}<br/>`
              })
              return html
            }
          },
          legend: {
            data: ['入站流量', '出站流量'],
            top: 30
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            axisLabel: {
              formatter: function(value) {
                const date = new Date(value)
                return date.getHours().toString().padStart(2, '0') + ':' + 
                       date.getMinutes().toString().padStart(2, '0')
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '流量 (bytes)',
            axisLabel: {
              formatter: function(value) {
                if (value > 1024 * 1024) {
                  return (value / (1024 * 1024)).toFixed(1) + 'MB'
                } else if (value > 1024) {
                  return (value / 1024).toFixed(1) + 'KB'
                }
                return value + 'B'
              }
            }
          },
          series: this.buildTrafficSeries()
        }

        this.trafficChart.setOption(option, true)
        console.log('流量图表更新完成')
      } catch (error) {
        console.error('更新流量图表失败:', error)
      }
    },

    // 构建流量图表系列数据
    buildTrafficSeries() {
      const series = []

      console.log('构建流量数据，原始数据:', this.trafficData)

      // 入站流量数据
      if (this.trafficData.inbound && this.trafficData.inbound.data && this.trafficData.inbound.data.result) {
        console.log('处理入站流量数据，结果数量:', this.trafficData.inbound.data.result.length)
        const inboundData = []
        this.trafficData.inbound.data.result.forEach(result => {
          if (result.values) {
            result.values.forEach(item => {
              inboundData.push([
                item[0] * 1000, // 时间戳转毫秒
                parseFloat(item[1]) || 0 // 流量值
              ])
            })
          }
        })

        console.log('入站流量数据点数量:', inboundData.length, inboundData.slice(0, 3))

        if (inboundData.length > 0) {
          series.push({
            name: '入站流量',
            type: 'line',
            smooth: true,
            data: inboundData,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              width: 2
            },
            areaStyle: {
              opacity: 0.1
            },
            emphasis: {
              focus: 'series'
            }
          })
        }
      } else {
        console.log('入站流量数据结构不正确')
      }

      // 出站流量数据
      if (this.trafficData.outbound && this.trafficData.outbound.data && this.trafficData.outbound.data.result) {
        console.log('处理出站流量数据，结果数量:', this.trafficData.outbound.data.result.length)
        const outboundData = []
        this.trafficData.outbound.data.result.forEach(result => {
          if (result.values) {
            result.values.forEach(item => {
              outboundData.push([
                item[0] * 1000, // 时间戳转毫秒
                parseFloat(item[1]) || 0 // 流量值
              ])
            })
          }
        })

        console.log('出站流量数据点数量:', outboundData.length, outboundData.slice(0, 3))

        if (outboundData.length > 0) {
          series.push({
            name: '出站流量',
            type: 'line',
            smooth: true,
            data: outboundData,
            itemStyle: {
              color: '#67C23A'
            },
            lineStyle: {
              width: 2
            },
            areaStyle: {
              opacity: 0.1
            },
            emphasis: {
              focus: 'series'
            }
          })
        }
      } else {
        console.log('出站流量数据结构不正确')
      }

      console.log('构建的流量系列:', series)
      return series
    },

    // 构建日志查询
    buildLogQuery() {
      let query = []
      
      // 基础查询：指定流和规则
      query.push(`_stream:{stream="SECURIO_FALCO_EVENTS"}`)
      query.push(`rule:"${this.filters.rule}"`)
      
      // 添加数据库IP过滤（如果有指定）
      if (this.filters.databaseIp && this.filters.databaseIp.trim() !== '') {
        query.push(`agent_hostip:"${this.filters.databaseIp.trim()}"`)
      }
      
      // 添加时间范围
      if (this.filters.timeRange === 'precise' && this.filters.startTime && this.filters.endTime) {
        // 精确时间范围
        query.push(`_time:[${this.filters.startTime},${this.filters.endTime}]`)
      } else {
        // 快速时间选择
        query.push(`_time:${this.filters.timeRange}`)
      }
      
      // 返回构建的查询字符串
      return query.join(' ')
    },

    // 构建连接详细日志查询
    buildLinkDetailQuery() {
      if (!this.selectedLinkInfo.sourceIp || !this.selectedLinkInfo.targetIp) {
        return ''
      }
      
      let query = []
      
      // 基础查询：指定流和规则
      query.push(`_stream:{stream="SECURIO_FALCO_EVENTS"}`)
      query.push(`rule:"${this.filters.rule}"`)
      
      // 添加来源IP和目标IP过滤
      query.push(`src_ip:"${this.selectedLinkInfo.sourceIp}"`)
      query.push(`agent_hostip:"${this.selectedLinkInfo.targetIp}"`)
      
      // 添加时间范围
      if (this.filters.timeRange === 'precise' && this.filters.startTime && this.filters.endTime) {
        // 精确时间范围
        query.push(`_time:[${this.filters.startTime},${this.filters.endTime}]`)
      } else {
        // 快速时间选择
        query.push(`_time:${this.filters.timeRange}`)
      }
      
      return query.join(' ')
    },

    // 处理桑基图连线点击事件
    handleSankeyLinkClick(linkData) {
      console.log('桑基图连线点击:', linkData)
      
      // 解析连线信息
      this.selectedLinkInfo = {
        sourceIp: linkData.source,
        targetIp: linkData.target,
        connectionCount: linkData.value || 0
      }
      
      // 显示连接详细日志对话框
      this.linkDetailDialogVisible = true
    },

    // 处理日志详情对话框关闭
    handleLinkDetailClose() {
      this.linkDetailDialogVisible = false
      this.selectedLinkInfo = {
        sourceIp: '',
        targetIp: '',
        connectionCount: 0
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  white-space: nowrap;
  color: #606266;
  font-size: 12px;
  font-weight: 500;
}

.time-range-row {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.charts-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-title i {
  margin-right: 8px;
  color: #409EFF;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.source {
  background-color: #ff7875;
}

.legend-color.target {
  background-color: #40a9ff;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-container {
  min-height: 400px;
  position: relative;
}

.time-series-chart {
  width: 100%;
  height: 400px;
}

.traffic-chart {
  width: 100%;
  height: 400px;
}

.no-data-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #909399;
}

.no-data-placeholder.large {
  padding: 80px 20px;
}

.no-data-section {
  margin-top: 20px;
}

.log-section {
  margin-top: 20px;
}

.link-detail-content {
  padding: 20px;
}

.link-detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.ip-text {
  font-weight: 600;
  color: #303133;
  margin: 0 5px;
  padding: 0 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.connection-count {
  margin-left: 15px;
  padding: 2px 8px;
  background: #e6f7ff;
  color: #1890ff;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .chart-legend {
    justify-content: center;
  }
}
</style>
    