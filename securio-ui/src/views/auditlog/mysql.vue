<template>
  <div class="app-container">
    <div class="custom-tabs-container">
      <div class="custom-tabs">
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'overview' }"
          @click="activeTab = 'overview'"
        >
          MySQL 日志概览
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'query' }"
          @click="activeTab = 'query'"
        >
          MySQL 日志探查
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'detail' }"
          @click="activeTab = 'detail'"
        >
          MySQL 实例统计
        </div>
      </div>
    </div>

    <div class="tab-content">
      <mysql-stat-overview v-if="activeTab === 'overview'"></mysql-stat-overview>
      <log-query-component
        v-else-if="activeTab === 'query'"
        title="数据库审计日志"
        :settings="logSettings"
        stream="AUDITLOG_MYSQL_USER"
      />
      <mysql-stat-detail v-else-if="activeTab === 'detail'" />
    </div>
  </div>
</template>

<script>
import MysqlStatOverview from '@/views/auditlog/components/mysql_stat_overview'
import LogQueryComponent from '@/components/log_query_component'
import MysqlStatDetail from '@/views/auditlog/components/mysql_stat_detail'

export default {
  name: 'MysqlAudit',
  components: {
    MysqlStatOverview,
    LogQueryComponent,
    MysqlStatDetail
  },
  data() {
    return {
      loading: false,
      activeTab: 'overview',
      logSettings: {
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          { prop: 'message.user', label: '用户', tag: true },
          { prop: 'message.ip', label: '客户端 IP' },
          { prop: 'agent_hostname', label: '数据库 IP' },
          { prop: 'message.cmd', label: '命令类型' },
          { prop: 'db_name', label: '数据库' },
          { prop: 'table_name', label: '表名' },
          { prop: 'message.rows', label: '影响行数' },
          { prop: 'message.query', label: '执行语句', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  methods: {
  }
}
</script>

<style scoped>
.app-container {
  padding: 0;
}

.custom-tabs-container {
  padding: 10px 0px 0px 10px;
}

.custom-tabs {
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden;
}

.custom-tab-item {
  padding: 8px 10px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
  position: relative;
}

.custom-tab-item.active {
  color: #E6A23C;
  font-weight: 500;
}

.custom-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #E6A23C;
}

.tab-content {
  margin-top: 20px;
}
</style>
