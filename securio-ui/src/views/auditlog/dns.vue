<template>
  <div class="app-container">
    <div class="custom-tabs-container">
      <div class="custom-tabs">
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'overview' }"
          @click="activeTab = 'overview'"
        >
          DNS 日志概览
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'query' }"
          @click="activeTab = 'query'"
        >
          DNS 查询日志
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'error' }"
          @click="activeTab = 'error'"
        >
          DNS 错误日志
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'named' }"
          @click="activeTab = 'named'"
        >
          DNS 命名日志
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'analyzer' }"
          @click="activeTab = 'analyzer'"
        >
          DNS IP分析器
        </div>
      </div>
    </div>

    <div class="tab-content">
      <dns-stat-overview v-if="activeTab === 'overview'"></dns-stat-overview>
      <log-query-component
        v-else-if="activeTab === 'query'"
        :key="activeTab"
        title="DNS 查询日志"
        :settings="logSettings" 
        stream="AUDITLOG_DNS_BIND_QUERY"
      />
      <log-query-component
        v-else-if="activeTab === 'error'"
        :key="activeTab"
        title="DNS 错误日志"
        :settings="errorLogSettings" 
        stream="AUDITLOG_DNS_BIND_ERROR"
      />
      <log-query-component
        v-else-if="activeTab === 'named'"
        :key="activeTab"
        title="DNS 命名日志"
        :settings="namedLogSettings"
        stream="AUDITLOG_DNS_BIND_NAMED"
      />
      <dns-ip-analyzer v-else-if="activeTab === 'analyzer'" />
    </div>
  </div>
</template>

<script>
import DnsStatOverview from '@/views/auditlog/components/dns_stat_overview'
import LogQueryComponent from '@/components/log_query_component'
import DnsIpAnalyzer from '@/views/auditlog/components/dns_ip_analyzer'

export default {
  name: 'DnsAudit',
  components: {
    DnsStatOverview,
    LogQueryComponent,
    DnsIpAnalyzer
  },
  data() {
    return {
      loading: false,
      activeTab: 'overview', 
      logSettings: { 
        columns: [
          { prop: 'event_time', label: '时间', width: '180', sortable: true },
          { prop: 'query_domain', label: '查询域名'},
          { prop: 'query_type', label: '查询类型', tag: true },
          { prop: 'client_ip', label: '客户端 IP' },
          { prop: 'server_ip', label: '服务器 IP' },
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      },
      errorLogSettings: { 
        columns: [
          { prop: 'event_time', label: '时间', width: '180', sortable: true },
          { prop: 'client_ip', label: '客户端 IP', width: '120' },
          { prop: 'client_domain', label: '请求域名', width: '180' },
          { prop: 'query_domain', label: '查询域名', width: '180' },
          { prop: 'message', label: '错误信息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      },
      namedLogSettings: { 
        columns: [
          { prop: 'event_time', label: '时间', width: '180', sortable: true },
          { prop: 'zone_name', label: '区域名称', width: '120'},
          { prop: 'error_code', label: '错误代码', width: '120', tag: true },
          { prop: 'operation', label: '操作', width: '120' },
          { prop: 'primary_ip', label: '主服务器 IP', width: '120' },
          { prop: 'primary_port', label: '端口', width: '120' },
          { prop: 'message', label: '错误信息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  methods: {
  }
}
</script>

<style scoped>
.app-container {
  padding: 0;
}

.custom-tabs-container {
  padding: 10px 0px 0px 10px;
  background-color: #fff; 
  border-bottom: 1px solid #e4e7ed; 
}

.custom-tabs {
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden;
}

.custom-tab-item {
  padding: 8px 10px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
  position: relative;
  border-bottom: 2px solid transparent; 
}

.custom-tab-item.active {
  color: #409EFF; 
  font-weight: 500;
  border-bottom-color: #409EFF; 
}

.tab-content {
  margin-top: 20px; 
  background-color: #fff; 
}
</style>