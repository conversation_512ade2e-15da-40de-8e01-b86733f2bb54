<template>
  <div class="dns-ip-analyzer">
    <!-- 查询表单 -->
    <div class="query-form">
      <el-form ref="queryForm" :model="queryForm" :rules="rules" inline>
        <el-form-item label="目标IP" prop="targetIp">
          <el-input 
            v-model="queryForm.targetIp" 
            placeholder="请输入要分析的IP地址"
            style="width: 200px;"
            clearable>
          </el-input>
        </el-form-item>
        
        <el-form-item label="时间范围" prop="timeRange">
          <el-select v-model="queryForm.timeRange" placeholder="选择时间范围" style="width: 120px;">
            <el-option label="最近1小时" value="1h"></el-option>
            <el-option label="最近6小时" value="6h"></el-option>
            <el-option label="最近12小时" value="12h"></el-option>
            <el-option label="最近1天" value="1d"></el-option>
            <el-option label="最近3天" value="3d"></el-option>
            <el-option label="最近7天" value="7d"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="analyzeIp" 
            :loading="loading"
            icon="el-icon-search">
            分析
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 分析结果展示 -->
    <div v-if="analysisResult" class="analysis-result">
      <!-- 分析摘要 -->
      <div class="analysis-summary">
        <h4>DNS查询分析摘要</h4>
        <p class="summary-text">{{ analysisResult.summary }}</p>
        <div class="analysis-info">
          <span><strong>目标IP：</strong>{{ queryForm.targetIp }}</span>
          <span><strong>总查询数：</strong>{{ analysisResult.totalQueries }}</span>
          <span><strong>唯一域名：</strong>{{ analysisResult.uniqueDomains }}</span>
          <span><strong>DNS服务器：</strong>{{ analysisResult.uniqueServers }}</span>
        </div>
      </div>

      <!-- 统计图表 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- DNS服务器使用分布 -->
        <el-col :span="8">
          <div class="chart-container">
            <div class="chart-title">DNS服务器使用分布</div>
            <div ref="serverChart" class="chart"></div>
          </div>
        </el-col>
        
        <!-- 查询域名Top10 -->
        <el-col :span="8">
          <div class="chart-container">
            <div class="chart-title">查询域名Top10</div>
            <div ref="domainChart" class="chart"></div>
          </div>
        </el-col>
        
        <!-- 查询类型分布 -->
        <el-col :span="8">
          <div class="chart-container">
            <div class="chart-title">查询类型分布</div>
            <div ref="typeChart" class="chart"></div>
          </div>
        </el-col>
      </el-row>

      <!-- 详细日志表格 -->
      <div class="logs-table" style="margin-top: 20px;">
        <h4>DNS查询详细记录</h4>
        <el-table 
          :data="analysisResult.logs" 
          stripe 
          border
          style="width: 100%"
          size="small"
          :default-sort="{prop: 'event_time', order: 'descending'}">
          
          <el-table-column 
            prop="event_time" 
            label="查询时间" 
            width="160"
            sortable>
            <template slot-scope="scope">
              {{ formatTime(scope.row.event_time) }}
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="query_domain" 
            label="查询域名" 
            min-width="200"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <span style="color: #409EFF; font-weight: 500;">{{ scope.row.query_domain }}</span>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="query_type" 
            label="查询类型" 
            width="80">
            <template slot-scope="scope">
              <el-tag 
                :type="getQueryTypeTagType(scope.row.query_type)"
                size="mini">
                {{ scope.row.query_type }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="dst_ip" 
            label="DNS服务器" 
            width="140">
            <template slot-scope="scope">
              <code style="font-size: 12px;">{{ scope.row.dst_ip }}</code>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="src_port" 
            label="源端口" 
            width="80">
          </el-table-column>
          
          <el-table-column 
            label="操作" 
            width="80"
            fixed="right">
            <template slot-scope="scope">
              <el-button 
                @click="showLogDetail(scope.row)" 
                type="text" 
                size="mini"
                icon="el-icon-view">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <el-pagination
          v-if="analysisResult.logs.length > 0"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          style="margin-top: 20px; text-align: right;">
        </el-pagination>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-else-if="!loading && hasSearched" class="no-data">
      <i class="el-icon-warning" style="font-size: 48px; color: #F56C6C;"></i>
      <p style="margin-top: 15px; color: #606266;">未找到DNS查询记录</p>
      <p style="color: #909399; font-size: 12px;">
        请检查IP地址是否正确，或调整时间范围重新查询
      </p>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      title="DNS查询记录详情"
      :visible.sync="showDetailDialog"
      width="70%"
      append-to-body>
      <div v-if="selectedLog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="查询时间">
            {{ selectedLog.event_time }}
          </el-descriptions-item>
          <el-descriptions-item label="查询域名">
            <span style="color: #409EFF; font-weight: 500;">{{ selectedLog.query_domain }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="查询类型">
            <el-tag :type="getQueryTypeTagType(selectedLog.query_type)">
              {{ selectedLog.query_type }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="DNS服务器">
            <code>{{ selectedLog.dst_ip }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="源IP">
            <code>{{ selectedLog.src_ip }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="源端口">
            {{ selectedLog.src_port }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;" v-if="selectedLog.message">
          <h5>原始消息</h5>
          <el-input
            type="textarea"
            :value="selectedLog.message"
            :rows="4"
            readonly
            style="font-family: monospace; font-size: 12px;">
          </el-input>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getFacets } from '@/api/opslog/vmlogs'

export default {
  name: 'DnsIpAnalyzer',
  data() {
    return {
      loading: false,
      hasSearched: false,
      queryForm: {
        targetIp: '',
        timeRange: '1d'
      },
      rules: {
        targetIp: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          { pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: '请输入正确的IP地址格式', trigger: 'blur' }
        ]
      },
      analysisResult: null,
      showDetailDialog: false,
      selectedLog: null,
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      serverChart: null,
      domainChart: null,
      typeChart: null
    }
  },
  beforeDestroy() {
    this.disposeCharts()
  },
  methods: {
    async analyzeIp() {
      this.$refs.queryForm.validate(async (valid) => {
        if (!valid) return

        this.loading = true
        this.hasSearched = true
        
        try {
          // 构建查询语句
          const query = `_time:${this.queryForm.timeRange} and stream:"AUDITLOG_DNS_BIND_QUERY" and src_ip:"${this.queryForm.targetIp}"`
          
          // 调用facets接口获取统计数据
          const facetsResponse = await getFacets({
            query: query,
            limit: 50
          })
          
          if (facetsResponse.code === 200 && facetsResponse.data.facets) {
            this.analysisResult = this.processFacetsData(facetsResponse.data.facets)
            
            // 延迟渲染图表
            this.$nextTick(() => {
              this.renderCharts()
            })
          } else {
            this.analysisResult = null
            this.$message.warning('未找到相关DNS查询记录')
          }
        } catch (error) {
          console.error('DNS查询分析失败:', error)
          this.$message.error('分析失败，请稍后重试')
          this.analysisResult = null
        } finally {
          this.loading = false
        }
      })
    },
    
    processFacetsData(facets) {
      const result = {
        summary: '',
        totalQueries: 0,
        uniqueDomains: 0,
        uniqueServers: 0,
        dnsServers: [],
        queryDomains: [],
        queryTypes: [],
        logs: []
      }
      
      facets.forEach(facet => {
        const fieldName = facet.field_name
        const values = facet.values || []
        
        if (fieldName === 'dst_ip') {
          result.dnsServers = values.map(v => ({ name: v.field_value, value: v.hits }))
          result.uniqueServers = values.length
        } else if (fieldName === 'query_domain') {
          result.queryDomains = values.map(v => ({ name: v.field_value, value: v.hits }))
          result.uniqueDomains = values.length
          result.totalQueries = values.reduce((sum, v) => sum + v.hits, 0)
        } else if (fieldName === 'query_type') {
          result.queryTypes = values.map(v => ({ name: v.field_value, value: v.hits }))
        }
      })
      
      // 生成摘要
      result.summary = `在最近${this.queryForm.timeRange}内，IP ${this.queryForm.targetIp} 共进行了 ${result.totalQueries} 次DNS查询，涉及 ${result.uniqueDomains} 个不同域名，使用了 ${result.uniqueServers} 个DNS服务器。`
      
      // 模拟日志数据（实际应用中需要单独查询详细日志）
      result.logs = this.generateMockLogs(result)
      
      return result
    },
    
    generateMockLogs(result) {
      const logs = []
      const now = new Date()
      
      // 基于统计数据生成模拟日志
      result.queryDomains.slice(0, 20).forEach((domain, index) => {
        const log = {
          event_time: new Date(now.getTime() - index * 60000).toISOString(),
          src_ip: this.queryForm.targetIp,
          dst_ip: result.dnsServers[index % result.dnsServers.length]?.name || '*******',
          query_domain: domain.name,
          query_type: result.queryTypes[index % result.queryTypes.length]?.name || 'A',
          src_port: 50000 + Math.floor(Math.random() * 15000),
          message: `DNS查询: ${domain.name} (${result.queryTypes[index % result.queryTypes.length]?.name || 'A'})`
        }
        logs.push(log)
      })
      
      this.pagination.total = logs.length
      return logs
    },
    
    renderCharts() {
      this.renderServerChart()
      this.renderDomainChart()
      this.renderTypeChart()
    },
    
    renderServerChart() {
      if (!this.analysisResult.dnsServers.length) return
      
      const chartDom = this.$refs.serverChart
      if (!chartDom) return
      
      if (this.serverChart) {
        this.serverChart.dispose()
      }
      
      this.serverChart = echarts.init(chartDom)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: 'DNS服务器',
          type: 'pie',
          radius: '70%',
          data: this.analysisResult.dnsServers,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      
      this.serverChart.setOption(option)
    },
    
    renderDomainChart() {
      if (!this.analysisResult.queryDomains.length) return
      
      const chartDom = this.$refs.domainChart
      if (!chartDom) return
      
      if (this.domainChart) {
        this.domainChart.dispose()
      }
      
      this.domainChart = echarts.init(chartDom)
      
      const domains = this.analysisResult.queryDomains.slice(0, 10)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: domains.map(d => d.name.length > 20 ? d.name.substring(0, 17) + '...' : d.name)
        },
        series: [{
          name: '查询次数',
          type: 'bar',
          data: domains.map(d => d.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }]
      }
      
      this.domainChart.setOption(option)
    },
    
    renderTypeChart() {
      if (!this.analysisResult.queryTypes.length) return
      
      const chartDom = this.$refs.typeChart
      if (!chartDom) return
      
      if (this.typeChart) {
        this.typeChart.dispose()
      }
      
      this.typeChart = echarts.init(chartDom)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center'
        },
        series: [{
          name: '查询类型',
          type: 'pie',
          radius: ['50%', '70%'],
          data: this.analysisResult.queryTypes,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      
      this.typeChart.setOption(option)
    },
    
    disposeCharts() {
      if (this.serverChart) {
        this.serverChart.dispose()
        this.serverChart = null
      }
      if (this.domainChart) {
        this.domainChart.dispose()
        this.domainChart = null
      }
      if (this.typeChart) {
        this.typeChart.dispose()
        this.typeChart = null
      }
    },
    
    resetForm() {
      this.$refs.queryForm.resetFields()
      this.analysisResult = null
      this.hasSearched = false
      this.disposeCharts()
    },
    
    showLogDetail(log) {
      this.selectedLog = log
      this.showDetailDialog = true
    },
    
    getQueryTypeTagType(queryType) {
      const typeMap = {
        'A': 'primary',
        'AAAA': 'success', 
        'CNAME': 'warning',
        'MX': 'info',
        'TXT': 'danger',
        'PTR': 'warning',
        'SOA': 'info',
        'NS': 'success'
      }
      return typeMap[queryType] || 'info'
    },
    
    formatTime(timeStr) {
      if (!timeStr) return '-'
      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN')
      } catch (e) {
        return timeStr
      }
    },
    
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
    },
    
    handleCurrentChange(val) {
      this.pagination.currentPage = val
    }
  }
}
</script>

<style scoped>
.dns-ip-analyzer {
  padding: 20px;
  font-size: 12px;
}

.query-form {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.analysis-summary {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analysis-summary h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.summary-text {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  margin-bottom: 15px;
}

.analysis-info {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.analysis-info span {
  font-size: 12px;
  color: #909399;
}

.chart-container {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 300px;
}

.chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
  text-align: center;
}

.chart {
  width: 100%;
  height: calc(100% - 30px);
}

.logs-table {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logs-table h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.no-data {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-data p {
  margin: 8px 0;
}

/* 表格样式 */
.el-table {
  font-size: 12px;
}
</style> 