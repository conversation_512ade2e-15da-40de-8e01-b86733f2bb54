<template>
  <div class="audit-overview behavior-stat-overview" v-loading="loading" element-loading-text="加载中...">
    <!-- 全局控制面板 -->
    <div class="global-controls-panel">
      <div class="global-title">时间范围控制</div>
      <div class="global-controls">
        <el-select v-model="globalTimeRange" placeholder="选择时间范围" size="small" @change="handleGlobalTimeRangeChange">
            <el-option label="最近 5 分钟" value="5m"></el-option>
            <el-option label="最近 15 分钟" value="15m"></el-option>
            <el-option label="最近 30 分钟" value="30m"></el-option>
            <el-option label="最近 1 小时" value="1h"></el-option>
          <el-option label="最近 6 小时" value="6h"></el-option>
          <el-option label="最近 24 小时" value="1d"></el-option>
          <el-option label="最近 3 天" value="3d"></el-option>
          <el-option label="最近 7 天" value="7d"></el-option>
          <el-option label="最近 14 天" value="14d"></el-option>
        </el-select>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text=""
          @change="handleAutoRefreshChange">
        </el-switch>
        <el-button type="primary" icon="el-icon-refresh" size="small" @click="refreshAllData">刷新数据</el-button>
      </div>
    </div>

    <!-- 统计数据卡片 -->
    <h3 class="section-title">数据总览</h3>
    <div class="stat-cards-container">
      <div class="stat-card" v-for="(item, index) in statItems" :key="index">
        <div class="stat-circle" :style="{ backgroundColor: item.bgColor }"></div>
        <div class="stat-content">
          <div class="stat-number" :style="{ color: item.color }">{{ item.value }}</div>
          <div class="stat-title">{{ item.title }}</div>
        </div>
      </div>
    </div>

    <!-- 图表分析 -->
    <h3 class="section-title">行为操作统计</h3>
    <div class="charts-container">
      <div class="chart-item">
        <div ref="actionChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div ref="appChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <!-- 应用分组和用户组统计 -->
    <h3 class="section-title">分组统计</h3>
    <div class="charts-container">
      <div class="chart-item">
        <div ref="appGroupChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div ref="userGroupChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <!-- 流量统计 -->
    <h3 class="section-title">流量统计</h3>
    <div class="charts-container">
      <div class="chart-item">
        <div ref="uploadTrafficChart" style="width: 100%; height: 350px;"></div>
      </div>
      <div class="chart-item">
        <div ref="downloadTrafficChart" style="width: 100%; height: 350px;"></div>
      </div>
    </div>

    <!-- 流量排名表格 -->
    <div class="stats-row">
      <div class="stats-card">
        <h3 class="stats-title">上行流量排名TOP10</h3>
        <el-table :data="uploadTrafficRanking.slice(0, 10)" size="small" border stripe v-loading="trafficLoading">
          <el-table-column prop="user_name" label="用户IP" width="150"></el-table-column>
          <el-table-column prop="sum_up" label="上行流量">
            <template slot-scope="scope">
              <span class="traffic-number">{{ formatBytes(scope.row.sum_up) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="stats-card">
        <h3 class="stats-title">下行流量排名TOP10</h3>
        <el-table :data="downloadTrafficRanking.slice(0, 10)" size="small" border stripe v-loading="trafficLoading">
          <el-table-column prop="user_name" label="用户IP" width="150"></el-table-column>
          <el-table-column prop="sum_down" label="下行流量">
            <template slot-scope="scope">
              <span class="traffic-number">{{ formatBytes(scope.row.sum_down) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 热门统计表格 -->
    <h3 class="section-title">热门统计</h3>
    <div class="stats-row">
      <div class="stats-card">
        <h3 class="stats-title">热门操作类型</h3>
        <el-table :data="topActions.slice(0, 10)" size="small" border stripe>
          <el-table-column prop="fieldValue" label="操作类型" width="180"></el-table-column>
          <el-table-column prop="hits" label="操作次数">
            <template slot-scope="scope">
              <span class="count-number">{{ formatNumber(scope.row.hits) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="stats-card">
        <h3 class="stats-title">热门应用</h3>
        <el-table :data="topApps.slice(0, 10)" size="small" border stripe>
          <el-table-column prop="fieldValue" label="应用名称" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="hits" label="使用次数">
            <template slot-scope="scope">
              <span class="count-number">{{ formatNumber(scope.row.hits) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="stats-card">
        <h3 class="stats-title">热门应用分组</h3>
        <el-table :data="topAppGroups.slice(0, 10)" size="small" border stripe>
          <el-table-column prop="fieldValue" label="应用分组" width="180"></el-table-column>
          <el-table-column prop="hits" label="使用次数">
            <template slot-scope="scope">
              <span class="count-number">{{ formatNumber(scope.row.hits) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 用户组统计 -->
    <h3 class="section-title">用户组统计</h3>
    <div class="stats-row">
      <div class="stats-card full-width">
        <h3 class="stats-title">热门用户组</h3>
        <el-table :data="topUserGroups.slice(0, 15)" size="small" border stripe>
          <el-table-column prop="fieldValue" label="用户组" width="200"></el-table-column>
          <el-table-column prop="hits" label="活动次数">
            <template slot-scope="scope">
              <span class="count-number">{{ formatNumber(scope.row.hits) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="percentage" label="占比" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.percentage }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getFacets, getUploadTrafficRanking, getDownloadTrafficRanking } from '@/api/opslog/vmlogs'

export default {
  name: 'BehaviorStatOverview',
  data() {
    return {
      loading: false,
      statItems: [
        {
          title: '总活动记录',
          value: '0',
          color: '#1890ff',
          bgColor: 'rgba(24, 144, 255, 0.15)'
        },
        {
          title: '活跃用户组',
          value: '0',
          color: '#52c41a',
          bgColor: 'rgba(82, 196, 26, 0.15)'
        },
        {
          title: '使用应用数',
          value: '0',
          color: '#f5222d',
          bgColor: 'rgba(245, 34, 45, 0.15)'
        },
        {
          title: '操作类型数',
          value: '0',
          color: '#fa8c16',
          bgColor: 'rgba(250, 140, 22, 0.15)'
        }
      ],
      actionChart: null,
      appChart: null,
      appGroupChart: null,
      userGroupChart: null,
      fieldStats: {}, // 字段统计信息
      topActions: [], // 热门操作
      topApps: [], // 热门应用
      topAppGroups: [], // 热门应用分组
      topUserGroups: [], // 热门用户组
      globalTimeRange: '1d', // 全局时间范围：默认最近 24 小时
      autoRefresh: false, // 自动刷新开关
      refreshTimer: null, // 自动刷新定时器
      uploadTrafficChart: null,
      downloadTrafficChart: null,
      uploadTrafficRanking: [],
      downloadTrafficRanking: [],
      trafficLoading: false
    }
  },
  mounted() {
    // 初始化图表
    this.initCharts()

    // 获取数据
    this.fetchData()

    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    if (this.actionChart) {
      this.actionChart.dispose()
    }
    if (this.appChart) {
      this.appChart.dispose()
    }
    if (this.appGroupChart) {
      this.appGroupChart.dispose()
    }
    if (this.userGroupChart) {
      this.userGroupChart.dispose()
    }
    if (this.uploadTrafficChart) {
      this.uploadTrafficChart.dispose()
    }
    if (this.downloadTrafficChart) {
      this.downloadTrafficChart.dispose()
    }

    // 清除自动刷新定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  },
  methods: {
    fetchData() {
      this.loading = true

      // 获取字段统计信息
      this.fetchFieldStats()

      // 获取流量统计数据
      this.fetchTrafficData()
    },

    // 获取字段统计信息
    fetchFieldStats() {
      // 构建查询参数
      const query = `_time:${this.globalTimeRange} and stream:"AUDITLOG_H3C_BEHAVIOR"`
      const fields = [
        'action_name',
        'app_name',
        'app_group',
        'user_group'
      ]

      // 调用后端 facets API 获取字段统计信息
      const requestData = {
        query: query,
        timeRange: null, // 时间范围已经包含在query中
        fields: fields,
        limit: 50,
        instance: null
      }

      getFacets(requestData)
        .then(response => {
          if (response.code === 200) {
            try {
              // 直接使用返回的数据对象
              const data = response.data
              console.log('Received behavior field stats data:', data) // 输出数据以便调试
              this.fieldStats = data

              // 计算总记录数
              let totalHits = 0
              if (data.facets && data.facets.length > 0) {
                // 使用第一个字段的总数作为参考
                const firstField = data.facets[0]
                if (firstField && firstField.values) {
                  totalHits = firstField.values.reduce((sum, item) => sum + item.hits, 0)
                }
              }
              this.statItems[0].value = this.formatNumber(totalHits)

              // 提取各字段统计数据
              if (data.facets) {
                data.facets.forEach(facet => {
                  switch (facet.fieldName) {
                    case 'action_name':
                      this.topActions = facet.values || []
                      this.statItems[3].value = this.formatNumber(this.topActions.length)
                      break
                    case 'app_name':
                      this.topApps = facet.values || []
                      this.statItems[2].value = this.formatNumber(this.topApps.length)
                      break
                    case 'app_group':
                      this.topAppGroups = facet.values || []
                      break
                    case 'user_group':
                      this.topUserGroups = facet.values || []
                      this.statItems[1].value = this.formatNumber(this.topUserGroups.length)
                      // 计算百分比
                      const totalUserGroupHits = this.topUserGroups.reduce((sum, item) => sum + item.hits, 0)
                      this.topUserGroups.forEach(item => {
                        item.percentage = totalUserGroupHits > 0 ? ((item.hits / totalUserGroupHits) * 100).toFixed(1) : '0.0'
                      })
                      break
                  }
                })
              }

              // 更新图表数据
              this.updateCharts()
            } catch (e) {
              console.error('解析行为字段统计数据失败:', e)
            }
          } else {
            this.$message.error('获取行为统计数据失败: ' + response.msg)
          }
        })
        .catch(error => {
          console.error('获取行为字段统计数据失败:', error)
          this.$message.error('获取行为统计数据失败: ' + (error.message || error))
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 获取流量统计数据
    fetchTrafficData() {
      this.trafficLoading = true

      // 获取上行流量排名
      getUploadTrafficRanking(this.globalTimeRange, 10)
        .then(response => {
          if (response.code === 200) {
            try {
              // 解析返回的数据
              const data = response.data
              if (typeof data === 'string') {
                // 如果返回的是字符串，按行分割并解析每行的JSON
                const lines = data.trim().split('\n')
                this.uploadTrafficRanking = lines.map(line => {
                  try {
                    return JSON.parse(line)
                  } catch (e) {
                    console.error('解析上行流量数据失败:', e, line)
                    return null
                  }
                }).filter(item => item !== null)
              } else {
                this.uploadTrafficRanking = data || []
              }
              console.log('上行流量排名数据:', this.uploadTrafficRanking)
            } catch (e) {
              console.error('处理上行流量数据失败:', e)
              this.uploadTrafficRanking = []
            }
          } else {
            console.error('获取上行流量排名失败:', response.msg)
            this.uploadTrafficRanking = []
          }
        })
        .catch(error => {
          console.error('获取上行流量排名失败:', error)
          this.uploadTrafficRanking = []
        })

      // 获取下行流量排名
      getDownloadTrafficRanking(this.globalTimeRange, 10)
        .then(response => {
          if (response.code === 200) {
            try {
              // 解析返回的数据
              const data = response.data
              if (typeof data === 'string') {
                // 如果返回的是字符串，按行分割并解析每行的JSON
                const lines = data.trim().split('\n')
                this.downloadTrafficRanking = lines.map(line => {
                  try {
                    return JSON.parse(line)
                  } catch (e) {
                    console.error('解析下行流量数据失败:', e, line)
                    return null
                  }
                }).filter(item => item !== null)
              } else {
                this.downloadTrafficRanking = data || []
              }
              console.log('下行流量排名数据:', this.downloadTrafficRanking)
            } catch (e) {
              console.error('处理下行流量数据失败:', e)
              this.downloadTrafficRanking = []
            }
          } else {
            console.error('获取下行流量排名失败:', response.msg)
            this.downloadTrafficRanking = []
          }
        })
        .catch(error => {
          console.error('获取下行流量排名失败:', error)
          this.downloadTrafficRanking = []
        })
        .finally(() => {
          this.trafficLoading = false
          // 更新流量图表
          this.updateTrafficCharts()
        })
    },

    formatNumber(num) {
      if (!num) return '0'
      return num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    },

    initCharts() {
      // 初始化操作类型图表
      this.actionChart = echarts.init(this.$refs.actionChart)

      // 初始化应用图表
      this.appChart = echarts.init(this.$refs.appChart)

      // 初始化应用分组图表
      this.appGroupChart = echarts.init(this.$refs.appGroupChart)

      // 初始化用户组图表
      this.userGroupChart = echarts.init(this.$refs.userGroupChart)

      // 初始化上行流量图表
      this.uploadTrafficChart = echarts.init(this.$refs.uploadTrafficChart)

      // 初始化下行流量图表
      this.downloadTrafficChart = echarts.init(this.$refs.downloadTrafficChart)

      // 设置初始数据
      this.updateCharts()
    },

    updateCharts() {
      // 更新操作类型图表 - 饼图
      this.updateActionChart()

      // 更新应用图表 - 柱状图
      this.updateAppChart()

      // 更新应用分组图表 - 环形图
      this.updateAppGroupChart()

      // 更新用户组图表 - 柱状图
      this.updateUserGroupChart()

      // 更新流量图表
      this.updateTrafficCharts()
    },

    updateActionChart() {
      if (!this.actionChart) return

      const actionData = this.topActions.slice(0, 10).map(item => ({
        value: item.hits,
        name: item.fieldValue
      }))

      const option = {
        title: {
          text: '操作类型分布',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: actionData.map(item => item.name),
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '操作类型',
            type: 'pie',
            radius: '70%',
            center: ['60%', '50%'],
            data: actionData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{b}: {d}%',
              fontSize: 12
            }
          }
        ]
      }

      this.actionChart.setOption(option)
    },

    updateAppChart() {
      if (!this.appChart) return

      const appData = this.topApps.slice(0, 10)
      const appNames = appData.map(item => item.fieldValue)
      const appValues = appData.map(item => item.hits)

      const option = {
        title: {
          text: '热门应用TOP10',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>${data.seriesName}: ${data.value.toLocaleString()}`;
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: appNames,
          axisLabel: {
            interval: 0,
            rotate: 45,
            fontSize: 11,
            margin: 15
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              return value >= 1000 ? (value / 1000).toFixed(1) + 'k' : value;
            }
          }
        },
        series: [
          {
            name: '使用次数',
            type: 'bar',
            data: appValues,
            barWidth: '60%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#67C23A' },
                { offset: 0.5, color: '#85ce61' },
                { offset: 1, color: '#67C23A' }
              ])
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return params.value > 1000 ? (params.value / 1000).toFixed(1) + 'k' : params.value;
              },
              fontSize: 10,
              color: '#666'
            }
          }
        ]
      }

      this.appChart.setOption(option)
    },

    updateAppGroupChart() {
      if (!this.appGroupChart) return

      const appGroupData = this.topAppGroups.slice(0, 8).map(item => ({
        value: item.hits,
        name: item.fieldValue
      }))

      const option = {
        title: {
          text: '应用分组分布',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: appGroupData.map(item => item.name),
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '应用分组',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            data: appGroupData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{b}: {d}%',
              fontSize: 12
            }
          }
        ]
      }

      this.appGroupChart.setOption(option)
    },

    updateUserGroupChart() {
      if (!this.userGroupChart) return

      const userGroupData = this.topUserGroups.slice(0, 10)
      const groupNames = userGroupData.map(item => item.fieldValue)
      const groupValues = userGroupData.map(item => item.hits)

      const option = {
        title: {
          text: '用户组活动统计TOP10',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>${data.seriesName}: ${data.value.toLocaleString()}`;
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: groupNames,
          axisLabel: {
            interval: 0,
            rotate: 45,
            fontSize: 11,
            margin: 15
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              return value >= 1000 ? (value / 1000).toFixed(1) + 'k' : value;
            }
          }
        },
        series: [
          {
            name: '活动次数',
            type: 'bar',
            data: groupValues,
            barWidth: '60%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#409EFF' },
                { offset: 0.5, color: '#66b1ff' },
                { offset: 1, color: '#409EFF' }
              ])
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return params.value > 1000 ? (params.value / 1000).toFixed(1) + 'k' : params.value;
              },
              fontSize: 10,
              color: '#666'
            }
          }
        ]
      }

      this.userGroupChart.setOption(option)
    },

    updateTrafficCharts() {
      // 更新上行流量图表
      this.updateUploadTrafficChart()

      // 更新下行流量图表
      this.updateDownloadTrafficChart()
    },

    updateUploadTrafficChart() {
      if (!this.uploadTrafficChart) return

      const uploadData = this.uploadTrafficRanking.slice(0, 10)
      const uploadNames = uploadData.map(item => item.user_name)
      const uploadValues = uploadData.map(item => parseInt(item.sum_up))

      const formatBytes = this.formatBytes // 保存方法引用

      const option = {
        title: {
          text: '上行流量TOP10',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>${data.seriesName}: ${formatBytes(data.value)}`;
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: uploadNames,
          axisLabel: {
            interval: 0,
            rotate: 45,
            fontSize: 11,
            margin: 15
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              return formatBytes(value);
            }
          }
        },
        series: [
          {
            name: '上行流量',
            type: 'bar',
            data: uploadValues,
            barWidth: '60%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#E6A23C' },
                { offset: 0.5, color: '#F0C78A' },
                { offset: 1, color: '#E6A23C' }
              ])
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return formatBytes(params.value);
              },
              fontSize: 10,
              color: '#666'
            }
          }
        ]
      }

      this.uploadTrafficChart.setOption(option)
    },

    updateDownloadTrafficChart() {
      if (!this.downloadTrafficChart) return

      const downloadData = this.downloadTrafficRanking.slice(0, 10)
      const downloadNames = downloadData.map(item => item.user_name)
      const downloadValues = downloadData.map(item => parseInt(item.sum_down))

      const formatBytes = this.formatBytes // 保存方法引用

      const option = {
        title: {
          text: '下行流量TOP10',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>${data.seriesName}: ${formatBytes(data.value)}`;
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: downloadNames,
          axisLabel: {
            interval: 0,
            rotate: 45,
            fontSize: 11,
            margin: 15
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              return formatBytes(value);
            }
          }
        },
        series: [
          {
            name: '下行流量',
            type: 'bar',
            data: downloadValues,
            barWidth: '60%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#F56C6C' },
                { offset: 0.5, color: '#F89898' },
                { offset: 1, color: '#F56C6C' }
              ])
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return formatBytes(params.value);
              },
              fontSize: 10,
              color: '#666'
            }
          }
        ]
      }

      this.downloadTrafficChart.setOption(option)
    },

    resizeCharts() {
      if (this.actionChart) {
        this.actionChart.resize()
      }
      if (this.appChart) {
        this.appChart.resize()
      }
      if (this.appGroupChart) {
        this.appGroupChart.resize()
      }
      if (this.userGroupChart) {
        this.userGroupChart.resize()
      }
      if (this.uploadTrafficChart) {
        this.uploadTrafficChart.resize()
      }
      if (this.downloadTrafficChart) {
        this.downloadTrafficChart.resize()
      }
    },

    // 处理全局时间范围变化
    handleGlobalTimeRangeChange() {
      // 刷新所有数据
      this.refreshAllData()
    },

    // 处理自动刷新开关变化
    handleAutoRefreshChange() {
      if (this.autoRefresh) {
        // 开启自动刷新，每 60 秒刷新一次
        this.refreshTimer = setInterval(() => {
          this.refreshAllData()
        }, 60000) // 60秒 = 60000毫秒

        this.$message.success('自动刷新已开启，每 60 秒刷新一次')
      } else {
        // 关闭自动刷新
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }

        this.$message.info('自动刷新已关闭')
      }
    },

    // 刷新所有数据
    refreshAllData() {
      // 刷新基本统计数据
      this.fetchData()
    },

    formatBytes(bytes) {
      if (bytes === null || bytes === undefined || isNaN(bytes)) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let i = 0
      let num = bytes
      while (num >= 1024 && i < units.length - 1) {
        num /= 1024
        i++
      }
      return `${num.toFixed(2)} ${units[i]}`
    }
  }
}
</script>

<style scoped>
@import '../../../assets/styles/audit-overview.css';

/* 行为统计特有样式 */
.count-number {
  color: #67C23A;
  font-weight: bold;
}

.chart-item {
  flex: 1;
  min-width: 45%;
  background-color: #fff;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.charts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

/* 数据总览卡片样式优化 */
.stat-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 24px 20px 24px 24px;
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 240px;
  position: relative;
  overflow: hidden;
  min-height: 100px;
}

.stat-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  position: absolute;
  left: -50px;
  top: -25px;
  opacity: 0.2;
  z-index: 1;
}

.stat-content {
  width: 100%;
  padding-left: 25px;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-number {
  font-size: 36px;
  font-weight: 500;
  line-height: 1.1;
  margin-bottom: 4px;
  font-family: "Arial", sans-serif;
  letter-spacing: 0.5px;
}

.stat-title {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: normal;
}

/* 调整section标题样式 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 30px 0 20px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: #67C23A;
  border-radius: 2px;
}

/* 全宽表格样式 */
.full-width {
  width: 100%;
}

.traffic-number {
  color: #67C23A;
  font-weight: bold;
}
</style> 