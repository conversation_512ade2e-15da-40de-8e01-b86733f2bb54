<template>
  <div class="mysql-stat-detail" v-loading="loading" element-loading-text="加载中...">
    <div class="detail-container">
      <!-- 查询控制面板 -->
      <div class="detail-controls-panel">
        <el-form :inline="true" size="small" @submit.native.prevent>
          <el-form-item label="数据库 IP">
            <el-select v-model="form.agent_hostname" placeholder="请选择IP"  style="width: 180px" @change="handleIpChange">
              <el-option
                v-for="ip in Object.keys(databaseConnections)"
                :key="ip"
                :label="ip"
                :value="ip">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="端口号">
            <el-select v-model="form.port" placeholder="请选择端口"  style="width: 120px">
              <el-option
                v-for="port in portOptions"
                :key="port"
                :label="port"
                :value="port">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-select v-model="timeRange" placeholder="选择时间范围" size="small" @change="handleTimeRangeChange">
              <el-option label="最近 24 小时" value="1d"></el-option>
              <el-option label="最近 3 天" value="3d"></el-option>
              <el-option label="最近 7 天" value="7d"></el-option>
              <el-option label="最近 14 天" value="14d"></el-option>
              <el-option label="最近 30 天" value="30d"></el-option>
              <el-option label="自定义" value="custom"></el-option>
            </el-select>
          </el-form-item>

          <!-- 自定义时间选择器，只在选择"自定义"时显示 -->
          <el-form-item v-if="timeRange === 'custom'" label="自定义时间">
            <el-date-picker
              v-model="customTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 380px;"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleCustomTimeChange">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchAllStats">查询</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 实例信息展示 -->
      <div v-if="instanceInfo.show" class="instance-info">
        <h3 class="instance-title">MySQL 实例统计 - {{ instanceInfo.ip }}:{{ instanceInfo.port }}</h3>
      </div>

      <!-- 统计数据卡片 -->
      <div v-if="statItems.length > 0" class="stat-cards-container">
        <div class="stat-card" v-for="(item, index) in statItems" :key="index">
          <div class="stat-circle" :style="{ backgroundColor: item.bgColor }"></div>
          <div class="stat-content">
            <div class="stat-number" :style="{ color: item.color }">{{ item.value }}</div>
            <div class="stat-title">{{ item.title }}</div>
          </div>
        </div>
      </div>

      <!-- 数据流向统计（桑基图） -->
      <h3 v-if="statItems.length > 0" class="section-title">数据流向统计</h3>
      <div v-if="statItems.length > 0" class="sankey-controls">
        <el-form :inline="true" size="small">
          <el-form-item label="客户端数量">
            <el-input-number v-model="sankeyForm.clientLimit" :min="1" :max="20" size="small" @change="fetchSankeyData"></el-input-number>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-refresh" @click="fetchSankeyData">刷新</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="success" icon="el-icon-search" @click="testSankeyChart">测试状态</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="statItems.length > 0" class="stats-card">
        <div ref="sankeyChart" class="sankey-chart" v-loading="sankeyLoading"></div>
      </div>

      <!-- 访问时间序列统计 -->
      <h3 v-if="statItems.length > 0" class="section-title">访问时间序列统计</h3>
      <div v-if="statItems.length > 0" class="time-series-controls">
        <el-form :inline="true" size="small">
          <el-form-item label="统计维度">
            <el-select v-model="timeSeriesForm.dimension" placeholder="选择统计维度" @change="fetchTimeSeriesData">
              <el-option label="按命令类型" value="cmd"></el-option>
              <el-option label="按用户" value="user"></el-option>
              <el-option label="按客户端" value="client"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-refresh" @click="fetchTimeSeriesData">刷新</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="statItems.length > 0" class="stats-card">
        <div ref="timeSeriesChart" class="time-series-chart" v-loading="timeSeriesLoading"></div>
      </div>

      <!-- 图表分析 -->
      <div v-if="statItems.length > 0" class="charts-container">
        <div class="chart-item">
          <div ref="userActionChart" style="width: 100%; height: 350px;"></div>
        </div>
        <div class="chart-item">
          <div ref="tableChart" style="width: 100%; height: 350px;"></div>
        </div>
      </div>

      <!-- 热门统计 -->
      <div v-if="statItems.length > 0" class="stats-row">
        <div class="stats-card">
          <h3 class="stats-title">热门用户</h3>
          <el-table :data="topUsers.slice(0, 10)" size="small" border stripe>
            <el-table-column prop="fieldValue" label="用户名" width="180"></el-table-column>
            <el-table-column prop="hits" label="操作次数"></el-table-column>
          </el-table>
        </div>
        <div class="stats-card">
          <h3 class="stats-title">热门客户端</h3>
          <el-table :data="topHosts.slice(0, 10)" size="small" border stripe>
            <el-table-column prop="fieldValue" label="客户端 IP" width="180"></el-table-column>
            <el-table-column prop="hits" label="连接次数"></el-table-column>
          </el-table>
        </div>
        <div class="stats-card">
          <h3 class="stats-title">热门数据库</h3>
          <el-table :data="topDatabases.slice(0, 10)" size="small" border stripe>
            <el-table-column prop="fieldValue" label="数据库名" width="180"></el-table-column>
            <el-table-column prop="hits" label="访问次数"></el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 登录失败统计 -->
      <div v-if="statItems.length > 0" class="stats-row">
        <div class="stats-card">
          <h3 class="stats-title">认证失败用户统计</h3>
          <el-table :data="failedLoginUserStats" size="small" border stripe v-loading="failedLoginLoading">
            <el-table-column prop="fieldValue" label="用户名" width="180"></el-table-column>
            <el-table-column prop="count" label="失败次数">
              <template slot-scope="scope">
                <span class="failed-count">{{ scope.row.count }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="stats-card">
          <h3 class="stats-title">认证失败客户端统计</h3>
          <el-table :data="failedLoginHostStats" size="small" border stripe v-loading="failedLoginLoading">
            <el-table-column prop="fieldValue" label="客户端 IP" width="180"></el-table-column>
            <el-table-column prop="count" label="失败次数">
              <template slot-scope="scope">
                <span class="failed-count">{{ scope.row.count }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div v-if="errorMsg" class="error-msg">{{ errorMsg }}</div>
    </div>
  </div>
</template>

<script>
// 导入 ECharts 主模块
import * as echarts from 'echarts/core'
// 导入桑基图图表
import { SankeyChart } from 'echarts/charts'
// 导入提示框、标题、图例等组件
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
// 引入 Canvas 渲染器
import { CanvasRenderer } from 'echarts/renderers'

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  SankeyChart,
  CanvasRenderer
])
import { getFacets, countUniqueValues } from '@/api/opslog/vmlogs'
import { getMysqlFailedLoginStats, getMysqlTimeSeriesStats, getMysqlDataFlowStats } from '@/api/opslog/mysql'

export default {
  name: 'MysqlStatDetail',
  data() {
    return {
      loading: false,
      form: {
        agent_hostname: '***********',
        port: '3306'
      },
      databaseConnections: {
        '***********': ['3306', '3310', '3313', '3312'],
        '***********': ['3307', '3313', '3309', '3310'],
        '***********': ['3308', '3309', '3311', '3306'],
        '***********': ['3311', '3312', '3308', '3307'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306'],
        '*************': ['3306']
      },
      portOptions: ['3306', '3310', '3313', '3312'],
      timeRange: '7d',
      customTimeRange: [], // 自定义时间范围
      instanceInfo: {
        show: false,
        ip: '',
        port: ''
      },
      statItems: [
        {
          title: '登录操作',
          value: '0',
          color: '#52c41a',
          bgColor: 'rgba(82, 196, 26, 0.15)'
        },
        {
          title: '数据操作',
          value: '0',
          color: '#f5222d',
          bgColor: 'rgba(245, 34, 45, 0.15)'
        }
      ],
      topUsers: [],
      topCommands: [],
      topHosts: [],
      topDatabases: [],
      topTables: [],
      failedLoginUserStats: [],
      failedLoginHostStats: [],
      failedLoginLoading: false,
      userActionChart: null,
      tableChart: null,
      sankeyChart: null,
      sankeyLoading: false,
      sankeyForm: {
        clientLimit: 10
      },
      timeSeriesChart: null,
      timeSeriesLoading: false,
      timeSeriesForm: {
        dimension: 'cmd'
      },
      errorMsg: ''
    }
  },
  beforeDestroy() {
    if (this.userActionChart) {
      this.userActionChart.dispose()
    }
    if (this.tableChart) {
      this.tableChart.dispose()
    }
    if (this.sankeyChart) {
      this.sankeyChart.dispose()
    }
    if (this.timeSeriesChart) {
      this.timeSeriesChart.dispose()
    }
  },
  computed: {
    // 获取当前选中IP的端口列表
    currentPorts() {
      return this.databaseConnections[this.form.agent_hostname] || [];
    }
  },
  methods: {
    // 处理时间范围变化
    handleTimeRangeChange(value) {
      if (value === 'custom') {
        // 如果选择了自定义，仅显示时间选择器，不触发查询
        return;
      }

      // 清除自定义时间范围
      this.customTimeRange = [];
    },

    // 处理自定义时间范围变化
    handleCustomTimeChange(value) {
      if (value && value.length === 2) {
        this.customTimeRange = value;
        console.log('自定义时间范围已设置:', value);
      }
    },

    // 获取当前时间配置（用于API调用）
    getCurrentTimeConfig() {
      if (this.timeRange === 'custom' && this.customTimeRange && this.customTimeRange.length === 2) {
        return {
          type: 'custom',
          startTime: this.customTimeRange[0],
          endTime: this.customTimeRange[1]
        };
      } else {
        return {
          type: 'quick',
          timeRange: this.timeRange
        };
      }
    },

    // 当IP改变时更新端口选项
    handleIpChange(ip) {
      this.portOptions = this.databaseConnections[ip] || [];
      // 如果当前端口不在新端口中，则重置端口
      if (this.portOptions.length > 0 && !this.portOptions.includes(this.form.port)) {
        this.form.port = this.portOptions[0];
      } else if (this.portOptions.length === 0) {
        this.form.port = '';
      }
    },
    async fetchAllStats() {
      this.errorMsg = ''
      if (!this.form.agent_hostname || !this.form.port) {
        this.errorMsg = '请填写数据库 IP 和端口号'
        return
      }

      this.loading = true
      this.instanceInfo.show = true
      this.instanceInfo.ip = this.form.agent_hostname
      this.instanceInfo.port = this.form.port

      try {
        // 获取字段统计信息
        await this.fetchFieldStats()
        
        // 获取登录失败统计信息
        await this.fetchFailedLoginStats()
        
        // 获取桑基图数据
        await this.fetchSankeyData()
        
        // 获取时间序列数据
        await this.fetchTimeSeriesData()
        
        // 更新图表
        this.$nextTick(() => {
          this.updateCharts()
        })
      } catch (e) {
        console.error('获取统计信息失败:', e)
        this.errorMsg = '获取统计信息失败: ' + (e.message || e)
      } finally {
        this.loading = false
      }
    },

    // 获取字段统计信息
    async fetchFieldStats() {
      const query = `stream:"AUDITLOG_MYSQL_USER" and agent_hostname:"${this.form.agent_hostname}" and fctags.port:"${this.form.port}"`
      const fields = [
        'message.user',
        'message.cmd',
        'message.host',
        'db_name',
        'table_name'
      ]

      try {
        const timeConfig = this.getCurrentTimeConfig();
        let requestData;

        if (timeConfig.type === 'custom') {
          // 使用自定义时间范围
          requestData = {
            query: query,
            startTime: timeConfig.startTime,
            endTime: timeConfig.endTime,
            fields: fields,
            limit: 100
          };
        } else {
          // 使用快速时间选择
          requestData = {
            query: query,
            timeRange: timeConfig.timeRange,
            fields: fields,
            limit: 100
          };
        }

        const response = await getFacets(requestData);
        if (response.code === 200) {
          const data = response.data
          console.log('Received field stats data:', data)

          // 提取热门用户
          const userStats = data.facets.find(item => item.fieldName === 'message.user')
          this.topUsers = userStats && userStats.values ? userStats.values : []

          // 提取热门命令
          const cmdStats = data.facets.find(item => item.fieldName === 'message.cmd')
          this.topCommands = cmdStats && cmdStats.values ? cmdStats.values : []

          // 计算登录操作数
          if (this.topCommands.length > 0) {
            const loginCommands = this.topCommands.filter(item =>
              item.fieldValue === 'Connect' || item.fieldValue === 'Login')
            const loginHits = loginCommands.reduce((sum, item) => sum + item.hits, 0)
            this.statItems[0].value = this.formatNumber(loginHits)

            // 计算数据操作数
            const dataCommands = this.topCommands.filter(item =>
              ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'truncate', 'delete'].includes(item.fieldValue))
            const dataHits = dataCommands.reduce((sum, item) => sum + item.hits, 0)
            this.statItems[1].value = this.formatNumber(dataHits)
          }

          // 提取热门客户端
          const hostStats = data.facets.find(item => item.fieldName === 'message.host')
          this.topHosts = hostStats && hostStats.values ? hostStats.values : []

          // 提取热门数据库
          const dbStats = data.facets.find(item => item.fieldName === 'db_name')
          this.topDatabases = dbStats && dbStats.values ? dbStats.values : []

          // 提取热门表
          const tableStats = data.facets.find(item => item.fieldName === 'table_name')
          this.topTables = tableStats && tableStats.values ? tableStats.values : []
        }
      } catch (error) {
        console.error('获取字段统计数据失败:', error)
        throw error
      }
    },

    // 获取登录失败统计信息
    async fetchFailedLoginStats() {
      this.failedLoginLoading = true

      try {
        // 构建查询条件
        const additionalQuery = `agent_hostname:"${this.form.agent_hostname}" and fctags.port:"${this.form.port}"`
        
        const timeConfig = this.getCurrentTimeConfig();

        // 获取登录失败用户统计
        const userResponse = await getMysqlFailedLoginStats(
          'user',
          timeConfig.type === 'custom' ? null : timeConfig.timeRange,
          10,
          additionalQuery,
          timeConfig.type === 'custom' ? timeConfig.startTime : undefined,
          timeConfig.type === 'custom' ? timeConfig.endTime : undefined
        );

        if (userResponse.code === 200) {
          this.failedLoginUserStats = userResponse.data || []
          if (this.failedLoginUserStats.length === 0) {
            this.failedLoginUserStats = [{ fieldValue: '暂无数据', count: 0 }]
          }
        } else {
          this.failedLoginUserStats = [{ fieldValue: '获取数据失败', count: 0 }]
        }

        // 获取登录失败客户端统计
        const hostResponse = await getMysqlFailedLoginStats(
          'host',
          timeConfig.type === 'custom' ? null : timeConfig.timeRange,
          10,
          additionalQuery,
          timeConfig.type === 'custom' ? timeConfig.startTime : undefined,
          timeConfig.type === 'custom' ? timeConfig.endTime : undefined
        );
        if (hostResponse.code === 200) {
          this.failedLoginHostStats = hostResponse.data || []
          if (this.failedLoginHostStats.length === 0) {
            this.failedLoginHostStats = [{ fieldValue: '暂无数据', count: 0 }]
          }
        } else {
          this.failedLoginHostStats = [{ fieldValue: '获取数据失败', count: 0 }]
        }
      } catch (error) {
        console.error('获取登录失败统计失败:', error)
        this.failedLoginUserStats = [{ fieldValue: '获取数据失败', count: 0 }]
        this.failedLoginHostStats = [{ fieldValue: '获取数据失败', count: 0 }]
      } finally {
        this.failedLoginLoading = false
      }
    },

    // 获取桑基图数据
    async fetchSankeyData() {
      this.sankeyLoading = true

      try {
        // 构建查询条件，只查询该实例的数据
        const additionalQuery = `agent_hostname:"${this.form.agent_hostname}" and fctags.port:"${this.form.port}"`
        
        console.log('桑基图查询条件:', additionalQuery)
        console.log('桑基图查询参数:', {
          timeConfig: this.getCurrentTimeConfig(),
          dbLimit: 1,
          clientLimit: this.sankeyForm.clientLimit,
          additionalQuery: additionalQuery
        })
        
        const timeConfig = this.getCurrentTimeConfig();

        const response = await getMysqlDataFlowStats(
          timeConfig.type === 'custom' ? null : timeConfig.timeRange,
          1, // 只有一个数据库
          this.sankeyForm.clientLimit,
          additionalQuery,
          timeConfig.type === 'custom' ? timeConfig.startTime : undefined,
          timeConfig.type === 'custom' ? timeConfig.endTime : undefined
        );
        
        console.log('桑基图API响应:', response)
        
        if (response.code === 200) {
          console.log('桑基图数据详情:', JSON.stringify(response.data, null, 2))
          
          // 检查数据结构
          if (response.data) {
            console.log('数据库列表:', response.data.databases)
            console.log('客户端到数据库连接:', response.data.clientToDbLinks)
            console.log('数据库到数据库名称连接:', response.data.dbToDbNameLinks)
          }
          
          this.updateSankeyChart(response.data)
        } else {
          console.error('获取桑基图数据失败:', response.msg)
          this.$message.error('获取桑基图数据失败: ' + response.msg)
        }
      } catch (error) {
        console.error('获取桑基图数据失败:', error)
        this.$message.error('获取桑基图数据失败: ' + error.message)
      } finally {
        this.sankeyLoading = false
      }
    },

    // 初始化桑基图
    initSankeyChart() {
      this.$nextTick(() => {
        const chartContainer = this.$refs.sankeyChart
        if (!chartContainer) {
          console.warn('桑基图容器未找到')
          return
        }

        if (!this.sankeyChart) {
          try {
            this.sankeyChart = echarts.init(chartContainer)
            console.log('桑基图初始化成功')
            
            // 设置初始选项
            const option = {
              title: {
                text: '客户端到数据库的数据流向',
                left: 'center',
                textStyle: {
                  fontSize: 16,
                  fontWeight: 'bold'
                }
              },
              tooltip: {
                trigger: 'item',
                triggerOn: 'mousemove',
                formatter: function(params) {
                  if (params.dataType === 'edge') {
                    return `${params.data.source.replace(/^client:|^db:/, '')} → ${params.data.target.replace(/^client:|^db:/, '')}<br/>访问量: <b>${params.data.value}</b>`
                  } else {
                    return `${params.name.replace(/^client:|^db:/, '')}<br/>总访问量: <b>${params.value || 0}</b>`
                  }
                }
              },
              series: [{
                type: 'sankey',
                layout: 'none',
                emphasis: {
                  focus: 'adjacency'
                },
                data: [],
                links: [],
                lineStyle: {
                  color: 'gradient',
                  curveness: 0.5
                },
                label: {
                  color: '#333',
                  fontSize: 12
                },
                levels: [{
                  depth: 0,
                  itemStyle: {
                    color: '#58a9f5'
                  },
                  lineStyle: {
                    color: 'source',
                    opacity: 0.6
                  }
                }, {
                  depth: 1,
                  itemStyle: {
                    color: '#ea7ccc'
                  },
                  lineStyle: {
                    color: 'target',
                    opacity: 0.6
                  }
                }]
              }]
            }
            
            this.sankeyChart.setOption(option)
            
          } catch (error) {
            console.error('桑基图初始化失败:', error)
            return
          }
        }
        
        const option = {
          title: {
            text: '客户端到数据库的数据流向',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove',
            formatter: function(params) {
              if (params.dataType === 'edge') {
                return `${params.data.source.replace(/^client:|^db:/, '')} → ${params.data.target.replace(/^client:|^db:/, '')}<br/>访问量: <b>${params.data.value}</b>`
              } else {
                return `${params.name.replace(/^client:|^db:/, '')}<br/>总访问量: <b>${params.value}</b>`
              }
            }
          },
          series: [{
            type: 'sankey',
            layout: 'none',
            emphasis: {
              focus: 'adjacency'
            },
            data: [],
            links: [],
            lineStyle: {
              color: 'gradient',
              curveness: 0.5
            },
            itemStyle: {
              borderWidth: 1,
              borderColor: '#aaa'
            },
            label: {
              position: 'right',
              formatter: function(params) {
                // 显示原始名称，去掉前缀
                if (params.name.includes('client:')) {
                  return params.name.replace('client:', '')
                } else if (params.name.includes('db:')) {
                  return params.name.replace('db:', '')
                }
                return params.name
              }
            }
          }]
        }

        this.sankeyChart.setOption(option)
        console.log('桑基图配置设置完成')
      })
    },

    // 更新桑基图
    updateSankeyChart(data) {
      console.log('更新桑基图数据:', data)
      
      if (!data) {
        console.warn('桑基图数据为空')
        this.showEmptySankeyChart()
        return
      }

      // 确保桑基图已初始化
      if (!this.sankeyChart) {
        console.log('桑基图未初始化，正在初始化...')
        this.initSankeyChart()
        // 延迟更新数据，确保图表已初始化
        setTimeout(() => {
          this.updateSankeyChart(data)
        }, 300)
        return
      }

      const nodes = []
      const links = []
      const nodeNames = new Set()

      // 检查是否有客户端到数据库的连接数据
      if (data.clientToDbLinks && data.clientToDbLinks.length > 0) {
        console.log('客户端到数据库连接数据:', data.clientToDbLinks)
        
        // 添加客户端节点
        const clientIps = new Set()
        data.clientToDbLinks.forEach(link => {
          if (link.source && link.source.trim() !== '') {
            clientIps.add(link.source)
          }
        })

        console.log('客户端IP列表:', Array.from(clientIps))

        clientIps.forEach(ip => {
          const nodeName = 'client:' + ip
          if (!nodeNames.has(nodeName)) {
            nodeNames.add(nodeName)
            const totalValue = data.clientToDbLinks
              .filter(link => link.source === ip)
              .reduce((sum, link) => sum + (link.value || 0), 0)
            
            nodes.push({
              name: nodeName,
              value: totalValue,
              itemStyle: {
                color: '#58a9f5'
              }
            })
          }
        })

        // 添加数据库节点
        const dbNodeName = 'db:' + this.form.agent_hostname + ':' + this.form.port
        if (!nodeNames.has(dbNodeName)) {
          nodeNames.add(dbNodeName)
          const totalValue = data.clientToDbLinks.reduce((sum, link) => sum + (link.value || 0), 0)
          
          nodes.push({
            name: dbNodeName,
            value: totalValue,
            itemStyle: {
              color: '#ea7ccc'
            }
          })
        }

        // 添加连接
        data.clientToDbLinks.forEach(link => {
          if (link.source && link.source.trim() !== '' && link.value > 0) {
            links.push({
              source: 'client:' + link.source,
              target: dbNodeName,
              value: link.value
            })
          }
        })
      } else {
        console.warn('没有客户端到数据库的连接数据，显示空状态')
        this.showEmptySankeyChart()
        return
      }

      console.log('桑基图节点:', nodes)
      console.log('桑基图连接:', links)

      // 检查是否有有效的节点和连接
      if (nodes.length === 0) {
        console.warn('没有有效的桑基图节点')
        this.showEmptySankeyChart()
        return
      }

      // 更新图表
      try {
        const option = {
          series: [{
            data: nodes,
            links: links,
            type: 'sankey',
            layout: 'none',
            emphasis: {
              focus: 'adjacency'
            },
            lineStyle: {
              color: 'gradient',
              curveness: 0.5
            },
            label: {
              color: '#333',
              fontSize: 12
            },
            levels: [{
              depth: 0,
              itemStyle: {
                color: '#58a9f5'
              },
              lineStyle: {
                color: 'source',
                opacity: 0.6
              }
            }, {
              depth: 1,
              itemStyle: {
                color: '#ea7ccc'
              },
              lineStyle: {
                color: 'target',
                opacity: 0.6
              }
            }]
          }]
        }
        
        this.sankeyChart.setOption(option, true) // 使用 merge 模式
        this.sankeyChart.resize()
        console.log('桑基图更新成功')
      } catch (error) {
        console.error('桑基图更新失败:', error)
        this.showEmptySankeyChart()
      }
    },

    // 显示空的桑基图
    showEmptySankeyChart() {
      if (!this.sankeyChart) {
        this.initSankeyChart()
        setTimeout(() => {
          this.showEmptySankeyChart()
        }, 300)
        return
      }

      // 显示空状态 - 只有数据库节点
      const dbNodeName = 'db:' + this.form.agent_hostname + ':' + this.form.port
      const emptyNodes = [{
        name: dbNodeName,
        value: 0,
        itemStyle: {
          color: '#ea7ccc'
        }
      }]

      try {
        this.sankeyChart.setOption({
          title: {
            text: '客户端到数据库的数据流向',
            subtext: '暂无数据',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            },
            subtextStyle: {
              fontSize: 12,
              color: '#999'
            }
          },
          series: [{
            data: emptyNodes,
            links: []
          }]
        }, true)

        this.sankeyChart.resize()
        console.log('显示空桑基图成功')
      } catch (error) {
        console.error('显示空桑基图失败:', error)
      }
    },

    // 测试桑基图状态
    testSankeyChart() {
      console.log('=== 桑基图状态检查 ===')
      console.log('桑基图实例:', this.sankeyChart)
      console.log('桑基图容器:', this.$refs.sankeyChart)
      console.log('表单数据:', this.form)
      console.log('时间配置:', this.getCurrentTimeConfig())
      
      if (this.$refs.sankeyChart) {
        console.log('桑基图容器尺寸:', {
          width: this.$refs.sankeyChart.offsetWidth,
          height: this.$refs.sankeyChart.offsetHeight
        })
      }
    },

    // 获取时间序列数据
    async fetchTimeSeriesData() {
      this.timeSeriesLoading = true

      try {
        // 构建查询条件
        const additionalQuery = `agent_hostname:"${this.form.agent_hostname}" and fctags.port:"${this.form.port}"`
        
        const timeConfig = this.getCurrentTimeConfig();

        const response = await getMysqlTimeSeriesStats(
          'all',
          this.timeSeriesForm.dimension,
          timeConfig.type === 'custom' ? null : timeConfig.timeRange,
          '1h',
          additionalQuery,
          timeConfig.type === 'custom' ? timeConfig.startTime : undefined,
          timeConfig.type === 'custom' ? timeConfig.endTime : undefined
        );
        
        if (response.code === 200) {
          this.updateTimeSeriesChart(response.data)
        } else {
          console.error('获取时间序列数据失败:', response.msg)
        }
      } catch (error) {
        console.error('获取时间序列数据失败:', error)
      } finally {
        this.timeSeriesLoading = false
      }
    },

    // 初始化时间序列图表
    initTimeSeriesChart() {
      if (!this.timeSeriesChart) {
        this.timeSeriesChart = echarts.init(this.$refs.timeSeriesChart)
      }

      const option = {
        title: {
          text: '访问时间序列统计',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: [],
          type: 'scroll',
          orient: 'horizontal',
          top: 50
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: 100,
          containLabel: true
        },
        xAxis: {
          type: 'time',
          boundaryGap: false
        },
        yAxis: {
          type: 'value'
        },
        series: []
      }

      this.timeSeriesChart.setOption(option)
    },

    // 更新时间序列图表
    updateTimeSeriesChart(data) {
      if (!this.timeSeriesChart) {
        this.initTimeSeriesChart()
      }

      if (!data || !data.data || !data.data.result) {
        return
      }

      const results = data.data.result
      const series = []
      const legendData = []

      results.slice(0, 10).forEach(result => {
        const metric = result.metric
        const values = result.values

        let dimensionName = ''
        if (this.timeSeriesForm.dimension === 'cmd') {
          dimensionName = metric['message.cmd'] || '未知命令'
        } else if (this.timeSeriesForm.dimension === 'user') {
          dimensionName = metric['message.user'] || '未知用户'
        } else if (this.timeSeriesForm.dimension === 'client') {
          dimensionName = metric['message.host'] || '未知客户端'
        }

        legendData.push(dimensionName)

        const seriesData = values.map(item => {
          const timestamp = item[0] * 1000
          const value = parseInt(item[1], 10) || 0
          return [timestamp, value]
        })

        seriesData.sort((a, b) => a[0] - b[0])

        series.push({
          name: dimensionName,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 5,
          data: seriesData
        })
      })

      this.timeSeriesChart.setOption({
        legend: {
          data: legendData
        },
        series: series
      })
    },

    formatNumber(num) {
      return num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    },

    updateCharts() {
      // 用户操作分布
      if (!this.userActionChart) {
        this.userActionChart = echarts.init(this.$refs.userActionChart)
      }
      const commandData = []
      const commandNames = []
      if (this.topCommands && this.topCommands.length > 0) {
        this.topCommands.forEach(item => {
          commandData.push({ value: item.hits, name: item.fieldValue })
          commandNames.push(item.fieldValue)
        })
      }
      const userActionOption = {
        title: { text: '操作类型分布', left: 'center' },
        tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
        legend: { orient: 'vertical', left: 'left', data: commandNames },
        series: [
          {
            name: '操作类型',
            type: 'pie',
            radius: '70%',
            roseType: 'area',
            itemStyle: { borderRadius: 5 },
            label: { show: true, formatter: '{b}: {d}%' },
            emphasis: {
              label: { fontSize: '15', fontWeight: 'bold' },
              itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' }
            },
            labelLine: { smooth: 0.2, length: 10, length2: 20 },
            data: commandData
          }
        ]
      }
      this.userActionChart.setOption(userActionOption)

      // 热门表访问统计
      if (!this.tableChart) {
        this.tableChart = echarts.init(this.$refs.tableChart)
      }
      const tableData = []
      const tableNames = []
      if (this.topTables && this.topTables.length > 0) {
        const topTables = this.topTables.slice(0, 10)
        topTables.forEach(item => {
          tableData.push(item.hits)
          tableNames.push(item.fieldValue || '未知表')
        })
      }
      const tableOption = {
        title: { text: '热门表访问统计', left: 'center' },
        tooltip: {
          trigger: 'axis', axisPointer: { type: 'shadow' },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>${data.seriesName}: ${data.value.toLocaleString()}`;
          }
        },
        grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
        xAxis: {
          type: 'category', data: tableNames,
          axisLabel: { interval: 0, rotate: 45, fontSize: 11, margin: 15 }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              return value >= 1000 ? (value / 1000).toFixed(1) + 'k' : value;
            }
          }
        },
        series: [
          {
            name: '访问次数', type: 'bar', data: tableData, barWidth: '60%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ]),
              borderRadius: [5, 5, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#5aa9fa' },
                  { offset: 0.7, color: '#1170d3' },
                  { offset: 1, color: '#1170d3' }
                ])
              }
            },
            label: {
              show: true, position: 'top',
              formatter: function(params) {
                return params.value > 1000 ? (params.value / 1000).toFixed(1) + 'k' : params.value;
              },
              fontSize: 10, color: '#666'
            }
          }
        ]
      }
      this.tableChart.setOption(tableOption)

      // 初始化桑基图和时间序列图（只初始化，不更新数据）
      this.$nextTick(() => {
        this.initSankeyChart()
        this.initTimeSeriesChart()
      })
    }
  }
}
</script>

<style scoped>
.mysql-stat-detail {
  padding: 0;
}
.detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}
.detail-controls-panel {
  margin-bottom: 20px;
}
.instance-info {
  margin-bottom: 20px;
}
.instance-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px;
  position: relative;
  padding-left: 12px;
}
.instance-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: #409EFF;
  border-radius: 2px;
}
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 30px 0 20px;
  position: relative;
  padding-left: 12px;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: #409EFF;
  border-radius: 2px;
}
.stat-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}
.stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 24px 20px 24px 24px;
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 300px;
  position: relative;
  overflow: hidden;
  min-height: 100px;
}
.stat-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  position: absolute;
  left: -50px;
  top: -25px;
  opacity: 0.2;
  z-index: 1;
}
.stat-content {
  width: 100%;
  padding-left: 25px;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.stat-number {
  font-size: 36px;
  font-weight: 500;
  line-height: 1.1;
  margin-bottom: 4px;
  font-family: "Arial", sans-serif;
  letter-spacing: 0.5px;
}
.stat-title {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: normal;
}
.sankey-controls, .time-series-controls {
  margin-bottom: 10px;
}
.sankey-chart, .time-series-chart {
  width: 100%;
  height: 400px;
}
.stats-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 18px 24px;
  margin-bottom: 20px;
}
.error-msg {
  color: #f56c6c;
  margin-top: 10px;
}
.charts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}
.chart-item {
  flex: 1;
  min-width: 45%;
  background-color: #fff;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}
.stats-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}
.stats-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}
.failed-count {
  color: #F56C6C;
  font-weight: bold;
}
</style> 