<template>
  <div class="app-container">
    <div class="custom-tabs-container">
      <div class="custom-tabs">
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'overview' }"
          @click="activeTab = 'overview'"
        >
          行为日志概览
        </div>
        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'statics' }"
          @click="activeTab = 'statics'"
        >
          流量统计
        </div>

        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'web_access' }"
          @click="activeTab = 'web_access'"
        >
          行为日志
        </div>

        <div
          class="custom-tab-item"
          :class="{ 'active': activeTab === 'all' }"
          @click="activeTab = 'all'"
        >
          全部日志
        </div>
      </div>
    </div>

    <div class="tab-content">
      <behavior-stat-overview v-if="activeTab === 'overview'"></behavior-stat-overview>
      <log-query-component
        v-else-if="activeTab === 'statics'"
        key="statistics"
        ref="statisticsLogQuery"
        stream="AUDITLOG_H3C_BEHAVIOR"
        :title="'H3C 上网流量日志'"
        :subtitle="'查询和分析H3C上网行为审计记录，监控用户网络访问行为和流量统计'"
        :settings="statisticsLogSettings"
        defaultTimeRange="1h"
      />
      <log-query-component
        v-else-if="activeTab === 'web_access'"
        key="web_access"
        ref="webAccessLogQuery"
        stream="AUDITLOG_H3C_BEHAVIOR"
        :title="'H3C 上网行为审计'"
        :subtitle="'查询和分析H3C上网行为审计记录，监控用户网络访问行为和流量统计'"
        :settings="webAccessSettings"
        defaultTimeRange="1h"
      />
      <log-query-component
        v-else-if="activeTab === 'all'"
        key="all_logs"
        ref="allLogsQuery"
        stream="AUDITLOG_H3C_BEHAVIOR"
        :title="'H3C 上网行为审计'"
        :subtitle="'查询和分析H3C上网行为审计记录，监控用户网络访问行为和流量统计'"
        :settings="logSettings"
        defaultTimeRange="1h"
      />
    </div>

    
  </div>
</template>

<script>
import BehaviorStatOverview from '@/views/auditlog/components/behavior_stat_overview'
import LogQueryComponent from '@/components/log_query_component'

export default {
  name: 'BehaviorAudit',
  components: {
    BehaviorStatOverview,
    LogQueryComponent
  },
  data() {
    return {
      loading: false,
      activeTab: 'overview',
      // 日志设置
      logSettings: {
        columns: [
          { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
          { prop: 'user_name', label: '用户IP', width: '120' },
          { prop: 'user_group', label: '用户组', width: '140', tag: true },
          { prop: 'log_type', label: '日志类型', width: '120', tag: true},
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      },
      webAccessSettings: {
        columns: [
          { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
          { prop: 'user_name', label: '用户IP', width: '120' },
          { prop: 'user_group_name', label: '用户组', width: '140', tag: true },
          { prop: 'src_ip', label: '源IP', width: '120' },
          { prop: 'dst_ip', label: '目标IP', width: '120' },
          { prop: 'url_domain', label: '访问域名', width: '200', showOverflowTooltip: true },
          { prop: 'url_cate_name', label: '网站分类', width: '120', tag: true },
          { prop: 'log_type', label: '日志类型', width: '120', tag: true, formatter: this.formatLogType },
          { prop: 'handle_action', label: '处理动作', width: '100', tag: true, formatter: this.formatHandleAction },
          { prop: 'term_platform', label: '终端平台', width: '120', formatter: this.formatTermPlatform },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right',
        // 默认过滤条件：只显示网页访问类型的日志
        defaultFilters: [
          { field: 'log_type', operator: '=', value: 'web_access' }
        ]
      },
      // 流量统计专用的日志设置
      statisticsLogSettings: {
        columns: [
          { prop: 'event_time', label: '事件时间', width: '180', sortable: true },
          { prop: 'user_ip', label: '用户IP', width: '120' },
          { prop: 'user_mac', label: 'MAC地址', width: '140', formatter: this.formatMacAddress },
          { prop: 'user_group', label: '用户组', width: '140', tag: true },
          { prop: 'app_name', label: '应用名称', width: '180', showOverflowTooltip: true, tag: true },
          { prop: 'app_group', label: '应用分组', width: '120', tag: true },
          { prop: 'upload_bytes', label: '上传流量', width: '120', formatter: this.formatBytes },
          { prop: 'download_bytes', label: '下载流量', width: '120', formatter: this.formatBytes },
          { prop: 'log_type', label: '日志类型', width: '120', tag: true, formatter: this.formatLogType },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right',
        // 默认过滤条件：只显示流量统计类型的日志
        defaultFilters: [
          { field: 'log_type', operator: '=', value: 'statistic_traffic' }
        ]
      }
    }
  },
  watch: {
    // 监听标签页切换
    activeTab(newTab, oldTab) {
      // 当切换到日志查询相关的标签页时，确保组件正确初始化
      if (['statics', 'web_access', 'all'].includes(newTab)) {
        // 使用 nextTick 确保组件已经渲染完成
        this.$nextTick(() => {
          this.refreshLogs();
        });
      }
    }
  },
  methods: {
    // 格式化字节数
    formatBytes(row, column, cellValue) {
      if (!cellValue || cellValue === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(cellValue) / Math.log(k));
      return parseFloat((cellValue / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // 格式化处理动作
    formatHandleAction(row, column, cellValue) {
      const actionMap = {
        '0': '允许',
        '1': '阻断',
        '2': '监控',
        '3': '警告'
      };
      return actionMap[cellValue] || cellValue || '未知';
    },
    
    // 格式化日志类型
    formatLogType(row, column, cellValue) {
      const typeMap = {
        'web_access': '网页访问',
        'statistic_traffic': '流量统计',
        'app_access': '应用访问',
        'file_transfer': '文件传输',
        'email': '邮件',
        'im': '即时通讯',
        'p2p': 'P2P下载',
        'game': '游戏',
        'video': '视频',
        'music': '音乐'
      };
      return typeMap[cellValue] || cellValue || '未知';
    },
    
    // 格式化终端平台
    formatTermPlatform(row, column, cellValue) {
      if (!cellValue || cellValue === '未知类型') {
        return '未知';
      }
      return cellValue;
    },
    
    // 格式化会话时长
    formatDuration(row, column, cellValue) {
      // 如果有直接的时长字段，使用它
      if (cellValue) {
        return this.formatSeconds(cellValue);
      }
      
      // 否则从开始时间和结束时间计算
      const startTime = row.session_start_time || row.create_time;
      const endTime = row.session_end_time || row.end_time;
      
      if (startTime && endTime) {
        const duration = endTime - startTime;
        return this.formatSeconds(duration);
      }
      
      return '未知';
    },
    
    // 格式化秒数为可读格式
    formatSeconds(seconds) {
      if (!seconds || seconds === 0) return '0秒';
      
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      if (hours > 0) {
        return `${hours}小时${minutes}分${secs}秒`;
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`;
      } else {
        return `${secs}秒`;
      }
    },
    
    // 格式化MAC地址
    formatMacAddress(row, column, cellValue) {
      if (!cellValue) return '未知';
      
      // 确保MAC地址格式统一（大写，用冒号分隔）
      return cellValue.toUpperCase().replace(/[:-]/g, ':');
    },
    
    // 刷新日志查询
    refreshLogs() {
      // 根据当前活动的标签页刷新对应的组件
      switch (this.activeTab) {
        case 'statics':
          if (this.$refs.statisticsLogQuery) {
            this.$refs.statisticsLogQuery.fetchData();
          }
          break;
        case 'web_access':
          if (this.$refs.webAccessLogQuery) {
            this.$refs.webAccessLogQuery.fetchData();
          }
          break;
        case 'all':
          if (this.$refs.allLogsQuery) {
            this.$refs.allLogsQuery.fetchData();
          }
          break;
        default:
          break;
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 0;
}

.custom-tabs-container {
  padding: 10px 0px 0px 10px;
}

.custom-tabs {
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden;
}

.custom-tab-item {
  padding: 8px 10px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
  position: relative;
}

.custom-tab-item.active {
  color: #67C23A;
  font-weight: 500;
}

.custom-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #67C23A;
}

.tab-content {
  margin-top: 20px;
}
</style>

<style>
/* 自定义表格样式 */
.log-message {
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 12px;
}

.el-table__row:hover .log-message {
  color: #409EFF;
}

/* 标签样式 */
.el-tag {
  margin-right: 5px;
  font-size: 12px;
}

/* 流量数据样式 */
.el-table__cell {
  font-size: 12px;
}
</style>