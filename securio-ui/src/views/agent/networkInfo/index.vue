<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关联的Agent唯一标识" prop="agentId">
        <el-input
          v-model="queryParams.agentId"
          placeholder="请输入关联的Agent唯一标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网络接口名称" prop="iface">
        <el-input
          v-model="queryParams.iface"
          placeholder="请输入网络接口名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网络适配器名称" prop="adapter">
        <el-input
          v-model="queryParams.adapter"
          placeholder="请输入网络适配器名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接口状态" prop="state">
        <el-input
          v-model="queryParams.state"
          placeholder="请输入接口状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最大传输单元" prop="mtu">
        <el-input
          v-model="queryParams.mtu"
          placeholder="请输入最大传输单元"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="MAC地址" prop="mac">
        <el-input
          v-model="queryParams.mac"
          placeholder="请输入MAC地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IP地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入IP地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="子网掩码" prop="netmask">
        <el-input
          v-model="queryParams.netmask"
          placeholder="请输入子网掩码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广播地址" prop="broadcast">
        <el-input
          v-model="queryParams.broadcast"
          placeholder="请输入广播地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createAt">
        <el-input
          v-model="queryParams.createAt"
          placeholder="请输入创建时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否删除" prop="isDel">
        <el-input
          v-model="queryParams.isDel"
          placeholder="请输入是否删除"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新时间" prop="updateAt">
        <el-input
          v-model="queryParams.updateAt"
          placeholder="请输入更新时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主机IP" prop="hostIp" v-if="!hideHostIpSearch">
        <el-input
          v-model="queryParams.hostIp"
          placeholder="请输入主机IP"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['agent:networkInfo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['agent:networkInfo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['agent:networkInfo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['agent:networkInfo:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="networkInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="自增主键ID" align="center" prop="id" />
      <el-table-column label="关联的Agent唯一标识" align="center" prop="agentId" />
      <el-table-column label="网络接口名称" align="center" prop="iface" />
      <el-table-column label="网络适配器名称" align="center" prop="adapter" />
      <el-table-column label="接口类型" align="center" prop="ifaceType" />
      <el-table-column label="接口状态" align="center" prop="state" />
      <el-table-column label="最大传输单元" align="center" prop="mtu" />
      <el-table-column label="MAC地址" align="center" prop="mac" />
      <el-table-column label="协议类型" align="center" prop="protoType" />
      <el-table-column label="IP地址" align="center" prop="address" />
      <el-table-column label="子网掩码" align="center" prop="netmask" />
      <el-table-column label="广播地址" align="center" prop="broadcast" />
      <el-table-column label="创建时间" align="center" prop="createAt" />
      <el-table-column label="是否删除" align="center" prop="isDel" />
      <el-table-column label="更新时间" align="center" prop="updateAt" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['agent:networkInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['agent:networkInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改存储Agent主机的网络接口信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联的Agent唯一标识" prop="agentId">
          <el-input v-model="form.agentId" placeholder="请输入关联的Agent唯一标识" />
        </el-form-item>
        <el-form-item label="网络接口名称" prop="iface">
          <el-input v-model="form.iface" placeholder="请输入网络接口名称" />
        </el-form-item>
        <el-form-item label="网络适配器名称" prop="adapter">
          <el-input v-model="form.adapter" placeholder="请输入网络适配器名称" />
        </el-form-item>
        <el-form-item label="接口状态" prop="state">
          <el-input v-model="form.state" placeholder="请输入接口状态" />
        </el-form-item>
        <el-form-item label="最大传输单元" prop="mtu">
          <el-input v-model="form.mtu" placeholder="请输入最大传输单元" />
        </el-form-item>
        <el-form-item label="MAC地址" prop="mac">
          <el-input v-model="form.mac" placeholder="请输入MAC地址" />
        </el-form-item>
        <el-form-item label="IP地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="子网掩码" prop="netmask">
          <el-input v-model="form.netmask" placeholder="请输入子网掩码" />
        </el-form-item>
        <el-form-item label="广播地址" prop="broadcast">
          <el-input v-model="form.broadcast" placeholder="请输入广播地址" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createAt">
          <el-input v-model="form.createAt" placeholder="请输入创建时间" />
        </el-form-item>
        <el-form-item label="是否删除" prop="isDel">
          <el-input v-model="form.isDel" placeholder="请输入是否删除" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateAt">
          <el-input v-model="form.updateAt" placeholder="请输入更新时间" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNetworkInfo, getNetworkInfo, delNetworkInfo, addNetworkInfo, updateNetworkInfo } from "@/api/agent/networkInfo";

export default {
  name: "NetworkInfo",
  props: {
    hostIp: {
      type: String,
      default: ''
    },
    hideHostIpSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 存储Agent主机的网络接口信息表格数据
      networkInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        agentId: null,
        iface: null,
        adapter: null,
        ifaceType: null,
        state: null,
        mtu: null,
        mac: null,
        protoType: null,
        address: null,
        netmask: null,
        broadcast: null,
        createAt: null,
        isDel: null,
        updateAt: null,
        hostIp: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        agentId: [
          { required: true, message: "关联的Agent唯一标识不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    if (this.hostIp) {
      this.queryParams.hostIp = this.hostIp;
    }
    this.getList();
  },
  watch: {
    hostIp: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.hostIp = newVal;
          this.getList();
        }
      }
    }
  },
  methods: {
    /** 查询存储Agent主机的网络接口信息列表 */
    getList() {
      this.loading = true;
      listNetworkInfo(this.queryParams).then(response => {
        this.networkInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        agentId: null,
        iface: null,
        adapter: null,
        ifaceType: null,
        state: null,
        mtu: null,
        mac: null,
        protoType: null,
        address: null,
        netmask: null,
        broadcast: null,
        createAt: null,
        isDel: null,
        updateAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加存储Agent主机的网络接口信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getNetworkInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改存储Agent主机的网络接口信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateNetworkInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNetworkInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除存储Agent主机的网络接口信息编号为"' + ids + '"的数据项？').then(function() {
        return delNetworkInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('agent/networkInfo/export', {
        ...this.queryParams
      }, `networkInfo_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
