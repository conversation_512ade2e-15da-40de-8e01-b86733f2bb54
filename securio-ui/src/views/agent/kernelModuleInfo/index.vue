<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模块名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入模块名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主机IP" prop="hostIp" v-if="!hideHostIpSearch">
        <el-input
          v-model="queryParams.hostIp"
          placeholder="请输入主机IP"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="kernelModuleInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主机IP" align="center" prop="hostIp" />
      <el-table-column label="模块名称" align="center" prop="name" />
      <el-table-column label="模块路径" align="center" prop="path" show-overflow-tooltip/>
      <el-table-column label="模块大小" align="center" prop="size" />
      <el-table-column label="是否加载" align="center" prop="loaded" />
      <el-table-column label="MD5哈希值" align="center" prop="md5" show-overflow-tooltip/>
      <el-table-column label="SHA-256哈希值" align="center" prop="sha256" show-overflow-tooltip/>
      <el-table-column label="文件所有者" align="center" prop="owner" />
      <el-table-column label="文件用户组" align="center" prop="group" />
      <el-table-column label="文件权限" align="center" prop="permission" />
      <el-table-column label="创建时间" align="center" prop="createAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.createAt) }}</template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.updateAt) }}</template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listKernelModuleInfo, getKernelModuleInfo, delKernelModuleInfo, addKernelModuleInfo, updateKernelModuleInfo } from "@/api/agent/kernelModuleInfo";

export default {
  name: "KernelModuleInfo",
  props: {
    hostIp: {
      type: String,
      default: ''
    },
    hideHostIpSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 存储内核模块信息表格数据
      kernelModuleInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        path: null,
        size: null,
        loaded: null,
        md5: null,
        sha256: null,
        owner: null,
        group: null,
        permission: null,
        lastUpdate: null,
        createAt: null,
        isDel: null,
        updateAt: null,
        hostIp: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "模块名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    if (this.hostIp) {
      this.queryParams.hostIp = this.hostIp;
    }
    this.getList();
  },
  watch: {
    hostIp: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.hostIp = newVal;
          this.getList();
        }
      }
    }
  },
  methods: {
    /** 查询存储内核模块信息列表 */
    getList() {
      this.loading = true;
      listKernelModuleInfo(this.queryParams).then(response => {
        this.kernelModuleInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        path: null,
        size: null,
        loaded: null,
        md5: null,
        sha256: null,
        owner: null,
        group: null,
        permission: null,
        lastUpdate: null,
        createAt: null,
        isDel: null,
        updateAt: null,
        hostIp: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加存储内核模块信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getKernelModuleInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改存储内核模块信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateKernelModuleInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addKernelModuleInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除存储内核模块信息编号为"' + ids + '"的数据项？').then(function() {
        return delKernelModuleInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('agent/kernelModuleInfo/export', {
        ...this.queryParams
      }, `kernelModuleInfo_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
