<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="128px">
      
      <el-form-item label="进程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入进程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="运行进程的用户" prop="user">
        <el-input
          v-model="queryParams.user"
          placeholder="请输入运行进程的用户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主机IP" prop="hostIp" v-if="!hideHostIpSearch">
        <el-input
          v-model="queryParams.hostIp"
          placeholder="请输入主机IP"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="processInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主机IP地址" align="center" prop="hostIp" />
      <el-table-column label="进程ID" align="center" prop="pid" />
      <el-table-column label="进程名称" align="center" prop="name" show-overflow-tooltip/>
      <el-table-column label="父进程ID" align="center" prop="ppid" />
      <el-table-column label="进程启动命令行" align="center" prop="cmd" show-overflow-tooltip/>
      <el-table-column label="进程启动参数" align="center" prop="argvs" show-overflow-tooltip/>
      <el-table-column label="运行进程的用户" align="center" prop="user" />
      <el-table-column label="进程启动时间" align="center" prop="startTime" />
      <el-table-column label="创建时间" align="center" prop="createAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.createAt) }}</template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.updateAt) }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['agent:processInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['agent:processInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改存储Agent主机的进程信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="关联的Agent唯一标识" prop="agentId">
          <el-input v-model="form.agentId" placeholder="请输入关联的Agent唯一标识" />
        </el-form-item>
        <el-form-item label="进程ID" prop="pid">
          <el-input v-model="form.pid" placeholder="请输入进程ID" />
        </el-form-item>
        <el-form-item label="进程名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入进程名称" />
        </el-form-item>
        <el-form-item label="父进程ID" prop="ppid">
          <el-input v-model="form.ppid" placeholder="请输入父进程ID" />
        </el-form-item>
        <el-form-item label="进程启动命令行" prop="cmd">
          <el-input v-model="form.cmd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="进程启动参数" prop="argvs">
          <el-input v-model="form.argvs" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="运行进程的用户" prop="user">
          <el-input v-model="form.user" placeholder="请输入运行进程的用户" />
        </el-form-item>
        <el-form-item label="进程启动时间" prop="startTime">
          <el-input v-model="form.startTime" placeholder="请输入进程启动时间" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createAt">
          <el-input v-model="form.createAt" placeholder="请输入创建时间" />
        </el-form-item>
        <el-form-item label="是否删除" prop="isDel">
          <el-input v-model="form.isDel" placeholder="请输入是否删除" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateAt">
          <el-input v-model="form.updateAt" placeholder="请输入更新时间" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProcessInfo, getProcessInfo, delProcessInfo, addProcessInfo, updateProcessInfo } from "@/api/agent/processInfo";

export default {
  name: "InventoryProcessInfo",
  props: {
    hostIp: {
      type: String,
      default: ''
    },
    hideHostIpSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 存储Agent主机的进程信息表格数据
      processInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        agentId: null,
        pid: null,
        name: null,
        ppid: null,
        cmd: null,
        argvs: null,
        user: null,
        startTime: null,
        createAt: null,
        isDel: null,
        updateAt: null,
        hostIp: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        agentId: [
          { required: true, message: "关联的Agent唯一标识不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    if (this.hostIp) {
      this.queryParams.hostIp = this.hostIp;
    }
    this.getList();
  },
  watch: {
    hostIp: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.hostIp = newVal;
          this.getList();
        }
      }
    }
  },
  methods: {
    /** 查询存储Agent主机的进程信息列表 */
    getList() {
      this.loading = true;
      listProcessInfo(this.queryParams).then(response => {
        this.processInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        agentId: null,
        pid: null,
        name: null,
        ppid: null,
        cmd: null,
        argvs: null,
        user: null,
        startTime: null,
        createAt: null,
        isDel: null,
        updateAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加存储Agent主机的进程信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getProcessInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改存储Agent主机的进程信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateProcessInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProcessInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除存储Agent主机的进程信息编号为"' + ids + '"的数据项？').then(function() {
        return delProcessInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('agent/processInfo/export', {
        ...this.queryParams
      }, `processInfo_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
