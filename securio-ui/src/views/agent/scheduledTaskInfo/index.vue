<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="运行用户" prop="user">
        <el-input
          v-model="queryParams.user"
          placeholder="请输入运行用户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主机IP" prop="hostIp" v-if="!hideHostIpSearch">
        <el-input
          v-model="queryParams.hostIp"
          placeholder="请输入主机IP"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    
    <el-table v-loading="loading" :data="scheduledTaskInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主机IP" align="center" prop="hostIp" />
      <el-table-column label="任务名称" align="center" prop="name" />
      <el-table-column label="执行时间/计划" align="center" prop="schedule" />
      <el-table-column label="执行的命令" align="center" prop="command" show-overflow-tooltip/>
      <el-table-column label="运行用户" align="center" prop="user" />
      <el-table-column label="是否启用" align="center" prop="enabled" />
      <el-table-column label="来源位置" align="center" prop="sourceLocation" show-overflow-tooltip/>
      <el-table-column label="创建时间" align="center" prop="createAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.createAt) }}</template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.updateAt) }}</template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listScheduledTaskInfo, getScheduledTaskInfo, delScheduledTaskInfo, addScheduledTaskInfo, updateScheduledTaskInfo } from "@/api/agent/scheduledTaskInfo";

export default {
  name: "ScheduledTaskInfo",
  props: {
    hostIp: {
      type: String,
      default: ''
    },
    hideHostIpSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 存储计划任务信息表格数据
      scheduledTaskInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        schedule: null,
        command: null,
        user: null,
        enabled: null,
        sourceLocation: null,
        lastUpdate: null,
        createAt: null,
        isDel: null,
        updateAt: null,
        hostIp: null
      },
      // 表单参数
      form: {
        name: null,
        schedule: null,
        command: null,
        user: null,
        enabled: null,
        sourceLocation: null,
        lastUpdate: null,
        createAt: null,
        isDel: null,
        updateAt: null,
        hostIp: null
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    if (this.hostIp) {
      this.queryParams.hostIp = this.hostIp;
    }
    this.getList();
  },
  watch: {
    hostIp: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.hostIp = newVal;
          this.getList();
        }
      }
    }
  },
  methods: {
    /** 查询存储计划任务信息列表 */
    getList() {
      this.loading = true;
      listScheduledTaskInfo(this.queryParams).then(response => {
        this.scheduledTaskInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        schedule: null,
        command: null,
        user: null,
        enabled: null,
        sourceLocation: null,
        lastUpdate: null,
        createAt: null,
        isDel: null,
        updateAt: null,
        hostIp: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加存储计划任务信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getScheduledTaskInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改存储计划任务信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateScheduledTaskInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addScheduledTaskInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除存储计划任务信息编号为"' + ids + '"的数据项？').then(function() {
        return delScheduledTaskInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('agent/scheduledTaskInfo/export', {
        ...this.queryParams
      }, `scheduledTaskInfo_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
