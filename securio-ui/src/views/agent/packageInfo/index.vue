<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="98px">
      
      <el-form-item label="软件包名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入软件包名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="软件包供应商" prop="vendor">
        <el-input
          v-model="queryParams.vendor"
          placeholder="请输入软件包供应商"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主机IP" prop="hostIp" v-if="!hideHostIpSearch">
        <el-input
          v-model="queryParams.hostIp"
          placeholder="请输入主机IP"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="packageInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主机IP" align="center" prop="hostIp" />
      <el-table-column label="软件包名称" align="center" prop="name" />
      <el-table-column label="软件包版本号" align="center" prop="version" />
      <el-table-column label="软件包安装时间" align="center" prop="installTime" />
      <el-table-column label="软件包架构" align="center" prop="architecture" />
      <el-table-column label="软件包供应商" align="center" prop="vendor" />
      <el-table-column label="软件包描述信息" align="center" prop="description" show-overflow-tooltip/>
      <el-table-column label="软件包大小" align="center" prop="size" />
      <el-table-column label="软件包格式" align="center" prop="format" />
      <el-table-column label="创建时间" align="center" prop="createAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.createAt) }}</template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.updateAt) }}</template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listPackageInfo, getPackageInfo, delPackageInfo, addPackageInfo, updatePackageInfo } from "@/api/agent/packageInfo";

export default {
  name: "PackageInfo",
  props: {
    hostIp: {
      type: String,
      default: ''
    },
    hideHostIpSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 存储Agent主机安装的软件包信息表格数据
      packageInfoList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        agentId: null,
        name: null,
        vendor: null,
        hostIp: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        agentId: [
          { required: true, message: "关联的Agent唯一标识不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    if (this.hostIp) {
      this.queryParams.hostIp = this.hostIp;
    }
    this.getList();
  },
  watch: {
    hostIp: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.hostIp = newVal;
          this.getList();
        }
      }
    }
  },
  methods: {
    /** 查询存储Agent主机安装的软件包信息列表 */
    getList() {
      this.loading = true;
      listPackageInfo(this.queryParams).then(response => {
        this.packageInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    }
  }
};
</script>
