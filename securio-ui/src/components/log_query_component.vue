<template>
  <div class="log-query-component" v-loading="loading" element-loading-text="查询中...">
    <!-- 1. 标题和配置区域 -->
    <div class="header-section">
      <div class="title-area">
        <div class="title-container">
          <h2 class="section-title">{{ title }}</h2>
          <div v-if="subtitle" class="section-subtitle">{{ subtitle }}</div>
        </div>
        <!-- 查看字段配置链接 -->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
          v-model="fieldInfoVisible">
          <div class="field-info">
            <h3>字段配置信息</h3>
            <div class="field-info-description">当前表格包含以下 {{ availableFields.length }} 个字段</div>
            <div class="field-info-list">
              <div v-for="field in availableFields" :key="field.prop" class="field-info-item">
                <div class="field-info-label">{{ field.label }}</div>
                <div class="field-info-prop">{{ field.prop }}</div>
              </div>
            </div>
          </div>
          <a slot="reference" class="field-info-link" href="javascript:void(0)" title="查看字段配置信息">
            <i class="el-icon-view"></i>
            <span>查看字段配置</span>
          </a>
        </el-popover>
      </div>

      <!-- 配置表格按钮 -->
      <div class="config-area">
        <el-popover
          placement="bottom-end"
          width="350"
          trigger="click"
          v-model="fieldSelectorVisible">
          <div class="field-selector">
            <h3>选择表格显示字段</h3>
            <div class="field-selector-description">勾选要显示的字段，取消勾选则隐藏该字段</div>
            <el-checkbox-group v-model="selectedFields" class="field-checkbox-group">
              <el-checkbox
                v-for="field in availableFields"
                :key="field.prop"
                :label="field.prop"
                class="field-checkbox-item">
                <span class="field-label">{{ field.label }}</span>
                <span class="field-prop">({{ field.prop }})</span>
              </el-checkbox>
            </el-checkbox-group>
            <div class="field-selector-footer">
              <el-button size="small" @click="selectAllFields">全选</el-button>
              <el-button size="small" @click="deselectAllFields">全不选</el-button>
              <el-button size="small" type="primary" @click="applyFieldSelection">应用</el-button>
              <el-button size="small" @click="fieldSelectorVisible = false">取消</el-button>
            </div>
          </div>
          <el-button slot="reference" type="primary" plain size="small" icon="el-icon-setting">
            配置表格
          </el-button>
        </el-popover>
      </div>
    </div>

    <!-- 2. 筛选区域 -->
    <div class="filter-section">
      <!-- 时间筛选器 -->
      <div class="time-filter">
        <el-select v-model="queryForm.quickTime" placeholder="选择时间范围" size="small" @change="handleTimeRangeChange" style="width: 150px">
          <el-option label="最近15分钟" value="15m"></el-option>
          <el-option label="最近1小时" value="1h"></el-option>
          <el-option label="最近6小时" value="6h"></el-option>
          <el-option label="最近1天" value="1d"></el-option>
          <el-option label="最近10天" value="10d"></el-option>
          <el-option label="最近30天" value="30d"></el-option>
          <el-option label="自定义" value="custom"></el-option>
        </el-select>

        <!-- 自定义时间选择器，只在选择"自定义"时显示 -->
        <el-date-picker
          v-if="queryForm.quickTime === 'custom'"
          v-model="queryForm.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 380px; margin-left: 10px;"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleCustomTimeChange">
        </el-date-picker>
      </div>

      <div class="search-area">
        <el-input
          v-model="queryForm.keyword"
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          clearable
          @input="updateKeywordFilter"
          @clear="updateKeywordFilter"
          @keyup.enter.native="handleQuery"
          title="按回车键进行搜索"
          style="width: 250px">
        </el-input>
      </div>

      <!-- 3. 条件过滤组件 -->
      <div class="filter-condition-area">
        <div class="filter-buttons">
          <el-popover
            placement="bottom-start"
            width="500"
            trigger="click"
            v-model="filterConditionVisible">
            <div class="filter-condition-builder">
              <h3>构建过滤条件</h3>

              <!-- 字段过滤条件 -->
              <div class="filter-section-item">
                <div class="filter-section-title">字段过滤</div>
                <div class="field-filters-container">
                  <div v-for="(filter, index) in fieldFilters" :key="index" class="field-filter-item">
                    <el-select
                      v-model="filter.field"
                      placeholder="选择字段"
                      size="small"
                      style="width: 120px; margin-right: 8px;"
                      @change="handleFieldFilterChange">
                      <el-option
                        v-for="field in availableFields"
                        :key="field.prop"
                        :label="field.label"
                        :value="field.prop">
                      </el-option>
                    </el-select>

                    <el-select
                      v-model="filter.operator"
                      placeholder="运算符"
                      size="small"
                      style="width: 80px; margin-right: 8px;"
                      @change="handleFieldFilterChange">
                      <el-option label="等于" value="="></el-option>
                      <el-option label="包含" value="~="></el-option>
                      <el-option label="开始于" value="^="></el-option>
                      <el-option label="结束于" value="$="></el-option>
                    </el-select>

                    <el-input
                      v-model="filter.value"
                      placeholder="值"
                      size="small"
                      style="width: 120px; margin-right: 8px;"
                      @input="handleFieldValueInput"
                      @keyup.enter.native="handleFieldFilterChange">
                    </el-input>

                    <el-button
                      v-if="fieldFilters.length > 1"
                      type="danger"
                      icon="el-icon-delete"
                      size="small"
                      circle
                      @click="removeFieldFilter(index)">
                    </el-button>
                  </div>
                </div>

                <div class="filter-actions">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="addFieldFilter">
                    添加字段过滤
                  </el-button>
                </div>
              </div>

              <div class="filter-condition-footer">
                <el-button size="small" @click="resetFilterConditions">重置</el-button>
                <el-button size="small" @click="filterConditionVisible = false">关闭</el-button>
              </div>
            </div>

            <div slot="reference" class="filter-trigger" title="点击设置字段过滤条件">
              <i class="el-icon-filter"></i>
              <span>条件过滤</span>
              <i class="el-icon-arrow-down"></i>
            </div>
          </el-popover>
        </div>

        <!-- LogQL 输入框和条数输入框 -->
        <div class="logql-container">
          <div class="logql-limit-container">
            <el-input
              v-model="logsqlQuery"
              placeholder="LogQL 查询语句"
              class="logql-input"
              clearable
              @input="manualQueryUpdate = true"
              @keyup.enter.native="handleQuery"
              title="按回车键进行搜索">
              <el-button slot="append" type="primary" @click="handleQuery">查询</el-button>
            </el-input>

            <el-input
              v-model.number="limitCount"
              size="small"
              class="limit-input"
              @change="handleLimitChange"
              @keyup.enter.native="handleQuery"
              placeholder="50"
              title="限制返回条数">
            </el-input>
            <span class="limit-label">条</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 4. 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
        v-if="tableData.length > 0">
        <!-- 动态生成列 -->
        <template v-for="(column, index) in visibleColumns">
          <el-table-column
            :key="index"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :sortable="column.sortable"
            :show-overflow-tooltip="column.showOverflowTooltip"
            :fixed="column.fixed"
            :min-width="column.minWidth">
            <template slot-scope="scope">
              <!-- 标签展示 -->
              <template v-if="column.tag">
                <el-tag size="mini">{{ formatCellValue(scope.row, column) }}</el-tag>
              </template>
              <!-- 普通文本展示 -->
              <template v-else>
                {{ formatCellValue(scope.row, column) }}
              </template>
            </template>
          </el-table-column>
        </template>

        <!-- 详情按钮列 -->
        <el-table-column
          v-if="settings.showDetailButton"
          fixed="right"
          label="操作"
          :width="settings.detailButtonWidth || '100'">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="showLogDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空数据提示 -->
      <el-empty v-if="tableData.length === 0 && !loading" description="暂无数据">
        <template slot="description">
          <p>暂无日志数据</p>
          <p class="text-gray-500 text-sm">请尝试调整查询条件或时间范围</p>
        </template>
      </el-empty>

      <!-- 移除分页控件 -->
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog title="日志详情" :visible.sync="logDetailVisible" width="70%" append-to-body>
      <div class="log-detail">
        <el-descriptions :column="1" border labelStyle="width: 180px">
          <el-descriptions-item label="时间">{{ currentLogDetail._time }}</el-descriptions-item>
          <el-descriptions-item label="流">{{ formatStream(currentLogDetail._stream) }}</el-descriptions-item>
          <el-descriptions-item label="消息">
            <pre>{{ currentLogDetail._msg }}</pre>
          </el-descriptions-item>
          <template v-for="(value, key) in additionalFields">
            <el-descriptions-item :label="key" :key="key">
              <span v-if="typeof value === 'object'">{{ JSON.stringify(value) }}</span>
              <span v-else>{{ value }}</span>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryLogs } from '@/api/opslog/vmlogs'

export default {
  name: 'LogQueryComponent',
  props: {
    title: {
      type: String,
      default: '日志查询'
    },
    subtitle: {
      type: String,
      default: ''
    },
    defaultQuery: {
      type: String,
      default: ''
    },
    defaultTimeRange: {
      type: String,
      default: '5m'
    },
    externalTimeRange: {
      type: Object,
      default: null
    },
    stream: {
      type: String,
      default: ''
    },
    instance: {
      type: String,
      default: 'vmlog1'
    },
    settings: {
      type: Object,
      default: () => ({
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          {
            prop: '_msg',
            label: '消息',
            showOverflowTooltip: true,
            className: 'log-message'
          }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right',
        defaultFilters: []
      })
    }
  },
  data() {
    return {
      loading: false,
      queryForm: {
        timeRange: [],
        quickTime: '5m',
        keyword: ''
      },
      fieldFilters: [],
      logsqlQuery: '',
      manualQueryUpdate: false,
      tableData: [],
      limitCount: 50, // 默认限制返回 50 条数据
      logDetailVisible: false,
      currentLogDetail: {},
      additionalFields: {},
      fieldSelectorVisible: false,
      fieldInfoVisible: false,
      filterConditionVisible: false,
      timeFilterVisible: false,
      selectedFields: [],
      visibleColumns: []
    }
  },
  watch: {
    filterConditionVisible(val) {
      // 当打开条件过滤面板时，确保至少有一个过滤条件
      if (val && this.fieldFilters.length === 0) {
        this.fieldFilters = [{
          field: '',
          operator: '=',
          value: ''
        }];
      } else if (val && this.fieldFilters.length > 0) {
        // 如果已有过滤条件，检查是否需要添加一个空的过滤条件供用户添加
        const hasEmptyFilter = this.fieldFilters.some(filter => 
          !filter.field || !filter.value
        );
        if (!hasEmptyFilter) {
          this.fieldFilters.push({
            field: '',
            operator: '=',
            value: ''
          });
        }
      }
    },
    // 监听外部时间范围变化
    externalTimeRange: {
      handler(newVal) {
        if (newVal) {
          console.log('收到外部时间范围参数:', newVal)
          this.applyExternalTimeRange(newVal)
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    // 获取可用字段列表，从 settings.columns 中提取，排除时间字段
    availableFields() {
      return this.settings.columns
        .filter(column => column.prop !== '_time') // 排除时间字段
        .map(column => ({
          prop: column.prop,
          label: column.label
        }));
    },
    // 显示时间范围
    displayTimeRange() {
      if (this.queryForm.quickTime === 'custom' && this.queryForm.timeRange && this.queryForm.timeRange.length === 2) {
        // 如果是自定义时间范围，显示时间范围
        const start = this.queryForm.timeRange[0].substring(5, 16).replace(' ', ' ');
        const end = this.queryForm.timeRange[1].substring(5, 16).replace(' ', ' ');
        return `${start} 至 ${end}`;
      } else if (this.queryForm.quickTime) {
        // 如果是快速时间，显示对应的文本
        const timeMap = {
          '5m': '最近5分钟',
          '15m': '最近15分钟',
          '30m': '最近30分钟',
          '1h': '最近1小时',
          '6h': '最近6小时',
          '12h': '最近12小时',
          '1d': '最近1天',
          '10d': '最近10天',
          '30d': '最近30天',
        };
        return timeMap[this.queryForm.quickTime] || this.queryForm.quickTime;
      }
      return '选择时间';
    }
  },
  created() {
    // 初始化选中的字段
    this.selectedFields = this.settings.columns.map(column => column.prop);
    this.visibleColumns = [...this.settings.columns];

    // 初始化字段过滤条件
    this.initializeFieldFilters();

    // 初始化查询
    if (this.defaultQuery) {
      console.log('defaultQuery', this.defaultQuery);
      // 如果有默认查询，则使用默认查询
      this.logsqlQuery = this.defaultQuery;
      this.manualQueryUpdate = true;

      // 如果指定了流但默认查询中没有包含流过滤条件，则添加流过滤条件
      if (this.stream && !this.logsqlQuery.includes(`stream:"${this.stream}"`) && !this.logsqlQuery.includes(`_stream:{stream="${this.stream}"}`)) {
        this.logsqlQuery = `_stream:{stream="${this.stream}"} ${this.logsqlQuery}`;
      }
    } else {
      // 如果没有默认查询，则初始化时间过滤器并构建查询
      this.initDefaultTimeFilter();
      this.buildQuery();
    }
  },
  mounted() {
    // 初始加载数据
    this.fetchData();
  },
  methods: {
    initDefaultTimeFilter() {
      if (this.defaultTimeRange === 'custom' && this.queryForm.timeRange.length === 2) {
        // 已经设置了自定义时间范围，不需要处理
        return;
      }

      this.queryForm.quickTime = this.defaultTimeRange || '1h';
      this.updateTimeFilter();
    },
    updateTimeFilter() {
      if (!this.manualQueryUpdate) {
        this.buildQuery();
      }
    },
    updateKeywordFilter() {
      if (!this.manualQueryUpdate) {
        this.buildQuery();
      }
    },
    buildQuery() {
      let query = [];

      // 添加流过滤条件（如果指定了流）
      if (this.stream) {
        query.push(`_stream:{stream="${this.stream}"}`);
      }

      // 添加时间过滤条件
      if (this.queryForm.quickTime && this.queryForm.quickTime !== 'custom') {
        query.push(`_time:${this.queryForm.quickTime}`);
      } else if (this.queryForm.timeRange && this.queryForm.timeRange.length === 2) {
        query.push(`_time:[${this.queryForm.timeRange[0]}, ${this.queryForm.timeRange[1]}]`);
      }

      // 添加关键字过滤条件
      if (this.queryForm.keyword) {
        query.push(this.queryForm.keyword);
      }

      // 添加字段过滤条件
      this.fieldFilters.forEach(filter => {
        if (filter.field && filter.value) {
          let operator = '';
          switch (filter.operator) {
            case '=':
              operator = ':';
              break;
            case '~=':
              operator = ':~';
              break;
            case '^=':
              operator = ':^';
              break;
            case '$=':
              operator = ':$';
              break;
            default:
              operator = ':';
          }
          query.push(`${filter.field}${operator}"${filter.value}"`);
        }
      });

      // 构建最终查询语句
      this.logsqlQuery = query.join(' ');
      this.manualQueryUpdate = false;
    },
    // 处理时间范围变化，直接触发查询
    handleTimeRangeChange(value) {
      if (value === 'custom') {
        // 如果选择了自定义，仅显示时间选择器，不触发查询
        return;
      }

      // 清除自定义时间范围
      this.queryForm.timeRange = [];
      this.updateTimeFilter();

      // 直接触发查询
      this.handleQuery();
    },

    // 处理自定义时间变化
    handleCustomTimeChange() {
      if (this.queryForm.timeRange && this.queryForm.timeRange.length === 2) {
        this.updateTimeFilter();

        // 直接触发查询
        this.handleQuery();
      }
    },

    // 处理字段过滤器变化
    handleFieldFilterChange() {
      // 构建查询并触发查询
      this.buildQuery();
      this.handleQuery();
    },

    // 处理字段值输入
    handleFieldValueInput() {
      // 仅构建查询，不触发查询（等待用户按回车键）
      this.buildQuery();
    },

    // 处理条数限制变化
    handleLimitChange(value) {
      // 验证输入值是否有效
      let num = parseInt(value);
      if (isNaN(num) || num < 1) {
        // 如果输入无效，重置为默认值 50
        this.limitCount = 50;
        this.$message.warning('返回条数必须是大于 0 的数字，已重置为 50');
      } else if (num > 1000) {
        // 限制最大值为 1000
        this.limitCount = 1000;
        this.$message.warning('返回条数最大为 1000，已自动调整');
      }

      // 当条数限制变化时，直接触发查询
      this.handleQuery();
    },
    handleQuery() {
      this.fetchData();
    },
    addFieldFilter() {
      this.fieldFilters.push({
        field: '',
        operator: '=',
        value: ''
      });
      // 不自动触发查询，因为新添加的过滤器还没有设置字段和值
    },
    removeFieldFilter(index) {
      this.fieldFilters.splice(index, 1);
      // 删除过滤器后自动应用并触发查询
      this.buildQuery();
      this.handleQuery();
    },
    applyFilterConditions() {
      this.buildQuery();
      this.handleQuery();
      this.filterConditionVisible = false;
    },
    resetFilterConditions() {
      // 重置过滤条件，保留默认过滤条件
      this.initializeFieldFilters();
      this.buildQuery();
      this.handleQuery();
    },
    applyFieldSelection() {
      // 根据选中的字段更新可见列
      this.visibleColumns = this.settings.columns.filter(column =>
        this.selectedFields.includes(column.prop)
      );
      this.fieldSelectorVisible = false;
    },
    selectAllFields() {
      // 选中所有字段
      this.selectedFields = this.availableFields.map(field => field.prop);
    },
    deselectAllFields() {
      // 取消选中所有字段
      this.selectedFields = [];
    },
    applyTimeFilter() {
      this.updateTimeFilter();
      this.handleQuery();
      this.timeFilterVisible = false;
    },
    fetchData() {
      this.loading = true;

      // 构建查询参数
      const query = this.logsqlQuery;
      
      // 检查查询是否为空
      if (!query || query.trim() === '') {
        console.log('查询字符串为空，跳过查询');
        this.loading = false;
        this.tableData = [];
        return;
      }
      
      const limit = this.limitCount; // 使用限制条数
      const instance = this.instance; // 使用传入的实例参数

      // 调用API方法
      queryLogs(query, null, null, limit, null, null, null, instance)
        .then(response => {
          if (response.code === 200) {
            const data = response.data;
            // 检查数据是否为空
            if (!data) {
              console.log('查询结果为空');
              this.tableData = [];
              return;
            }
            // 解析返回的日志数据
            this.parseLogsData(data);
          } else {
            this.$message.error(response.msg || '查询失败');
            this.tableData = [];
          }
        })
        .catch(error => {
          console.error('查询日志失败:', error);
          this.$message.error('查询日志失败: ' + (error.message || error));
          this.tableData = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    parseLogsData(data) {
      try {
        // 先检查数据是否为空
        if (!data) {
          console.log('日志数据为空');
          this.tableData = [];
          return;
        }

        // 尝试解析返回的日志数据
        // VictoriaLogs返回的是每行一个JSON对象的格式
        const lines = data.trim().split('\n');
        const logs = lines.map(line => {
          try {
            return JSON.parse(line);
          } catch (e) {
            console.error('解析日志行失败:', e, line);
            return null;
          }
        }).filter(log => log !== null);

        this.tableData = logs;

      } catch (e) {
        console.error('解析日志数据失败:', e);
        this.$message.error('解析日志数据失败');
        this.tableData = [];
      }
    },
    // 移除分页相关的方法
    formatStream(stream) {
      try {
        // 尝试解析流字段，它可能是JSON字符串
        const streamObj = typeof stream === 'string' ? JSON.parse(stream) : stream;
        if (typeof streamObj === 'object' && streamObj !== null) {
          return Object.entries(streamObj)
            .map(([key, value]) => `${key}=${value}`)
            .join(', ');
        }
        return stream;
      } catch (e) {
        return stream;
      }
    },
    showLogDetail(log) {
      this.currentLogDetail = log;

      // 提取额外字段（排除_time, _msg, _stream）
      this.additionalFields = {};

      // 递归展平对象，处理嵌套属性
      const flattenObject = (obj, prefix = '') => {
        Object.entries(obj).forEach(([key, value]) => {
          const newKey = prefix ? `${prefix}.${key}` : key;

          // 排除基本字段
          if (['_time', '_msg', '_stream'].includes(newKey)) {
            return;
          }

          // 如果是对象且不是数组，递归展平
          if (value && typeof value === 'object' && !Array.isArray(value)) {
            flattenObject(value, newKey);
          } else {
            this.additionalFields[newKey] = value;
          }
        });
      };

      flattenObject(log);
      this.logDetailVisible = true;
    },
    // 获取嵌套属性值
    getNestedValue(obj, path) {
      if (!obj || !path) return '';

      // 先检查属性是否直接存在，处理包含点号的属性名
      if (obj.hasOwnProperty(path)) {
        return obj[path];
      }

      // 处理嵌套路径，如 'message.user'
      const parts = path.split('.');
      let value = obj;

      for (const part of parts) {
        if (value === undefined || value === null) {
          return '';
        }

        // 处理数组索引，如 'objects[0].name'
        const match = part.match(/^([^\[]+)\[(\d+)\]$/);
        if (match) {
          const [, arrayName, index] = match;
          value = value[arrayName];
          if (Array.isArray(value) && value.length > index) {
            value = value[parseInt(index)];
          } else {
            return '';
          }
        } else {
          // 尝试直接访问属性
          value = value[part];
        }
      }

      return value !== undefined && value !== null ? value : '';
    },
    initializeFieldFilters() {
      // 初始化字段过滤条件
      this.fieldFilters = [];
      
      // 如果有默认过滤条件，则添加到字段过滤器中
      if (this.settings.defaultFilters && this.settings.defaultFilters.length > 0) {
        this.fieldFilters = [...this.settings.defaultFilters];
      }
      
      // 确保至少有一个空的过滤条件供用户添加
      if (this.fieldFilters.length === 0) {
        this.fieldFilters.push({
          field: '',
          operator: '=',
          value: ''
        });
      }
    },
    formatCellValue(row, column) {
      // 获取原始值
      const rawValue = this.getNestedValue(row, column.prop);
      
      // 如果列配置中有 formatter 函数，则使用它进行格式化
      if (column.formatter && typeof column.formatter === 'function') {
        try {
          return column.formatter(row, column, rawValue);
        } catch (error) {
          console.error('格式化函数执行失败:', error);
          return rawValue;
        }
      }
      
      // 否则返回原始值
      return rawValue;
    },
    applyExternalTimeRange(timeRangeConfig) {
      if (!timeRangeConfig) return
      
      console.log('应用外部时间范围配置:', timeRangeConfig)
      
      // 如果是快速时间选择
      if (timeRangeConfig.quickTime) {
        this.queryForm.quickTime = timeRangeConfig.quickTime
        this.queryForm.timeRange = []
        console.log('设置快速时间选择:', timeRangeConfig.quickTime)
      }
      // 如果是自定义时间范围
      else if (timeRangeConfig.startTime && timeRangeConfig.endTime) {
        this.queryForm.quickTime = 'custom'
        this.queryForm.timeRange = [timeRangeConfig.startTime, timeRangeConfig.endTime]
        console.log('设置自定义时间范围:', this.queryForm.timeRange)
      }
      // 如果是原始格式（兼容性）
      else if (timeRangeConfig.type === 'quick' && timeRangeConfig.value) {
        this.queryForm.quickTime = timeRangeConfig.value
        this.queryForm.timeRange = []
        console.log('设置原始格式快速时间:', timeRangeConfig.value)
      }
      else if (timeRangeConfig.type === 'custom' && timeRangeConfig.range) {
        this.queryForm.quickTime = 'custom'
        this.queryForm.timeRange = timeRangeConfig.range
        console.log('设置原始格式自定义时间:', timeRangeConfig.range)
      }
      
      // 更新时间过滤器
      this.updateTimeFilter()
    },
    
    // 获取当前时间配置（供父组件使用）
    getCurrentTimeConfig() {
      if (this.queryForm.quickTime === 'custom' && this.queryForm.timeRange && this.queryForm.timeRange.length === 2) {
        return {
          type: 'custom',
          startTime: this.queryForm.timeRange[0],
          endTime: this.queryForm.timeRange[1],
          range: this.queryForm.timeRange
        }
      } else {
        return {
          type: 'quick',
          quickTime: this.queryForm.quickTime,
          value: this.queryForm.quickTime
        }
      }
    }
  }
}
</script>

<style scoped>
.log-query-component {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

/* 标题和配置区域 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.title-area {
  display: flex;
  align-items: center;
}

.title-container {
  display: flex;
  flex-direction: column;
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.section-subtitle {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.config-area {
  display: flex;
  align-items: center;
}

.field-info-link {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: #409EFF;
  text-decoration: none;
  transition: color 0.3s;
  margin-left: 15px;
}

.field-info-link:hover {
  color: #66b1ff;
}

.field-info-link i {
  margin-right: 4px;
  font-size: 14px;
}

.field-info {
  padding: 15px;
}

.field-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
}

.field-info-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 15px;
}

.field-info-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.field-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 5px;
  border-bottom: 1px dashed #ebeef5;
}

.field-info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.field-info-label {
  font-weight: 500;
  color: #303133;
}

.field-info-prop {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.field-selector {
  padding: 15px;
}

.field-selector h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
}

.field-selector-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 15px;
}

.field-checkbox-group {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.field-checkbox-item {
  display: block;
  margin-bottom: 8px;
  padding: 5px;
  border-bottom: 1px dashed #ebeef5;
}

.field-checkbox-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.field-label {
  font-weight: 500;
  color: #303133;
}

.field-prop {
  font-size: 11px;
  color: #909399;
  margin-left: 5px;
}

.field-selector-footer {
  margin-top: 15px;
  text-align: right;
  display: flex;
  justify-content: space-between;
}

.field-selector-footer button {
  margin-left: 8px;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.time-filter {
  margin-right: 15px;
  flex-shrink: 0;
}

.time-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
  user-select: none;
  height: 32px;
  box-sizing: border-box;
  min-width: 150px;
}

.time-trigger:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
}

.time-trigger:hover i,
.time-trigger:hover span {
  color: #409EFF;
}

.time-trigger i {
  font-size: 14px;
  transition: color 0.3s;
}

.time-trigger i:first-child {
  margin-right: 5px;
  color: #606266;
}

.time-trigger i:last-child {
  margin-left: 5px;
  font-size: 12px;
  color: #909399;
}

.time-trigger span {
  font-size: 14px;
  color: #606266;
  transition: color 0.3s;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time-filter-panel {
  padding: 10px;
}

.filter-panel-footer {
  margin-top: 15px;
  text-align: right;
}

.search-area {
  margin-right: 15px;
  flex-shrink: 0;
}

.filter-condition-area {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: space-between;
}

.filter-buttons {
  flex-shrink: 0;
  margin-right: 15px;
}

.filter-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
  user-select: none;
  height: 32px;
  box-sizing: border-box;
}

.filter-trigger:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
}

.filter-trigger:hover i,
.filter-trigger:hover span {
  color: #409EFF;
}

.filter-trigger i {
  font-size: 14px;
  transition: color 0.3s;
}

.filter-trigger i:first-child {
  margin-right: 5px;
  color: #606266;
}

.filter-trigger i:last-child {
  margin-left: 5px;
  font-size: 12px;
  color: #909399;
}

.filter-trigger span {
  font-size: 14px;
  color: #606266;
  transition: color 0.3s;
}

.logql-container {
  flex-grow: 1;
  width: 100%;
}

.logql-limit-container {
  display: flex;
  align-items: center;
}

.logql-input {
  flex-grow: 1;
  width: calc(100% - 120px);
}

.limit-input {
  width: 60px;
  margin-left: 10px;
  text-align: center;
}

.limit-label {
  margin-left: 5px;
  color: #606266;
  font-size: 14px;
}

.filter-condition-builder {
  padding: 15px;
}

.filter-condition-builder h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #303133;
}

.filter-section-item {
  margin-bottom: 15px;
}

.filter-section-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #606266;
}

.field-filters-container {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 10px;
  padding: 5px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.field-filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 5px;
  border-bottom: 1px dashed #ebeef5;
}

.field-filter-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.filter-actions {
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
}

.custom-time-range {
  margin-top: 10px;
}

.filter-condition-footer {
  margin-top: 15px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
}

.filter-condition-footer button {
  margin-left: 8px;
}

/* 表格区域 */
.table-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.el-table {
  margin-top: 0;
}

/* 日志详情样式 */
.log-detail pre {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }

  .search-area {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .logql-input {
    margin-left: 0;
    margin-top: 15px;
    width: 100%;
  }
}
</style>
