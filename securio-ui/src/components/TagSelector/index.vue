<template>
  <div class="tag-selector">
    <!-- 简单模式：输入框 + 帮助 -->
    <div v-if="mode === 'simple'" class="simple-mode">
      <el-input
        v-model="inputValue"
        :placeholder="placeholder"
        clearable
        @input="handleInput"
        @clear="handleClear"
      >
        <template slot="prepend">
          <el-select v-model="searchType" style="width: 80px">
            <el-option label="包含" value="contains" />
            <el-option label="精确" value="exact" />
          </el-select>
        </template>
        <template slot="append">
          <el-button icon="el-icon-question" @click="showHelp = !showHelp" size="mini"></el-button>
        </template>
      </el-input>

      <!-- 帮助面板 -->
      <div v-if="showHelp" class="help-panel">
        <div class="help-content">
          <h4>标签搜索说明</h4>
          <p><strong>格式：</strong> 标签名:标签值</p>

          <div class="quick-tags">
            <h5>快速选择：</h5>
            <div class="tag-categories">
              <div v-for="category in tagCategories" :key="category.name" class="tag-category">
                <span class="category-name">{{ category.name }}：</span>
                <el-tag
                  v-for="tag in category.tags"
                  :key="tag.value"
                  size="mini"
                  :type="tag.type"
                  class="quick-tag"
                  @click="selectQuickTag(tag.value)"
                >
                  {{ tag.label }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="search-modes">
            <h5>搜索模式：</h5>
            <p><strong>包含：</strong> 标签值包含输入内容</p>
            <p><strong>精确：</strong> 标签值完全匹配</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 高级模式：可视化选择器 -->
    <div v-else-if="mode === 'advanced'" class="advanced-mode">
      <el-popover
        placement="bottom-start"
        width="400"
        trigger="click"
        v-model="showAdvanced"
      >
        <div class="advanced-selector">
          <div class="selector-header">
            <h4>标签选择器</h4>
            <el-button size="mini" type="text" @click="clearAll">清空</el-button>
          </div>

          <div class="selector-body">
            <!-- 标签分类 -->
            <el-tabs v-model="activeTab" type="card" size="small">
              <el-tab-pane
                v-for="category in tagCategories"
                :key="category.name"
                :label="category.name"
                :name="category.name"
              >
                <div class="tag-options">
                  <div
                    v-for="tag in category.tags"
                    :key="tag.value"
                    class="tag-option"
                    :class="{ active: selectedTags.includes(tag.value) }"
                    @click="toggleTag(tag.value)"
                  >
                    <el-tag :type="tag.type" size="small">{{ tag.label }}</el-tag>
                    <span class="tag-description">{{ tag.description }}</span>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <div class="selector-footer">
            <div class="selected-tags">
              <span class="label">已选择：</span>
              <el-tag
                v-for="tag in selectedTags"
                :key="tag"
                size="mini"
                closable
                @close="removeTag(tag)"
              >
                {{ tag }}
              </el-tag>
            </div>
            <div class="actions">
              <el-button size="mini" @click="showAdvanced = false">取消</el-button>
              <el-button size="mini" type="primary" @click="confirmSelection">确定</el-button>
            </div>
          </div>
        </div>

        <el-input
          slot="reference"
          v-model="displayValue"
          :placeholder="placeholder"
          readonly
          clearable
          @clear="handleClear"
        >
          <template slot="append">
            <el-button icon="el-icon-arrow-down" size="mini"></el-button>
          </template>
        </el-input>
      </el-popover>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TagSelector',
  props: {
    value: {
      type: String,
      default: ''
    },
    mode: {
      type: String,
      default: 'simple', // simple | advanced
      validator: value => ['simple', 'advanced'].includes(value)
    },
    placeholder: {
      type: String,
      default: '请输入或选择标签'
    }
  },
  data() {
    return {
      inputValue: '',
      searchType: 'contains',
      showHelp: false,
      showAdvanced: false,
      activeTab: '环境',
      selectedTags: [],
      tagCategories: [
        {
          name: '环境',
          tags: [
            { label: 'production', value: 'environment:production', type: 'danger', description: '生产环境' },
            { label: 'staging', value: 'environment:staging', type: 'warning', description: '预发环境' },
            { label: 'test', value: 'environment:test', type: 'info', description: '测试环境' },
            { label: 'development', value: 'environment:development', type: 'success', description: '开发环境' }
          ]
        },
        {
          name: '服务',
          tags: [
            { label: 'user-api', value: 'service:user-api', type: 'primary', description: '用户API服务' },
            { label: 'order-service', value: 'service:order-service', type: 'primary', description: '订单服务' },
            { label: 'payment-gateway', value: 'service:payment-gateway', type: 'primary', description: '支付网关' },
            { label: 'notification-service', value: 'service:notification-service', type: 'primary', description: '通知服务' }
          ]
        },
        {
          name: '团队',
          tags: [
            { label: 'backend', value: 'team:backend', type: 'success', description: '后端团队' },
            { label: 'frontend', value: 'team:frontend', type: 'success', description: '前端团队' },
            { label: 'devops', value: 'team:devops', type: 'success', description: '运维团队' },
            { label: 'security', value: 'team:security', type: 'success', description: '安全团队' }
          ]
        },
        {
          name: '监控',
          tags: [
            { label: 'grafana', value: 'monitor_system:grafana', type: 'info', description: 'Grafana监控' },
            { label: 'prometheus', value: 'monitor_system:prometheus', type: 'info', description: 'Prometheus监控' },
            { label: 'zabbix', value: 'monitor_system:zabbix', type: 'info', description: 'Zabbix监控' },
            { label: 'n9e', value: 'monitor_system:n9e', type: 'info', description: '夜莺监控' }
          ]
        }
      ]
    }
  },
  computed: {
    displayValue() {
      if (this.mode === 'advanced') {
        if (this.selectedTags.length === 0) {
          return ''
        } else if (this.selectedTags.length === 1) {
          return this.selectedTags[0]
        } else {
          return `${this.selectedTags.length} 个标签已选择`
        }
      }
      return this.inputValue
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (this.mode === 'simple') {
          this.inputValue = newVal
        } else {
          // 支持多种分隔符： AND, OR, 逗号, 分号
          if (newVal) {
            this.selectedTags = newVal.split(/\s*(?:AND|OR|,|;)\s*/).filter(tag => tag.trim())
          } else {
            this.selectedTags = []
          }
        }
      }
    }
  },
  methods: {
    handleInput(value) {
      this.$emit('input', value)
      this.$emit('change', value, this.searchType)
    },

    handleClear() {
      this.inputValue = ''
      this.selectedTags = []
      this.$emit('input', '')
      this.$emit('change', '', this.searchType)
    },

    selectQuickTag(value) {
      if (this.mode === 'simple') {
        this.inputValue = value
        this.handleInput(value)
        this.showHelp = false
      }
    },

    toggleTag(value) {
      const index = this.selectedTags.indexOf(value)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      } else {
        this.selectedTags.push(value)
      }
    },

    removeTag(value) {
      const index = this.selectedTags.indexOf(value)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      }
    },

    clearAll() {
      this.selectedTags = []
    },

    confirmSelection() {
      // 支持多种组合方式
      const value = this.selectedTags.join(' AND ')
      this.$emit('input', value)
      this.$emit('change', value, this.searchType)
      this.showAdvanced = false
      this.$message.success(`已选择 ${this.selectedTags.length} 个标签`)
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-selector {
  .simple-mode {
    position: relative;

    .help-panel {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      z-index: 1000;
      background: white;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      margin-top: 2px;

      .help-content {
        padding: 15px;

        h4 {
          margin: 0 0 10px 0;
          color: #303133;
          font-size: 14px;
        }

        h5 {
          margin: 10px 0 5px 0;
          color: #606266;
          font-size: 12px;
        }

        p {
          margin: 5px 0;
          color: #909399;
          font-size: 12px;
          line-height: 1.4;
        }

        .quick-tags {
          margin: 15px 0;

          .tag-categories {
            .tag-category {
              margin-bottom: 8px;
              display: flex;
              align-items: center;
              flex-wrap: wrap;

              .category-name {
                font-size: 12px;
                color: #606266;
                margin-right: 8px;
                min-width: 50px;
              }

              .quick-tag {
                margin: 2px 4px 2px 0;
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                  transform: scale(1.05);
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }
              }
            }
          }
        }

        .search-modes {
          margin-top: 15px;
          padding-top: 10px;
          border-top: 1px solid #ebeef5;
        }
      }
    }
  }

  .advanced-mode {
    .advanced-selector {
      .selector-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h4 {
          margin: 0;
          color: #303133;
          font-size: 14px;
        }
      }

      .selector-body {
        .tag-options {
          max-height: 200px;
          overflow-y: auto;

          .tag-option {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              background-color: #f5f7fa;
            }

            &.active {
              background-color: #ecf5ff;
              border: 1px solid #b3d8ff;
            }

            .tag-description {
              margin-left: 10px;
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }

      .selector-footer {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #ebeef5;

        .selected-tags {
          margin-bottom: 10px;

          .label {
            font-size: 12px;
            color: #606266;
            margin-right: 8px;
          }

          .el-tag {
            margin-right: 5px;
            margin-bottom: 5px;
          }
        }

        .actions {
          text-align: right;

          .el-button {
            margin-left: 8px;
          }
        }
      }
    }
  }
}
</style>
