<template>
  <div ref="chartRef" style="width: 100%; height: 300px"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    chartData: {
      type: Object,
      required: true
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      handler: 'update<PERSON><PERSON>',
      deep: true
    },
    options: {
      handler: 'updateChart',
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartRef)
      this.updateChart()
    },
    updateChart() {
      if (!this.chart) return

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: this.chartData.labels
        },
        series: [{
          name: '告警分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: this.chartData.labels.map((label, index) => ({
            value: this.chartData.datasets[0].data[index],
            name: label,
            itemStyle: {
              color: this.chartData.datasets[0].backgroundColor[index]
            }
          }))
        }],
        ...this.options
      }

      this.chart.setOption(option)
    }
  }
}
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 300px;
}
</style> 