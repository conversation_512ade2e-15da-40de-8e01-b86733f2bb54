<template>
  <div ref="chartRef" style="width: 100%; height: 300px"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'Trend<PERSON><PERSON>',
  props: {
    chartData: {
      type: Object,
      required: true
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      handler: 'update<PERSON><PERSON>',
      deep: true
    },
    options: {
      handler: 'updateChart',
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartRef)
      this.updateChart()
    },
    updateChart() {
      if (!this.chart) return

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.chartData.labels,
          boundaryGap: false
        },
        yAxis: {
          type: 'value'
        },
        series: this.chartData.datasets.map(dataset => ({
          name: dataset.label,
          type: 'line',
          data: dataset.data,
          smooth: true,
          areaStyle: dataset.fill ? {
            opacity: 0.1
          } : undefined,
          itemStyle: {
            color: dataset.borderColor
          },
          lineStyle: {
            color: dataset.borderColor
          }
        })),
        ...this.options
      }

      this.chart.setOption(option)
    }
  }
}
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 300px;
}
</style> 