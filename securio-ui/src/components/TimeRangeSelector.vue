<template>
  <div class="time-range-selector">
    <!-- 时间范围类型选择 -->
    <div class="range-type-selector">
      <el-radio-group v-model="rangeType" @change="handleRangeTypeChange" size="small">
        <el-radio-button label="quick">快速选择</el-radio-button>
        <el-radio-button label="precise">精确时间</el-radio-button>
      </el-radio-group>
    </div>
    
    <!-- 快速时间范围选择 -->
    <div v-if="rangeType === 'quick'" class="quick-time-selector">
      <el-select v-model="quickRange" placeholder="选择时间范围" @change="handleQuickRangeChange" style="width: 200px;">
        <el-option label="最近5分钟" value="5m" />
        <el-option label="最近10分钟" value="10m" />
        <el-option label="最近30分钟" value="30m" />
        <el-option label="最近1小时" value="1h" />
        <el-option label="最近1天" value="1d" />
      </el-select>
    </div>
    
    <!-- 精确时间选择器 -->
    <div v-if="rangeType === 'precise'" class="precise-time-selector">
      <div class="time-selector">
        <label>{{ label }}：</label>
        <el-date-picker
          v-model="selectedTime"
          type="datetime"
          placeholder="选择时间"
          size="small"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleTimeChange"
          style="width: 200px; margin-right: 15px;">
        </el-date-picker>
        <span v-if="timeRange.start && timeRange.end" class="time-range-text">
          范围：{{ timeRange.start }} ~ {{ timeRange.end }}
        </span>
      </div>
      
      <!-- 时间线组件 -->
      <div v-if="showTimeline" class="timeline-container">
        <div class="timeline-wrapper">
          <!-- 时间线背景 -->
          <div class="timeline-background">
            <!-- 时间刻度标记 - 每5分钟一个 -->
            <div v-for="i in 25" :key="i" class="timeline-tick" :style="{ left: `${(i-1)*4.16666}%` }">
              <div class="tick-mark" :class="{ 'tick-major': i % 3 === 1 }"></div>
              <div v-if="i % 3 === 1" class="tick-label">{{ formatTimelineLabel(i) }}</div>
            </div>
          </div>
          
          <!-- 中心时间线 -->
          <div class="timeline-center-line"></div>
          
          <!-- 可拖动的时间范围框 -->
          <div 
            class="timeline-range-box"
            ref="timelineBox"
            :style="timelineBoxStyle"
            @mousedown="startDrag">
            <!-- 左右调整手柄 -->
            <div class="range-handle range-handle-left" @mousedown.stop="e => startResize('left', e)"></div>
            <div class="range-handle range-handle-right" @mousedown.stop="e => startResize('right', e)"></div>
            
            <!-- 时间范围显示 -->
            <div class="range-time-display">
              {{ formatDuration(timeRange.duration) }}
            </div>
          </div>
          
          <!-- 时间线底部标签 -->
          <div class="timeline-labels">
            <span class="timeline-label timeline-label-start">{{ formatTimelineTime(timelineStartTime) }}</span>
            <span class="timeline-label timeline-label-center">{{ selectedTime }}</span>
            <span class="timeline-label timeline-label-end">{{ formatTimelineTime(timelineEndTime) }}</span>
          </div>
          
          <!-- 滑块选择的时间 -->
          <div v-if="sliderCenterTime && sliderCenterTime !== selectedTime" class="slider-time" :style="sliderTimeStyle">
            <span>选择时间: {{ sliderCenterLabel }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimeRangeSelector',
  props: {
    // 标签文本
    label: {
      type: String,
      default: '时间范围'
    },
    // 是否显示时间线
    showTimeline: {
      type: Boolean,
      default: true
    },
    // 默认时间，用于精确时间模式
    defaultTime: {
      type: String,
      default: ''
    },
    // 默认时间范围持续时长（分钟），用于精确时间模式
    defaultDuration: {
      type: Number,
      default: 60
    },
    // 拖动灵敏度
    dragSensitivity: {
      type: Number,
      default: 0.5
    },
    // 新增：默认模式 ('precise' 或 'quick')
    defaultMode: {
      type: String,
      default: 'precise',
      validator: (value) => ['precise', 'quick'].includes(value)
    },
    // 新增：默认的快速选择时间范围
    defaultQuickRange: {
      type: String,
      default: '1h'
    }
  },
  data() {
    return {
      selectedTime: null,     // 用户选择的中心时间点（固定的时间线基准）
      sliderCenterTime: null, // 滑块中心时间点（可拖拽变化）
      timeRange: {
        start: null,
        end: null,
        duration: 4 // 默认前后2分钟，总共4分钟
      },
      
      // 拖拽相关
      isDragging: false,
      isResizing: false,
      resizeType: null,
      dragStartX: 0,
      dragInitialPercent: 0,
      timelineWidth: 0,
      sliderOffsetPercent: 0, // 滑块相对于时间线中心的偏移百分比
      
      // 防抖相关
      debounceTimer: null,
      
      // 时间范围类型
      rangeType: 'precise',
      quickRange: null,
      // 可选的快速时间范围
      quickTimeRanges: [
        { label: '最近 15 分钟', value: '15m' },
        { label: '最近 30 分钟', value: '30m' },
        { label: '最近 1 小时', value: '1h' },
        { label: '最近 6 小时', value: '6h' },
        { label: '最近 12 小时', value: '12h' },
        { label: '最近 1 天', value: '1d' },
        { label: '最近 7 天', value: '7d' }
      ],
      quickTimeOptions: []
    }
  },
  created() {
    // 初始化时间
    const now = new Date();
    this.selectedTime = this.defaultTime || this.formatDateTime(now);
    this.sliderCenterTime = this.selectedTime;
    this.timeRange.duration = this.defaultDuration;
    this.calculateTimeRange(this.sliderCenterTime);
  },
  mounted() {
    this.initDefaultTime();
    if (this.showTimeline) {
      window.addEventListener('resize', this.handleResize);
    }
  },
  beforeDestroy() {
    if (this.showTimeline) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  computed: {
    // 时间线样式计算
    timelineBoxStyle() {
      if (!this.selectedTime) return {}
      
      const totalMinutes = 120 // 时间线总跨度(2小时)
      const rangeMinutes = this.timeRange.duration
      
      // 计算宽度百分比
      const boxWidth = (rangeMinutes / totalMinutes) * 100
      
      // 计算左侧位置
      const boxLeft = 50 - (boxWidth / 2) + this.sliderOffsetPercent
      
      return {
        left: boxLeft + '%',
        width: boxWidth + '%'
      }
    },
    // 时间线起始时间（固定基于 selectedTime，不随滑块变化）
    timelineStartTime() {
      if (!this.selectedTime) return null
      const center = new Date(this.selectedTime)
      const start = new Date(center.getTime() - 60 * 60 * 1000) // 前1小时
      return start
    },
    // 时间线结束时间（固定基于 selectedTime，不随滑块变化）
    timelineEndTime() {
      if (!this.selectedTime) return null
      const center = new Date(this.selectedTime)
      const end = new Date(center.getTime() + 60 * 60 * 1000) // 后1小时
      return end
    },
    // 滑块中心标签
    sliderCenterLabel() {
      if (!this.sliderCenterTime) return this.selectedTime
      return this.formatDateTime(new Date(this.sliderCenterTime))
    },
    
    // 滑块选择时间框的样式
    sliderTimeStyle() {
      if (!this.selectedTime || !this.sliderCenterTime || this.sliderCenterTime === this.selectedTime) {
        return { display: 'none' }
      }
      
      const totalMinutes = 120 // 时间线总跨度(2小时)
      const rangeMinutes = this.timeRange.duration
      
      // 计算滑块框的宽度百分比
      const boxWidth = (rangeMinutes / totalMinutes) * 100
      
      // 计算滑块框的左侧位置
      const boxLeft = 50 - (boxWidth / 2) + this.sliderOffsetPercent
      
      // 滑块选择时间框应该在滑块框的中心位置
      let sliderTimeLeft = boxLeft + (boxWidth / 2)
      
      // 确保不超出时间线范围（留出一些边距）
      sliderTimeLeft = Math.max(5, Math.min(95, sliderTimeLeft))
      
      return {
        left: sliderTimeLeft + '%',
        transform: 'translateX(-50%)',
        display: 'block'
      }
    }
  },
  methods: {
    // 格式化时间线上的标签（固定基于 selectedTime，不随滑块变化）
    formatTimelineLabel(index) {
      if (!this.selectedTime) return '';
      
      const offsetMinutes = (index - 1) * 5 - 60;
      const center = new Date(this.selectedTime); // 使用固定的 selectedTime
      const time = new Date(center.getTime() + offsetMinutes * 60 * 1000);
      
      return time.getHours().toString().padStart(2, '0') + ':' + 
             time.getMinutes().toString().padStart(2, '0');
    },
    
    // 格式化时间线起始/结束时间显示
    formatTimelineTime(time) {
      if (!time) return '';
      
      return time.getHours().toString().padStart(2, '0') + ':' + 
             time.getMinutes().toString().padStart(2, '0') + ':' +
             time.getSeconds().toString().padStart(2, '0');
    },
    
    // 处理时间选择变化
    handleTimeChange(time) {
      if (time) {
        this.selectedTime = time; // 更新固定的时间线基准
        this.sliderCenterTime = time; // 重置滑块位置
        this.sliderOffsetPercent = 0; // 重置偏移
        this.timeRange.duration = this.defaultDuration;
        this.calculateTimeRange(time);
        
        // 触发事件
        this.emitTimeChange();
      } else {
        this.timeRange = { start: null, end: null, duration: this.defaultDuration };
        this.sliderCenterTime = null;
        this.$emit('time-change', null);
      }
    },
    
    // 计算时间范围（基于滑块中心时间）
    calculateTimeRange(centerTime) {
      if (!centerTime) return;
      
      const center = new Date(centerTime);
      const halfDuration = this.timeRange.duration / 2;
      
      // 确保时间计算精确到秒
      const halfDurationMs = Math.round(halfDuration * 60 * 1000);
      
      const start = new Date(center.getTime() - halfDurationMs);
      const end = new Date(center.getTime() + halfDurationMs);
      
      this.timeRange.start = this.formatDateTime(start);
      this.timeRange.end = this.formatDateTime(end);
    },
    
    // 格式化日期时间
    formatDateTime(date) {
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + ' ' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0') + ':' +
        String(date.getSeconds()).padStart(2, '0');
    },
    
    // 格式化持续时间显示
    formatDuration(minutes) {
      if (minutes < 1) {
        // 小于1分钟时，只显示秒数
        const seconds = Math.round(minutes * 60);
        return seconds + '秒';
      } else if (minutes < 60) {
        // 小于60分钟时，显示分钟和秒数
        const wholeMinutes = Math.floor(minutes);
        const remainingSeconds = Math.round((minutes - wholeMinutes) * 60);
        
        if (remainingSeconds === 0) {
          return wholeMinutes + '分钟';
        } else {
          return wholeMinutes + '分钟' + remainingSeconds + '秒';
        }
      } else {
        // 大于等于60分钟时，显示小时、分钟和秒数
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = Math.floor(minutes % 60);
        const remainingSeconds = Math.round(((minutes % 60) - remainingMinutes) * 60);
        
        let result = hours + '小时';
        if (remainingMinutes > 0) {
          result += remainingMinutes + '分钟';
        }
        if (remainingSeconds > 0) {
          result += remainingSeconds + '秒';
        }
        return result;
      }
    },
    
    // 开始拖拽
    startDrag(event) {
      if (this.isResizing) return;
      
      this.isDragging = true;
      this.dragStartX = event.clientX;
      
      const timelineWidth = event.currentTarget.parentElement.offsetWidth;
      this.timelineWidth = timelineWidth;
      this.dragInitialPercent = this.sliderOffsetPercent;
      
      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('mouseup', this.stopDrag);
      event.preventDefault();
    },
    
    // 拖拽过程中
    onDrag(event) {
      if (!this.isDragging) return;
      
      const deltaX = event.clientX - this.dragStartX;
      
      // 重新设计灵敏度计算：直接将像素转换为百分比偏移
      // 使用更小的基准值，让 dragSensitivity 的变化更加明显
      const pixelToPercentRatio = 0.2; // 基础转换比例：1像素 = 0.2%
      const deltaPercent = deltaX * pixelToPercentRatio * this.dragSensitivity;
      
      let newOffsetPercent = this.dragInitialPercent + deltaPercent;
      
      const boxWidth = parseFloat(this.timelineBoxStyle.width);
      const maxOffset = (100 - boxWidth) / 2;
      newOffsetPercent = Math.max(-maxOffset, Math.min(maxOffset, newOffsetPercent));
      
      this.sliderOffsetPercent = newOffsetPercent;
      
      // 更新滑块中心时间，但不改变时间线坐标基准
      const timeOffsetMinutes = (newOffsetPercent / 100) * 120;
      this.updateSliderCenterTime(timeOffsetMinutes);
    },
    
    // 停止拖拽
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
      
      this.scheduleEmitTimeChange();
    },
    
    // 更新滑块中心时间（不影响时间线坐标基准）
    updateSliderCenterTime(offsetMinutes) {
      const baseCenter = new Date(this.selectedTime); // 基于固定的 selectedTime
      // 将偏移量转换为毫秒，确保精确到秒
      const offsetMilliseconds = Math.round(offsetMinutes * 60 * 1000);
      const sliderCenter = new Date(baseCenter.getTime() + offsetMilliseconds);
      this.sliderCenterTime = this.formatDateTime(sliderCenter);
      this.calculateTimeRange(this.sliderCenterTime); // 只更新时间范围，不触发事件
    },
    
    // 开始调整大小
    startResize(type, event) {
      this.isResizing = true;
      this.resizeType = type;
      this.dragStartX = event.clientX;
      
      const timelineWidth = event.currentTarget.parentElement.parentElement.offsetWidth;
      this.timelineWidth = timelineWidth;
      this.initialBoxWidth = this.timeRange.duration;
      
      document.addEventListener('mousemove', this.onResize);
      document.addEventListener('mouseup', this.stopResize);
      event.preventDefault();
    },
    
    // 调整大小过程中
    onResize(event) {
      if (!this.isResizing) return;
      
      const deltaX = event.clientX - this.dragStartX;
      
      // 使用相同的简化灵敏度计算方式
      const pixelToPercentRatio = 0.2; // 基础转换比例：1像素 = 0.2%
      const deltaPercent = deltaX * pixelToPercentRatio * this.dragSensitivity;
      
      let newDuration = this.initialBoxWidth;
      const durationChange = (deltaPercent / 100) * 120;
      
      if (this.resizeType === 'left') {
        newDuration = this.initialBoxWidth - durationChange;
      } else if (this.resizeType === 'right') {
        newDuration = this.initialBoxWidth + durationChange;
      }
      
      // 限制持续时间范围（最小30秒，最大60分钟），精确到秒级
      newDuration = Math.max(0.5, Math.min(60, Math.round(newDuration * 60) / 60));
      
      this.timeRange.duration = newDuration;
      this.calculateTimeRange(this.sliderCenterTime);
    },
    
    // 停止调整大小
    stopResize() {
      this.isResizing = false;
      this.resizeType = null;
      document.removeEventListener('mousemove', this.onResize);
      document.removeEventListener('mouseup', this.stopResize);
      
      this.scheduleEmitTimeChange();
    },
    
    // 触发时间变化事件
    emitTimeChange() {
      let timeData = null;
      if (this.rangeType === 'quick') {
        timeData = {
          rangeType: 'quick',
          quickTimeRange: this.quickRange
        };
      } else if (this.rangeType === 'precise') {
        timeData = {
          selectedTime: this.selectedTime,
          sliderCenterTime: this.sliderCenterTime,
          timeRange: { ...this.timeRange },
          sliderOffsetPercent: this.sliderOffsetPercent,
          // 添加时间范围类型
          rangeType: this.rangeType
        };
      }
      this.$emit('time-change', timeData);
    },
    
    // 防抖触发时间变化事件
    scheduleEmitTimeChange() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      
      this.debounceTimer = setTimeout(() => {
        this.emitTimeChange();
      }, 800);
    },
    
    // 获取当前时间数据
    getTimeData() {
      return {
        selectedTime: this.selectedTime,
        sliderCenterTime: this.sliderCenterTime,
        timeRange: { ...this.timeRange },
        sliderOffsetPercent: this.sliderOffsetPercent
      };
    },
    
    // 设置时间数据
    setTimeData(timeData) {
      if (timeData) {
        this.selectedTime = timeData.selectedTime;
        this.sliderCenterTime = timeData.sliderCenterTime;
        this.timeRange = { ...timeData.timeRange };
        this.sliderOffsetPercent = timeData.sliderOffsetPercent || 0;
      }
    },
    
    // 处理时间范围类型变化
    handleRangeTypeChange(value) {
      this.rangeType = value;
      if (value === 'precise') {
        this.selectedTime = this.defaultTime || this.formatDateTime(new Date());
        this.sliderCenterTime = this.selectedTime;
        this.timeRange.duration = this.defaultDuration;
        this.calculateTimeRange(this.selectedTime);
      } else {
        this.selectedTime = null;
        this.sliderCenterTime = null;
        this.timeRange = { start: null, end: null, duration: 4 };
      }
    },
    
    // 处理快速时间范围选择
    handleQuickRangeChange(value) {
      if (!value) return;
      
      // 清空精确时间数据
      this.selectedTime = null;
      this.sliderCenterTime = null;
      this.timeRange = { start: null, end: null, duration: 4 };
      
      // 设置快速时间值
      this.quickRange = value;
      
      // 触发时间变化事件
      this.emitTimeChange();
    },
    
    // 初始化时间线
    initTimeLine() {
      // 实现初始化时间线的逻辑
    },
    
    // 初始化默认时间
    initDefaultTime() {
      // 填充快速时间选项
      this.quickTimeOptions = this.quickTimeRanges.map(item => ({ value: item.value, label: item.label }));
      
      // 根据默认模式设置初始状态
      if (this.defaultMode === 'quick' && this.quickTimeRanges.some(r => r.value === this.defaultQuickRange)) {
        this.rangeType = 'quick';
        this.quickRange = this.defaultQuickRange;
      } else {
        this.rangeType = 'precise';
        const defaultTime = this.defaultTime ? new Date(this.defaultTime) : new Date();
        if (this.showTimeline) {
          this.setSliderCenterTime(defaultTime.getTime());
          this.setTimeRange(this.defaultDuration);
        }
      }
      
      // 在下一帧触发初始事件，确保父组件能接收到
      this.$nextTick(() => {
        this.emitTimeChange();
      });
    },
    
    // 处理窗口大小变化
    handleResize() {
      // 实现处理窗口大小变化的逻辑
    },
    
    // 设置滑块中心时间
    setSliderCenterTime(time) {
      if (time) {
        const date = new Date(time);
        this.selectedTime = this.formatDateTime(date);
        this.sliderCenterTime = this.selectedTime;
        this.sliderOffsetPercent = 0;
      }
    },
    
    // 设置时间范围
    setTimeRange(duration) {
      this.timeRange.duration = duration;
      this.calculateTimeRange(this.sliderCenterTime);
    },
    
    // 处理快速时间变化
    handleQuickTimeChange(value) {
      this.quickRange = value;
      this.emitTimeChange();
    }
  }
}
</script>

<style scoped>
.time-range-selector {
  padding: 15px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 10px;
}

.range-type-selector {
  margin-bottom: 15px;
}

.quick-time-selector {
  margin-bottom: 15px;
}

.time-selector {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.time-selector label {
  margin-right: 10px;
  font-weight: 500;
  color: #606266;
  font-size: 12px;
}

.time-range-text {
  color: #909399;
  font-size: 12px;
}

/* 时间线样式 */
.timeline-container {
  padding: 10px 0 20px 0;
  margin-bottom: 15px;
}

.timeline-wrapper {
  position: relative;
  width: calc(100% - 40px);
  height: 80px;
  margin: 0 auto;
}

.timeline-background {
  position: absolute;
  top: 25px;
  left: 0;
  width: 100%;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
}

.timeline-tick {
  position: absolute;
  top: -8px;
  width: 2px;
  height: 20px;
}

.tick-mark {
  width: 1px;
  height: 8px;
  background-color: #c0c4cc;
}

.tick-mark.tick-major {
  height: 12px;
  width: 2px;
  background-color: #909399;
}

.tick-label {
  position: absolute;
  top: 14px;
  left: -20px;
  width: 40px;
  text-align: center;
  font-size: 11px;
  color: #909399;
}

.timeline-center-line {
  position: absolute;
  top: 15px;
  left: 50%;
  width: 2px;
  height: 24px;
  background: #f56c6c;
  border-radius: 1px;
}

.timeline-range-box {
  position: absolute;
  top: 15px;
  height: 24px;
  min-width: 60px;
  background: rgba(64, 158, 255, 0.2);
  border: 2px solid #409EFF;
  border-radius: 4px;
  cursor: move;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  z-index: 10;
  transition: background-color 0.2s;
}

.timeline-range-box:hover {
  background: rgba(64, 158, 255, 0.3);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.range-handle {
  position: absolute;
  top: -2px;
  width: 8px;
  height: 28px;
  background: #409EFF;
  cursor: col-resize;
  border-radius: 2px;
  transition: background-color 0.2s;
}

.range-handle:hover {
  background: #66b1ff;
}

.range-handle-left {
  left: -4px;
}

.range-handle-right {
  right: -4px;
}

.range-time-display {
  font-size: 11px;
  color: #409EFF;
  font-weight: 500;
  white-space: nowrap;
  padding: 0 4px;
  text-align: center;
  min-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.timeline-labels {
  position: absolute;
  top: 55px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #909399;
}

.timeline-label {
  max-width: 80px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
}

.timeline-label-center {
  color: #f56c6c;
  font-weight: 500;
}

.slider-time {
  position: absolute;
  top: 45px;
  background-color: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.5);
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 12px;
  color: #409EFF;
  font-weight: 500;
  white-space: nowrap;
  z-index: 15;
  transition: left 0.1s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .time-selector label {
    margin-right: 0;
    margin-bottom: 5px;
  }
  
  .timeline-wrapper {
    width: calc(100% - 20px);
  }
  
  .tick-label {
    font-size: 9px;
  }
  
  .timeline-label {
    font-size: 10px;
    max-width: 60px;
  }
}
</style> 