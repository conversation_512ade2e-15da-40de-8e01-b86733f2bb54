<template>
  <div class="incident-timeline">
    <div class="drawer-container">
      <div class="timeline-header">
        <div class="alert-info">
          <span class="label">告警ID:</span>
          <span class="value">{{ alertId || 'N/A' }}</span>
          <span class="label">EventID:</span>
          <span class="value">{{ eventId || 'N/A' }}</span>
          <span v-if="fdIncidentId" class="label">FlashDuty故障ID:</span>
          <span v-if="fdIncidentId" class="value">{{ fdIncidentId }}</span>
        </div>
        <div class="timeline-filters">
          <el-select
            v-model="selectedTypes"
            multiple
            collapse-tags
            placeholder="筛选操作类型"
            style="width: 300px; margin-right: 10px;"
            @change="filterTimeline"
          >
            <el-option
              v-for="type in actionTypes"
              :key="type.code"
              :label="type.description"
              :value="type.code"
            />
          </el-select>
          <el-button @click="refreshTimeline" icon="el-icon-refresh">刷新</el-button>
        </div>
      </div>

      <div class="timeline-content" v-loading="loading">
        <div v-if="!loading && timelineData.length === 0" class="no-data">
          <el-empty description="暂无时间线数据"></el-empty>
        </div>
        
        <el-timeline v-else>
          <el-timeline-item
            v-for="(group, index) in groupedTimeline"
            :key="index"
            :timestamp="group.timestamp"
            placement="top"
            :color="getTimelineColor(group.items[0].type)"
            size="large"
          >
            <div class="timeline-group">
              <div
                v-for="(item, itemIndex) in group.items"
                :key="itemIndex"
                class="timeline-item"
                :class="getItemClass(item.type)"
              >
                <div class="item-header">
                  <span class="operation-type">{{ item.typeDescription }}</span>
                  <span class="operator">
                    {{ item.creatorName || '系统' }}
                  </span>
                </div>
                <div class="item-content" v-if="item.detailDescription" v-html="formatDetailDescription(item.detailDescription)"></div>
                <div class="item-detail" v-if="showDetail && item.detail">
                  <el-collapse accordion>
                    <el-collapse-item title="查看详细信息" :name="itemIndex">
                      <pre>{{ JSON.stringify(item.detail, null, 2) }}</pre>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <div class="drawer-footer">
        <el-checkbox v-model="showDetail">显示详细信息</el-checkbox>
      </div>
    </div>
  </div>
</template>

<script>
import { queryIncidentList, queryIncidentFeed, getPersonNames } from '@/api/alert/incident'

export default {
  name: 'IncidentTimeline',
  props: {
    eventId: String,
    fdIncidentId: String,
    alertId: [String, Number],
    alertOccurredAt: [String, Number]
  },
  data() {
    return {
      loading: false,
      incidentId: '',
      timelineData: [],
      groupedTimeline: [],
      selectedTypes: [],
      actionTypes: [],
      showDetail: false,
      personNameCache: {}
    }
  },
  watch: {
    '$props': {
      handler(newProps) {
        if (newProps.fdIncidentId || newProps.eventId) {
          this.initTimeline()
        }
      },
      deep: true,
      immediate: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.fdIncidentId || this.eventId) {
        this.initTimeline()
      }
    })
  },
  async created() {
    await this.loadActionTypes()
  },
  methods: {
    async initTimeline() {
      if (this.fdIncidentId) {
        this.incidentId = this.fdIncidentId
        await this.loadTimeline()
      } else if (this.eventId) {
        try {
          this.loading = true
          await this.findIncidentByEventId()
          if (this.incidentId) {
            await this.loadTimeline()
          }
        } catch (error) {
          console.error('初始化时间线失败:', error)
          this.$message.error('加载时间线数据失败')
        } finally {
          this.loading = false
        }
      }
    },

    async findIncidentByEventId() {
      try {
        let alertDate = new Date()
        if (this.alertOccurredAt) {
          const timestamp = typeof this.alertOccurredAt === 'string' ? 
            parseInt(this.alertOccurredAt) : this.alertOccurredAt
          alertDate = new Date(timestamp)
        }
        
        const startOfDay = new Date(alertDate.getFullYear(), alertDate.getMonth(), alertDate.getDate())
        const endOfDay = new Date(alertDate.getFullYear(), alertDate.getMonth(), alertDate.getDate() + 1)
        
        const params = {
          limit: 1,
          p: 1,
          startTime: Math.floor(startOfDay.getTime() / 1000),
          endTime: Math.floor(endOfDay.getTime() / 1000),
          labels: {
            eventId: this.eventId
          }
        }
        
        const response = await queryIncidentList(params)
        
        if (response.code === 200 && response.data) {
          const incidentData = response.data.data
          if (incidentData && incidentData.items && incidentData.items.length > 0) {
            this.incidentId = incidentData.items[0].incident_id
          } else {
            this.$message.warning('未找到对应的故障记录')
            this.incidentId = ''
          }
        } else {
          this.$message.error('查询故障列表失败: ' + (response.msg || '未知错误'))
        }
      } catch (error) {
        console.error('查询故障ID失败:', error)
        this.$message.error('查询故障ID失败')
      }
    },

    async loadTimeline() {
      if (!this.incidentId) return
      
      try {
        const params = {
          incident_id: this.incidentId,
          limit: 100,
          p: 1,
          asc: false,
          types: this.selectedTypes.length > 0 ? this.selectedTypes : undefined
        }
        
        const response = await queryIncidentFeed(params)
        
        if (response.code === 200 && response.data) {
          // 修复数据访问路径
          if (response.data.data && response.data.data.items) {
            this.timelineData = response.data.data.items
          } else if (response.data.items) {
            this.timelineData = response.data.items
          } else if (Array.isArray(response.data)) {
            this.timelineData = response.data
          } else {
            this.timelineData = response.data
          }
          
          await this.groupTimeline()
        } else {
          this.$message.error(response.msg || '查询时间线失败')
        }
      } catch (error) {
        console.error('查询时间线失败:', error)
        this.$message.error('查询时间线失败')
      }
    },

    async loadActionTypes() {
      this.actionTypes = [
        { code: 'i_comm', description: '故障.评论' },
        { code: 'i_notify', description: '故障.通知' },
        { code: 'i_new', description: '故障.触发' },
        { code: 'i_assign', description: '故障.分派' },
        { code: 'i_a_rspd', description: '故障.添加处理人' },
        { code: 'i_ack', description: '故障.开始处理' },
        { code: 'i_unack', description: '故障.取消处理' },
        { code: 'i_snooze', description: '故障.暂缓处理' },
        { code: 'i_wake', description: '故障.取消暂缓' },
        { code: 'i_rslv', description: '故障.解决' },
        { code: 'i_reopen', description: '故障.重开' },
        { code: 'i_merge', description: '故障.合并' },
        { code: 'i_m_silence', description: '故障.触发静默' },
        { code: 'i_m_inhibat', description: '故障.触发抑制' },
        { code: 'i_m_flapping', description: '故障.触发收敛' },
        { code: 'i_storm', description: '故障.触发风暴' },
        { code: 'i_r_rc', description: '故障.更新根本原因' },
        { code: 'i_r_desc', description: '故障.更新描述' },
        { code: 'i_r_rsltn', description: '故障.更新解决方案' },
        { code: 'i_r_resp', description: '故障.更新处理人' },
        { code: 'i_r_impact', description: '故障.更新影响' },
        { code: 'i_r_title', description: '故障.更新标题' },
        { code: 'i_r_severity', description: '故障.更新故障严重程度' },
        { code: 'i_r_field', description: '故障.更新自定义字段' },
        { code: 'i_custom', description: '故障.触发自定义操作' }
      ]
    },

    async groupTimeline() {
      const groups = {}
      
      this.timelineData.forEach(item => {
        const timestamp = item.created_at || item.createdAt
        const timestampKey = Math.floor(timestamp / 60) * 60
        
        if (!groups[timestampKey]) {
          groups[timestampKey] = {
            timestamp: this.formatTimestamp(timestamp),
            timestampRaw: timestamp,
            items: []
          }
        }
        
        groups[timestampKey].items.push(item)
      })
      
      const sortedGroups = Object.values(groups).sort((a, b) => {
        return b.timestampRaw - a.timestampRaw
      })
      
      for (const group of sortedGroups) {
        for (const item of group.items) {
          const creatorId = item.creator_id || 0
          const creatorName = creatorId === 0 ? '系统' : await this.getPersonNameById(creatorId)
          
          const convertedItem = {
            ...item,
            type: item.type,
            typeDescription: this.getTypeDescription(item.type),
            creatorId: creatorId,
            creatorName: creatorName,
            detailDescription: await this.parseDetailDescription(item.type, item.detail, item)
          }
          
          const index = group.items.indexOf(item)
          group.items[index] = convertedItem
        }
      }
      
      this.groupedTimeline = sortedGroups
    },

    async filterTimeline() {
      await this.loadTimeline()
    },

    async refreshTimeline() {
      await this.loadTimeline()
    },

    getTimelineColor(type) {
      const colorMap = {
        'i_new': '#F56C6C',
        'i_ack': '#409EFF',
        'i_rslv': '#67C23A',
        'i_reopen': '#E6A23C',
        'i_comm': '#909399',
        'i_assign': '#409EFF',
        'i_r_title': '#409EFF',
        'i_r_severity': '#E6A23C'
      }
      return colorMap[type] || '#909399'
    },

    getItemClass(type) {
      const classMap = {
        'i_new': 'item-trigger',
        'i_ack': 'item-process',
        'i_rslv': 'item-resolve',
        'i_reopen': 'item-reopen',
        'i_comm': 'item-comment'
      }
      return classMap[type] || 'item-default'
    },

    formatTimestamp(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    getTypeDescription(type) {
      const typeMap = {
        'i_comm': '故障.评论',
        'i_notify': '故障.通知',
        'i_new': '故障.触发',
        'i_assign': '故障.分派',
        'i_a_rspd': '故障.添加处理人',
        'i_ack': '故障.开始处理',
        'i_unack': '故障.取消处理',
        'i_snooze': '故障.暂缓处理',
        'i_wake': '故障.取消暂缓',
        'i_rslv': '故障.解决',
        'i_reopen': '故障.重开',
        'i_merge': '故障.合并',
        'i_m_silence': '故障.触发静默',
        'i_m_inhibat': '故障.触发抑制',
        'i_m_flapping': '故障.触发收敛',
        'i_storm': '故障.触发风暴',
        'i_r_rc': '故障.更新根本原因',
        'i_r_desc': '故障.更新描述',
        'i_r_rsltn': '故障.更新解决方案',
        'i_r_resp': '故障.更新处理人',
        'i_r_impact': '故障.更新影响',
        'i_r_title': '故障.更新标题',
        'i_r_severity': '故障.更新故障严重程度',
        'i_r_field': '故障.更新自定义字段',
        'i_custom': '故障.触发自定义操作'
      }
      return typeMap[type] || type
    },

    async parseDetailDescription(type, detail, item) {
      if (!detail) return ''
      
      try {
        switch (type) {
          case 'i_notify':
            return this.parseNotifyDescription(detail)
          case 'i_assign':
            return await this.parseAssignDescription(detail)
          case 'i_new':
            return this.parseNewIncidentDescription(detail)
          case 'i_rslv':
            return await this.parseResolveDescription(detail, item)
          case 'i_ack':
            return await this.parseAckDescription(detail, item)
          case 'i_comm':
            return detail.comment ? `评论: ${detail.comment}` : ''
          default:
            return JSON.stringify(detail)
        }
      } catch (e) {
        return String(detail)
      }
    },

    parseNotifyDescription(detail) {
      let description = '系统触发了通知，详情如下\n'
      
      if (detail.by === 'wecom') {
        description += '**企业微信机器人** 通过 '
      } else if (detail.by === 'email') {
        description += '**企业邮件** 通过 '
      } else {
        description += `**${detail.by}** 通过 `
      }
      
      if (detail.robots && detail.robots.length > 0) {
        const aliases = detail.robots.map(robot => robot.alias).join('、')
        description += `${aliases} 通知到群组`
      } else {
        description += '未知通道 通知到群组'
      }
      
      return description
    },

    async parseAssignDescription(detail) {
      let description = '系统通过分派策略 '
      
      if (detail.escalate_rule_name) {
        description += `${detail.escalate_rule_name} `
      }
      
      const layerIdx = detail.layer_idx !== undefined ? detail.layer_idx + 1 : 1
      description += `的环节${layerIdx} 分派，处理人员为 `
      
      if (detail.to && detail.to.length > 0) {
        const personNames = await this.getPersonNamesById(detail.to)
        description += personNames.join('、')
      } else {
        description += '未知'
      }
      
      return description
    },

    parseNewIncidentDescription(detail) {
      let description = '系统触发新故障 '
      
      if (detail.title) {
        description += `【${detail.title}】`
      }
      
      if (detail.description) {
        description += detail.description
      }
      
      if (detail.severity) {
        description += `，严重程度为${detail.severity}`
      }
      
      return description
    },

    async parseResolveDescription(detail, item) {
      if (detail.from === 'console') {
        const creatorId = item.creator_id || item.creatorId
        const creatorName = await this.getPersonNameById(creatorId)
        return `${creatorName} 手动关闭故障`
      } else if (detail.from === 'event') {
        return '故障自动恢复，系统自动关闭'
      } else {
        return `故障已解决 - 来源: ${detail.from || 'N/A'}`
      }
    },

    async parseAckDescription(detail, item) {
      const creatorId = item.creator_id || item.creatorId
      const creatorName = await this.getPersonNameById(creatorId)
      return `${creatorName} 认领了该故障`
    },

    async getPersonNameById(personId) {
      if (!personId) return '未知用户'
      
      const personIdStr = String(personId)
      
      if (this.personNameCache[personIdStr]) {
        return this.personNameCache[personIdStr]
      }
      
      try {
        const response = await getPersonNames([personIdStr])
        if (response.code === 200 && response.data) {
          this.personNameCache[personIdStr] = response.data[personIdStr] || `用户${personIdStr}`
          return this.personNameCache[personIdStr]
        }
      } catch (error) {
        console.error('获取人员姓名失败:', error)
      }
      
      return `用户${personIdStr}`
    },

    async getPersonNamesById(personIds) {
      if (!personIds || personIds.length === 0) return ['未知用户']
      
      const personIdStrs = personIds.map(id => String(id))
      const uncachedIds = personIdStrs.filter(id => !this.personNameCache[id])
      
      if (uncachedIds.length > 0) {
        try {
          const response = await getPersonNames(uncachedIds)
          if (response.code === 200 && response.data) {
            Object.assign(this.personNameCache, response.data)
          }
        } catch (error) {
          console.error('批量获取人员姓名失败:', error)
        }
      }
      
      return personIdStrs.map(id => this.personNameCache[id] || `用户${id}`)
    },

    formatDetailDescription(description) {
      if (!description) return ''
      
      let formatted = description.replace(/\n/g, '<br>')
      formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong style="color: #333; font-weight: bold;">$1</strong>')
      
      return formatted
    },

    clearTimelineData() {
      this.timelineData = []
      this.groupedTimeline = []
      this.incidentId = ''
      this.selectedTypes = []
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.incident-timeline {
  .drawer-container {
    padding: 24px 32px;
    display: flex;
    flex-direction: column;
    height: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #EBEEF5;
    flex-shrink: 0;

    .alert-info {
      .label {
        color: #909399;
        margin-right: 8px;
      }
      
      .value {
        color: #303133;
        font-weight: bold;
        margin-right: 20px;
      }
    }

    .timeline-filters {
      display: flex;
      align-items: center;
    }
  }

  .timeline-content {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 24px;
    padding-right: 8px;

    .no-data {
      text-align: center;
      padding: 60px 20px;
    }
  }

  .timeline-group {
    .timeline-item {
      background: #FAFAFA;
      border: 1px solid #EBEEF5;
      border-radius: 8px;
      padding: 16px 20px;
      margin-bottom: 12px;
      transition: all 0.3s ease;

      &:hover {
        background: #F5F7FA;
        border-color: #C0C4CC;
      }

      &.item-trigger {
        border-left: 4px solid #F56C6C;
      }

      &.item-process {
        border-left: 4px solid #409EFF;
      }

      &.item-resolve {
        border-left: 4px solid #67C23A;
      }

      &.item-reopen {
        border-left: 4px solid #E6A23C;
      }

      &.item-comment {
        border-left: 4px solid #909399;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .operation-type {
          font-weight: bold;
          color: #303133;
        }

        .operator {
          color: #909399;
          font-size: 12px;
        }
      }

      .item-content {
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
        word-break: break-all;
      }

      .item-detail {
        margin-top: 10px;
        
        pre {
          background: #F5F7FA;
          padding: 10px;
          border-radius: 4px;
          font-size: 12px;
          color: #606266;
          max-height: 200px;
          overflow-y: auto;
        }
      }
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 24px;
    border-top: 1px solid #EBEEF5;
    margin-top: auto;
    flex-shrink: 0;
    background: #fff;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .drawer-container {
      padding: 16px 20px;
    }
    
    .timeline-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      
      .timeline-filters {
        justify-content: center;
        
        .el-select {
          width: 100% !important;
          max-width: 300px;
        }
      }
    }
  }

  // Element UI时间线样式调整
  :deep(.el-timeline) {
    .el-timeline-item__timestamp {
      color: #409EFF;
      font-weight: bold;
      font-size: 13px;
    }
    
    .el-timeline-item__node {
      width: 16px;
      height: 16px;
    }
    
    .el-timeline-item__wrapper {
      padding-left: 32px;
    }
  }
}
</style> 