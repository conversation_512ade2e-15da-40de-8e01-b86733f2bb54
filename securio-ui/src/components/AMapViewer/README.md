# 高德地图组件使用说明

## 功能介绍

`AMapViewer` 是一个基于高德地图JS API 2.0的Vue组件，用于在页面中显示地图并标记特定位置。

## 配置步骤

### 1. 申请高德地图API密钥

1. 访问 [高德开放平台](https://console.amap.com/dev/key/app)
2. 注册并登录开发者账号
3. 创建新应用，选择"Web端(JS API)"平台
4. 获取 **Web服务API Key**
5. 在应用管理中创建 **安全密钥**

### 2. 配置密钥

在 `src/config/amap.js` 文件中配置你的密钥：

```javascript
export const AMAP_CONFIG = {
  // 替换为你的实际密钥
  key: '你的高德地图WebKey',
  securityJsCode: '你的高德地图安全密钥',
  // ... 其他配置
}
```

或者通过环境变量配置（推荐）：

```bash
# 在项目根目录创建 .env.local 文件
VUE_APP_AMAP_KEY=你的高德地图WebKey
VUE_APP_AMAP_SECURITY_CODE=你的高德地图安全密钥
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| center | Array | [116.397428, 39.90923] | 地图中心点坐标 [经度, 纬度] |
| zoom | Number | 10 | 地图缩放级别 (3-18) |
| height | String | '300px' | 地图容器高度 |
| showMarker | Boolean | true | 是否显示位置标记 |
| markerTitle | String | 'IP位置' | 标记点标题 |
| markerContent | String | '' | 标记点信息窗体内容 |
| viewMode3D | Boolean | false | 是否启用3D视图 |
| mapStyle | String | 'normal' | 地图主题样式 |

## 组件事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| map-loaded | 地图加载完成 | map - 地图实例 |
| marker-click | 标记点被点击 | event - 点击事件对象 |

## 使用示例

```vue
<template>
  <a-map-viewer
    :center="[120.123456, 30.123456]"
    :zoom="12"
    height="400px"
    :show-marker="true"
    marker-title="目标位置"
    marker-content="这是一个示例位置"
    @map-loaded="handleMapLoaded"
    @marker-click="handleMarkerClick"
  />
</template>

<script>
import AMapViewer from '@/components/AMapViewer/index.vue'

export default {
  components: {
    AMapViewer
  },
  methods: {
    handleMapLoaded(map) {
      console.log('地图加载完成', map)
    },
    handleMarkerClick(event) {
      console.log('标记被点击', event)
    }
  }
}
</script>
```

## 地图样式

支持以下地图样式：
- `normal` - 标准地图
- `dark` - 深色地图  
- `light` - 浅色地图
- `whitesmoke` - 白烟地图
- `fresh` - 清新地图
- `grey` - 灰色地图
- `graffiti` - 涂鸦地图
- `macaron` - 马卡龙地图
- `blue` - 蓝色地图
- `darkblue` - 深蓝地图
- `wine` - 酒红地图

## 注意事项

1. 确保网络可以访问高德地图API服务
2. 密钥配置错误会导致地图无法加载
3. 组件会自动验证配置的有效性
4. 建议使用环境变量来管理密钥，避免将密钥提交到版本控制系统 