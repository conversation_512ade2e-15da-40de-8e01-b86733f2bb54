<template>
  <div class="amap-container">
    <div v-if="loading" class="loading-overlay">
      <i class="el-icon-loading"></i>
      <span>地图加载中...</span>
    </div>
    <div v-if="error" class="error-overlay">
      <i class="el-icon-warning"></i>
      <span>{{ error }}</span>
    </div>
    <div :id="containerId" class="map-container" :style="{ height: height }"></div>
  </div>
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader'
import { AMAP_CONFIG, validateAmapConfig } from '@/config/amap'

export default {
  name: 'AMapViewer',
  props: {
    // 地图中心点坐标 [经度, 纬度]
    center: {
      type: Array,
      default: () => [116.397428, 39.90923] // 默认北京坐标
    },
    // 地图缩放级别
    zoom: {
      type: Number,
      default: 10
    },
    // 地图高度
    height: {
      type: String,
      default: '300px'
    },
    // 是否显示标记点
    showMarker: {
      type: Boolean,
      default: true
    },
    // 标记点标题
    markerTitle: {
      type: String,
      default: 'IP位置'
    },
    // 标记点内容
    markerContent: {
      type: String,
      default: ''
    },
    // 是否启用3D视图
    viewMode3D: {
      type: Boolean,
      default: false
    },
    // 地图主题样式
    mapStyle: {
      type: String,
      default: 'normal' // normal, dark, light, whitesmoke, fresh, grey, graffiti, macaron, blue, darkblue, wine
    }
  },
  data() {
    return {
      map: null,
      marker: null,
      loading: true,
      error: null,
      containerId: `amap-container-${Math.random().toString(36).substr(2, 9)}` // 生成唯一ID
    }
  },
  watch: {
    center: {
      handler(newCenter) {
        if (this.map && newCenter && newCenter.length === 2) {
          this.updateMapCenter(newCenter)
        }
      },
      deep: true
    },
    zoom(newZoom) {
      if (this.map) {
        this.map.setZoom(newZoom)
      }
    }
  },
  mounted() {
    this.initMap()
  },
  beforeDestroy() {
    this.destroyMap()
  },
  methods: {
    async initMap() {
      try {
        this.loading = true
        this.error = null

        // 验证配置
        const configValidation = validateAmapConfig()
        if (!configValidation.isValid) {
          throw new Error(`配置错误: ${configValidation.errors.join(', ')}`)
        }

        // 配置高德地图安全密钥
        window._AMapSecurityConfig = {
          securityJsCode: AMAP_CONFIG.securityJsCode
        }

        // 加载高德地图API
        const AMap = await AMapLoader.load({
          key: AMAP_CONFIG.key,
          version: AMAP_CONFIG.version,
          plugins: AMAP_CONFIG.plugins
        })

        // 创建地图实例
        this.map = new AMap.Map(this.containerId, {
          viewMode: this.viewMode3D ? '3D' : '2D',
          zoom: this.zoom,
          center: this.center,
          mapStyle: `amap://styles/${this.mapStyle}`,
          showIndoorMap: false,
          features: AMAP_CONFIG.defaultMapOptions.features
        })

        // 添加地图控件
        this.addMapControls(AMap)

        // 添加标记点
        if (this.showMarker) {
          this.addMarker(AMap)
        }

        // 地图加载完成
        this.map.on('complete', () => {
          this.loading = false
          this.$emit('map-loaded', this.map)
        })

      } catch (error) {
        console.error('地图初始化失败:', error)
        this.loading = false
        
        if (error.message && error.message.includes('配置错误')) {
          this.error = error.message
        } else {
          this.error = '地图加载失败，请检查网络连接或API配置'
        }
      }
    },

    addMapControls(AMap) {
      // 添加比例尺控件
      const scale = new AMap.Scale({
        position: 'LB'
      })
      this.map.addControl(scale)

      // 添加工具栏控件
      const toolbar = new AMap.ToolBar({
        position: 'RB'
      })
      this.map.addControl(toolbar)
    },

    addMarker(AMap) {
      if (!this.center || this.center.length !== 2) {
        return
      }

      // 创建标记点
      this.marker = new AMap.Marker({
        position: new AMap.LngLat(this.center[0], this.center[1]),
        title: this.markerTitle,
        icon: new AMap.Icon({
          size: new AMap.Size(25, 34),
          image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
          imageSize: new AMap.Size(25, 34)
        })
      })

      // 添加标记到地图
      this.map.add(this.marker)

      // 如果有标记内容，添加信息窗体
      if (this.markerContent) {
        const infoWindow = new AMap.InfoWindow({
          content: this.markerContent,
          offset: new AMap.Pixel(0, -30)
        })

        this.marker.on('click', () => {
          infoWindow.open(this.map, this.marker.getPosition())
        })
      }

      // 发射标记点击事件
      this.marker.on('click', (e) => {
        this.$emit('marker-click', {
          position: e.target.getPosition(),
          marker: this.marker
        })
      })
    },

    updateMapCenter(newCenter) {
      if (!this.map || !newCenter || newCenter.length !== 2) {
        return
      }

      // 更新地图中心点
      this.map.setCenter(newCenter)

      // 更新标记点位置
      if (this.marker) {
        this.marker.setPosition(newCenter)
      }
    },

    destroyMap() {
      if (this.marker) {
        this.marker.setMap(null)
        this.marker = null
      }
      
      if (this.map) {
        this.map.destroy()
        this.map = null
      }
    },

    // 刷新地图大小（当容器大小变化时调用）
    resizeMap() {
      if (this.map) {
        this.map.getSize()
      }
    }
  }
}
</script>

<style scoped>
.amap-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.map-container {
  width: 100%;
  min-height: 200px;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
  font-size: 14px;
  color: #606266;
}

.loading-overlay i {
  font-size: 24px;
  margin-bottom: 10px;
  animation: rotating 1s linear infinite;
}

.error-overlay {
  color: #F56C6C;
}

.error-overlay i {
  font-size: 24px;
  margin-bottom: 10px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 覆盖高德地图默认样式 */
.amap-container >>> .amap-logo {
  display: none !important;
}

.amap-container >>> .amap-copyright {
  display: none !important;
}
</style> 