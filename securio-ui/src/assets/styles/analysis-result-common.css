/* 分析结果组件公共样式 */

/* 分析摘要容器样式 */
.analysis-summary {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

/* 分析摘要标题样式 */
.analysis-summary h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

/* 摘要文本样式 */
.summary-text {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 15px;
  font-size: 12px;
}

/* 分析信息容器样式 */
.analysis-info {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  font-size: 12px;
}

.analysis-info span {
  margin: 2px 0;
}

/* 通用无数据提示样式 */
.no-data-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.no-data-tip i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #DCDFE6;
}

/* 通用图表容器样式 */
.chart-container {
  background: #fff;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #303133;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.chart-wrapper {
  width: 100%;
  position: relative;
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .analysis-info {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .analysis-info {
    flex-direction: column;
    gap: 8px;
  }
}

/* IP链接通用样式 */
.ip-link {
  color: #303133;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
}

.ip-link:hover {
  color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
  padding: 2px 4px;
  border-radius: 4px;
} 