import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken, setToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'
import { shouldUseWeComOAuth2, generateWeComOAuth2Url } from '@/utils/wechat'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/register','/aindex','/chat','/dl']

router.beforeEach((to, from, next) => {
  NProgress.start()
  
  // 检查URL参数中是否有token（用于OAuth2回调）
  if (to.query.token) {
    console.log('Found token in URL params, setting token...')
    
    // 检查登录类型
    const loginType = to.query.login_type || 'unknown'
    console.log('Login type:', loginType)
    
    // 验证登录来源
    if (loginType === 'wecom') {
      console.log('WeCom OAuth2 login detected')
    }
    
    setToken(to.query.token)
    
    // 清除URL中的敏感参数，避免暴露在地址栏
    const newQuery = { ...to.query }
    delete newQuery.token
    delete newQuery.login_type
    next({ path: to.path, query: newQuery, replace: true })
    return
  }
  
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      // 检查是否有redirect参数
      if (to.query.redirect) {
        console.log('已登录用户访问login页面，检测到redirect参数:', to.query.redirect)
        try {
          // 解码redirect参数并跳转
          const redirectPath = decodeURIComponent(to.query.redirect)
          console.log('跳转到redirect页面:', redirectPath)
          next(redirectPath)
        } catch (error) {
          console.warn('解码redirect参数失败:', error)
          // 如果解码失败，使用原始参数
          next(to.query.redirect)
        }
      } else {
        // 没有redirect参数，跳转到首页
        next({ path: '/' })
      }
      NProgress.done()
    } else if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      if (store.getters.roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          isRelogin.show = false
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
            store.dispatch('LogOut').then(() => {
              Message.error(err)
              next({ path: '/' })
            })
          })
      } else {
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // 检查是否在微信环境中
      if (shouldUseWeComOAuth2()) {
        console.log('WeChat environment detected, redirecting to WeCom OAuth2...')
        const currentPage = window.location.origin + to.fullPath
        const oauth2Url = generateWeComOAuth2Url(currentPage)
        window.location.href = oauth2Url
        return
      } else {
        // 非微信环境，跳转到登录页
        next(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
        NProgress.done()
      }
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
