{"name": "securio", "version": "1.0.0", "description": "securio 安全运营平台", "author": "悟空", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "NODE_ENV=development vue-cli-service build", "build:prod-no-compress": "NODE_ENV=development vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/securio-Vue.git"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@handsontable/vue": "^15.2.0", "@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "bootstrap": "^4.6.2", "clipboard": "2.0.8", "codemirror": "^5.65.17", "core-js": "3.25.3", "echarts": "5.4.0", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "handsontable": "^15.2.0", "highlight.js": "^9.18.5", "jquery": "^3.6.0", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "lodash": "^4.17.21", "marked": "^4.3.0", "moment": "^2.30.1", "nprogress": "0.2.0", "popper.js": "^1.16.1", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vue": "2.6.12", "vue-codemirror": "^4.0.6", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-highlightjs": "^1.3.3", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "watermark-dom": "2.3.0"}, "devDependencies": {"@antv/g6": "^4.8.21", "@antv/layout": "^0.3.25", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}