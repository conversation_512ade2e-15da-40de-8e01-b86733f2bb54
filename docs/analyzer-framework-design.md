# Securio 分析器框架设计文档

## 概述

Securio 分析器框架是一个灵活、可扩展的主机行为分析系统，支持多种数据源的分析和可视化展示。目前已支持 **12种核心分析器**，涵盖主机基础信息、网络行为、系统日志、应用访问等多个维度的安全分析。

## 目录

- [框架架构](#框架架构)
- [核心组件](#核心组件)
- [支持的分析器类型](#支持的分析器类型)
- [前端设计](#前端设计)
- [后端设计](#后端设计)
- [实现新分析器指南](#实现新分析器指南)
- [最佳实践](#最佳实践)
- [LogQueryComponent使用指南](#logquerycomponent使用指南)
- [示例：H3C上网行为分析器](#示例h3c上网行为分析器)
- [示例：主机SYSLOG分析器](#示例主机syslog分析器)

## 框架架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Vue.js)                        │
├─────────────────────────────────────────────────────────────┤
│  HostAnalyzer.vue │ AnalysisResult Components │ LogQuery    │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Spring Boot)                   │
├─────────────────────────────────────────────────────────────┤
│  AnalyzerType │ 12种分析器实现 │ 统一分析接口              │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                │
├─────────────────────────────────────────────────────────────┤
│  VictoriaLogs │ CMDB │ Agent Data │ External APIs         │
└─────────────────────────────────────────────────────────────┘
```

### 核心设计原则

1. **模块化设计** - 每个分析器独立实现，可单独开发和测试
2. **类型安全** - 通过枚举和接口确保类型安全
3. **统一规范** - 统一的数据格式和展示规范
4. **可扩展性** - 支持新增分析器类型和数据源
5. **异步处理** - 支持长时间运行的分析任务

## 支持的分析器类型

### 已实现的12种分析器

| 分析器名称 | 类型代码 | 实现状态 | 支持IP类型 | 主要功能 |
|-----------|---------|---------|-----------|---------|
| **主机基本信息分析** | `host_basic_info` | ✅ 已实现 | SERVER, PUBLIC | 主机基础信息收集和展示 |
| **主机资产分析** | `host_asset` | ✅ 已实现 | SERVER | 主机资产清单和配置分析 |
| **主机网络连接分析** | `host_network_connection` | ✅ 已实现 | SERVER | 网络连接状态和流量分析 |
| **主机命令分析** | `command_analysis` | ✅ 已实现 | SERVER | 命令执行行为分析 |
| **主机系统日志分析** | `syslog_analysis` | ✅ 已实现 | ALL (非UNKNOWN) | 系统日志聚合分析 |
| **DNS查询行为分析** | `dns_query_analysis` | ✅ 已实现 | ALL (非UNKNOWN) | DNS查询行为和异常检测 |
| **Nginx访问日志分析** | `nginx_access_log` | ✅ 已实现 | SERVER | Web访问行为分析 |
| **AC访问日志分析** | `ac_access_log` | ✅ 已实现 | OFFICE_CLIENT, VPN_USER | 访问控制日志分析 |
| **H3C上网行为分析** | `h3c_behavior_log` | ✅ 已实现 | OFFICE_CLIENT, VPN_USER | 上网行为监控分析 |
| **Zeek网络连接分析** | `zeek_conn_analysis` | ✅ 已实现 | ALL | 网络连接深度分析 |
| **Zeek HTTP连接分析** | `zeek_http_analysis` | ✅ 已实现 | ALL | HTTP协议行为分析 |
| **Zeek SSH连接分析** | `zeek_ssh_analysis` | ✅ 已实现 | ALL | SSH连接行为分析 |

### 分析器分类

#### 🖥️ 主机基础分析器 (4种)
- **主机基本信息分析** - 系统基础信息
- **主机资产分析** - 资产清单管理
- **主机网络连接分析** - 网络连接监控
- **主机命令分析** - 命令执行审计

#### 📊 系统日志分析器 (2种)  
- **主机系统日志分析** - 系统日志聚合
- **DNS查询行为分析** - DNS查询监控

#### 🌐 Web访问分析器 (3种)
- **Nginx访问日志分析** - Web服务访问
- **AC访问日志分析** - 访问控制审计
- **H3C上网行为分析** - 上网行为监控

#### 🔍 网络深度分析器 (3种)
- **Zeek网络连接分析** - 网络连接深度分析
- **Zeek HTTP连接分析** - HTTP协议分析
- **Zeek SSH连接分析** - SSH连接分析

## 核心组件

### 1. AnalyzerType 枚举

```java
public enum AnalyzerType {
    // 主机基础分析器
    HOST_BASIC_INFO("主机基本信息分析", "host_basic_info"),
    HOST_ASSET("主机资产分析", "host_asset"),
    HOST_NETWORK_CONNECTION("主机网络连接分析", "host_network_connection"),
    COMMAND_ANALYSIS("主机命令分析", "command_analysis"),
    
    // 系统日志分析器
    SYSLOG_ANALYSIS("主机系统日志分析", "syslog_analysis"),
    DNS_QUERY_ANALYSIS("DNS查询行为分析", "dns_query_analysis"),
    
    // Web访问分析器
    NGINX_ACCESS_LOG("Nginx访问日志分析", "nginx_access_log"),
    AC_ACCESS_LOG("AC访问日志分析", "ac_access_log"),
    H3C_BEHAVIOR_LOG("H3C上网行为分析", "h3c_behavior_log"),
    
    // 网络深度分析器
    ZEEK_CONN_ANALYSIS("Zeek网络连接分析", "zeek_conn_analysis"),
    ZEEK_HTTP_ANALYSIS("Zeek HTTP连接分析", "zeek_http_analysis"),
    ZEEK_SSH_ANALYSIS("Zeek SSH连接分析", "zeek_ssh_analysis");
}
```

### 2. IpType 枚举

```java
public enum IpType {
    SERVER("服务器IP"),           // 10.x.x.x (除特殊网段)
    OFFICE_CLIENT("办公区客户端IP"), // 10.1.x.x
    VPN_USER("VPN用户IP"),        // 10.250.x.x / 10.251.x.x
    PUBLIC("公网IP"),             // 非内网IP
    UNKNOWN("未知类型");          // 无法识别的IP
}
```

### 3. 分析器支持矩阵

| IP类型 \ 分析器 | 主机基础 | 系统日志 | Web访问 | 网络深度 |
|----------------|---------|---------|---------|---------|
| **SERVER** | ✅ 全支持 | ✅ 全支持 | ✅ Nginx | ✅ 全支持 |
| **OFFICE_CLIENT** | ❌ | ✅ 全支持 | ✅ AC/H3C | ✅ 全支持 |
| **VPN_USER** | ❌ | ✅ 全支持 | ✅ AC/H3C | ✅ 全支持 |
| **PUBLIC** | ✅ 基础信息 | ✅ 全支持 | ❌ | ✅ 全支持 |

## 前端设计

### 1. 主分析页面 (host.vue)

**核心功能**：
- IP地址输入和验证
- 时间范围选择
- 分析器类型检测
- 异步分析执行
- 结果展示和Tab切换

**关键组件**：
```vue
<template>
  <div class="host-analyze">
    <!-- IP输入和时间选择 -->
    <el-form ref="analysisForm" :model="analysisForm">
      <el-form-item label="主机IP" prop="hostIp">
        <el-input v-model="analysisForm.hostIp" />
      </el-form-item>
      <time-range-selector v-model="analysisForm.timeRange" />
    </el-form>
    
    <!-- 分析结果展示 -->
    <el-tabs v-if="analysisResult">
      <el-tab-pane v-for="(result, analyzerType) in analysisResult.analyzerResults">
        <component 
          :is="getResultComponent(analyzerType)"
          :data="result.data"
          :analyzer-type="analyzerType"
          :host-ip="analysisForm.hostIp" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
```

### 2. 结果展示组件

**命名规范**：`{AnalyzerName}Result.vue`

**标准结构**：
```vue
<template>
  <div class="analyzer-result">
    <!-- 分析摘要 -->
    <div class="analysis-summary">
      <h4>分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标IP：</strong>{{ data.targetIp }}</span>
        <span><strong>记录总数：</strong>{{ data.totalRecords }}</span>
        <span><strong>分析时间：</strong>{{ data.timeRangeDescription }}</span>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card" v-for="stat in statistics" :key="stat.key">
        <div class="stat-icon"><i :class="stat.icon"></i></div>
        <div class="stat-info">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>
    
    <!-- 图表展示 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12" v-for="chart in charts" :key="chart.id">
          <div class="chart-container">
            <h4>{{ chart.title }}</h4>
            <div :ref="chart.ref" class="chart"></div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 详细数据展示 -->
    <div class="details-section">
      <log-query-component
        :stream="dataStream"
        :settings="logSettings"
        :default-time-range="defaultTimeRange"
        :initial-query-condition="baseQueryCondition" />
    </div>
  </div>
</template>
```

### 3. 样式规范

**使用公共样式**：
```css
@import '../../../../assets/styles/analysis-result-common.css';
```

**关键样式类**：
- `.analysis-summary` - 分析摘要容器
- `.statistics-cards` - 统计卡片网格
- `.chart-container` - 图表容器
- `.details-section` - 详细数据区域

## 后端设计

### 1. 分析器实现

**基础模板**：
```java
@Component
public class ExampleAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(ExampleAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Override
    public String getName() {
        return "示例分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.EXAMPLE;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 定义支持的IP类型
        return ipType == IpType.SERVER || ipType == IpType.OFFICE_CLIENT;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        try {
            // 1. 构建查询条件
            String query = buildQuery(context);
            
            // 2. 获取数据
            String logsResult = victoriaLogsService.queryLogs(query, 1000, "60s", null);
            Map<String, Object> facetsData = getFacetsStatistics(query);
            
            // 3. 解析和分析数据
            List<Map<String, Object>> records = parseData(logsResult);
            Map<String, Object> analysis = analyzeData(records, facetsData);
            
            // 4. 构建结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("targetIp", context.getHostIp());
            resultData.put("records", records);
            resultData.put("analysis", analysis);
            resultData.put("analysisSummary", generateSummary(analysis));
            
            // 添加时间信息供前端组件构建LogQL使用
            if (context.getStartTime() != null) {
                resultData.put("startTime", context.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getEndTime() != null) {
                resultData.put("endTime", context.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            if (context.getTimeRange() != null) {
                resultData.put("timeRange", context.getTimeRange());
            }
            resultData.put("timeRangeDescription", getTimeRangeDescription(context));
            
            return AnalysisResult.success(getType(), getName(), resultData);
            
        } catch (Exception e) {
            logger.error("分析失败: {}", e.getMessage(), e);
            return AnalysisResult.failure(getType(), getName(), "分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public int getPriority() {
        return 1; // 优先级：1-10，数字越小优先级越高
    }
    
    @Override
    public long getEstimatedExecutionTime() {
        return 5000; // 预估执行时间(毫秒)
    }
    
    // 私有辅助方法
    private String buildQuery(AnalysisContext context) {
        String timeQuery = TimeFormatUtil.buildTimeQuery(
            context.getStartTimeStr(), 
            context.getEndTimeStr(), 
            context.getTimeRange()
        );
        return String.format("%s and stream:\"LOG_STREAM\" and src_ip:\"%s\"", 
            timeQuery, context.getHostIp());
    }
    
    private Map<String, Object> getFacetsStatistics(String baseQuery) {
        // 使用facets接口获取聚合统计
        String facetsResult = victoriaLogsService.facets(baseQuery, 50, null);
        return parseFacetsResult(facetsResult);
    }
    
    private List<Map<String, Object>> parseData(String logsResult) {
        // 解析日志数据
        // 实现具体的解析逻辑
        return new ArrayList<>();
    }
    
    private Map<String, Object> analyzeData(List<Map<String, Object>> records, Map<String, Object> facetsData) {
        // 分析数据，生成统计信息和图表数据
        // 实现具体的分析逻辑
        return new HashMap<>();
    }
    
    private String generateSummary(Map<String, Object> analysis) {
        // 生成分析摘要
        return "基于分析结果生成的智能摘要";
    }
}
```

### 2. 数据格式规范

**AnalysisContext (输入)**：
```java
public class AnalysisContext {
    private String hostIp;          // 目标IP
    private LocalDateTime startTime; // 开始时间
    private LocalDateTime endTime;   // 结束时间
    private String timeRange;        // 时间范围 (如 "1h", "30m")
    private Map<String, Object> parameters; // 额外参数
}
```

**AnalysisResult (输出)**：
```java
public class AnalysisResult {
    private boolean success;                    // 成功标志
    private AnalyzerType analyzerType;         // 分析器类型
    private String analyzerName;               // 分析器名称
    private Map<String, Object> data;          // 分析结果数据
    private String errorMessage;               // 错误信息
    private long executionTime;                // 执行时间
}
```

**标准数据字段**：
```java
// 结果数据中的标准字段
Map<String, Object> data = {
    "targetIp": "*********",                  // 目标IP
    "totalRecords": 1234,                     // 总记录数
    "analysisSummary": "分析摘要文本",         // 分析摘要
    "timeRangeDescription": "最近 1h",        // 时间范围描述（显示用）
    
    // 时间信息（供前端组件构建LogQL使用）
    "timeRange": "1h",                        // 时间范围（如："1h", "30m"）
    "startTime": "2023-12-01T10:00:00",       // 开始时间（ISO格式）
    "endTime": "2023-12-01T11:00:00",         // 结束时间（ISO格式）
    
    "statistics": {                           // 统计信息
        "uniqueUsers": 10,
        "uniqueApps": 5,
        // ... 其他统计
    },
    "chartData": {                           // 图表数据
        "categories": [...],
        "series": [...],
        // ... 图表配置
    },
    "records": [...],                        // 详细记录
    "facetsData": {...}                      // Facets聚合数据
};
```

## 实现新分析器指南

> **⚠️ 开发前必读：核心设计规则**  
> 1. **后端分析器不要返回大量原始日志记录**，使用前端`LogQueryComponent`展示详细日志  
> 2. **使用后端API构建LogQL**，调用`/app/log-query/build-logql`接口，不要前端拼接查询语句

### 步骤1：定义分析器类型

1. 在 `AnalyzerType.java` 中添加新的枚举值：
```java
NEW_ANALYZER("new_analyzer", "新分析器描述");
```

2. 确定支持的IP类型和数据源
   - `IpType.SERVER` - 服务器IP (10.x.x.x，除10.1、10.250、10.251网段)
   - `IpType.OFFICE_CLIENT` - 办公区客户端IP (10.1.x.x)
   - `IpType.VPN_USER` - VPN用户IP (10.250.x.x / 10.251.x.x)
   - `IpType.PUBLIC` - 公网IP
   - `IpType.UNKNOWN` - 未知类型

**当前分析器IP类型支持情况**：
- **H3CBehaviorLogAnalyzer** - 支持`OFFICE_CLIENT`和`VPN_USER`
- **ACAccessLogAnalyzer** - 支持`OFFICE_CLIENT`和`VPN_USER`  
- **HostBasicInfoAnalyzer** - 支持`SERVER`和`PUBLIC`（排除`OFFICE_CLIENT`和`VPN_USER`）
- **CommandAnalyzer** - 仅支持`SERVER`
- **DnsQueryAnalyzer** - 支持所有非`UNKNOWN`类型
- **SyslogAnalyzer** - 支持所有非`UNKNOWN`类型

### 步骤2：实现后端分析器

1. 创建分析器类：
```java
@Component
public class NewAnalyzer implements Analyzer {
    // 实现所有接口方法
}
```

2. **实现核心分析逻辑**（遵循设计规则）：
   - ✅ **数据查询和获取**：使用facets接口获取聚合统计
   - ✅ **数据解析和清洗**：处理统计数据而非原始日志
   - ✅ **统计分析和聚合**：生成图表数据和摘要信息
   - ✅ **结果格式化**：返回分析结果，避免大量原始记录
   - ❌ **避免**：不要在结果中包含大量原始日志记录

### 步骤3：创建前端展示组件

1. 创建结果组件：
```bash
touch securio-ui/src/views/analyze/components/AnalysisResult/NewAnalyzerResult.vue
```

2. **实现组件结构**（遵循设计规则）：
   - ✅ **分析摘要**：展示后端生成的分析结果
   - ✅ **统计卡片**：显示关键指标
   - ✅ **图表展示**：可视化统计数据
   - ✅ **详细数据**：使用`LogQueryComponent`组件，通过后端API构建LogQL

3. **正确的日志查询实现**：
```vue
<template>
  <div class="analyzer-result">
    <!-- 分析摘要和图表 -->
    <div class="analysis-summary">...</div>
    <div class="charts-section">...</div>
    
    <!-- ✅ 正确：使用LogQueryComponent展示详细日志 -->
    <div class="details-section">
      <log-query-component
        ref="logQuery"
        :key="logQueryKey"
        :title="'详细日志'"
        :subtitle="`查询分析IP ${data.targetIp} 的详细记录`"
        :defaultQuery="builtLogQLQuery"
        :externalTimeRange="timeConfig"
        :settings="logSettings"
      />
    </div>
  </div>
</template>

<script>
import { buildLogQL } from '@/api/log/logQuery'
import { convertTimeRange } from '@/utils/timeFormatter'

export default {
  data() {
    return {
      logQueryKey: 1,
      builtLogQLQuery: '',
      timeConfig: {}
    }
  },
  methods: {
    async buildLogQLQuery() {
      const params = {
        stream: 'AUDITLOG_H3C_BEHAVIOR',
        customFilters: [
          { field: 'src_ip', operator: '=', value: this.data.targetIp },
          { field: 'log_type', operator: '=', value: 'web_access' }
        ],
        timeRange: convertTimeRange(this.data.timeRange, '30m')
      }
      
      const response = await buildLogQL(params)
      if (response.code === 200) {
        this.builtLogQLQuery = response.data.logQL
        this.timeConfig = response.data.timeConfig
        this.logQueryKey = Date.now()
      }
    }
  }
}
</script>
```

4. 在 `host.vue` 中注册组件：
```javascript
import NewAnalyzerResult from './components/AnalysisResult/NewAnalyzerResult.vue'

export default {
  components: {
    NewAnalyzerResult
  },
  methods: {
    getResultComponent(analyzerType) {
      if (analyzerType === 'new_analyzer') {
        return NewAnalyzerResult
      }
      // ...
    }
  }
}
```

### 步骤4：配置和测试

1. 确保分析器被Spring容器自动扫描
2. 测试不同IP类型的支持情况
3. 验证数据格式和展示效果
4. 性能测试和优化

## 最佳实践

### 1. 核心设计规则

#### 1.1 日志记录处理规范

**规则1：后端分析器尽量不返回原始的日志记录**
- **原因**：避免大量数据传输，提高响应速度，降低内存占用
- **实现**：分析器主要返回统计结果、图表数据和分析摘要
- **日志详情展示**：使用前端封装的`LogQueryComponent`组件来展示详细日志
- **示例**：
```java
// ❌ 错误做法：返回大量原始日志记录
Map<String, Object> resultData = new HashMap<>();
resultData.put("rawLogs", allLogRecords); // 避免返回大量原始数据

// ✅ 正确做法：返回分析结果和少量关键信息
Map<String, Object> resultData = new HashMap<>();
resultData.put("analysis", statisticsData);    // 统计分析结果
resultData.put("chartData", chartData);        // 图表数据
resultData.put("totalRecords", recordCount);   // 记录总数
resultData.put("analysisSummary", summary);    // 分析摘要
// 详细日志通过前端LogQueryComponent查询
```

#### 1.2 LogQL构建规范

**规则2：使用后端统一接口构建LogQL查询语句**
- **原因**：确保查询语法正确性，统一时间格式处理，避免安全风险
- **实现**：调用`/app/log-query/build-logql` API构建LogQL
- **特别注意**：时间参数的格式转换和验证必须在后端处理
- **示例**：
```javascript
// ❌ 错误做法：前端直接拼接LogQL
const logQL = `_time:${timeRange} and stream:"${stream}" and src_ip:"${ip}"`

// ✅ 正确做法：使用后端API构建LogQL
const params = {
  stream: 'AUDITLOG_H3C_BEHAVIOR',
  customFilters: [
    { field: 'src_ip', operator: '=', value: targetIp },
    { field: 'log_type', operator: '=', value: 'web_access' }
  ],
  timeRange: convertTimeRange(this.data.timeRange, '30m')
}

const response = await buildLogQL(params)
if (response.code === 200) {
  this.builtLogQLQuery = response.data.logQL
  this.timeConfig = response.data.timeConfig
}
```

**时间格式处理注意事项**：
- 中文时间格式（如"最近30分钟"）必须在后端转换为英文格式（如"30m"）
- 时间范围转换使用`convertTimeRange()`工具函数进行预处理
- 后端API会自动处理时区转换和格式验证

**LogQueryComponent参数说明**：
- ✅ 使用 `defaultQuery` 参数传递预构建的LogQL查询语句
- ✅ 使用 `externalTimeRange` 参数传递时间范围配置
- ❌ **不要使用** `external-query` 参数（此参数不存在）

### 2. 错误处理

```java
@Override
public AnalysisResult analyze(AnalysisContext context) {
    try {
        // 分析逻辑
        return AnalysisResult.success(getType(), getName(), data);
    } catch (DataAccessException e) {
        logger.error("数据访问异常: {}", e.getMessage(), e);
        return AnalysisResult.failure(getType(), getName(), "数据访问失败，请检查数据源连接");
    } catch (TimeoutException e) {
        logger.error("分析超时: {}", e.getMessage(), e);
        return AnalysisResult.failure(getType(), getName(), "分析超时，请缩小时间范围后重试");
    } catch (Exception e) {
        logger.error("分析异常: {}", e.getMessage(), e);
        return AnalysisResult.failure(getType(), getName(), "分析失败: " + e.getMessage());
    }
}
```

### 3. 性能优化

- **数据量控制**：合理设置查询限制
- **并行处理**：支持异步分析
- **缓存策略**：对重复查询结果进行缓存
- **分页查询**：大数据量分页处理

### 4. 日志记录

```java
private static final Logger logger = LoggerFactory.getLogger(AnalyzerClass.class);

// 记录关键执行步骤
logger.info("开始执行{}分析，目标IP: {}", getName(), context.getHostIp());
logger.debug("构建查询条件: {}", query);
logger.info("{}分析完成，耗时: {}ms", getName(), executionTime);
```

### 5. 数据验证

```java
private void validateContext(AnalysisContext context) {
    if (context.getHostIp() == null || context.getHostIp().trim().isEmpty()) {
        throw new IllegalArgumentException("主机IP不能为空");
    }
    
    if (!isValidIpAddress(context.getHostIp())) {
        throw new IllegalArgumentException("无效的IP地址格式");
    }
}
```

## 示例：H3C上网行为分析器

### 后端实现要点

```java
@Component
public class H3CBehaviorLogAnalyzer implements Analyzer {
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.H3C_BEHAVIOR_LOG;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        return ipType == IpType.OFFICE_CLIENT; // 仅支持办公区客户端
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        // 1. 使用facets接口获取聚合统计
        Map<String, Object> facetsData = getFacetsStatistics(query);
        
        // 2. 获取详细日志记录
        List<Map<String, Object>> records = parseH3CBehaviorLogs(logsResult);
        
        // 3. 分析行为数据
        Map<String, Object> analysis = analyzeBehaviorData(records, facetsData);
        
        return AnalysisResult.success(getType(), getName(), resultData);
    }
    
    private Map<String, Object> getFacetsStatistics(String baseQuery) {
        String facetsResult = victoriaLogsService.facets(baseQuery, 50, null);
        // 解析url_category、url_domain、dst_ip等字段的facets数据
        return parseFacetsData(facetsResult);
    }
}
```

### 前端实现要点

```vue
<template>
  <div class="h3c-behavior-log-result">
    <!-- 分析摘要（使用公共样式） -->
    <div class="analysis-summary">...</div>
    
    <!-- 统计卡片 -->
    <div class="statistics-cards">...</div>
    
    <!-- 图表展示（无标题，避免与图例重合） -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-container">
            <h4>网站分类分布</h4>
            <div ref="urlCategoryChart" class="chart"></div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- Tab式日志查询 -->
    <div class="log-details-section">
      <div class="custom-tabs-container">
        <div class="custom-tabs">
          <div class="custom-tab-item" :class="{ 'active': activeTab === 'web_access' }">
            网页访问日志
          </div>
        </div>
      </div>
      
      <div class="tab-content">
        <log-query-component
          :defaultQuery="logQueries.web_access"
          :externalTimeRange="timeConfig"
          :settings="webAccessSettings" />
      </div>
    </div>
  </div>
</template>
```

## 示例：主机SYSLOG分析器

主机SYSLOG分析器展示了如何实现基于日志查询的分析器，专注于多种系统日志类型的展示和查询。

### 后端实现要点

```java
@Component
public class SyslogAnalyzer implements Analyzer {
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.SYSLOG_ANALYSIS;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        return ipType != IpType.UNKNOWN; // 支持所有已知类型的IP
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        // 后端逻辑留空，主要用于前端日志展示
        Map<String, Object> analysisData = new HashMap<>();
        analysisData.put("targetIp", context.getHostIp());
        analysisData.put("timeRange", getTimeRangeDescription(context));
        
        // 设置支持的日志流类型
        List<String> supportedStreams = Arrays.asList(
            "AUDITLOG_SYSTEM_AUDIT",
            "AUDITLOG_SYSLOG_COMMAND", 
            "AUDITLOG_SYSTEM_SECURE",
            "AUDITLOG_SYSLOG_MESSAGE",
            "AUDITLOG_SYSLOG_CRON"
        );
        analysisData.put("supportedStreams", supportedStreams);
        
        return AnalysisResult.success(getType(), getName(), analysisData);
    }
}
```

### 前端实现要点

```vue
<template>
  <div class="syslog-analysis-result">
    <!-- 分析摘要 -->
    <div class="analysis-summary">...</div>

    <!-- Tab式日志查询 -->
    <div class="log-details-section">
      <div class="custom-tabs">
        <div class="custom-tab-item" @click="switchTab('system_audit')">
          系统审计日志
        </div>
        <!-- 其他Tab... -->
      </div>
      
      <div class="tab-content">
        <log-query-component
          v-if="activeTab === 'system_audit'"
          :defaultQuery="logQueries.system_audit"
          :externalTimeRange="timeConfig"
          :settings="logSettings" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      streamMapping: {
        system_audit: 'AUDITLOG_SYSTEM_AUDIT',
        command: 'AUDITLOG_SYSLOG_COMMAND',
        secure: 'AUDITLOG_SYSTEM_SECURE',
        message: 'AUDITLOG_SYSLOG_MESSAGE',
        cron: 'AUDITLOG_SYSLOG_CRON'
      }
    }
  },
  methods: {
    async buildLogQLForTab(tabName) {
      const params = {
        stream: this.streamMapping[tabName],
        customFilters: [
          { field: 'agent_hostip', operator: '=', value: this.data.targetIp }
        ],
        timeRange: convertTimeRange(this.data.timeRange, '1d')
      }
      
      const response = await buildLogQL(params)
      if (response.code === 200) {
        this.logQueries[tabName] = response.data.logQL
        this.timeConfig = response.data.timeConfig
      }
    }
  }
}
</script>
```

### 关键实现要点

1. **后端设计要点**：
   - 支持所有已知类型的IP地址
   - 后端逻辑留空，专注于前端日志展示
   - 返回支持的日志流类型列表
   - 生成简单的分析摘要

2. **前端设计要点**：
   - 使用`agent_hostip`参数构建LogQL（对应后端`agent_hostip`字段）
   - 通过Tab切换展示不同类型的系统日志
   - 正确使用`defaultQuery`和`externalTimeRange`参数
   - 实现多个日志流的统一查询管理

3. **支持的日志类型**：
   - `AUDITLOG_SYSTEM_AUDIT` - 系统审计日志
   - `AUDITLOG_SYSLOG_COMMAND` - 命令执行日志
   - `AUDITLOG_SYSTEM_SECURE` - 安全相关日志
   - `AUDITLOG_SYSLOG_MESSAGE` - 系统消息日志
   - `AUDITLOG_SYSLOG_CRON` - 计划任务日志

4. **数据流程**：
   ```
   用户输入主机IP → 后端准备日志类型 → 前端Tab切换 → LogQueryComponent查询详细日志
   ```

这个SYSLOG分析器展示了如何实现专注于日志查询的分析器，适用于需要查看多种系统日志类型但不需要复杂统计分析的场景。

## 扩展方向

### 1. 新数据源支持
- 防火墙日志分析
- 数据库审计日志
- 邮件系统日志
- 应用程序日志

### 2. 高级分析功能
- 异常行为检测
- 用户行为画像
- 安全威胁分析
- 趋势预测分析

### 3. 性能优化
- 分布式分析
- 实时流处理
- 机器学习集成
- 智能缓存策略

---

## 总结

Securio分析器框架提供了一个完整、灵活的主机行为分析解决方案。通过标准化的接口设计和组件化的前端架构，可以快速扩展新的分析能力。

关键成功要素：
1. **遵循设计规范** - 确保代码质量和可维护性
2. **充分测试验证** - 保证分析结果的准确性
3. **性能优化** - 确保系统的响应速度
4. **用户体验** - 提供直观、易用的界面

通过本框架，开发团队可以高效地实现各种业务场景下的主机行为分析需求。

## 完整示例：主机命令分析器

为了更好地展示框架的使用方法，下面提供一个完整的主机命令分析器实现示例：

### 1. 后端分析器实现

```java
@Component
public class CommandAnalyzer implements Analyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(CommandAnalyzer.class);
    
    @Autowired
    private VictoriaLogsService victoriaLogsService;
    
    @Override
    public String getName() {
        return "主机命令行为分析器";
    }
    
    @Override
    public AnalyzerType getType() {
        return AnalyzerType.COMMAND_ANALYSIS;
    }
    
    @Override
    public boolean supports(IpType ipType) {
        // 主要支持服务器IP的命令分析
        return ipType == IpType.SERVER;
    }
    
    @Override
    public AnalysisResult analyze(AnalysisContext context) {
        logger.info("开始执行主机命令分析，主机IP: {}", context.getHostIp());
        
        try {
            // 构建查询语句 - 注意使用dst_ip字段
            String query = String.format("%s and stream:\"AUDITLOG_SYSLOG_COMMAND\" and dst_ip:\"%s\"", 
                timeQuery, context.getHostIp());
            
            // 使用facets接口获取统计数据（不返回大量原始日志）
            Map<String, Object> facetsData = getFacetsStatistics(query, context.getHostIp());
            
            // 分析命令数据
            Map<String, Object> commandAnalysis = analyzeCommandData(null, facetsData, context.getHostIp());
            
            // 构建分析结果
            Map<String, Object> analysisData = new HashMap<>();
            analysisData.put("targetIp", context.getHostIp());
            analysisData.put("commandAnalysis", commandAnalysis);
            analysisData.put("analysisSummary", generateAnalysisSummary(context.getHostIp(), null, commandAnalysis));
            analysisData.put("timeRange", getTimeRangeDescription(context));
            
            return AnalysisResult.success(getType(), getName(), analysisData);
            
        } catch (Exception e) {
            logger.error("主机命令分析失败，主机IP: {}", context.getHostIp(), e);
            return AnalysisResult.failure(getType(), getName(), "主机命令分析失败: " + e.getMessage());
        }
    }
    
    // 其他辅助方法...
}
```

### 2. 前端组件实现

```vue
<template>
  <div class="command-analysis-result">
    <!-- 分析摘要 -->
    <div v-if="data.analysisSummary" class="analysis-summary">
      <h4>主机命令行为分析摘要</h4>
      <p class="summary-text">{{ data.analysisSummary }}</p>
      <div class="analysis-info">
        <span><strong>目标主机：</strong>{{ data.targetIp }}</span>
        <span><strong>命令总数：</strong>{{ data.commandAnalysis && data.commandAnalysis.totalCommands || 0 }}</span>
        <span><strong>分析时间：</strong>{{ data.timeRangeDescription }}</span>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div v-if="data.commandAnalysis" class="statistics-cards">
      <div class="stat-card">
        <div class="stat-icon"><i class="el-icon-tickets"></i></div>
        <div class="stat-info">
          <div class="stat-value">{{ data.commandAnalysis.totalCommands || 0 }}</div>
          <div class="stat-label">命令执行总数</div>
        </div>
      </div>
      <!-- 更多统计卡片... -->
    </div>

    <!-- 图表展示区域 -->
    <el-row v-if="data.commandAnalysis" :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-title">命令执行Top10</div>
          <div v-else ref="commandsChart" class="chart"></div>
        </div>
      </el-col>
      <!-- 更多图表... -->
    </el-row>

    <!-- ✅ 正确：使用LogQueryComponent展示详细日志 -->
    <div class="log-details-section" style="margin-top: 20px;">
      <div class="custom-tabs-container">
        <div class="custom-tabs">
          <div class="custom-tab-item active">命令执行日志</div>
        </div>
      </div>
      
      <div class="tab-content">
        <log-query-component
          ref="commandLogQuery"
          :key="logQueryKey"
          :title="'命令执行日志'"
          :subtitle="`查询主机 ${data.targetIp} 的命令执行记录`"
          :settings="commandLogSettings"
          :defaultQuery="builtLogQLQuery"
          :externalTimeRange="timeConfig"
        />
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import LogQueryComponent from '@/components/log_query_component'
import { convertTimeRange } from '@/utils/timeFormatter'
import { buildLogQL } from '@/api/log/logQuery'

export default {
  name: 'CommandAnalysisResult',
  components: {
    LogQueryComponent
  },
  data() {
    return {
      logQueryKey: 1,
      builtLogQLQuery: '',
      timeConfig: {},
      // 命令日志组件设置
      commandLogSettings: {
        columns: [
          { prop: 'event_time', label: '执行时间', width: '180', sortable: true },
          { prop: 'user', label: '执行用户', width: '120' },
          { prop: 'hostname', label: '主机名', width: '140' },
          { prop: 'command', label: '命令', minWidth: '300', showOverflowTooltip: true },
          { prop: 'working_dir', label: '工作目录', width: '200', showOverflowTooltip: true },
          { prop: 'message', label: '原始消息', showOverflowTooltip: true, className: 'log-message' }
        ],
        showDetailButton: true,
        detailButtonWidth: '100',
        detailButtonFixed: 'right'
      }
    }
  },
  methods: {
    async buildLogQLQuery() {
      try {
        // ✅ 正确：使用customFilters自定义条件过滤传递agent_hostip参数
        const params = {
          stream: 'AUDITLOG_SYSLOG_COMMAND',
          customFilters: [
            { field: 'agent_hostip', operator: '=', value: this.data.targetIp }
          ],
          timeRange: convertTimeRange(this.data.timeRange, '1d')
        }

        const response = await buildLogQL(params)

        if (response.code === 200) {
          this.builtLogQLQuery = response.data.logQL
          this.timeConfig = response.data.timeConfig
          this.logQueryKey = Date.now() // 触发组件重新渲染
        }
      } catch (error) {
        console.error('构建LogQL异常:', error)
        this.$message.error('构建命令查询语句异常: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/styles/analysis-result-common.css';
/* 组件样式... */
</style>
```

### 3. 枚举类型配置

```java
// 在AnalyzerType.java中添加
COMMAND_ANALYSIS("主机命令分析", "command_analysis"),
```

### 4. 主分析页面集成

```javascript
// 在host.vue中添加组件导入和映射
import CommandAnalysisResult from './components/AnalysisResult/CommandAnalysisResult'

export default {
  components: {
    CommandAnalysisResult
  },
  methods: {
    getResultComponent(analyzerType) {
      if (analyzerType === 'command_analysis') {
        return CommandAnalysisResult
      }
      // 其他映射...
    }
  }
}
```

### 5. 关键实现要点

1. **后端设计要点**：
   - 使用`dst_ip`字段过滤主机（注意不是`src_ip`）
   - 流名称为`AUDITLOG_SYSLOG_COMMAND`
   - 使用facets接口获取统计，不返回大量原始日志
   - 实现命令分类功能（系统管理、文件操作、网络操作等）

2. **前端设计要点**：
   - 使用`agent_hostip`参数构建LogQL（对应后端`agent_hostip`字段）
   - 通过后端API构建LogQL，避免前端拼接
   - 正确使用`defaultQuery`和`externalTimeRange`参数
   - 实现命令执行统计、类型分布、用户活动等图表

3. **数据流程**：
   ```
   用户输入主机IP → 后端分析器facets统计 → 前端图表展示 → LogQueryComponent详细日志
   ```

这个完整示例展示了框架的所有关键特性和最佳实践，开发者可以参考此模式实现新的分析器。

## LogQueryComponent使用指南

> **📖 详细使用文档**  
> LogQueryComponent是分析器中用于展示详细日志的核心组件。  
> 完整使用文档请参考：[LogQueryComponent使用文档](./log-query-component-usage.md)

### 快速开始

```vue
<template>
  <div class="analyzer-result">
    <!-- 分析摘要和图表 -->
    <div class="analysis-summary">...</div>
    <div class="charts-section">...</div>
    
    <!-- ✅ 正确：使用LogQueryComponent展示详细日志 -->
    <div class="details-section">
      <log-query-component
        ref="logQuery"
        :key="logQueryKey"
        :title="'详细日志'"
        :subtitle="`查询分析IP ${data.targetIp} 的详细记录`"
        :defaultQuery="builtLogQLQuery"
        :externalTimeRange="timeConfig"
        :settings="logSettings"
      />
    </div>
  </div>
</template>
```

### 关键参数说明

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `defaultQuery` | String | **预构建的LogQL查询语句** | `'_time:1h and src_ip:"*************"'` |
| `externalTimeRange` | Object | **外部时间范围配置** | `{ quickTime: '1h' }` |
| `settings` | Object | **表格列配置和功能设置** | 见使用文档 |
| `stream` | String | **日志流名称** | `'AUDITLOG_H3C_BEHAVIOR'` |

### 集成后端API的推荐实现

```vue
<script>
import { buildLogQL } from '@/api/log/logQuery'
import { convertTimeRange } from '@/utils/timeFormatter'

export default {
  data() {
    return {
      logQueryKey: 1,
      builtLogQLQuery: '',
      timeConfig: {}
    }
  },
  methods: {
    async buildLogQLQuery() {
      const params = {
        stream: 'AUDITLOG_H3C_BEHAVIOR',
        customFilters: [
          { field: 'src_ip', operator: '=', value: this.data.targetIp },
          { field: 'log_type', operator: '=', value: 'web_access' }
        ],
        timeRange: convertTimeRange(this.data.timeRange, '30m')
      }
      
      const response = await buildLogQL(params)
      if (response.code === 200) {
        this.builtLogQLQuery = response.data.logQL
        this.timeConfig = response.data.timeConfig
        this.logQueryKey = Date.now()
      }
    }
  }
}
</script>
```

    <!-- Tab式日志查询 -->
    <div class="log-details-section">
      <div class="custom-tabs-container">
        <div class="custom-tabs">
          <div class="custom-tab-item" :class="{ 'active': activeTab === 'web_access' }">
            网页访问日志
          </div>
        </div>
      </div>
      
      <div class="tab-content">
        <log-query-component
          :defaultQuery="logQueries.web_access"
          :externalTimeRange="timeConfig"
          :settings="webAccessSettings" />
      </div>
    </div>
  </div>
</template>
``` 