# DNS查询分析器使用文档

## 概述

DNS查询分析器是一个专门用于分析特定IP地址DNS查询行为的分析工具。它可以深入分析某个IP的DNS查询模式，帮助识别网络行为特征、安全威胁和异常活动。

## 功能特性

### 1. 多维度统计分析
- **DNS服务器使用分布**：分析目标IP使用的DNS服务器分布情况
- **查询域名统计**：统计最频繁查询的域名Top10
- **查询类型分布**：分析DNS查询类型（A、AAAA、CNAME等）的分布
- **域名分类分析**：自动识别域名类型（开发工具、搜索引擎、社交通讯等）

### 2. 实时日志查询
- 基于VictoriaLogs的高性能日志查询
- 支持灵活的时间范围选择（1小时-7天）
- 实时获取DNS查询详细记录

### 3. 可视化展示
- ECharts图表展示统计数据
- 响应式设计，支持移动端
- 直观的数据展示界面

## 使用方法

### 1. 在主机分析中使用

DNS查询分析器已集成到主机分析系统中，会根据IP类型自动触发：

```bash
# 访问主机分析页面
http://your-domain/analyze/host

# 输入目标IP，系统会自动执行DNS查询分析
```

### 2. 在DNS审计页面中使用

可以在DNS审计页面的"DNS IP分析器"标签页中独立使用：

```bash
# 访问DNS审计页面
http://your-domain/auditlog/dns

# 切换到"DNS IP分析器"标签页
# 输入目标IP和时间范围进行分析
```

### 3. 通过API调用

如果集成了主机分析API，可以通过API获取DNS查询分析结果：

```bash
POST /app/host-analysis/analyze
{
  "hostIp": "*********",
  "timeRange": "1d"
}
```

## 查询参数说明

### 时间范围选项
- `1h` - 最近1小时
- `6h` - 最近6小时  
- `12h` - 最近12小时
- `1d` - 最近1天
- `3d` - 最近3天
- `7d` - 最近7天

### VictoriaLogs查询语句示例

分析器会自动构建如下格式的查询语句：

```bash
# 基本查询格式
_time:1d and stream:"AUDITLOG_DNS_BIND_QUERY" and src_ip:"*********"

# 获取统计数据的facets查询
curl http://*************:9428/select/logsql/facets \
  -d 'query=_time:1d and stream:"AUDITLOG_DNS_BIND_QUERY" and src_ip:"*********"' \
  -d 'limit=50'
```

## 分析结果说明

### 1. 分析摘要
提供DNS查询活动的总体概况：
- 目标IP地址
- 总查询次数
- 唯一域名数量
- 使用的DNS服务器数量

### 2. 统计图表

#### DNS服务器使用分布（饼图）
显示目标IP使用不同DNS服务器的比例，帮助识别：
- 主要使用的DNS服务器
- 是否存在异常的DNS服务器

#### 查询域名Top10（水平柱状图）
展示查询频率最高的10个域名，有助于：
- 识别主要访问的服务
- 发现可疑域名

#### 查询类型分布（环形图）
显示不同DNS查询类型的分布：
- A记录：IPv4地址查询
- AAAA记录：IPv6地址查询
- CNAME记录：别名查询
- MX记录：邮件服务器查询
- 其他类型

#### 域名分类分布（饼图）
自动识别域名所属类别：
- 开发工具（GitHub、GitLab等）
- 搜索引擎（Google、百度等）
- 社交通讯（QQ、微信等）
- 电商平台（淘宝、京东等）
- 视频娱乐（优酷、B站等）
- 办公软件（Office、Adobe等）
- 云服务（AWS、阿里云等）

### 3. 详细日志记录
提供完整的DNS查询记录列表，包含：
- 查询时间
- 查询域名
- 查询类型
- DNS服务器IP
- 源端口

## 技术架构

### 后端实现
- **框架**: Spring Boot
- **分析器**: `DnsQueryAnalyzer`
- **日志服务**: VictoriaLogsService
- **数据解析**: FastJSON

### 前端实现
- **框架**: Vue.js + Element UI
- **图表**: ECharts
- **组件**: `DnsQueryAnalysisResult.vue`、`DnsIpAnalyzer.vue`

### 数据流程
1. 用户输入目标IP和时间范围
2. 构建VictoriaLogs查询语句
3. 调用facets接口获取统计数据
4. 解析并分析数据
5. 生成可视化图表和报告

## 配置说明

### VictoriaLogs配置
确保VictoriaLogs配置正确：

```yaml
victoria:
  logs:
    url: http://*************:9429
    url2: http://*************:9428
```

### 日志流配置
确保DNS查询日志正确流入指定stream：
- Stream名称: `AUDITLOG_DNS_BIND_QUERY`
- 必需字段: `src_ip`, `dst_ip`, `query_domain`, `query_type`, `event_time`

## 故障排除

### 1. 无法获取数据
- 检查VictoriaLogs服务是否正常
- 确认目标IP在指定时间范围内有DNS查询活动
- 验证stream名称是否正确

### 2. 图表不显示
- 检查浏览器控制台是否有JavaScript错误
- 确认ECharts库是否正确加载
- 验证数据格式是否符合预期

### 3. 性能问题
- 适当缩小时间范围
- 调整limit参数
- 检查VictoriaLogs性能状况

## 扩展功能

### 1. 自定义域名分类
可以在`categorizeDomain`方法中添加更多域名分类规则：

```java
private String categorizeDomain(String domain) {
    if (domain == null) return "其他";
    
    String lowerDomain = domain.toLowerCase();
    
    // 添加自定义分类规则
    if (lowerDomain.contains("your-custom-domain")) {
        return "自定义类别";
    }
    
    // ... 其他分类逻辑
}
```

### 2. 添加新的统计维度
可以扩展facets查询，添加更多统计字段：

```java
// 在getFacetsStatistics方法中添加新字段
if ("new_field".equals(fieldName)) {
    facetsData.put("newFieldData", parseFacetValues(facet));
}
```

### 3. 导出功能
可以添加将分析结果导出为Excel或PDF的功能。

## 更新日志

### v1.0.0 (2025-01-20)
- 初始版本发布
- 支持基本的DNS查询分析功能
- 集成VictoriaLogs日志查询
- 提供可视化图表展示
- 支持多种时间范围查询 