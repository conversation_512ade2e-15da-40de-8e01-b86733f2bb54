# IP行为分析功能说明

## 功能概述

IP行为分析功能通过分析VictoriaLogs中的Nginx访问日志，生成目标IP的网络行为桑基图，展示IP之间的流量关系。

## 功能特点

1. **实时数据分析**：基于VictoriaLogs日志系统进行实时数据查询
2. **桑基图可视化**：使用ECharts桑基图直观展示网络流量关系
3. **流量数据表格**：桑基图下方显示详细的流量数据表格，包含流向、来源、目标、请求数和占比信息
4. **主机名显示**：优先显示主机名（hostname），提供更友好的IP展示方式
5. **双向流量分析**：同时分析来源IP到目标IP和目标IP到上游IP的流量
6. **灵活时间选择**：支持精确时间范围选择和快速时间选项
7. **智能分析摘要**：自动生成分析结果摘要和流量统计
8. **时区智能转换**：自动处理东八区时间到UTC时间的转换

## 时区处理机制

### 时区转换逻辑
- **前端时间**：用户在界面上选择的时间默认为东八区时间（Asia/Shanghai）
- **后端转换**：自动将东八区时间转换为UTC时间用于VictoriaLogs查询
- **查询执行**：VictoriaLogs使用UTC时间进行日志查询
- **结果展示**：查询结果转换回东八区时间向用户展示

### 转换示例
```
前端输入: 2024-12-31T14:00:00 (东八区下午2点)
后端转换: 2024-12-31T06:00:00Z (UTC上午6点)
查询条件: _time:[2024-12-31T06:00:00Z,2024-12-31T06:00:00Z]
```

### 支持的时间格式
- `2024-12-31T14:00:00Z` - ISO 8601格式带Z
- `2024-12-31T14:00:00` - ISO 8601格式不带Z
- `2024-12-31 14:00:00` - 简单格式
- `2024-12-31T14:00:00.123Z` - 带毫秒格式

## 技术架构

### 后端组件

#### 1. DTO类
- `IpBehaviorAnalysisRequestDTO`：请求参数封装
- `IpBehaviorAnalysisResponseDTO`：响应结果封装
  - `SankeyChartData`：桑基图数据结构
  - `SankeyNode`：桑基图节点
  - `SankeyLink`：桑基图连接

#### 2. 服务层
- `IpBehaviorAnalysisService`：服务接口
- `IpBehaviorAnalysisServiceImpl`：服务实现类

#### 3. 控制器
- `IpBehaviorAnalysisController`：REST API控制器

#### 4. 工具类
- `TimeFormatUtil`：时间格式转换和时区转换工具

### 前端组件

#### 1. API接口
- `ipBehaviorAnalysis.js`：前端API封装

#### 2. 组件
- `IPFlowSankeyChart`：IP流量桑基图组件（基于ECharts）
- `TimeRangeSelector`：时间范围选择组件

#### 3. 页面
- `attack.vue`：IP行为分析主页面

## 数据流程

1. **用户输入**：用户在前端页面输入目标IP和时间范围（东八区时间）
2. **参数验证**：前端验证IP格式和时间范围有效性
3. **API调用**：前端调用后端分析接口
4. **时区转换**：后端将东八区时间转换为UTC时间
5. **数据查询**：后端使用UTC时间查询VictoriaLogs获取网络流量数据（使用vmlog2实例）
   - 查询来源IP到目标IP的请求统计
   - 查询目标IP到上游IP的请求统计
6. **数据处理**：后端处理查询结果，构建桑基图数据结构
7. **结果返回**：后端返回桑基图数据和分析摘要
8. **图表渲染**：前端使用ECharts渲染桑基图

## VictoriaLogs查询

### 快速时间选择
快速时间选择使用VictoriaLogs的相对时间语法，更高效简洁：
```sql
-- 快速时间选择示例
_time:1h and stream:"NGINX_ACCESS" and message.upstream_ip:"************" | stats by (message.remote_addr) count() cnt | sort by (cnt desc) | limit 10
_time:30m and stream:"NGINX_ACCESS" and message.remote_addr:"************" | stats by (message.upstream_ip) count() cnt | sort by (cnt desc) | limit 10
```

支持的快速时间格式：
- `5m` - 最近5分钟
- `10m` - 最近10分钟  
- `30m` - 最近30分钟
- `1h` - 最近1小时
- `1d` - 最近1天

### 精确时间选择（时区转换后的查询格式）
精确时间选择使用时间范围语法，需要时区转换：
```sql
-- 原始东八区时间: 2024-12-31T14:00:00 到 2024-12-31T16:00:00
-- 转换为UTC时间: 2024-12-31T06:00:00Z 到 2024-12-31T08:00:00Z

-- 来源IP查询
_time:[2024-12-31T06:00:00Z,2024-12-31T08:00:00Z] and stream:"NGINX_ACCESS" and message.upstream_ip:"************" | stats by (message.remote_addr) count() cnt | sort by (cnt desc) | limit 10

-- 上游IP查询  
_time:[2024-12-31T06:00:00Z,2024-12-31T08:00:00Z] and stream:"NGINX_ACCESS" and message.remote_addr:"************" | stats by (message.upstream_ip) count() cnt | sort by (cnt desc) | limit 10
```

**重要说明**：VictoriaLogs的时间范围查询格式要求：
- 时间格式：`yyyy-MM-ddTHH:mm:ssZ`
- 必须包含末尾的 `Z` 标识UTC时间
- 时间范围语法：`_time:[startTimeZ,endTimeZ]`

### 查询逻辑选择
系统会根据用户的选择自动判断使用哪种查询方式：
1. **快速时间选择**：直接使用相对时间语法（如 `_time:1h`）
2. **精确时间选择**：使用时间范围语法，并进行时区转换

**注意**：所有IP行为分析查询都使用VictoriaLogs的`vmlog2`实例，确保访问正确的Nginx日志数据源。

## 桑基图设计

### 节点类型
- **source**：来源IP（红色）
- **target**：目标IP（蓝色）
- **upstream**：上游IP（绿色）

### 连接关系
- 来源IP → 目标IP：请求次数
- 目标IP → 上游IP：请求次数

### 样式配置
- 节点宽度：20px
- 节点间距：30px
- 连接透明度：0.7
- 支持鼠标悬停高亮

### 主机名显示
- 优先显示格式：`hostname (IP)`
- 长主机名截断：`hostname... (IP)`
- 无主机名时显示：`IP`

## 流量数据表格

### 表格功能
桑基图下方自动渲染详细的流量数据表格，采用**双表格并排布局**：

#### 双表格设计
- 🟢 **左侧：入站流量表格** - 显示所有指向目标IP的流量
- 🔵 **右侧：出站流量表格** - 显示所有从目标IP发出的流量
- 📱 **响应式布局** - 小屏幕设备自动调整为上下排列

#### 表格列说明
**入站流量表格**：
1. **来源IP**：流量的起始点，显示图标和主机名/IP信息
2. **方向**：绿色箭头表示入站方向
3. **目标IP**：流量的终点（本机IP），高亮显示
4. **请求数**：该连接的总请求次数
5. **占比**：在总流量中的百分比

**出站流量表格**：
1. **来源IP**：流量的起始点（本机IP），高亮显示
2. **方向**：蓝色箭头表示出站方向  
3. **目标IP**：流量的终点，显示图标和主机名/IP信息
4. **请求数**：该连接的总请求次数
5. **占比**：在总流量中的百分比

#### 视觉特性
- 🎯 **目标IP高亮**：本机IP使用蓝色背景高亮显示
- 🎨 **颜色区分**：入站表格使用绿色主题，出站表格使用蓝色主题
- 📊 **记录统计**：每个表格显示对应的记录数量
- 🔤 **统一字体**：所有表格内容使用12px字体大小

### 流量统计摘要
表格下方显示流量汇总信息：

- **总流量**：所有连接的请求总和
- **入站流量**：目标IP接收的请求总数
- **出站流量**：目标IP发出的请求总数

### 表格特性
- ✅ **双表格布局**：入站和出站流量分离显示，提高可读性
- ✅ **自动排序**：默认按请求数降序排列
- ✅ **响应式设计**：支持移动设备显示，小屏幕自动调整为垂直布局
- ✅ **数据高亮**：目标IP和重要数据使用颜色突出显示
- ✅ **文本省略**：长文本支持tooltip完整显示
- ✅ **主题区分**：入站表格绿色主题，出站表格蓝色主题
- ✅ **美观设计**：渐变背景和现代化UI设计

### 使用场景
1. **流量分析**：通过双表格快速对比入站和出站流量分布
2. **安全监控**：左侧入站表格重点关注潜在攻击来源
3. **性能优化**：右侧出站表格分析上游服务依赖关系
4. **容量规划**：了解双向网络负载分布情况
5. **故障排查**：快速定位网络连接异常的方向和节点

## API接口

### 执行IP行为分析
```
POST /app/ip-behavior-analysis/analyze
```

#### 请求参数
```json
{
  "targetIp": "************",
  "timeRange": "1h",
  "analysisDepth": "basic",
  "timeout": 120
}
```

**快速时间选择示例**：
```json
{
  "targetIp": "************",
  "timeRange": "1h",
  "startTime": "",
  "endTime": "",
  "analysisDepth": "basic",
  "timeout": 120
}
```

**精确时间选择示例**：
```json
{
  "targetIp": "************",
  "timeRange": "precise",
  "startTime": "2024-12-31T14:00:00Z",
  "endTime": "2024-12-31T16:00:00Z",
  "analysisDepth": "basic",
  "timeout": 120
}
```

**参数说明**：
- `targetIp`: 目标IP地址（必填）
- `timeRange`: 时间范围类型
  - 快速选择：`5m`、`10m`、`30m`、`1h`、`1d` 等
  - 精确选择：`precise`
- `startTime`: 开始时间（精确选择时必填，东八区时间）
- `endTime`: 结束时间（精确选择时必填，东八区时间）
- `analysisDepth`: 分析深度（`basic`、`deep`、`comprehensive`）
- `timeout`: 超时时间（秒）

#### 响应结果
```json
{
  "code": 200,
  "msg": "IP行为分析完成",
  "data": {
    "targetIp": "************",
    "timeRange": "2024-12-31T14:00:00Z ~ 2024-12-31T16:00:00Z",
    "generateTime": "2024-12-31 14:30:00",
    "analysisSummary": "IP ************ 在分析时间段内共接收来自 8 个来源IP的 1,234,567 次请求，向 6 个上游主机发起了 987,654 次请求。",
    "sankeyData": {
      "nodes": [
        {"name": "***************", "type": "source"},
        {"name": "************", "type": "target"},
        {"name": "************", "type": "upstream"}
      ],
      "links": [
        {"source": "***************", "target": "************", "value": 470630},
        {"source": "************", "target": "************", "value": 269363}
      ]
    }
  }
}
```

## 配置选项

### 分析深度
- **basic**：基础分析（默认）
- **deep**：深度分析
- **comprehensive**：全面分析

### 超时设置
- 默认值：120秒
- 范围：30-600秒

## 使用说明

1. **进入分析页面**：导航到"分析" -> "IP行为分析"
2. **输入参数**：
   - 输入目标IP地址
   - 选择时间范围（支持快速选择或精确时间，自动处理时区转换）
3. **配置选项**：点击"配置选项"按钮设置分析深度和超时时间
4. **开始分析**：点击"开始分析"按钮
5. **查看结果**：
   - 查看分析摘要了解整体情况
   - 通过桑基图观察流量关系
   - 鼠标悬停查看详细数据

## 故障排除

### 常见问题

1. **无网络流量数据**
   - 检查目标IP是否正确
   - 确认时间范围内是否有日志数据（注意时区转换）
   - 验证VictoriaLogs连接状态

2. **时区相关问题**
   - 确认前端传递的时间格式正确
   - 检查后端时区转换日志
   - 验证VictoriaLogs中的时间戳格式

3. **查询超时**
   - 缩小时间范围
   - 增加超时时间设置
   - 检查VictoriaLogs性能

4. **桑基图显示异常**
   - 检查浏览器控制台错误
   - 确认ECharts库已正确加载
   - 验证数据格式正确性

### 时区调试

#### 后端日志调试
设置日志级别为DEBUG可查看详细的时区转换过程：
```yaml
logging:
  level:
    com.wiwj.securio.app.util.TimeFormatUtil: DEBUG
    com.wiwj.securio.app.service.impl.IpBehaviorAnalysisServiceImpl: DEBUG
```

#### 时区转换测试
可以使用测试类验证时区转换：
```java
// 运行测试
TimeFormatUtilTest.main(new String[]{});

// 手动测试特定时间
TimeFormatUtil.testTimeConversion("2024-12-31T14:00:00Z");
```

### 时区转换示例
```
输入时间（东八区）: 2024-12-31T14:00:00Z
转换为UTC时间: 2024-12-31T06:00:00Z
VictoriaLogs查询: _time:[2024-12-31T06:00:00Z,2024-12-31T06:00:00Z]

说明：东八区比UTC快8小时，所以：
- 东八区 14:00 = UTC 06:00
- 转换公式：UTC时间 = 东八区时间 - 8小时
- VictoriaLogs时间范围查询必须包含末尾的Z
```

## 扩展功能

### 计划中的功能
1. **威胁情报集成**：结合威胁情报库标记恶意IP
2. **行为模式识别**：识别异常访问模式
3. **历史对比分析**：与历史数据进行对比
4. **导出功能**：支持导出分析报告
5. **实时监控**：支持实时流量监控
6. **多时区支持**：支持更多时区的自动转换

### 自定义扩展
- 支持自定义查询条件
- 支持多维度流量分析
- 支持自定义图表样式
- 支持自定义时区设置 