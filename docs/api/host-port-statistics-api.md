# 主机端口统计API文档

## 获取主机端口统计信息

### 接口描述

根据主机IP获取主机的端口统计信息，包括端口状态分布和外部连接详情。

### 请求方式

```
GET /app/host/stat/port/{hostIp}
```

### 路径参数

| 参数名 | 类型   | 是否必须 | 描述     |
| ------ | ------ | -------- | -------- |
| hostIp | String | 是       | 主机的IP地址 |

### 响应参数

| 参数名   | 类型   | 描述         |
| -------- | ------ | ------------ |
| code     | Integer| 状态码，200表示成功 |
| message  | String | 响应消息     |
| data     | Object | 响应数据     |

### data参数说明

| 参数名      | 类型   | 描述         |
| ----------- | ------ | ------------ |
| portStats   | Object | 端口状态统计数据，key为状态名称，value为数量 |
| connections | Array  | 外部连接详情列表 |
| totalPorts  | Integer| 总端口数     |

### connections元素说明

| 参数名      | 类型    | 描述         |
| ----------- | ------- | ------------ |
| localIp     | String  | 本地IP地址   |
| localPort   | Integer | 本地端口号   |
| remoteIp    | String  | 远程IP地址   |
| remotePort  | Integer | 远程端口号   |
| status      | String  | 连接状态     |
| protocol    | String  | 协议类型     |
| processName | String  | 进程名       |
| pid         | Integer | 进程ID       |

### 示例

#### 请求

```
GET /app/host/stat/port/*************
```

#### 成功响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "portStats": {
      "LISTEN": 10,
      "ESTABLISHED": 5,
      "CLOSE_WAIT": 2,
      "TIME_WAIT": 0,
      "CLOSED": 0
    },
    "connections": [
      {
        "localIp": "*************",
        "localPort": 8080,
        "remoteIp": "*************",
        "remotePort": 5000,
        "status": "ESTABLISHED",
        "protocol": "tcp",
        "processName": "java",
        "pid": 12345
      },
      {
        "localIp": "*************",
        "localPort": 22,
        "remoteIp": "*************",
        "remotePort": 54321,
        "status": "ESTABLISHED",
        "protocol": "tcp",
        "processName": "sshd",
        "pid": 678
      }
    ],
    "totalPorts": 17
  }
}
```

#### 失败响应

```json
{
  "code": 500,
  "message": "获取端口统计信息失败: 内部服务器错误"
}
```

### 备注

- 端口状态包括：LISTEN, ESTABLISHED, CLOSE_WAIT, TIME_WAIT, CLOSED等
- 如果某个状态不存在任何端口，其值将为0
- 总端口数计算自所有状态的端口数之和
