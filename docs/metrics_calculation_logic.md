# Securio 系统指标计算逻辑文档

本文档详细说明了 Securio 系统中各项指标的计算逻辑，包括数据来源、计算方法和展示方式。

## 目录

1. [日志统计指标](#1-日志统计指标)
2. [性能指标](#2-性能指标)
3. [吞吐量指标](#3-吞吐量指标)
4. [错误统计指标](#4-错误统计指标)
5. [组件健康状态指标](#5-组件健康状态指标)
6. [日志分布指标](#6-日志分布指标)

## 1. 日志统计指标

### 1.1 数据来源

日志统计指标通过查询 VictoriaMetrics 时序数据库中的 `securio_log_stats_total` 指标获取，按日志类型（log_type）进行分组。

### 1.2 计算方法

#### 1.2.1 总日志数

```java
String totalUrl = victoriaMetricsUrl + "/api/v1/query?query=sum(increase(securio_log_stats_total[100y])) by (log_type)";
```

使用 `increase` 函数计算从数据库开始记录到现在的所有增量，相当于获取总量。

#### 1.2.2 今日日志数

```java
// 获取今天 0 点的时间
Calendar today = Calendar.getInstance();
today.set(Calendar.HOUR_OF_DAY, 0);
today.set(Calendar.MINUTE, 0);
today.set(Calendar.SECOND, 0);
today.set(Calendar.MILLISECOND, 0);
long todayStartTimeMillis = today.getTimeInMillis();
long currentTimeMillis = System.currentTimeMillis()+1000*60*60;

// 计算从今天 0 点到现在的时间范围（秒）
long todayDurationSeconds = (currentTimeMillis - todayStartTimeMillis) / 1000;
String todayUrl = victoriaMetricsUrl + "/api/v1/query?query=sum(increase(securio_log_stats_total[" + todayDurationSeconds + "s])) by (log_type)";
```

计算从今天 0 点到当前时间的日志增量。

#### 1.2.3 昨日日志数

```java
// 获取昨天 0 点的时间
Calendar yesterday = Calendar.getInstance();
yesterday.set(Calendar.HOUR_OF_DAY, 0);
yesterday.set(Calendar.MINUTE, 0);
yesterday.set(Calendar.SECOND, 0);
yesterday.set(Calendar.MILLISECOND, 0);
yesterday.add(Calendar.DAY_OF_MONTH, -1);
long yesterdayStartTimeSec = yesterday.getTimeInMillis() / 1000;
long todayStartTimeSec = todayStartTimeMillis / 1000;

// 计算昨天一整天的时间范围（秒）
long yesterdayDurationSeconds = todayStartTimeSec - yesterdayStartTimeSec;
String yesterdayUrl = victoriaMetricsUrl + "/api/v1/query?query=sum(increase(securio_log_stats_total[" + yesterdayDurationSeconds + "s] offset " + yesterdayDurationSeconds + "s)) by (log_type)";
```

使用 `offset` 参数计算昨天一整天（从昨天 0 点到今天 0 点）的日志增量。

## 2. 性能指标

### 2.1 数据来源

性能指标通过查询 VictoriaMetrics 时序数据库中的多个指标获取：
- 平均处理时间：`securio_component_avg_processing_time`
- 每分钟处理量：`securio_log_stats_minute`
- 每小时处理量：`securio_log_stats_hour`
- 每天处理量：`securio_log_stats_day`

### 2.2 计算方法

#### 2.2.1 平均处理时间

```java
String avgTimeQuery = "avg(securio_component_avg_processing_time)";
```

计算所有组件的平均处理时间（纳秒）。

#### 2.2.2 每分钟处理量

```java
String rateMinuteQuery = "sum(securio_log_stats_minute)";
```

获取系统每分钟处理的日志总量。

#### 2.2.3 每小时处理量

```java
String rateHourQuery = "sum(securio_log_stats_hour)";
```

获取系统每小时处理的日志总量。

#### 2.2.4 每天处理量

```java
String rateDayQuery = "sum(securio_log_stats_day)";
```

获取系统每天处理的日志总量。

## 3. 吞吐量指标

### 3.1 数据来源

吞吐量指标根据选择的时间单位，查询不同的 VictoriaMetrics 指标：
- 每分钟吞吐量：`securio_log_stats_minute`
- 每小时吞吐量：`securio_log_stats_hour`
- 每天吞吐量：`securio_log_stats_day`

### 3.2 计算方法

#### 3.2.1 选择指标

```java
String metricName;
switch (timeUnit) {
    case "minute":
        metricName = "securio_log_stats_minute";
        break;
    case "hour":
        metricName = "securio_log_stats_hour";
        break;
    case "day":
        metricName = "securio_log_stats_day";
        break;
    default:
        metricName = "securio_log_stats_hour"; // 默认使用小时
}
```

#### 3.2.2 总吞吐量

```java
String totalQuery = "sum(" + metricName + ")";
```

计算所有日志类型的总吞吐量。

#### 3.2.3 各日志类型吞吐量

```java
String typeQuery = metricName;
```

获取每种日志类型的吞吐量。

#### 3.2.4 统计计算

```java
// 按吞吐量排序
logTypeThroughputs.sort((a, b) -> Long.compare(b.getThroughput(), a.getThroughput()));

// 计算平均吞吐量、最大吞吐量和最小吞吐量
if (!logTypeThroughputs.isEmpty()) {
    long sum = 0;
    long max = Long.MIN_VALUE;
    long min = Long.MAX_VALUE;

    for (ThroughputDTO.LogTypeThroughput typeThroughput : logTypeThroughputs) {
        long throughput = typeThroughput.getThroughput();
        sum += throughput;
        max = Math.max(max, throughput);
        min = Math.min(min, throughput);
    }

    dto.setAvgThroughput((double) sum / logTypeThroughputs.size());
    dto.setMaxThroughput(max);
    dto.setMinThroughput(min);
}
```

计算平均吞吐量、最大吞吐量和最小吞吐量，并按吞吐量从高到低排序。

## 4. 错误统计指标

### 4.1 数据来源

错误统计指标通过查询 VictoriaMetrics 时序数据库中的以下指标获取：
- 总错误数：`securio_component_errors`
- 总丢弃数：`securio_component_dropped`
- 总接收数：`securio_component_received`

### 4.2 计算方法

#### 4.2.1 总错误数

```java
String errorsQuery = "sum(securio_component_errors)";
```

计算所有组件的错误总数。

#### 4.2.2 总丢弃数

```java
String droppedQuery = "sum(securio_component_dropped)";
```

计算所有组件的丢弃消息总数。

#### 4.2.3 错误率和丢弃率

```java
String receivedQuery = "sum(securio_component_received)";
// ...
if (totalReceived > 0) {
    dto.setErrorRate(dto.getTotalErrors() * 100.0 / totalReceived);
    dto.setDroppedRate(dto.getTotalDropped() * 100.0 / totalReceived);
}
```

错误率 = 总错误数 / 总接收数 * 100%
丢弃率 = 总丢弃数 / 总接收数 * 100%

## 5. 组件健康状态指标

### 5.1 数据来源

组件健康状态指标通过查询 VictoriaMetrics 时序数据库中的以下指标获取：
- 接收消息数：`securio_component_received`
- 处理消息数：`securio_component_processed`
- 错误数：`securio_component_errors`
- 丢弃消息数：`securio_component_dropped`
- 平均处理时间：`securio_component_avg_processing_time`

### 5.2 计算方法

#### 5.2.1 健康状态判断

```java
// 设置健康状态
for (ComponentStatDTO dto : componentMap.values()) {
    if (dto.getErrors() > 0 || dto.getDropped() > 0) {
        dto.setHealthStatus("异常");
    } else if (dto.getReceived() > 0 && dto.getProcessed() == 0) {
        dto.setHealthStatus("阻塞");
    } else {
        dto.setHealthStatus("正常");
    }
}
```

- 异常：组件有错误或丢弃消息
- 阻塞：组件有接收消息但没有处理消息
- 正常：其他情况

## 6. 日志分布指标

### 6.1 数据来源

日志分布指标通过查询 VictoriaMetrics 时序数据库中的 `securio_log_stats_total` 指标获取，按日志类型（log_type）进行分组。

### 6.2 计算方法

#### 6.2.1 日志类型分布

计算每种日志类型的数量和百分比。

#### 6.2.2 日志处理量排名

按处理量对日志类型进行排序，获取排名数据。

## 总结

Securio 系统的指标计算主要基于 VictoriaMetrics 时序数据库中存储的各种指标，通过不同的查询和计算方法获取各类统计数据，为系统监控和性能分析提供支持。所有指标都通过 RESTful API 提供给前端，并在仪表盘中以图表和数据卡片的形式展示。
