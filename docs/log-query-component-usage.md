# LogQueryComponent 使用文档

## 概述

`LogQueryComponent` 是一个通用的日志查询组件，提供了完整的日志查询、过滤、展示功能。支持LogQL查询语法，可以与VictoriaLogs集成查询日志数据。

## 组件位置

```
securio-ui/src/components/log_query_component.vue
```

## 基本用法

```vue
<template>
  <log-query-component
    :title="'日志查询'"
    :subtitle="'查询详细日志信息'"
    :stream="'YOUR_LOG_STREAM'"
    :settings="logSettings"
  />
</template>

<script>
import LogQueryComponent from '@/components/log_query_component'

export default {
  components: {
    LogQueryComponent
  },
  data() {
    return {
      logSettings: {
        columns: [
          { prop: '_time', label: '时间', width: '180', sortable: true },
          { prop: '_msg', label: '消息', showOverflowTooltip: true }
        ]
      }
    }
  }
}
</script>
```

## Props 参数

### 基本参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `title` | String | `'日志查询'` | 组件标题 |
| `subtitle` | String | `''` | 组件副标题 |
| `stream` | String | `''` | 日志流名称，会自动添加到查询条件中 |
| `instance` | String | `'vmlog1'` | VictoriaLogs实例名称 |

### 查询参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `defaultQuery` | String | `''` | **默认LogQL查询语句**，组件初始化时使用 |
| `defaultTimeRange` | String | `'5m'` | 默认时间范围（如：'5m', '1h', '1d'） |
| `externalTimeRange` | Object | `null` | **外部时间范围配置**，用于动态设置时间 |

### 显示配置

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `settings` | Object | 见下文 | **表格和功能配置** |

## settings 配置详解

```javascript
const settings = {
  // 表格列配置（必须）
  columns: [
    {
      prop: '_time',              // 字段名
      label: '时间',               // 显示标签
      width: '180',               // 列宽
      sortable: true,             // 是否可排序
      showOverflowTooltip: true,  // 内容过长时显示tooltip
      fixed: 'left',              // 固定列：'left' | 'right'
      minWidth: '120',            // 最小宽度
      tag: false,                 // 是否显示为标签样式
      formatter: (row, column, value) => {  // 自定义格式化函数
        return value
      }
    },
    {
      prop: '_msg',
      label: '消息',
      showOverflowTooltip: true,
      className: 'log-message'
    }
  ],
  
  // 详情按钮配置
  showDetailButton: true,         // 是否显示详情按钮
  detailButtonWidth: '100',       // 详情按钮列宽
  detailButtonFixed: 'right',     // 详情按钮固定位置
  
  // 默认过滤条件
  defaultFilters: [
    {
      field: 'src_ip',            // 过滤字段
      operator: '=',              // 操作符：'=' | '~=' | '^=' | '$='
      value: '***********'        // 过滤值
    }
  ]
}
```

## 高级用法

### 1. 使用默认查询语句

适用于需要预设查询条件的场景：

```vue
<template>
  <log-query-component
    :title="'预设查询'"
    :defaultQuery="prebuiltQuery"
    :stream="'AUDITLOG_NGINX'"
    :settings="logSettings"
  />
</template>

<script>
export default {
  data() {
    return {
      prebuiltQuery: '_time:1h and src_ip:"*************" and status:200'
    }
  }
}
</script>
```

### 2. 外部时间范围控制

适用于需要从父组件动态设置时间范围的场景：

```vue
<template>
  <log-query-component
    :title="'时间同步查询'"
    :externalTimeRange="timeConfig"
    :stream="'AUDITLOG_SYSTEM'"
    :settings="logSettings"
  />
</template>

<script>
export default {
  data() {
    return {
      timeConfig: {
        // 方式1：快速时间选择
        quickTime: '1h'
      },
      // 方式2：自定义时间范围
      // timeConfig: {
      //   startTime: '2024-01-01 00:00:00',
      //   endTime: '2024-01-01 23:59:59'
      // }
    }
  }
}
</script>
```

### 3. 监听时间范围变化

```vue
<template>
  <log-query-component
    ref="logQuery"
    :externalTimeRange="timeConfig"
    :settings="logSettings"
  />
</template>

<script>
export default {
  watch: {
    // 监听外部数据变化，更新时间范围
    analysisData: {
      handler(newData) {
        if (newData && newData.timeRange) {
          this.updateTimeConfig(newData.timeRange)
        }
      },
      deep: true
    }
  },
  methods: {
    updateTimeConfig(timeRange) {
      // 转换时间范围格式
      this.timeConfig = {
        quickTime: this.convertTimeRange(timeRange)
      }
    },
    
    // 获取当前组件的时间配置
    getCurrentTimeConfig() {
      if (this.$refs.logQuery) {
        return this.$refs.logQuery.getCurrentTimeConfig()
      }
      return null
    }
  }
}
</script>
```

### 4. 结合后端API构建查询

推荐的最佳实践：

```vue
<template>
  <log-query-component
    ref="logQuery"
    :key="logQueryKey"
    :title="'智能查询'"
    :subtitle="`分析IP ${targetIp} 的行为记录`"
    :defaultQuery="builtLogQLQuery"
    :externalTimeRange="timeConfig"
    :settings="logSettings"
  />
</template>

<script>
import { buildLogQL } from '@/api/log/logQuery'
import { convertTimeRange } from '@/utils/timeFormatter'

export default {
  data() {
    return {
      logQueryKey: 1,
      builtLogQLQuery: '',
      timeConfig: {},
      targetIp: '*************'
    }
  },
  async created() {
    await this.buildLogQLQuery()
  },
  methods: {
    async buildLogQLQuery() {
      try {
        const params = {
          stream: 'AUDITLOG_H3C_BEHAVIOR',
          srcIp: this.targetIp,
          timeRange: convertTimeRange('最近1小时', '1h'),
          customFilters: [
            { field: 'log_type', operator: '=', value: 'web_access' }
          ]
        }
        
        const response = await buildLogQL(params)
        if (response.code === 200) {
          this.builtLogQLQuery = response.data.logQL
          this.timeConfig = response.data.timeConfig
          
          // 触发组件更新
          this.logQueryKey = Date.now()
        }
      } catch (error) {
        console.error('构建LogQL失败:', error)
      }
    }
  }
}
</script>
```

## 常见字段配置示例

### 1. 基础系统日志

```javascript
const systemLogSettings = {
  columns: [
    { prop: '_time', label: '时间', width: '180', sortable: true },
    { prop: 'level', label: '级别', width: '80', tag: true },
    { prop: 'service', label: '服务', width: '120' },
    { prop: '_msg', label: '消息', showOverflowTooltip: true }
  ]
}
```

### 2. 网络访问日志

```javascript
const accessLogSettings = {
  columns: [
    { prop: '_time', label: '时间', width: '180', sortable: true },
    { prop: 'src_ip', label: '源IP', width: '140' },
    { prop: 'dst_ip', label: '目标IP', width: '140' },
    { prop: 'dst_port', label: '端口', width: '80' },
    { prop: 'protocol', label: '协议', width: '80', tag: true },
    { prop: 'status', label: '状态', width: '80', tag: true },
    { prop: '_msg', label: '详情', showOverflowTooltip: true }
  ]
}
```

### 3. 带自定义格式化的配置

```javascript
const customLogSettings = {
  columns: [
    { 
      prop: '_time', 
      label: '时间', 
      width: '180', 
      formatter: (row, column, value) => {
        return new Date(value).toLocaleString()
      }
    },
    {
      prop: 'size',
      label: '大小',
      width: '100',
      formatter: (row, column, value) => {
        return value ? `${(value / 1024).toFixed(2)} KB` : '-'
      }
    }
  ]
}
```

## 注意事项

### 1. 参数优先级

- `defaultQuery` 优先级最高，如果设置了此参数，组件将直接使用它
- 如果没设置 `defaultQuery`，组件会根据 `stream`、`defaultTimeRange` 等参数自动构建查询

### 2. 时间格式

- 时间范围参数必须使用VictoriaLogs支持的格式：`5m`, `1h`, `1d`, `30d` 等
- 自定义时间必须使用格式：`yyyy-MM-dd HH:mm:ss`

### 3. 性能考虑

- 大数据量查询时建议设置合理的时间范围
- 组件默认限制返回50条记录，可通过界面调整
- 复杂查询建议使用后端API构建LogQL

### 4. 错误处理

```vue
<script>
export default {
  methods: {
    async buildLogQLQuery() {
      try {
        // API调用
      } catch (error) {
        console.error('LogQL构建失败:', error)
        this.$message.error('查询构建失败，请检查参数配置')
        
        // 降级处理：使用基础查询
        this.builtLogQLQuery = `_stream:{stream="${this.stream}"} _time:1h`
      }
    }
  }
}
</script>
```

## 实际使用案例

### 案例1：DNS查询分析

```vue
<template>
  <log-query-component
    :title="'DNS查询日志'"
    :subtitle="`查询分析IP ${targetIp} 的DNS查询记录`"
    :defaultQuery="dnsQuery"
    :externalTimeRange="timeConfig"
    :settings="dnsLogSettings"
  />
</template>

<script>
export default {
  data() {
    return {
      targetIp: '*************',
      dnsQuery: '',
      timeConfig: { quickTime: '1h' },
      dnsLogSettings: {
        columns: [
          { prop: 'event_time', label: '查询时间', width: '180', sortable: true },
          { prop: 'query_domain', label: '查询域名', minWidth: '200', showOverflowTooltip: true },
          { prop: 'query_type', label: '查询类型', width: '100', tag: true },
          { prop: 'dst_ip', label: 'DNS服务器', width: '140' },
          { prop: 'src_port', label: '源端口', width: '100' }
        ]
      }
    }
  },
  async created() {
    await this.buildDnsQuery()
  },
  methods: {
    async buildDnsQuery() {
      const params = {
        stream: 'AUDITLOG_DNS_QUERY',
        srcIp: this.targetIp,
        timeRange: 'recent_1h'
      }
      
      const response = await buildLogQL(params)
      if (response.code === 200) {
        this.dnsQuery = response.data.logQL
        this.timeConfig = response.data.timeConfig
      }
    }
  }
}
</script>
```

### 案例2：多Tab日志查询

```vue
<template>
  <div class="multi-tab-logs">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="网页访问" name="web_access">
        <log-query-component
          :key="logQueryKeys.web_access"
          :title="'网页访问日志'"
          :defaultQuery="logQueries.web_access"
          :settings="webAccessSettings"
        />
      </el-tab-pane>
      
      <el-tab-pane label="应用访问" name="app_access">
        <log-query-component
          :key="logQueryKeys.app_access"
          :title="'应用访问日志'"
          :defaultQuery="logQueries.app_access"
          :settings="appAccessSettings"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'web_access',
      logQueryKeys: {
        web_access: 1,
        app_access: 1
      },
      logQueries: {
        web_access: '',
        app_access: ''
      }
    }
  },
  async created() {
    await this.buildAllLogQueries()
  },
  methods: {
    async buildAllLogQueries() {
      await Promise.all([
        this.buildLogQLForTab('web_access'),
        this.buildLogQLForTab('app_access')
      ])
    },
    
    async buildLogQLForTab(tabType) {
      const params = {
        stream: 'AUDITLOG_H3C_BEHAVIOR',
        srcIp: this.targetIp,
        timeRange: 'recent_1h',
        customFilters: [
          { field: 'log_type', operator: '=', value: tabType }
        ]
      }
      
      const response = await buildLogQL(params)
      if (response.code === 200) {
        this.logQueries[tabType] = response.data.logQL
        this.logQueryKeys[tabType] = Date.now()
      }
    }
  }
}
</script>
```

## 总结

`LogQueryComponent` 是一个功能强大且灵活的日志查询组件。通过合理配置参数，可以满足各种日志查询和展示需求。建议：

1. **使用后端API构建LogQL**：确保查询语法正确和安全
2. **合理配置settings**：根据日志类型选择合适的字段和显示方式
3. **注意性能优化**：大数据量查询时设置合理的时间范围和条数限制
4. **处理异常情况**：提供降级方案和错误提示 