# TimeRangeSelector 时间筛选组件

一个可复用的时间筛选组件，包含精确时间选择器和可拖拽的时间线滑块功能。

## 功能特性

- 精确时间选择器
- 可拖拽的时间线滑块
- 可调整时间范围大小
- 防抖处理，避免频繁触发事件
- 响应式设计，支持移动端
- 高度可配置

## 基本用法

```vue
<template>
  <div>
    <!-- 基本用法 -->
    <time-range-selector
      @time-change="handleTimeChange"
    />
    
    <!-- 自定义配置 -->
    <time-range-selector
      ref="timeSelector"
      label="选择时间"
      :show-timeline="true"
      :default-duration="10"
      :drag-sensitivity="0.3"
      @time-change="handleTimeChange"
    />
  </div>
</template>

<script>
import TimeRangeSelector from '@/components/TimeRangeSelector'

export default {
  components: {
    TimeRangeSelector
  },
  data() {
    return {
      currentTimeData: null
    }
  },
  methods: {
    handleTimeChange(timeData) {
      this.currentTimeData = timeData;
      console.log('时间数据:', timeData);
      
      if (timeData) {
        console.log('选择时间:', timeData.selectedTime);
        console.log('滑块中心时间:', timeData.sliderCenterTime);
        console.log('时间范围:', timeData.timeRange);
      }
    },
    
    // 获取当前时间数据
    getCurrentTimeData() {
      return this.$refs.timeSelector.getTimeData();
    },
    
    // 设置时间数据
    setTimeData(timeData) {
      this.$refs.timeSelector.setTimeData(timeData);
    }
  }
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| label | String | '精确时间' | 时间选择器的标签文本 |
| showTimeline | Boolean | true | 是否显示时间线滑块 |
| defaultTime | String | null | 默认时间（格式：yyyy-MM-dd HH:mm:ss） |
| defaultDuration | Number | 4 | 默认时间范围持续时间（分钟） |
| dragSensitivity | Number | 0.5 | 拖动灵敏度（0-1之间，值越小越不灵敏） |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| time-change | timeData | 时间变化时触发 |

### timeData 数据结构

```javascript
{
  selectedTime: '2024-01-01 12:00:00',      // 用户选择的中心时间点
  sliderCenterTime: '2024-01-01 12:05:00',  // 滑块中心时间点
  timeRange: {                               // 时间范围
    start: '2024-01-01 11:58:00',
    end: '2024-01-01 12:02:00',
    duration: 4                              // 持续时间（分钟）
  },
  sliderOffsetPercent: 5.2                   // 滑块偏移百分比
}
```

## Methods 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getTimeData | - | Object | 获取当前时间数据 |
| setTimeData | timeData | - | 设置时间数据 |

## 使用场景

### 1. 日志查询页面

```vue
<template>
  <div>
    <time-range-selector
      label="查询时间"
      :default-duration="5"
      @time-change="handleLogTimeChange"
    />
    <!-- 日志列表 -->
  </div>
</template>

<script>
export default {
  methods: {
    handleLogTimeChange(timeData) {
      // 构建日志查询条件
      if (timeData) {
        this.buildLogQuery(timeData);
      }
    },
    
    buildLogQuery(timeData) {
      const center = new Date(timeData.sliderCenterTime);
      const offsetMinutes = Math.ceil(timeData.timeRange.duration / 2);
      
      // 构建查询语句
      const query = `_time:${center.toISOString()} offset ${offsetMinutes}m`;
      this.executeLogQuery(query);
    }
  }
}
</script>
```

### 2. 监控图表页面

```vue
<template>
  <div>
    <time-range-selector
      label="监控时间"
      :show-timeline="false"
      @time-change="handleMonitorTimeChange"
    />
    <!-- 监控图表 -->
  </div>
</template>

<script>
export default {
  methods: {
    handleMonitorTimeChange(timeData) {
      // 更新图表时间范围
      if (timeData) {
        this.updateChartTimeRange(timeData.timeRange);
      }
    }
  }
}
</script>
```

### 3. 数据分析页面

```vue
<template>
  <div>
    <time-range-selector
      label="分析时间段"
      :default-duration="30"
      :drag-sensitivity="0.3"
      @time-change="handleAnalysisTimeChange"
    />
    <!-- 分析结果 -->
  </div>
</template>

<script>
export default {
  methods: {
    handleAnalysisTimeChange(timeData) {
      // 执行数据分析
      if (timeData) {
        this.performAnalysis(timeData);
      }
    }
  }
}
</script>
```

## 样式自定义

组件使用了 scoped 样式，如需自定义样式，可以通过以下方式：

```vue
<style>
/* 自定义时间选择器样式 */
.time-range-selector .time-selector label {
  color: #333;
  font-weight: bold;
}

/* 自定义时间线样式 */
.time-range-selector .timeline-range-box {
  border-color: #67c23a;
  background: rgba(103, 194, 58, 0.2);
}
</style>
```

## 注意事项

1. 时间格式统一使用 `yyyy-MM-dd HH:mm:ss` 格式
2. 时间线总跨度固定为2小时（前后各1小时）
3. 时间范围持续时间限制在2-60分钟之间
4. 拖拽和调整大小操作有800ms的防抖延迟
5. 组件会自动处理时区转换（东八区） 