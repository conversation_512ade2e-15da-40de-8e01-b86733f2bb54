# 网宿WAF攻击日志审计系统使用指南

## 概述

网宿WAF攻击日志审计系统是一个专门用于分析和监控网宿CDN WAF攻击事件的安全审计平台。系统通过对攻击日志的实时收集、统计分析和可视化展示，帮助安全团队快速识别威胁、分析攻击趋势并制定防护策略。

## 功能特性

### 1. 攻击统计概览
- **总攻击次数**: 指定时间范围内的攻击事件总数
- **独立攻击IP**: 发起攻击的唯一IP地址数量
- **攻击类型数**: 检测到的不同攻击类型种类
- **被攻击域名**: 受到攻击的域名数量
- **高危攻击数**: 被标记为高危险等级的攻击事件数

### 2. 攻击趋势分析
- **时间序列图表**: 展示攻击活动的时间分布趋势
- **多维度指标**: 支持攻击次数、独立IP数、被攻击域名数等指标
- **攻击类型过滤**: 可按特定攻击类型进行趋势分析
- **灵活时间步长**: 支持5分钟到1天的不同时间粒度

### 3. Top攻击IP排名
- **威胁IP识别**: 自动识别攻击频次最高的IP地址
- **地理位置信息**: 显示攻击来源的省份和城市
- **威胁等级评估**: 根据攻击频次自动评估威胁等级
- **快速分析功能**: 一键分析特定IP的攻击行为

### 4. 多维度图表分析
- **攻击类型分布**: 饼图展示各类攻击的占比
- **处理动作分布**: 显示WAF的处理策略分布
- **被攻击域名分析**: 识别最常被攻击的目标
- **地域分布统计**: 攻击来源的地理分布
- **请求方法分析**: HTTP方法的攻击分布
- **时间分布模式**: 24小时攻击活动模式

### 5. 日志详细探查
- **实时日志查询**: 支持复杂条件的日志搜索
- **字段级过滤**: 可按攻击IP、类型、域名等字段筛选
- **详情查看**: 提供攻击事件的完整详细信息
- **导出功能**: 支持查询结果的导出

## 系统架构

```
攻击事件 → 网宿WAF → 日志收集 → VictoriaLogs → 后端API → 前端展示
```

### 数据流向
1. **攻击检测**: 网宿WAF检测并记录攻击事件
2. **日志收集**: 攻击日志通过数据管道收集到VictoriaLogs
3. **数据处理**: 后端API从VictoriaLogs查询和聚合数据
4. **可视化展示**: 前端界面展示统计图表和详细信息

### 关键组件
- **VictoriaLogs**: 高性能日志存储和查询引擎
- **后端API**: Spring Boot应用，提供数据聚合和查询接口
- **前端界面**: Vue.js应用，提供交互式数据可视化

## API接口文档

### 1. 攻击统计接口
```http
GET /opslog/wangsu-attack/stats?timeRange={timeRange}
```

**参数说明**:
- `timeRange`: 时间范围 (5m, 30m, 1h, 6h, 12h, 1d, 3d, 7d)

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "totalAttacks": 1523,
    "uniqueIps": 342,
    "attackTypes": 8,
    "attackedHosts": 12,
    "highRiskAttacks": 89
  }
}
```

### 2. 图表数据接口
```http
GET /opslog/wangsu-attack/charts?timeRange={timeRange}&limit={limit}
```

**参数说明**:
- `timeRange`: 时间范围
- `limit`: 返回记录数限制 (默认1000)

### 3. 攻击趋势接口
```http
GET /opslog/wangsu-attack/timeseries?start={start}&end={end}&step={step}&metric={metric}&attackType={attackType}
```

**参数说明**:
- `start`: 开始时间 (ISO 8601格式)
- `end`: 结束时间 (ISO 8601格式)
- `step`: 时间步长 (5m, 15m, 30m, 1h, 3h, 6h, 12h, 1d)
- `metric`: 统计指标 (count, unique_ips, unique_hosts)
- `attackType`: 攻击类型过滤 (可选)

### 4. Top攻击IP接口
```http
GET /opslog/wangsu-attack/top-ips?timeRange={timeRange}&limit={limit}
```

**参数说明**:
- `timeRange`: 时间范围
- `limit`: 返回记录数限制 (默认20)

**响应示例**:
```json
{
  "code": 200,
  "data": [
    {
      "rank": 1,
      "ip": "**************",
      "province": "浙江",
      "city": "杭州",
      "count": 245
    }
  ]
}
```

## 攻击类型说明

### 常见攻击类型
- **BOT_RATE_LIMIT**: 机器人流量限制触发
- **SQL_INJECTION**: SQL注入攻击
- **XSS**: 跨站脚本攻击
- **CSRF**: 跨站请求伪造
- **CMD_INJECTION**: 命令注入攻击
- **PATH_TRAVERSAL**: 路径遍历攻击
- **FILE_UPLOAD**: 恶意文件上传

### 处理动作类型
- **1 (阻断)**: WAF拦截并阻止请求
- **2 (告警)**: WAF记录攻击但允许请求通过
- **3 (放行)**: WAF判断为误报，正常放行

## 使用指南

### 1. 攻击概览监控
1. 进入"网宿WAF攻击概览"页面
2. 选择合适的时间范围（建议从1小时开始）
3. 查看统计卡片了解整体攻击态势
4. 关注高危攻击数和独立攻击IP数的变化

### 2. 攻击趋势分析
1. 在时间序列分析区域设置时间范围
2. 选择合适的时间步长（建议1小时或6小时）
3. 根据需要选择统计指标（攻击次数、独立IP数等）
4. 可选择特定攻击类型进行专项分析
5. 观察攻击活动的时间模式和峰值

### 3. 威胁IP识别
1. 查看Top攻击IP排名表格
2. 关注威胁等级为"极高"和"高"的IP地址
3. 点击"分析"按钮查看特定IP的攻击趋势
4. 结合地理位置信息判断攻击来源特征

### 4. 多维度分析
1. 查看攻击类型分布，识别主要威胁类型
2. 分析处理动作分布，评估WAF防护效果
3. 检查被攻击域名分布，识别重点保护目标
4. 观察地域分布，了解攻击来源特征
5. 分析时间分布模式，识别攻击活跃时段

### 5. 详细日志查询
1. 切换到"网宿WAF攻击探查"标签页
2. 使用时间范围和字段过滤器缩小查询范围
3. 查看具体的攻击事件详情
4. 分析攻击载荷和WAF规则匹配情况

## 告警和响应

### 告警阈值建议
- **攻击次数突增**: 1小时内攻击次数超过平时3倍
- **新增高危IP**: 单IP 1小时内攻击超过100次
- **新攻击类型**: 检测到新的攻击类型
- **域名攻击集中**: 单域名1小时内攻击超过50%

### 响应流程
1. **立即响应**: 发现高危攻击时立即查看详情
2. **威胁评估**: 分析攻击规模、类型和目标
3. **防护调整**: 根据攻击特征调整WAF规则
4. **IP封禁**: 对确认的恶意IP进行封禁
5. **持续监控**: 跟踪攻击趋势变化

## 性能优化

### 查询优化建议
- 避免查询过长的时间范围（建议不超过7天）
- 使用合适的时间步长减少数据点数量
- 在高峰期适当增加查询间隔
- 利用攻击类型过滤减少数据量

### 系统监控
- 监控VictoriaLogs的查询性能
- 关注API响应时间变化
- 定期清理过期的日志数据
- 监控系统资源使用情况

## 故障排除

### 常见问题
1. **数据加载失败**: 检查VictoriaLogs服务状态
2. **图表显示异常**: 清除浏览器缓存重新加载
3. **查询超时**: 缩小时间范围或增加过滤条件
4. **数据不一致**: 等待数据同步完成

### 日志位置
- 后端日志: `securio-logmgr/logs/`
- VictoriaLogs日志: 查看VictoriaLogs服务日志
- 前端错误: 浏览器开发者工具Console

## 测试验证

系统提供了完整的测试脚本用于验证功能：

```bash
# 运行功能测试
./scripts/test_wangsu_attack_logs.sh
```

测试内容包括：
- VictoriaLogs服务连通性
- 各API接口功能验证
- 性能测试
- 数据一致性验证

## 版本更新日志

### v1.0.0 (初始版本)
- 实现基础攻击统计功能
- 提供多维度图表分析
- 支持攻击趋势时间序列分析
- 集成Top攻击IP排名
- 完整的日志详细查询功能

## 技术支持

如遇到技术问题，请联系：
- 开发团队: <EMAIL>
- 运维团队: <EMAIL>

---

*最后更新: 2025年1月* 