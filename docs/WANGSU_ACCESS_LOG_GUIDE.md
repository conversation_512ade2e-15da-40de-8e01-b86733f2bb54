# 网宿 WAF 访问日志使用指南

## 概述

网宿 WAF 访问日志功能提供了对网宿 CDN 访问日志的全面分析和统计展示。该功能包括：

1. **实时统计指标**：总请求数、独立访客、成功率、流量统计等
2. **多维度图表分析**：按状态码、浏览器、操作系统、地域等维度进行统计
3. **详细日志查询**：支持复杂的日志查询和过滤
4. **响应式设计**：适配不同屏幕尺寸

## 数据架构

### 数据流向
```
网宿CDN → 日志收集 → VictoriaLogs → 后端API → 前端展示
```

### 技术架构
- **数据存储**：VictoriaLogs (vmlog2实例)
- **数据流名称**：`SECURIO_WANGSU_WAF_ACCESS`
- **后端API**：Spring Boot REST接口
- **前端展示**：Vue.js + ECharts

## 功能特性

### 1. 统计指标卡片
- **总请求数**：查询语句 `count() totalReq`
- **独立访客**：查询语句 `count_uniq(host) uniqReq`
- **成功率**：基于状态码200的成功请求比例
- **服务端到客户端流量**：查询语句 `sum(size) totalSize`
- **客户端请求大小**：查询语句 `sum(upsize) totalUpSize`

### 2. 时间序列折线图
- **渠道筛选**：支持输入特定渠道名称进行分析
- **时间范围选择**：支持自定义开始和结束时间
- **步长控制**：支持5分钟到1天的不同时间步长
- **多种指标**：
  - 请求数：`stats by (channel) count(*)`
  - 响应流量：`stats by (channel) sum(size)`
  - 请求流量：`stats by (channel) sum(upsize)`
  - 独立访客：`stats by (channel) count_uniq(host)`

### 3. 渠道排名表格
- **Top 50排名**：显示访问量最高的前50个渠道
- **实时排名**：支持不同时间范围的排名统计
- **交互操作**：
  - 点击渠道名称可快速选择进行时间序列分析
  - 支持按访问次数排序
  - 显示各渠道占比
- **数据展示**：
  - 排名徽章（前三名特殊样式）
  - 访问次数格式化显示
  - 百分比占比计算
- **查询语句**：`stats by (channel) count() cnt | sort by (cnt desc) | limit 50`

### 4. 多维度图表
- 状态码分布
- 请求方法分布
- 浏览器类型分布
- 操作系统分布
- 地域分布（省份）
- 设备类型分布
- 频道分布
- 运营商分布
- 协议分布
- 请求方案分布

### 5. 时间范围控制
支持以下时间范围：
- 最近 5 分钟 (5m)
- 最近 30 分钟 (30m)
- 最近 1 小时 (1h)
- 最近 6 小时 (6h)
- 最近 12 小时 (12h)
- 最近 24 小时 (1d)
- 最近 3 天 (3d)
- 最近 7 天 (7d)

### 6. 自动刷新
- 支持自动刷新功能
- 刷新间隔：60秒
- 可手动开启/关闭

## API 接口

### 1. 获取统计指标
```http
GET /opslog/wangsu/stats?timeRange=1h
```

**响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalRequests": 17024137,
    "successRequests": 14654957,
    "uniqueRequests": 151893,
    "totalSize": 1917097898743,
    "totalUpSize": 33659747936,
    "successRate": 86.08
  }
}
```

### 2. 获取图表数据
```http
GET /opslog/wangsu/charts?timeRange=1h&limit=1000
```

**响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "facets": [
      {
        "field_name": "code",
        "values": [
          {"field_value": "200", "hits": 14646354},
          {"field_value": "101", "hits": 595030}
        ]
      }
    ]
  }
}
```

### 3. 获取时间序列数据
```http
GET /opslog/wangsu/timeseries?channel=appapi.5i5j.com&start=2025-07-01Z&end=2025-07-02Z&step=1h&metric=count
```

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| channel | String | 是 | 渠道名称 |
| start | String | 是 | 开始时间（ISO格式） |
| end | String | 是 | 结束时间（ISO格式） |
| step | String | 否 | 时间步长，默认1h |
| metric | String | 否 | 统计指标类型，默认count |

**metric参数支持的值**：
- `count`：请求数统计
- `size`：响应流量统计
- `upsize`：请求流量统计
- `unique`：独立访客统计

**响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "status": "success",
    "data": {
      "resultType": "matrix",
      "result": [
        {
          "metric": {
            "__name__": "count(*)",
            "channel": "appapi.5i5j.com"
          },
          "values": [
            [1751335200, "379094"],
            [1751338800, "1852812"],
            [1751342400, "2980367"]
          ]
        }
      ]
    }
  }
}
```

### 4. 获取渠道排名数据
```http
GET /opslog/wangsu/channel-ranking?timeRange=1d&limit=50
```

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| timeRange | String | 否 | 时间范围，默认1d |
| limit | Integer | 否 | 返回记录数，默认50 |

**响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "rank": 1,
      "channel": "appapi.5i5j.com",
      "count": 11402868
    },
    {
      "rank": 2,
      "channel": "beijing.cbs.bacic5i5j.com",
      "count": 5774643
    },
    {
      "rank": 3,
      "channel": "3dvr-cdn.5i5j.com",
      "count": 5437540
    }
  ]
}
```

## 数据字段说明

### 关键字段
| 字段名 | 说明 | 示例 |
|--------|------|------|
| host | 客户端IP地址 | ************ |
| channel | 访问频道/域名 | suzhou.1zu.com |
| code | HTTP状态码 | 200, 404, 500 |
| size | 响应大小（字节） | 3469 |
| upsize | 请求大小（字节） | 265 |
| clientprovince | 客户端省份 | beijing, shanghai |
| browser | 浏览器类型 | Chrome, Safari |
| os | 操作系统 | Windows 10, iOS |
| device | 设备类型 | Apple, HuaWei |
| method | HTTP方法 | GET, POST |
| protocol | 协议版本 | HTTP/1.1, HTTP/2.0 |
| scheme | 请求方案 | https, http |

### 完整字段列表
系统支持18个字段的统计分析：
- browser, bsize, cdnip, channel
- clientcountry, clientisp, clientprovince
- code, device, method, os
- protocol, scheme, servercountry
- serverisp, serverprovince, upsize, host

## 使用指南

### 1. 访问页面
导航至：**访问日志管理 → 网宿 WAF 访问日志**

### 2. 查看统计概览
- 页面默认显示最近1小时的统计数据
- 5个统计卡片展示核心指标
- 10个图表展示多维度分析

### 3. 调整时间范围
- 使用页面顶部的时间范围选择器
- 选择后自动刷新所有数据

### 4. 使用时间序列分析
- **默认展示**：页面加载时自动显示 `appapi.5i5j.com` 渠道的最近24小时趋势图
- **快速开始**：点击渠道排名表格中任意渠道的"分析"按钮，自动填入渠道并开始分析
- **手动输入渠道**：在渠道输入框中输入要分析的渠道，如：`appapi.5i5j.com`
- **选择时间范围**：使用日期时间选择器设置分析的开始和结束时间
- **设置时间步长**：从下拉菜单选择合适的时间间隔
  - 5分钟：适合短时间精细分析
  - 1小时：适合日常监控
  - 1天：适合长期趋势分析
- **选择统计指标**：
  - 请求数：查看访问量趋势
  - 响应流量：分析下行流量变化
  - 请求流量：分析上行流量变化
  - 独立访客：查看用户活跃度趋势
- **智能刷新**：修改任何参数后，系统会自动刷新图表（500毫秒防抖）
- **无需手动查询**：所有参数变更都会自动触发数据更新

### 5. 使用渠道排名表格
- **查看排名**：表格默认显示最近1天内访问量最高的前50个渠道
- **调整时间范围**：使用下拉菜单选择不同的统计时间范围
  - 最近1小时：查看当前热门渠道
  - 最近1天：查看日常访问模式
  - 最近7天：查看长期趋势
- **交互操作**：
  - 点击渠道名称：自动填入时间序列分析的渠道输入框
  - 点击"分析"按钮：快速跳转到时间序列分析区域
  - 排序功能：按访问次数进行排序
- **数据理解**：
  - 排名：基于访问次数的排序位置
  - 访问次数：该渠道在指定时间范围内的总请求数
  - 占比：该渠道访问量占所有渠道总访问量的百分比

### 6. 开启自动刷新
- 点击"自动刷新"开关
- 系统每60秒自动更新数据

### 7. 查看详细日志
- 点击"网宿 WAF 日志探查"标签
- 支持复杂的LogQL查询
- 可按多个字段进行过滤

## 性能优化

### 1. 数据加载优化
- **并行加载**：统计数据和图表数据并行获取
- **后端聚合**：所有统计计算在后端完成
- **缓存机制**：VictoriaLogs内置查询缓存

### 2. 前端优化
- **响应式设计**：自适应不同屏幕尺寸
- **图表懒加载**：按需渲染图表
- **内存管理**：组件销毁时清理图表实例

### 3. 查询优化
- **时间索引**：使用 `_time` 字段进行时间过滤
- **流过滤**：精确指定数据流名称
- **限制结果**：合理设置 limit 参数

## 性能测试结果

### 查询性能
| 时间范围 | 查询时间 | 数据量 |
|----------|----------|--------|
| 5分钟 | 100ms | 961,334 |
| 1小时 | 103ms | 17,020,284 |
| 6小时 | 172ms | 48,900,678 |
| 1天 | 339ms | 48,900,678 |

### 统计指标示例
- **总请求数**：17,024,137
- **成功请求数**：14,654,957
- **独立访客**：151,893
- **成功率**：86.08%
- **服务端到客户端流量**：1,917,097,898,743 bytes
- **客户端请求大小**：33,659,747,936 bytes

## 技术实现

### 1. 后端实现
- **控制器**：`WangsuLogAuditController`
- **服务层**：`VictoriaLogsService`
- **数据处理**：并行查询，结果聚合
- **错误处理**：完整的异常处理机制

### 2. 前端实现
- **API调用**：统一使用 `@/api/opslog/wangsu.js`
- **组件设计**：`wangsu_stat_overview.vue`
- **图表渲染**：ECharts 响应式图表
- **状态管理**：Vue 组件状态管理

### 3. 数据安全
- **API封装**：前端不直接访问VictoriaLogs
- **参数验证**：后端接口参数校验
- **错误处理**：优雅的错误处理和用户提示

## 故障排除

### 1. 数据不显示
- 检查VictoriaLogs服务状态
- 确认数据流 `SECURIO_WANGSU_WAF_ACCESS` 存在
- 检查时间范围设置

### 2. 查询性能慢
- 缩小时间范围
- 检查VictoriaLogs资源使用情况
- 优化查询语句

### 3. 图表不渲染
- 检查浏览器控制台错误
- 确认ECharts库加载正常
- 检查图表容器DOM元素

### 4. API调用失败
- 检查后端服务状态
- 确认API路径正确
- 检查网络连接

## 测试验证

### 1. 功能测试
运行测试脚本：
```bash
bash scripts/test_wangsu_logs.sh
```

### 2. 性能测试
- VictoriaLogs服务健康检查
- 不同时间范围的查询性能测试
- 数据完整性验证

### 3. 集成测试
- 前后端API接口测试
- 数据流完整性测试
- 用户界面功能测试

## 更新日志

### v1.4.2 (2025-07-01)
**功能优化**：
- 🚀 页面默认显示：进入页面时自动显示 `appapi.5i5j.com` 渠道的时间序列折线图
- 📊 即时数据展示：无需手动操作即可查看默认渠道的最近24小时数据趋势
- ⚡ 加载优化：使用 `$nextTick` 确保DOM渲染完成后再加载图表数据

**用户体验提升**：
- 🎯 零操作展示：打开页面即可看到核心数据
- 📈 默认配置：预设最佳的查询参数（24小时时间范围，1小时步长，请求数指标）
- 🔄 智能加载：页面加载完成后自动执行时间序列查询

### v1.4.1 (2025-07-01)
**功能优化**：
- ✨ 新增自动查询功能：点击渠道排名表格的"分析"按钮自动执行时间序列查询
- 🔄 智能刷新：修改时间范围、步长、统计指标时自动刷新折线图
- ⚡ 防抖优化：500毫秒防抖机制，避免频繁请求
- 🛡️ 表单验证：自动验证渠道名称和时间范围，避免无效请求
- 📱 用户体验：自动滚动到图表区域，提供即时反馈

**技术改进**：
- 🔧 添加防抖定时器管理
- 🧹 完善组件销毁时的资源清理
- 📊 优化图表渲染性能

### v1.4.0 (2025-07-01)
**新增功能**：
- ✨ 新增渠道排名表格功能
- 📊 显示Top 50访问量最高的渠道
- 🏆 前三名排名特殊徽章样式
- 🔗 支持点击渠道名称快速进行时间序列分析
- 📈 显示各渠道访问量占比
- ⏰ 支持多种时间范围的排名统计（1小时到7天）
- 🎯 新增后端API接口：`/opslog/wangsu/channel-ranking`

**优化改进**：
- 💫 优化表格交互体验，支持排序和快速操作
- 🎨 美化排名徽章和数据展示样式
- 📱 响应式设计，适配移动端显示

### v1.3.0 (2025-06-30)
**新增功能**：
- ✨ 新增时间序列折线图功能
- 📊 支持4种统计指标：请求数、响应流量、请求流量、独立访客
- ⏱️ 支持8种时间步长：从5分钟到1天
- 🎯 新增后端API接口：`/opslog/wangsu/timeseries`
- 📈 ECharts图表展示，支持数据缩放和交互

**优化改进**：
- 🔄 流量数据自动单位转换（B/KB/MB/GB）
- 🎨 图表样式优化，平滑曲线和面积填充
- 📱 响应式设计，适配不同屏幕尺寸

### v1.2.0 (2025-06-29)
**架构优化**：
- 🏗️ 重构数据获取架构，移除前端直接调用VictoriaLogs
- 🔒 统一通过后端API获取数据，提高安全性
- 🚀 新增后端API接口：`/opslog/wangsu/charts`
- 📝 完善错误处理和日志记录

**移除功能**：
- ❌ 移除前端硬编码的VictoriaLogs地址
- ❌ 移除axios直接调用VictoriaLogs的代码

### v1.1.0 (2025-06-28)
**功能增强**：
- 📊 完善多维度统计图表
- 🔄 新增自动刷新功能
- ⏰ 支持多种时间范围选择

### v1.0.0 (2025-06-27)
**初始版本**：
- 📈 基础统计卡片展示
- 📊 多维度数据可视化
- 🔍 日志探查功能

## 联系支持

如有问题或建议，请联系技术支持团队。 