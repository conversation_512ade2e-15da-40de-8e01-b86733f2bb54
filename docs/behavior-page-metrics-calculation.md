# 上网行为审计页面指标计算逻辑文档

## 概述

本文档详细描述了 `securio-ui/src/views/auditlog/behavior.vue` 页面中各个指标的计算逻辑和数据处理方式。该页面主要用于展示和分析 H3C 上网行为审计日志数据。

## 页面结构

### 标签页组织

页面采用标签页结构，包含以下四个标签页：

1. **行为日志概览** (`overview`) - 数据统计和可视化分析
2. **流量统计** (`statics`) - 流量统计类型日志查询
3. **行为日志** (`web_access`) - 网页访问类型日志查询  
4. **全部日志** (`all`) - 所有类型日志查询

## 数据源配置

### 日志流配置
- **数据流**: `AUDITLOG_H3C_BEHAVIOR`
- **实例**: `vmlog1` (默认)
- **默认时间范围**: 1小时

### 日志类型分类
- `web_access` - 网页访问日志
- `statistic_traffic` - 流量统计日志
- `app_access` - 应用访问日志
- `file_transfer` - 文件传输日志
- `email` - 邮件日志
- `im` - 即时通讯日志
- `p2p` - P2P下载日志
- `game` - 游戏日志
- `video` - 视频日志
- `music` - 音乐日志

## 概览页面指标计算

### 1. 数据总览卡片

#### 1.1 总活动记录
- **计算逻辑**: 统计指定时间范围内所有行为日志的总数量
- **数据来源**: 通过 `getFacets` API 获取第一个字段的所有值的 hits 总和
- **查询条件**: `_time:{timeRange} and stream:"AUDITLOG_H3C_BEHAVIOR"`
- **格式化**: 使用千分位分隔符显示 (如: 1,234,567)

#### 1.2 活跃用户组
- **计算逻辑**: 统计有活动记录的不同用户组数量
- **数据来源**: `user_group` 字段的不重复值数量
- **计算方式**: `topUserGroups.length`

#### 1.3 使用应用数
- **计算逻辑**: 统计被使用的不同应用数量
- **数据来源**: `app_name` 字段的不重复值数量
- **计算方式**: `topApps.length`

#### 1.4 操作类型数
- **计算逻辑**: 统计不同操作类型的数量
- **数据来源**: `action_name` 字段的不重复值数量
- **计算方式**: `topActions.length`

### 2. 图表分析

#### 2.1 操作类型分布图 (饼图)
- **数据来源**: `action_name` 字段统计
- **显示内容**: 各操作类型的占比分布
- **图表类型**: 饼图
- **显示格式**: 百分比 + 具体数值

#### 2.2 热门应用TOP10 (柱状图)
- **数据来源**: `app_name` 字段统计，取前10名
- **排序方式**: 按使用次数降序
- **图表类型**: 垂直柱状图
- **数值格式**: 超过1000显示为 "1.2k" 格式

#### 2.3 应用分组分布 (环形图)
- **数据来源**: `app_group` 字段统计，取前8名
- **图表类型**: 环形饼图 (内半径40%, 外半径70%)
- **显示内容**: 各应用分组的使用分布

#### 2.4 用户组活动统计TOP10 (柱状图)
- **数据来源**: `user_group` 字段统计，取前10名
- **排序方式**: 按活动次数降序
- **图表类型**: 垂直柱状图

### 3. 流量统计

#### 3.1 上行流量排名TOP10
- **API接口**: `getUploadTrafficRanking(timeRange, limit)`
- **查询逻辑**: 
  ```sql
  _time:{timeRange} and stream:"AUDITLOG_H3C_BEHAVIOR" and log_type:"statistic_traffic"
  | stats sum(upload_bytes) as sum_up by user_name
  | sort sum_up desc
  | limit 10
  ```
- **数据字段**: `user_name`, `sum_up`
- **显示格式**: 字节数转换为可读格式 (B, KB, MB, GB, TB)

#### 3.2 下行流量排名TOP10
- **API接口**: `getDownloadTrafficRanking(timeRange, limit)`
- **查询逻辑**:
  ```sql
  _time:{timeRange} and stream:"AUDITLOG_H3C_BEHAVIOR" and log_type:"statistic_traffic"
  | stats sum(download_bytes) as sum_down by user_name
  | sort sum_down desc
  | limit 10
  ```
- **数据字段**: `user_name`, `sum_down`
- **显示格式**: 字节数转换为可读格式

### 4. 热门统计表格

#### 4.1 热门操作类型
- **数据来源**: `topActions` 数组，显示前10条
- **排序方式**: 按操作次数降序
- **显示字段**: 操作类型名称、操作次数

#### 4.2 热门应用
- **数据来源**: `topApps` 数组，显示前10条
- **排序方式**: 按使用次数降序
- **显示字段**: 应用名称、使用次数

#### 4.3 热门应用分组
- **数据来源**: `topAppGroups` 数组，显示前10条
- **排序方式**: 按使用次数降序
- **显示字段**: 应用分组名称、使用次数

#### 4.4 热门用户组
- **数据来源**: `topUserGroups` 数组，显示前15条
- **排序方式**: 按活动次数降序
- **显示字段**: 用户组名称、活动次数、占比
- **占比计算**: `(item.hits / totalUserGroupHits) * 100`

## 日志查询页面指标计算

### 1. 数据格式化方法

#### 1.1 字节数格式化 (`formatBytes`)
```javascript
formatBytes(row, column, cellValue) {
  if (!cellValue || cellValue === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(cellValue) / Math.log(k));
  return parseFloat((cellValue / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
```
- **用途**: 格式化上传/下载流量字段
- **计算逻辑**: 二进制单位转换 (1024进制)
- **精度**: 保留2位小数

#### 1.2 处理动作格式化 (`formatHandleAction`)
```javascript
formatHandleAction(row, column, cellValue) {
  const actionMap = {
    '0': '允许',
    '1': '阻断', 
    '2': '监控',
    '3': '警告'
  };
  return actionMap[cellValue] || cellValue || '未知';
}
```
- **用途**: 将数字代码转换为中文描述
- **映射关系**: 0=允许, 1=阻断, 2=监控, 3=警告

#### 1.3 日志类型格式化 (`formatLogType`)
```javascript
formatLogType(row, column, cellValue) {
  const typeMap = {
    'web_access': '网页访问',
    'statistic_traffic': '流量统计',
    'app_access': '应用访问',
    'file_transfer': '文件传输',
    'email': '邮件',
    'im': '即时通讯',
    'p2p': 'P2P下载',
    'game': '游戏',
    'video': '视频',
    'music': '音乐'
  };
  return typeMap[cellValue] || cellValue || '未知';
}
```
- **用途**: 将英文日志类型转换为中文描述

#### 1.4 终端平台格式化 (`formatTermPlatform`)
```javascript
formatTermPlatform(row, column, cellValue) {
  if (!cellValue || cellValue === '未知类型') {
    return '未知';
  }
  return cellValue;
}
```
- **用途**: 处理终端平台字段的显示

#### 1.5 MAC地址格式化 (`formatMacAddress`)
```javascript
formatMacAddress(row, column, cellValue) {
  if (!cellValue) return '未知';
  return cellValue.toUpperCase().replace(/[:-]/g, ':');
}
```
- **用途**: 统一MAC地址格式为大写冒号分隔

#### 1.6 会话时长格式化 (`formatDuration`)
```javascript
formatDuration(row, column, cellValue) {
  // 如果有直接的时长字段，使用它
  if (cellValue) {
    return this.formatSeconds(cellValue);
  }
  
  // 否则从开始时间和结束时间计算
  const startTime = row.session_start_time || row.create_time;
  const endTime = row.session_end_time || row.end_time;
  
  if (startTime && endTime) {
    const duration = endTime - startTime;
    return this.formatSeconds(duration);
  }
  
  return '未知';
}
```
- **计算逻辑**: `session_end_time - session_start_time`
- **备用字段**: `end_time - create_time`
- **格式化**: 转换为 "X小时Y分Z秒" 格式

#### 1.7 秒数格式化 (`formatSeconds`)
```javascript
formatSeconds(seconds) {
  if (!seconds || seconds === 0) return '0秒';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}小时${minutes}分${secs}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${secs}秒`;
  } else {
    return `${secs}秒`;
  }
}
```

### 2. 字段配置

#### 2.1 全部日志配置 (`logSettings`)
- **显示字段**: 事件时间、用户IP、用户组、日志类型、原始消息
- **过滤条件**: 无默认过滤
- **用途**: 显示所有类型的日志记录

#### 2.2 网页访问配置 (`webAccessSettings`)
- **显示字段**: 事件时间、用户IP、用户组、源IP、目标IP、访问域名、网站分类、日志类型、处理动作、终端平台、原始消息
- **默认过滤**: `log_type = 'web_access'`
- **用途**: 专门显示网页访问行为日志

#### 2.3 流量统计配置 (`statisticsLogSettings`)
- **显示字段**: 事件时间、用户IP、MAC地址、用户组、应用名称、应用分组、上传流量、下载流量、日志类型、原始消息
- **默认过滤**: `log_type = 'statistic_traffic'`
- **用途**: 专门显示流量统计日志

## 数据刷新机制

### 1. 自动刷新
- **刷新间隔**: 60秒
- **控制方式**: 通过开关控制
- **实现方式**: `setInterval` 定时器

### 2. 手动刷新
- **触发方式**: 点击刷新按钮
- **刷新范围**: 所有数据和图表

### 3. 标签页切换刷新
- **触发条件**: 切换到日志查询相关标签页
- **实现方式**: `watch` 监听 `activeTab` 变化
- **延迟处理**: 使用 `$nextTick` 确保组件渲染完成

## 时间范围控制

### 支持的时间范围
- 5分钟、15分钟、30分钟
- 1小时、6小时、24小时  
- 3天、7天、14天

### 时间范围格式
- **VictoriaLogs格式**: `_time:5m`, `_time:1h`, `_time:1d`
- **自定义范围**: `_time:[2025-05-26T10:01:01,2025-05-26T10:05:32]`

## 性能优化

### 1. 数据限制
- **概览统计**: 字段统计限制50条
- **流量排名**: 限制TOP10
- **热门统计**: 显示TOP10-15条

### 2. 图表优化
- **响应式**: 监听窗口大小变化自动调整
- **内存管理**: 组件销毁时释放图表实例
- **渐进加载**: 分别加载不同类型的数据

### 3. 组件复用
- **唯一标识**: 使用 `key` 属性强制重新渲染
- **独立引用**: 不同标签页使用不同的 `ref` 名称
- **状态隔离**: 每个标签页维护独立的查询状态

## API接口说明

### 1. 字段统计接口
- **接口**: `getFacets(requestData)`
- **参数**: 查询语句、时间范围、字段列表、限制条数
- **返回**: 各字段的统计分布数据

### 2. 流量排名接口
- **上行流量**: `getUploadTrafficRanking(timeRange, limit)`
- **下行流量**: `getDownloadTrafficRanking(timeRange, limit)`
- **返回**: 用户流量排名数据

### 3. 日志查询接口
- **接口**: `queryLogs(query, null, null, limit, null, null, null, instance)`
- **参数**: LogQL查询语句、限制条数、实例名称
- **返回**: 符合条件的日志记录

## 错误处理

### 1. 数据解析错误
- **JSON解析**: 捕获解析异常，过滤无效数据
- **字段缺失**: 提供默认值或友好提示
- **类型转换**: 安全的数值转换

### 2. 网络错误
- **超时处理**: API调用超时提示
- **重试机制**: 手动刷新重新获取数据
- **降级显示**: 部分数据失败时显示可用数据

### 3. 用户体验
- **加载状态**: 显示加载动画和提示文字
- **空数据**: 友好的空状态提示
- **错误提示**: 明确的错误信息和建议操作

---

**文档版本**: v1.0  
**最后更新**: 2025-01-28  
**维护人员**: 开发团队 